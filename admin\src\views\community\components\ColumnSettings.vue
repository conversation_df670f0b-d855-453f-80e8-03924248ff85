<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="列设置"
    width="600px"
  >
    <div class="column-settings">
      <div class="settings-header">
        <span>自定义表格显示列</span>
        <el-button size="small" @click="resetToDefault">重置默认</el-button>
      </div>
      
      <div class="column-list">
        <draggable
          v-model="localColumns"
          item-key="key"
          @end="handleDragEnd"
        >
          <template #item="{ element }">
            <div class="column-item">
              <div class="column-info">
                <el-icon class="drag-handle"><Rank /></el-icon>
                <el-checkbox
                  v-model="element.visible"
                  @change="updateColumnVisibility"
                >
                  {{ element.label }}
                </el-checkbox>
              </div>
              <div class="column-controls">
                <el-input-number
                  v-model="element.width"
                  :min="80"
                  :max="500"
                  size="small"
                  controls-position="right"
                  @change="updateColumnWidth"
                />
                <span class="width-label">px</span>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Rank } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'

const props = defineProps({
  modelValue: Boolean,
  columns: {
    type: Array,
    required: true
  },
  visibleColumns: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'update'])

const localColumns = ref([])

// 监听props变化，更新本地数据
watch(() => props.columns, (newColumns) => {
  localColumns.value = newColumns.map(col => ({
    ...col,
    visible: props.visibleColumns.some(visCol => visCol.key === col.key)
  }))
}, { immediate: true })

const resetToDefault = () => {
  localColumns.value = props.columns.map(col => ({
    ...col,
    visible: true
  }))
}

const updateColumnVisibility = () => {
  // 实时更新可见性
}

const updateColumnWidth = () => {
  // 实时更新宽度
}

const handleDragEnd = () => {
  // 处理拖拽结束
}

const saveSettings = () => {
  const visibleColumns = localColumns.value
    .filter(col => col.visible)
    .map(col => ({
      ...col,
      visible: undefined // 移除临时属性
    }))
  
  emit('update', visibleColumns)
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.column-settings {
  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;
    
    span {
      font-weight: 600;
      color: #303133;
    }
  }

  .column-list {
    max-height: 400px;
    overflow-y: auto;

    .column-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #ebeef5;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fafafa;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f9ff;
        border-color: #3b82f6;
      }

      .column-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .drag-handle {
          cursor: move;
          color: #909399;
          
          &:hover {
            color: #3b82f6;
          }
        }
      }

      .column-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .width-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>