const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Login-BGEpWB-C.js","assets/vue-vendor-DGsK9sC4.js","assets/element-plus-DcSKpKA8.js","assets/utils-4VKArNEK.js","assets/Login-rSoNIJ5o.css","assets/DataScreenFullscreen-Bdu3VI8N.js","assets/DataScreen-BsKps5zq.js","assets/LineChart-Ba008-uu.js","assets/chart-Bup65vvO.js","assets/LineChart-OVmPlT36.css","assets/DoughnutChart-JEDVUFw0.js","assets/DoughnutChart-BIJ7x2iS.css","assets/echarts-DTArWCqr.js","assets/DataScreen-C8XmEIfT.css","assets/DataScreenFullscreen-DClPWAwn.css","assets/TestPage-DewmrKVF.js","assets/TestPage-DGSIHe7Z.css","assets/ModernDashboard-09GTIX74.js","assets/ModernDashboard-D_FhOm-E.css","assets/el-progress-Dw9yTa91.css","assets/el-tag-DljBBxJR.css","assets/el-select-CvzM3W2w.css","assets/RouteChecker-P5FJFeH5.js","assets/RouteChecker-D0WcVVkE.css","assets/el-tab-pane-DTGC0oAx.css","assets/el-table-column-CKoPG0Y8.css","assets/el-checkbox-DIj50LEB.css","assets/el-col-Ds2mGN2S.css","assets/el-card-fwQOLwdi.css","assets/SimpleDashboard-BvrVIwgP.js","assets/SimpleDashboard-C-QOgmFl.css","assets/el-timeline-item-BvbJTz1y.css","assets/SecurityManagement-DqGJdqK5.js","assets/PageLayout-OFR6SHfu.js","assets/PageLayout-DahFmBjs.css","assets/SecurityManagement-Bl5ckS82.css","assets/el-loading-DLSpKYce.css","assets/el-pagination-BNQcHhjS.css","assets/el-checkbox-group-D_6SYB2i.css","assets/el-form-item-DCFsf57O.css","assets/el-switch-B5lTGWdM.css","assets/el-input-number-DUUPPWGj.css","assets/Reports-CaxDczgg.js","assets/Reports-BWfI56SW.css","assets/el-empty-D4ZqTl4F.css","assets/el-date-picker-Db-ufUiu.css","assets/GroupList-QUEx6z1l.js","assets/community-Cx7BK33_.js","assets/chunk-KZPPZA2C-BZQYgWVq.js","assets/UserProfile-nYpRIurq.js","assets/format-3eU4VJ9V.js","assets/UserProfile-g2JeI23d.css","assets/ImageUpload-DAXj9MB_.js","assets/ImageUpload-BNDQ9u7E.css","assets/el-upload-q8uObtwj.css","assets/GroupList-CvGe-faG.css","assets/el-radio-group-BzMpJalG.css","assets/el-radio-BuDgLcOG.css","assets/el-radio-button-CSkroacn.css","assets/el-collapse-item-BqS7tZDP.css","assets/AutoRules-ChzNPyhI.js","assets/AutoRules-BsKmJ0KW.css","assets/EventManagement-CKksJpCO.js","assets/EventManagement-CRdY7go3.css","assets/ContentModeration-BJj8Aods.js","assets/ContentModeration-DirNfgnc.css","assets/AnalyticsDashboard-CuLWUSSS.js","assets/AnalyticsDashboard-DU4O3ZOQ.css","assets/TemplateManagement-Bea_zQEn.js","assets/TemplateManagement-jzrs_Ea7.css","assets/el-descriptions-item-o9ObloqJ.css","assets/el-text-3XkjT9nK.css","assets/GroupAdd-VdFI-xtG.js","assets/GroupAdd-ygBwBWC7.css","assets/GroupDetail-C5mlI8G2.js","assets/GroupDetail-BgYVefpA.css","assets/GroupMarketing-XD6stnjv.js","assets/marketing-D5ylsfMy.js","assets/GroupMarketing-lv5KkDw6.css","assets/GroupList-DnmHT4g9.js","assets/index-Do9uvhBr.js","assets/index-BJ8EPzFa.css","assets/GroupList-Bi8p_QLn.css","assets/DistributorList-Erukm10S.js","assets/DistributorList-kPRLj1LG.css","assets/DistributorDetail-Db3Si0Dr.js","assets/export-1WkTdiMF.js","assets/DistributorDetail-clmkuS2f.css","assets/CustomerManagement-ClG51h49.js","assets/StatCard-WpSR56Tk.js","assets/StatCard-zv7YkiNn.css","assets/CustomerManagement-D3ouH-X7.css","assets/el-alert-G57rL0jl.css","assets/DistributorDashboard-DKxouNiz.js","assets/DistributorDashboard-CYBg0_CZ.css","assets/GroupManagement-DFNGXo8X.js","assets/GroupManagement-CQ1_hA3i.css","assets/PromotionLinks-B6UMxVp0.js","assets/browser-DJkR4j8n.js","assets/PromotionLinks-EJvbIL5R.css","assets/el-color-picker-DOhQXICb.css","assets/el-slider-DtISwLyR.css","assets/CommissionLogs-_PM3wy9o.js","assets/CommissionLogs-DoBL2dkA.css","assets/el-link-B58a4a3I.css","assets/OrderList-S3UZWe8r.js","assets/OrderList-D6gfdl-q.css","assets/OwnerDashboard-CzQTq_lG.js","assets/OwnerDashboard-ChJx5Qdc.css","assets/FinanceDashboard-JEsymmcK.js","assets/finance-Cskg9amr.js","assets/FinanceDashboard-BL78GUcf.css","assets/CommissionLog-DalRnms0.js","assets/CommissionLog-CZOsge_q.css","assets/TransactionList-D1DxzW9i.js","assets/TransactionList-ucktdfU6.css","assets/WithdrawManage-CyeKTC7f.js","assets/WithdrawManage-CKkTsuI2.css","assets/UserCenter-DX5yO-S0.js","assets/AvatarUpload-QrJM1H43.js","assets/AvatarUpload-CBsjY5bz.css","assets/user-mKGRZpRV.js","assets/UserCenter-iE71WOG5.css","assets/UserList-CKsEKLdA.js","assets/UserList-BvuaQDD8.css","assets/Profile-tttXcO_w.js","assets/Profile-BCJjq1Q7.css","assets/UserAnalytics-Cmh5UXPl.js","assets/UserAnalytics-DcrgRIrd.css","assets/UserAdd-BOvRXeCi.js","assets/UserAdd-CEie6NoO.css","assets/SubstationList-DDQ-erKb.js","assets/substation-Bk2UQYn7.js","assets/SubstationList-CC1_2-70.css","assets/SubstationFinance-Co9hI-Gm.js","assets/SubstationFinance-CWJBXcs1.css","assets/SubstationPermissions-D2vRH3n1.js","assets/SubstationPermissions-Gp2Ow3yL.css","assets/SubstationAnalytics-Bd6Snhpj.js","assets/SubstationAnalytics-BfoEr8Yi.css","assets/AgentDashboard-XqAlNjir.js","assets/agent-SmxfvIrI.js","assets/AgentDashboard-Bjx2Tuqg.css","assets/AgentList-Dkj2HjhV.js","assets/AgentList-DMKRb6bv.css","assets/AgentApplications-ByLKtHvk.js","assets/AgentApplications-KeRWhJ10.css","assets/AgentCommission-Cz2P9BmQ.js","assets/AgentCommission-CIYEszOS.css","assets/AgentHierarchy-CVuo509U.js","assets/AgentHierarchy-BwGmLdku.css","assets/AgentPerformance-BDMi80cu.js","assets/AgentPerformance-DvqYonSd.css","assets/Dashboard-CnERKQsZ.js","assets/anti-block-BQ2PwvXK.js","assets/Dashboard-CQ6pQzUR.css","assets/EnhancedDashboard-jrKvGisZ.js","assets/EnhancedDashboard-BJd09IXY.css","assets/DomainList-Cl7hEoti.js","assets/DomainList-g-IIA-LC.css","assets/ShortLinkList-Co1FXK9v.js","assets/ShortLinkList-B6e-U5Of.css","assets/Analytics-BGcX8c47.js","assets/Analytics-W_bWcuzF.css","assets/ContentManagement-BFW8lonL.js","assets/content-BaH0vSdJ.js","assets/ContentManagement-CfKSZq1p.css","assets/ContentTemplates-DiI83_TV.js","assets/ContentTemplates-DgET0AxN.css","assets/AIGenerator-CA6nj_Cf.js","assets/AIGenerator-CU3qjOle.css","assets/RoleManagement-DVE5qPyC.js","assets/RoleManagement-pNcTws4p.css","assets/el-tree-C2sTlbKd.css","assets/PermissionManagement-woFlq7pl.js","assets/PermissionManagement-N7Br9Fr1.css","assets/LinkManagement-DdLYLk7s.js","assets/promotion-Bns02O4K.js","assets/LinkManagement-3Lxj6nGn.css","assets/LandingPages-B02a-OJB.js","assets/LandingPages-CZ9hcL7r.css","assets/Analytics-CaYNhaAK.js","assets/Analytics-BZLK6Iji.css","assets/OrderList-DbvoxFwQ.js","assets/OrderList-5dRjZ2fA.css","assets/OrderAnalytics-DnQftSh4.js","assets/OrderAnalytics-BLQIG9Tf.css","assets/OrderDetail-BhfV-_yQ.js","assets/OrderDetail-B_Ybh9fJ.css","assets/Settings-mvAAbHcQ.js","assets/system-CZ_rLt3R.js","assets/Settings-BFWfCzUD.css","assets/el-time-picker-B4D4rMOz.css","assets/SystemMonitor-Cyso9dJg.js","assets/SystemMonitor-B87BXpxf.css","assets/DeploymentMonitor-DllIuJ0F.js","assets/DeploymentMonitor-4kM7JNDq.css","assets/DataExport-BtOv5d6l.js","assets/DataExport-Bvx1OpEI.css","assets/Notifications-DIfDlwOJ.js","assets/Notifications-DZ_bUbQR.css","assets/OperationLogs-7VE7c_my.js","assets/OperationLogs-B1Iywmax.css","assets/FunctionTest-Dz1mkhiN.js","assets/FunctionTest-A4X9tfUv.css","assets/UserGuide-WSTSMlFE.js","assets/UserGuide-CKL1aRCv.css","assets/PaymentChannels-CbTpmc2p.js","assets/PaymentChannels-CrdDvC3W.css","assets/FileManagement-CkXECOgY.js","assets/FileManagement-DRIuEPz8.css","assets/PermissionLogs-BG7jSSWT.js","assets/PermissionLogs-xoxm-zIw.css","assets/PaymentSettings-CSwx3iS5.js","assets/payment-D7px_L1O.js","assets/PaymentSettings-BhjaPOwH.css","assets/PaymentOrders-D2erkauk.js","assets/PaymentOrders-O3f0dFDV.css","assets/PaymentRefunds-BZXvU9S8.js","assets/PaymentRefunds-BNuT-4jI.css","assets/403-CtjY_-j_.js","assets/403-Dt5B10JX.css","assets/NotFound-0frYsztm.js","assets/NotFound-C0VFs2EQ.css"])))=>i.map(i=>d[i]);
import{a4 as e,k as t,l as a,E as o,af as n,r as s,c as i,ag as r,y as l,z as c,t as m,B as d,C as u,T as p,F as _,Y as h,D as g,u as f,ah as v,d as b,e as y,H as E,ai as D,a2 as k,aj as L,ak as w,al as A,ad as I,am as T}from"./vue-vendor-DGsK9sC4.js";import{a as P,b as R,N as O}from"./utils-4VKArNEK.js";import{Q as V,R as j,S,o as C,T as M,U as x,V as U,W as F,X as B,Y as G,Z as N,_ as $,$ as q,a0 as K,a1 as z,a2 as H,a3 as W,a4 as J,a5 as X,a6 as Q,a7 as Y,a8 as Z,a9 as ee,aa as te,ab as ae,ac as oe,ad as ne,ae as se,af as ie,ag as re,ah as le,ai as ce,aj as me,ak as de,al as ue,am as pe,an as _e,ao as he,ap as ge,aq as fe,ar as ve,as as be,at as ye,au as Ee,av as De,aw as ke,ax as Le,ay as we,az as Ae,aA as Ie,aB as Te,aC as Pe,aD as Re,aE as Oe,aF as Ve,aG as je,aH as Se,aI as Ce,aJ as Me,aK as xe,aL as Ue,aM as Fe,aN as Be,aO as Ge,aP as Ne,aQ as $e,aR as qe}from"./element-plus-DcSKpKA8.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const Ke={id:"app"},ze={__name:"App",setup:n=>(console.log("晨鑫流量变现系统 管理后台启动"),(n,s)=>{const i=e("router-view");return a(),t("div",Ke,[o(i)])})},He={},We=function(e,t,a){let o=Promise.resolve();if(t&&t.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),a=e?.nonce||e?.getAttribute("nonce");o=Promise.allSettled(t.map(e=>{if((e=function(e){return"/admin/"+e}(e))in He)return;He[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${o}`))return;const n=document.createElement("link");return n.rel=t?"stylesheet":"modulepreload",t||(n.as="script"),n.crossOrigin="",n.href=e,a&&n.setAttribute("nonce",a),document.head.appendChild(n),t?new Promise((t,a)=>{n.addEventListener("load",t),n.addEventListener("error",()=>a(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return o.then(t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)})},Je="Admin-Token";function Xe(){return P.get(Je)}function Qe(e){return P.set(Je,e,{expires:7})}function Ye(){P.remove(Je),P.remove("Admin-Refresh-Token")}const Ze=R.create({baseURL:"/api/v1",timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});Ze.interceptors.request.use(e=>{const t=Xe();return t&&(e.headers.Authorization=`Bearer ${t}`),"get"===e.method&&(e.params={...e.params,_t:Date.now()}),e},e=>(console.error("请求拦截器错误:",e),Promise.reject(e))),Ze.interceptors.response.use(e=>{const t=e.data;return 200!==t.code&&0!==t.code?(V({message:t.message||"请求失败",type:"error",duration:5e3}),50008!==t.code&&50012!==t.code&&50014!==t.code||j.confirm("你已被登出，可以取消继续留在该页面，或者重新登录","确定登出",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{Ye(),location.reload()}),Promise.reject(new Error(t.message||"请求失败"))):t},e=>{console.error("响应拦截器错误:",e);let t="请求失败";if(e.response){const{status:a,data:o}=e.response;switch(a){case 400:t=o.message||"请求参数错误";break;case 401:t="未授权，请重新登录",Ye(),ha.push("/login");break;case 403:t="拒绝访问";break;case 404:t="请求地址出错";break;case 408:t="请求超时";break;case 500:t="服务器内部错误";break;case 501:t="服务未实现";break;case 502:t="网关错误";break;case 503:t="服务不可用";break;case 504:t="网关超时";break;case 505:t="HTTP版本不受支持";break;default:t=o.message||`连接错误${a}`}}else t=e.request?"网络连接异常，请检查网络":e.message||"请求配置错误";return V({message:t,type:"error",duration:5e3}),Promise.reject(e)});const et=()=>Ze({url:"/admin/auth/user",method:"get"}),tt=e=>Ze({url:"/admin/auth/profile",method:"put",data:e}),at=e=>Ze({url:"/admin/auth/password",method:"put",data:e}),ot=n("user",()=>{const e=s(Xe()),t=s(null),a=i(()=>t.value?.roles||[]),o=i(()=>t.value?.nickname||""),n=i(()=>t.value?.avatar||""),r=i(()=>t.value?.role||""),l=i(()=>"admin"===r.value),c=i(()=>"substation"===r.value),m=i(()=>"agent"===r.value),d=i(()=>"distributor"===r.value),u=i(()=>"group_owner"===r.value),p=i(()=>"user"===r.value),_=i(()=>{try{const e=localStorage.getItem("sessionInfo");return e?JSON.parse(e):null}catch{return null}}),h=i(()=>{if(!e.value||!_.value)return!1;const t=new Date(_.value.loginTime);return new Date-t<864e5}),g=()=>{window.autoLogoutTimer&&clearTimeout(window.autoLogoutTimer),window.autoLogoutTimer=setTimeout(()=>{f()},864e5)},f=async()=>{try{await Ze({url:"/admin/auth/logout",method:"post"})}catch(e){}finally{v(),t.value=null,localStorage.removeItem("sessionInfo"),window.autoLogoutTimer&&(clearTimeout(window.autoLogoutTimer),window.autoLogoutTimer=null)}},v=()=>{Ye(),e.value=""};return{token:e,userInfo:t,roles:a,nickname:o,avatar:n,userRole:r,sessionInfo:_,isSessionValid:h,isAdmin:l,isSubstation:c,isAgent:m,isDistributor:d,isGroupOwner:u,isUser:p,login:async a=>{try{const o=await(e=>Ze({url:"/admin/auth/login",method:"post",data:e}))(a),n=o.data;if(n.success){Qe(n.data.token),e.value=n.data.token,t.value=n.data.user;const a={loginTime:(new Date).toISOString(),userAgent:navigator.userAgent,role:n.data.user.role,userId:n.data.user.id,username:n.data.user.username};return localStorage.setItem("sessionInfo",JSON.stringify(a)),g(),n}throw new Error(n.message||"登录失败")}catch(o){if(console.error("登录失败:",o),o.response){const e=o.response.status,t=o.response.data?.message||o.message;switch(e){case 401:throw new Error("用户名或密码错误");case 403:throw new Error("账户已被禁用或权限不足");case 429:throw new Error("登录尝试过于频繁，请稍后再试");case 500:throw new Error("服务器内部错误，请稍后重试");default:throw new Error(t||"登录失败")}}throw o}},getUserInfo:async()=>{try{const e=(await et()).data;if(e.success)return t.value=e.data.user,e;throw new Error(e.message||"获取用户信息失败")}catch(e){throw console.error("获取用户信息失败:",e),e}},logout:f,resetToken:v,setToken:t=>{Qe(t),e.value=t},setUserInfo:e=>{t.value=e},setupAutoLogout:g,hasPermission:e=>{if(!t.value)return!1;if(l.value)return!0;return({substation:["user_management","agent_management","order_management","group_management","finance_view"],agent:["team_management","commission_view","performance_view","application_management"],distributor:["customer_management","group_management","promotion_management","commission_view"],group_owner:["group_management","content_management","template_management"],user:["profile_management","order_view"]}[r.value]||[]).includes(e)},hasRouteAccess:e=>{if(!t.value)return!1;if(l.value)return!0;try{const{checkMenuPermission:t}=require("@/config/navigation");return t({path:e},r.value)}catch{return!1}},getUserDefaultRoute:()=>{try{const{getUserDefaultRoute:e}=require("@/config/navigation");return e(r.value)}catch{return"/dashboard"}},enterPreviewMode:()=>{const a="preview-mode-token-"+Date.now();Qe(a),e.value=a,t.value={id:1,username:"admin",nickname:"超级管理员 (预览)",email:"<EMAIL>",avatar:"/default-avatar.png",role:"admin",roles:["admin"],permissions:["*"]},console.log("🎭 预览模式已激活，用户信息已设置")}}}),nt={admin:{allowedRoutes:["*"],defaultRoute:"/dashboard",workbench:"/dashboard"},substation:{allowedRoutes:["/dashboard","/user","/community","/orders","/finance","/agent","/substation","/distribution/distributors","/promotion"],defaultRoute:"/dashboard",workbench:"/dashboard"},agent:{allowedRoutes:["/agent/dashboard","/agent/list","/agent/applications","/agent/commission","/agent/hierarchy","/agent/performance","/user/center","/user/profile","/finance/commission-logs","/promotion/links"],defaultRoute:"/agent/dashboard",workbench:"/agent/dashboard"},distributor:{allowedRoutes:["/distributor/dashboard","/distribution/customers","/community/groups","/orders/list","/finance/commission-logs","/promotion/links","/user/center","/user/profile"],defaultRoute:"/distributor/dashboard",workbench:"/distributor/dashboard"},group_owner:{allowedRoutes:["/owner/dashboard","/community/groups","/content/management","/content/templates","/user/center","/user/profile","/orders/list"],defaultRoute:"/owner/dashboard",workbench:"/owner/dashboard"},user:{allowedRoutes:["/user/center","/user/profile","/orders/my","/community/my-groups"],defaultRoute:"/user/center",workbench:"/user/center"}};function st(e,t){if(!t||!e)return!1;const a=nt[t];if(!a)return!1;if(a.allowedRoutes.includes("*"))return!0;const o=e.path||e;return a.allowedRoutes.some(e=>e===o||!!o.startsWith(e+"/"))}function it(e,t){return t&&e?e.filter(e=>!e.meta?.hidden&&(!!st(e,t)&&!(e.children&&e.children.length>0&&(e.children=it(e.children,t),0===e.children.length&&e.redirect)))):[]}const rt={admin:"超级管理员",substation:"分站管理员",agent:"代理商",distributor:"分销员",group_owner:"群主",user:"普通用户"};function lt(e){return rt[e]||"未知角色"}const ct=Object.freeze(Object.defineProperty({__proto__:null,checkMenuPermission:st,filterRoutesByRole:it,getRoleDisplayName:lt,getUserDefaultRoute:function(e){const t=nt[e];return t?.defaultRoute||"/user/center"},roleDisplayNames:rt,roleNavigationConfig:nt},Symbol.toStringTag,{value:"Module"})),mt=(e,t)=>{const a=e.__vccOpts||e;for(const[o,n]of t)a[o]=n;return a},dt={class:"enhanced-menu-item"},ut={class:"menu-item-content"},pt={key:1,class:"default-icon"},_t={key:0,class:"menu-title"},ht={key:0,class:"menu-badge"},gt={key:1,class:"menu-shortcut"},ft={key:2,class:"notification-dot"},vt={class:"menu-item-content"},bt={key:1,class:"default-icon"},yt={key:0,class:"menu-title"},Et={key:0,class:"menu-badge"},Dt={key:1,class:"notification-dot"},kt={__name:"ModernMenuItem",props:{item:{type:Object,required:!0},basePath:{type:String,default:""},collapsed:{type:Boolean,default:!1}},setup(n){const s=n,g=r();ot();const f=i(()=>s.item.children&&s.item.children.length>0),v=i(()=>s.item.path.startsWith("/")?s.item.path:`${s.basePath}/${s.item.path}`.replace(/\/+/g,"/")),b=()=>{s.item.meta?.external?window.open(s.item.path,"_blank"):g.push(v.value)},y=()=>({Monitor:"dashboard-icon",DataLine:"datascreen-icon",User:"user-icon",UserFilled:"user-icon",Comment:"community-icon",Money:"finance-icon",Tickets:"order-icon",Setting:"system-icon",DataAnalysis:"analytics-icon",Connection:"network-icon",Document:"document-icon",Lock:"security-icon"}[s.item.meta?.icon]||"default-icon"),E=()=>({Monitor:ve,DataLine:fe,Comment:ge,User:he,UserFilled:_e,Document:pe,Share:ue,Grid:de,Avatar:me,Money:ce,List:le,OfficeBuilding:re,Lock:ie,Connection:se,TrendCharts:Q,Tools:ne,Edit:oe,DocumentCopy:ae,MagicStick:te,Key:ee,Link:Z,Tickets:Y,DataAnalysis:Q,Upload:X,Download:J,Goods:W,Medal:H,Cpu:z,Setting:K,InfoFilled:q,CreditCard:$,Folder:N,View:G,Bell:B}[s.item.meta?.icon]||ve),D=()=>["/system/notifications","/orders/list","/user/list"].includes(s.item.path);return(s,i)=>{const r=M,g=S,k=U,L=e("ModernMenuItem",!0),w=F;return a(),t("div",dt,[f.value?(a(),l(k,{key:1,content:n.item.meta?.title,placement:"right",disabled:!n.collapsed,"show-after":500},{default:c(()=>[o(w,{index:v.value,class:"menu-item-group"},{title:c(()=>[m("div",vt,[m("div",{class:C(["menu-icon",y()])},[n.item.meta?.icon?(a(),l(r,{key:0},{default:c(()=>[(a(),l(u(E())))]),_:1})):(a(),t("div",bt,i[3]||(i[3]=[m("div",{class:"icon-dot"},null,-1)])))],2),o(p,{name:"menu-text"},{default:c(()=>[n.collapsed?d("",!0):(a(),t("span",yt,x(n.item.meta?.title),1))]),_:1}),n.item.meta?.badge&&!n.collapsed?(a(),t("div",Et,x(n.item.meta.badge),1)):d("",!0),D()&&!n.collapsed?(a(),t("div",Dt)):d("",!0)])]),default:c(()=>[(a(!0),t(_,null,h(n.item.children,e=>(a(),l(L,{key:e.path,item:e,"base-path":v.value,collapsed:n.collapsed,class:"submenu-item"},null,8,["item","base-path","collapsed"]))),128))]),_:1},8,["index"])]),_:1},8,["content","disabled"])):(a(),l(k,{key:0,content:n.item.meta?.title,placement:"right",disabled:!n.collapsed,"show-after":500},{default:c(()=>[o(g,{index:v.value,class:"menu-item-single",onClick:b},{default:c(()=>[m("div",ut,[m("div",{class:C(["menu-icon",y()])},[n.item.meta?.icon?(a(),l(r,{key:0},{default:c(()=>[(a(),l(u(E())))]),_:1})):(a(),t("div",pt,i[0]||(i[0]=[m("div",{class:"icon-dot"},null,-1)])))],2),o(p,{name:"menu-text"},{default:c(()=>[n.collapsed?d("",!0):(a(),t("span",_t,x(n.item.meta?.title),1))]),_:1}),n.item.meta?.badge&&!n.collapsed?(a(),t("div",ht,x(n.item.meta.badge),1)):d("",!0),n.item.meta?.shortcut&&!n.collapsed?(a(),t("div",gt,x(n.item.meta.shortcut),1)):d("",!0),D()&&!n.collapsed?(a(),t("div",ft)):d("",!0)]),i[1]||(i[1]=m("div",{class:"menu-item-bg"},null,-1)),i[2]||(i[2]=m("div",{class:"menu-item-indicator"},null,-1))]),_:1,__:[1,2]},8,["index"])]),_:1},8,["content","disabled"]))])}}},Lt=mt(kt,[["__scopeId","data-v-154748a4"]]),wt={class:"notification-content"},At={class:"notification-header"},It={class:"notification-list"},Tt={class:"notification-icon"},Pt={class:"notification-content"},Rt={class:"notification-title"},Ot={class:"notification-message"},Vt={class:"notification-time"},jt=mt({__name:"NotificationDrawer",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:n}){const r=e,d=n,u=i({get:()=>r.modelValue,set:e=>d("update:modelValue",e)}),p=s([{id:1,title:"系统通知",message:"系统将于今晚进行维护升级",time:new Date,read:!1},{id:2,title:"订单提醒",message:"您有新的订单需要处理",time:new Date(Date.now()-36e5),read:!1},{id:3,title:"安全提醒",message:"检测到异常登录，请注意账户安全",time:new Date(Date.now()-72e5),read:!0}]),v=()=>{u.value=!1},b=()=>{p.value.forEach(e=>{e.read=!0})},y=e=>{const t=new Date-e,a=Math.floor(t/6e4),o=Math.floor(t/36e5),n=Math.floor(t/864e5);return a<1?"刚刚":a<60?`${a}分钟前`:o<24?`${o}小时前`:`${n}天前`};return(e,n)=>{const s=be,i=M,r=ye;return a(),l(r,{modelValue:u.value,"onUpdate:modelValue":n[0]||(n[0]=e=>u.value=e),title:"通知中心",direction:"rtl",size:"400px","before-close":v},{default:c(()=>[m("div",wt,[m("div",At,[n[2]||(n[2]=m("h3",null,"最新通知",-1)),o(s,{text:"",onClick:b},{default:c(()=>n[1]||(n[1]=[g("全部已读",-1)])),_:1,__:[1]})]),m("div",It,[(a(!0),t(_,null,h(p.value,e=>(a(),t("div",{key:e.id,class:"notification-item"},[m("div",Tt,[o(i,null,{default:c(()=>[o(f(B))]),_:1})]),m("div",Pt,[m("div",Rt,x(e.title),1),m("div",Ot,x(e.message),1),m("div",Vt,x(y(e.time)),1)])]))),128))])])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-fb61e5ca"]]),St=mt({__name:"ShortcutHelp",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,s=t,r=i({get:()=>n.modelValue,set:e=>s("update:modelValue",e)}),d=()=>{r.value=!1};return(e,t)=>{const n=be,s=Ee;return a(),l(s,{modelValue:r.value,"onUpdate:modelValue":t[0]||(t[0]=e=>r.value=e),title:"快捷键帮助",width:"600px","before-close":d},{footer:c(()=>[o(n,{onClick:d},{default:c(()=>t[1]||(t[1]=[g("关闭",-1)])),_:1,__:[1]})]),default:c(()=>[t[2]||(t[2]=m("div",{class:"shortcut-help"},[m("div",{class:"shortcut-section"},[m("h4",null,"导航快捷键"),m("div",{class:"shortcut-list"},[m("div",{class:"shortcut-item"},[m("div",{class:"shortcut-keys"},[m("kbd",null,"Ctrl"),g(" + "),m("kbd",null,"B")]),m("div",{class:"shortcut-desc"},"切换侧边栏")]),m("div",{class:"shortcut-item"},[m("div",{class:"shortcut-keys"},[m("kbd",null,"Ctrl"),g(" + "),m("kbd",null,"K")]),m("div",{class:"shortcut-desc"},"打开搜索")]),m("div",{class:"shortcut-item"},[m("div",{class:"shortcut-keys"},[m("kbd",null,"F11")]),m("div",{class:"shortcut-desc"},"全屏切换")])])]),m("div",{class:"shortcut-section"},[m("h4",null,"功能快捷键"),m("div",{class:"shortcut-list"},[m("div",{class:"shortcut-item"},[m("div",{class:"shortcut-keys"},[m("kbd",null,"Ctrl"),g(" + "),m("kbd",null,"S")]),m("div",{class:"shortcut-desc"},"保存当前页面")]),m("div",{class:"shortcut-item"},[m("div",{class:"shortcut-keys"},[m("kbd",null,"Ctrl"),g(" + "),m("kbd",null,"R")]),m("div",{class:"shortcut-desc"},"刷新页面")]),m("div",{class:"shortcut-item"},[m("div",{class:"shortcut-keys"},[m("kbd",null,"Esc")]),m("div",{class:"shortcut-desc"},"关闭弹窗")])])]),m("div",{class:"shortcut-section"},[m("h4",null,"帮助快捷键"),m("div",{class:"shortcut-list"},[m("div",{class:"shortcut-item"},[m("div",{class:"shortcut-keys"},[m("kbd",null,"?")]),m("div",{class:"shortcut-desc"},"显示快捷键帮助")])])])],-1))]),_:1,__:[2]},8,["modelValue"])}}},[["__scopeId","data-v-27c1e945"]]),Ct={class:"modern-admin-layout"},Mt={class:"logo-section"},xt={class:"logo-container"},Ut={key:0,class:"logo-text"},Ft={key:0,class:"user-profile-card"},Bt={class:"user-avatar"},Gt=["src"],Nt={class:"user-info"},$t={class:"user-name"},qt={class:"user-role"},Kt={class:"user-actions"},zt={class:"navigation-menu"},Ht={class:"sidebar-footer"},Wt={key:0,class:"system-status"},Jt={class:"top-header"},Xt={class:"header-left"},Qt={class:"header-right"},Yt={class:"search-container"},Zt={class:"header-actions"},ea={class:"user-menu"},ta={class:"user-trigger"},aa={class:"username"},oa={class:"user-info-header"},na={class:"user-details"},sa={class:"name"},ia={class:"role"},ra={class:"page-content"},la={__name:"ModernLayout",setup(n){const w=v(),A=r(),I=ot(),T=s(!1),P=s(""),R=s(!1),O=s(!1),S=s(3),U=s(!1),F=i(()=>w.path),G=i(()=>w.matched.filter(e=>e.meta&&e.meta.title&&"/dashboard"!==e.path)),N=i(()=>{const e=I.userInfo?.role;if(console.log("🔍 导航过滤调试:",{userInfo:I.userInfo,userRole:e,hasToken:!!I.token}),!e)return console.log("⚠️ 用户角色为空，返回空路由"),[];const t=A.options.routes.filter(e=>"/login"!==e.path&&"/404"!==e.path&&"/403"!==e.path&&"/"!==e.path&&!e.meta?.hidden);console.log("📋 所有可用路由:",t.map(e=>({path:e.path,title:e.meta?.title})));const a=it(t,e);return console.log("✅ 过滤后的路由:",a.map(e=>({path:e.path,title:e.meta?.title}))),a}),$=()=>{T.value=!T.value},q=()=>{document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen()},z=()=>{U.value=!U.value,document.documentElement.classList.toggle("dark",U.value)},H=()=>{window.location.reload()},W=()=>{P.value.trim()&&console.log("搜索:",P.value)},J=()=>{j.confirm("确定要退出登录吗？","确认退出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await I.logout(),V.success("退出登录成功"),A.push("/login")}catch(e){V.error("退出登录失败")}})},X=e=>{(e.ctrlKey||e.metaKey)&&"k"===e.key&&(e.preventDefault(),document.querySelector(".search-input input")?.focus()),(e.ctrlKey||e.metaKey)&&"b"===e.key&&(e.preventDefault(),$()),"F11"===e.key&&(e.preventDefault(),q()),"?"!==e.key||e.ctrlKey||e.metaKey||(O.value=!0)};return b(()=>I.userInfo,e=>{console.log("👤 用户信息变化:",e),e&&console.log("✅ 用户信息已设置，角色:",e.role)},{immediate:!0,deep:!0}),b(()=>localStorage.getItem("preview-mode"),e=>{console.log("🎭 预览模式状态变化:",e),"true"!==e||I.userInfo||(console.log("⚠️ 预览模式已启用但用户信息未设置，尝试手动设置"),I.setUserInfo({id:"preview-user",username:"admin",nickname:"超级管理员 (预览)",name:"预览用户",email:"<EMAIL>",avatar:"/default-avatar.png",role:"admin",roles:["admin"],permissions:["*"]}))},{immediate:!0}),y(()=>{document.addEventListener("keydown",X);const e="true"===localStorage.getItem("preview-mode");console.log("🔍 组件挂载时检查:",{isPreviewMode:e,hasUserInfo:!!I.userInfo,userRole:I.userInfo?.role})}),E(()=>{document.removeEventListener("keydown",X)}),(n,s)=>{const i=M,r=be,v=we,b=Le,y=De,E=Ie,w=Te,A=Oe,V=je,j=Se,X=Ce,Q=Be,Y=Ne,Z=e("router-view");return a(),t("div",Ct,[m("aside",{class:C(["modern-sidebar",{collapsed:T.value}])},[m("div",Mt,[m("div",xt,[s[10]||(s[10]=D('<div class="logo-icon" data-v-c8bd835a><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-c8bd835a><defs data-v-c8bd835a><linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%" data-v-c8bd835a><stop offset="0%" style="stop-color:#3b82f6;" data-v-c8bd835a></stop><stop offset="100%" style="stop-color:#8b5cf6;" data-v-c8bd835a></stop></linearGradient></defs><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="url(#logoGradient)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" data-v-c8bd835a></path></svg></div>',1)),o(p,{name:"fade"},{default:c(()=>[T.value?d("",!0):(a(),t("div",Ut,s[9]||(s[9]=[m("h1",null,"LinkHub Pro",-1),m("p",null,"晨鑫流量变现系统",-1)])))]),_:1})])]),o(p,{name:"slide-fade"},{default:c(()=>[T.value?d("",!0):(a(),t("div",Ft,[m("div",Bt,[m("img",{src:f(I).avatar||"/default-avatar.png",alt:"用户头像"},null,8,Gt),s[11]||(s[11]=m("div",{class:"status-indicator online"},null,-1))]),m("div",Nt,[m("div",$t,x(f(I).nickname||"管理员"),1),m("div",qt,x(f(lt)(f(I).userInfo?.role)),1)]),m("div",Kt,[o(y,{trigger:"click"},{dropdown:c(()=>[o(b,null,{default:c(()=>[o(v,{onClick:s[0]||(s[0]=e=>n.$router.push("/user/profile"))},{default:c(()=>[o(i,null,{default:c(()=>[o(f(he))]),_:1}),s[12]||(s[12]=g(" 个人资料 ",-1))]),_:1,__:[12]}),o(v,{onClick:s[1]||(s[1]=e=>n.$router.push("/user/settings"))},{default:c(()=>[o(i,null,{default:c(()=>[o(f(K))]),_:1}),s[13]||(s[13]=g(" 账户设置 ",-1))]),_:1,__:[13]}),o(v,{divided:"",onClick:J},{default:c(()=>[o(i,null,{default:c(()=>[o(f(Ae))]),_:1}),s[14]||(s[14]=g(" 退出登录 ",-1))]),_:1,__:[14]})]),_:1})]),default:c(()=>[o(r,{text:"",class:"user-menu-btn"},{default:c(()=>[o(i,null,{default:c(()=>[o(f(ke))]),_:1})]),_:1})]),_:1})])]))]),_:1}),m("nav",zt,[o(w,{class:"menu-scrollbar"},{default:c(()=>[o(E,{"default-active":F.value,collapse:T.value,"unique-opened":!0,"background-color":"transparent","text-color":"rgba(255, 255, 255, 0.8)","active-text-color":"#ffffff",class:"sidebar-menu",router:""},{default:c(()=>[(a(!0),t(_,null,h(N.value,e=>(a(),l(Lt,{key:e.path,item:e,"base-path":e.path,collapsed:T.value},null,8,["item","base-path","collapsed"]))),128))]),_:1},8,["default-active","collapse"])]),_:1})]),m("div",Ht,[o(p,{name:"fade"},{default:c(()=>[T.value?d("",!0):(a(),t("div",Wt,s[15]||(s[15]=[m("div",{class:"status-item"},[m("div",{class:"status-dot success"}),m("span",null,"系统运行正常")],-1),m("div",{class:"version-info"},"v2.0.1",-1)])))]),_:1})])],2),m("main",{class:C(["main-content",{expanded:T.value}])},[m("header",Jt,[m("div",Xt,[o(r,{text:"",class:"collapse-btn",onClick:$},{default:c(()=>[o(i,{size:"20"},{default:c(()=>[T.value?(a(),l(f(Re),{key:1})):(a(),l(f(Pe),{key:0}))]),_:1})]),_:1}),o(V,{separator:"/",class:"breadcrumb-nav"},{default:c(()=>[o(A,{to:{path:"/dashboard"}},{default:c(()=>[o(i,null,{default:c(()=>[o(f(Ve))]),_:1}),s[16]||(s[16]=g(" 控制台 ",-1))]),_:1,__:[16]}),(a(!0),t(_,null,h(G.value,e=>(a(),l(A,{key:e.path},{default:c(()=>[g(x(e.meta.title),1)]),_:2},1024))),128))]),_:1})]),m("div",Qt,[m("div",Yt,[o(j,{modelValue:P.value,"onUpdate:modelValue":s[2]||(s[2]=e=>P.value=e),placeholder:"搜索功能、用户、订单...","prefix-icon":"Search",class:"search-input",onKeyup:k(W,["enter"])},null,8,["modelValue"])]),m("div",Zt,[o(X,{value:S.value,hidden:0===S.value},{default:c(()=>[o(r,{text:"",class:"action-btn",onClick:s[3]||(s[3]=e=>R.value=!0)},{default:c(()=>[o(i,null,{default:c(()=>[o(f(B))]),_:1})]),_:1})]),_:1},8,["value","hidden"]),o(r,{text:"",class:"action-btn",onClick:q},{default:c(()=>[o(i,null,{default:c(()=>[o(f(Me))]),_:1})]),_:1}),o(r,{text:"",class:"action-btn",onClick:z},{default:c(()=>[o(i,null,{default:c(()=>[U.value?(a(),l(f(xe),{key:0})):(a(),l(f(Ue),{key:1}))]),_:1})]),_:1}),o(r,{text:"",class:"action-btn",onClick:H},{default:c(()=>[o(i,null,{default:c(()=>[o(f(Fe))]),_:1})]),_:1})]),m("div",ea,[o(y,{trigger:"click"},{dropdown:c(()=>[o(b,{class:"user-dropdown-menu"},{default:c(()=>[m("div",oa,[o(Q,{src:f(I).avatar,size:"large"},{default:c(()=>[o(i,null,{default:c(()=>[o(f(he))]),_:1})]),_:1},8,["src"]),m("div",na,[m("div",sa,x(f(I).nickname||"管理员"),1),m("div",ia,x(f(lt)(f(I).userInfo?.role)),1)])]),o(Y,{style:{margin:"12px 0"}}),o(v,{onClick:s[4]||(s[4]=e=>n.$router.push("/user/profile"))},{default:c(()=>[o(i,null,{default:c(()=>[o(f(he))]),_:1}),s[17]||(s[17]=g(" 个人中心 ",-1))]),_:1,__:[17]}),o(v,{onClick:s[5]||(s[5]=e=>n.$router.push("/user/settings"))},{default:c(()=>[o(i,null,{default:c(()=>[o(f(K))]),_:1}),s[18]||(s[18]=g(" 账户设置 ",-1))]),_:1,__:[18]}),o(v,{onClick:s[6]||(s[6]=e=>n.$router.push("/system/help"))},{default:c(()=>[o(i,null,{default:c(()=>[o(f($e))]),_:1}),s[19]||(s[19]=g(" 帮助中心 ",-1))]),_:1,__:[19]}),o(v,{divided:"",onClick:J},{default:c(()=>[o(i,null,{default:c(()=>[o(f(Ae))]),_:1}),s[20]||(s[20]=g(" 退出登录 ",-1))]),_:1,__:[20]})]),_:1})]),default:c(()=>[m("div",ta,[o(Q,{src:f(I).avatar,size:"small"},{default:c(()=>[o(i,null,{default:c(()=>[o(f(he))]),_:1})]),_:1},8,["src"]),m("span",aa,x(f(I).nickname||"管理员"),1),o(i,{class:"dropdown-icon"},{default:c(()=>[o(f(Ge))]),_:1})])]),_:1})])])]),m("div",ra,[o(Z,null,{default:c(({Component:e,route:t})=>[o(p,{name:"page-transition",mode:"out-in"},{default:c(()=>[(a(),l(L,null,[(a(),l(u(e),{key:t.path}))],1024))]),_:2},1024)]),_:1})])],2),o(jt,{modelValue:R.value,"onUpdate:modelValue":s[7]||(s[7]=e=>R.value=e)},null,8,["modelValue"]),o(St,{modelValue:O.value,"onUpdate:modelValue":s[8]||(s[8]=e=>O.value=e)},null,8,["modelValue"])])}}},ca=mt(la,[["__scopeId","data-v-c8bd835a"]]),ma=9e5;const da=new class{constructor(){this.lastActivity=Date.now(),this.sessionTimer=null,this.idleTimer=null,this.isActive=!0,this.initSessionMonitoring()}initSessionMonitoring(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.addEventListener(e,this.updateActivity.bind(this),!0)}),this.startSessionCheck(),this.startIdleCheck()}updateActivity(){this.lastActivity=Date.now(),this.isActive=!0}startSessionCheck(){this.sessionTimer=setInterval(()=>{const e=localStorage.getItem("admin_token");if(e)try{1e3*JSON.parse(atob(e.split(".")[1])).exp<Date.now()&&this.handleSessionExpired()}catch(t){console.error("Token解析失败:",t),this.handleSessionExpired()}else this.handleSessionExpired()},6e4)}startIdleCheck(){this.idleTimer=setInterval(()=>{Date.now()-this.lastActivity>ma&&this.isActive&&this.handleIdleTimeout()},3e4)}handleSessionExpired(){this.cleanup(),j.alert("您的登录会话已过期，请重新登录","会话过期",{confirmButtonText:"重新登录",type:"warning",showClose:!1,closeOnClickModal:!1,closeOnPressEscape:!1}).then(()=>{this.logout()})}handleIdleTimeout(){this.isActive=!1,j.confirm("检测到您已长时间未操作，是否继续保持登录？","空闲提醒",{confirmButtonText:"继续使用",cancelButtonText:"退出登录",type:"warning"}).then(()=>{this.updateActivity()}).catch(()=>{this.logout()})}logout(){localStorage.removeItem("admin_token"),localStorage.removeItem("admin_user"),localStorage.removeItem("admin_permissions"),localStorage.removeItem("admin_roles"),ha.push("/login")}getCurrentSession(){const e=localStorage.getItem("admin_token");if(!e)return null;try{const t=JSON.parse(atob(e.split(".")[1]));return{id:t.jti||"session_"+Date.now(),userId:t.sub,username:t.username,exp:t.exp,iat:t.iat,isValid:1e3*t.exp>Date.now()}}catch(t){return console.error("解析会话信息失败:",t),null}}cleanup(){this.sessionTimer&&(clearInterval(this.sessionTimer),this.sessionTimer=null),this.idleTimer&&(clearInterval(this.idleTimer),this.idleTimer=null)}destroy(){this.cleanup();["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.removeEventListener(e,this.updateActivity.bind(this),!0)})}},ua=(e,t,a,o={})=>{const n={type:"session",username:e,action:t,sessionId:a,details:o,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent};console.log("Session Log:",n)},pa=(e,t,a={})=>{const o={type:"login",username:e,success:t,details:a,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,ip:"unknown"};console.log("Login Log:",o)},_a=[{path:"/login",name:"Login",component:()=>We(()=>import("./Login-BGEpWB-C.js"),__vite__mapDeps([0,1,2,3,4])),meta:{title:"登录",hidden:!0}},{path:"/",redirect:"/dashboard"},{path:"/fullscreen-data-screen",name:"FullscreenDataScreen",component:()=>We(()=>import("./DataScreenFullscreen-Bdu3VI8N.js"),__vite__mapDeps([5,1,2,6,7,8,9,10,11,12,3,13,14])),meta:{title:"数据大屏",hidden:!0,fullscreen:!0}},{path:"/test",name:"TestPage",component:()=>We(()=>import("./TestPage-DewmrKVF.js"),__vite__mapDeps([15,1,2,3,16])),meta:{title:"测试页面",hidden:!0}},{path:"/dashboard-direct",name:"DashboardDirect",component:()=>We(()=>import("./ModernDashboard-09GTIX74.js"),__vite__mapDeps([17,1,2,3,18,19,20,21])),meta:{title:"直接仪表板",hidden:!0}},{path:"/route-checker",name:"RouteChecker",component:()=>We(()=>import("./RouteChecker-P5FJFeH5.js"),__vite__mapDeps([22,2,1,3,23,24,25,26,20,27,28,19])),meta:{title:"路由检测工具",hidden:!0}},{path:"/simple-dashboard",name:"SimpleDashboard",component:()=>We(()=>import("./SimpleDashboard-BvrVIwgP.js"),__vite__mapDeps([29,1,2,3,30,31])),meta:{title:"简化仪表板",hidden:!0}},{path:"/dashboard",component:ca,meta:{title:"数据看板",icon:"Monitor"},children:[{path:"",name:"Dashboard",component:()=>We(()=>import("./ModernDashboard-09GTIX74.js"),__vite__mapDeps([17,1,2,3,18,19,20,21])),meta:{title:"数据看板",icon:"Monitor"}},{path:"/security",name:"SecurityManagement",component:()=>We(()=>import("./SecurityManagement-DqGJdqK5.js"),__vite__mapDeps([32,2,1,33,34,3,35,36,37,20,21,25,26,28,24,38,39,40,41,27])),meta:{title:"安全管理",requiresAuth:!0}}]},{path:"/data-screen",component:ca,meta:{title:"数据大屏",icon:"DataLine"},children:[{path:"",name:"DataScreen",component:()=>We(()=>import("./DataScreen-BsKps5zq.js"),__vite__mapDeps([6,7,8,1,2,9,10,11,12,3,13])),meta:{title:"数据大屏",icon:"DataLine"}},{path:"reports",name:"DashboardReports",component:()=>We(()=>import("./Reports-CaxDczgg.js"),__vite__mapDeps([42,2,1,33,34,3,43,25,26,20,44,28,27,45,21])),meta:{title:"数据报表",icon:"DataAnalysis"}}]},{path:"/community",component:ca,redirect:"/community/groups",meta:{title:"社群管理",icon:"Comment"},children:[{path:"groups",name:"GroupList",component:()=>We(()=>import("./GroupList-QUEx6z1l.js"),__vite__mapDeps([46,1,2,47,48,49,50,3,51,36,31,28,27,20,52,53,54,19,55,37,21,25,26,39,40,56,57,41,44,45,58,59])),meta:{title:"社群列表",icon:"UserFilled"}},{path:"rules",name:"AutoRules",component:()=>We(()=>import("./AutoRules-ChzNPyhI.js"),__vite__mapDeps([60,1,2,33,34,50,3,61,36,28,25,26,40,20,39,21])),meta:{title:"自动化规则",icon:"MagicStick"}},{path:"events",name:"EventManagement",component:()=>We(()=>import("./EventManagement-CKksJpCO.js"),__vite__mapDeps([62,2,1,33,34,50,3,63,44,27,28,20,24,36,39,41,45,56,58])),meta:{title:"活动管理",icon:"Ticket"}},{path:"moderation",name:"ContentModeration",component:()=>We(()=>import("./ContentModeration-BJj8Aods.js"),__vite__mapDeps([64,1,2,33,34,50,3,65,36,37,20,21,25,26,24])),meta:{title:"内容审核",icon:"ShieldCheck"}},{path:"analytics",name:"CommunityAnalytics",component:()=>We(()=>import("./AnalyticsDashboard-CuLWUSSS.js"),__vite__mapDeps([66,2,1,33,34,12,3,67,27,28,45])),meta:{title:"数据分析",icon:"DataLine"}},{path:"members/:id",name:"UserProfile",component:()=>We(()=>import("./UserProfile-nYpRIurq.js"),__vite__mapDeps([49,2,1,50,3,51,36,31,28,27,20])),meta:{title:"用户画像",hidden:!0}},{path:"templates",name:"TemplateManagement",component:()=>We(()=>import("./TemplateManagement-Bea_zQEn.js"),__vite__mapDeps([68,1,2,50,47,48,3,69,36,70,41,54,19,37,20,21,27,25,26,71,40,56,58,28,39])),meta:{title:"模板管理",icon:"Document"}},{path:"add",name:"GroupAdd",component:()=>We(()=>import("./GroupAdd-VdFI-xtG.js"),__vite__mapDeps([72,1,2,47,48,3,73,39,40,28,54,19,56,57,20,21,27,41])),meta:{title:"创建群组",icon:"Plus"}},{path:"detail/:id",name:"GroupDetail",component:()=>We(()=>import("./GroupDetail-C5mlI8G2.js"),__vite__mapDeps([74,1,2,33,34,3,75,28,27,20])),meta:{title:"群组详情",icon:"View",hidden:!0}},{path:"marketing",name:"GroupMarketing",component:()=>We(()=>import("./GroupMarketing-XD6stnjv.js"),__vite__mapDeps([76,2,1,77,3,78,36,24,54,19,40,56,57,27,41,37,20,21,25,26,28,39])),meta:{title:"营销配置",icon:"Promotion"}}]},{path:"/distribution",component:ca,redirect:"/distribution/groups",name:"Distribution",meta:{title:"分销管理",icon:"Share"},children:[{path:"groups",name:"DistributionGroupList",component:()=>We(()=>import("./GroupList-DnmHT4g9.js"),__vite__mapDeps([79,1,2,80,81,37,20,21,3,82,36,39,56,57,41,25,26])),meta:{title:"分销组管理",icon:"Grid"}},{path:"distributors",name:"DistributorList",component:()=>We(()=>import("./DistributorList-Erukm10S.js"),__vite__mapDeps([83,1,2,80,81,37,20,21,3,84,36,25,26,40,27,28,39])),meta:{title:"分销商管理",icon:"UserFilled"}},{path:"detail/:id",name:"DistributorDetail",component:()=>We(()=>import("./DistributorDetail-Db3Si0Dr.js"),__vite__mapDeps([85,1,2,86,3,87,36,39,56,57,20,21,25,26,28])),meta:{title:"分销员详情",icon:"User",hidden:!0}},{path:"customers",name:"CustomerManagement",component:()=>We(()=>import("./CustomerManagement-ClG51h49.js"),__vite__mapDeps([88,2,1,89,28,90,3,91,36,59,92,45,70,39,37,20,21,25,26,27])),meta:{title:"客户管理",icon:"UserFilled"}}]},{path:"/distributor",component:ca,redirect:"/distributor/dashboard",name:"Distributor",meta:{title:"分销员工作台",icon:"User",roles:["distributor"]},children:[{path:"dashboard",name:"DistributorDashboard",component:()=>We(()=>import("./DistributorDashboard-DKxouNiz.js"),__vite__mapDeps([93,1,2,7,8,9,10,11,3,94,36,44,56,58,28,27,20,90])),meta:{title:"工作台",icon:"Monitor",roles:["distributor"]}},{path:"group-management",name:"DistributorGroupManagement",component:()=>We(()=>import("./GroupManagement-DFNGXo8X.js"),__vite__mapDeps([95,2,1,3,96,36,39,41,28,37,20,21,25,26,19,27])),meta:{title:"群组管理",icon:"Comment",roles:["distributor"]}},{path:"promotion-links",name:"DistributorPromotionLinks",component:()=>We(()=>import("./PromotionLinks-B6UMxVp0.js"),__vite__mapDeps([97,2,1,98,3,99,36,39,45,37,20,21,25,26,28,27,92,41,100,101,70])),meta:{title:"推广链接",icon:"Link",roles:["distributor"]}},{path:"commission-logs",name:"DistributorCommissionLogs",component:()=>We(()=>import("./CommissionLogs-_PM3wy9o.js"),__vite__mapDeps([102,2,1,7,8,9,3,103,36,70,37,20,21,25,26,104,45,28,56,58,27])),meta:{title:"佣金查看",icon:"Money",roles:["distributor"]}},{path:"order-list",name:"DistributorOrderList",component:()=>We(()=>import("./OrderList-S3UZWe8r.js"),__vite__mapDeps([105,2,1,3,106,36,24,31,28,37,20,21,25,26,104,70,45,27])),meta:{title:"订单查看",icon:"Tickets",roles:["distributor"]}}]},{path:"/owner",component:ca,redirect:"/owner/dashboard",name:"Owner",meta:{title:"群主工作台",icon:"Comment",roles:["group_owner"]},children:[{path:"dashboard",name:"OwnerDashboard",component:()=>We(()=>import("./OwnerDashboard-CzQTq_lG.js"),__vite__mapDeps([107,1,2,89,28,90,7,8,9,10,11,3,108,59,24,92,25,26,20,39,41,21,44,56,58,27])),meta:{title:"群主工作台",icon:"Monitor",roles:["group_owner"]}}]},{path:"/finance",component:ca,redirect:"/finance/dashboard",name:"Finance",meta:{title:"财务管理",icon:"Money"},children:[{path:"dashboard",name:"FinanceDashboard",component:()=>We(()=>import("./FinanceDashboard-JEsymmcK.js"),__vite__mapDeps([109,1,2,7,8,9,12,110,48,50,3,111,59,92,20,25,26,28,27])),meta:{title:"财务总览",icon:"DataLine"}},{path:"commission-logs",name:"CommissionLog",component:()=>We(()=>import("./CommissionLog-DalRnms0.js"),__vite__mapDeps([112,1,2,110,48,86,50,3,113,36,41,37,20,21,25,26,39,45,27,28])),meta:{title:"佣金明细",icon:"Medal"}},{path:"transactions",name:"TransactionList",component:()=>We(()=>import("./TransactionList-D1DxzW9i.js"),__vite__mapDeps([114,1,2,110,48,80,81,37,20,21,3,115,36,70,25,26,27,28,39,45])),meta:{title:"交易记录",icon:"Goods"}},{path:"withdraw",name:"WithdrawManage",component:()=>We(()=>import("./WithdrawManage-CyeKTC7f.js"),__vite__mapDeps([116,1,2,86,50,3,117,36,92,70,37,20,21,25,26,39,45,41,27,28])),meta:{title:"提现管理",icon:"Upload"}}]},{path:"/user",component:ca,redirect:"/user/center",name:"User",meta:{title:"用户管理",icon:"User"},children:[{path:"center",name:"UserCenter",component:()=>We(()=>import("./UserCenter-DX5yO-S0.js"),__vite__mapDeps([118,2,1,89,28,90,7,8,9,119,120,54,19,121,48,3,122,39,56,58,44,27,20])),meta:{title:"用户中心",icon:"User"}},{path:"list",name:"UserList",component:()=>We(()=>import("./UserList-CKsEKLdA.js"),__vite__mapDeps([123,33,2,1,34,121,48,50,3,124,38,26,37,20,21,25,39,54,19,41,27,56,57,36,70])),meta:{title:"用户列表",icon:"UserFilled"}},{path:"profile",name:"Profile",component:()=>We(()=>import("./Profile-tttXcO_w.js"),__vite__mapDeps([125,119,1,2,120,54,19,3,126,27,28,39,20])),meta:{title:"个人资料",icon:"Avatar"}},{path:"analytics",name:"UserAnalytics",component:()=>We(()=>import("./UserAnalytics-Cmh5UXPl.js"),__vite__mapDeps([127,12,1,2,3,128,39,20,21,45,56,58,27,28])),meta:{title:"用户分析",icon:"DataAnalysis"}},{path:"add",name:"UserAdd",component:()=>We(()=>import("./UserAdd-BOvRXeCi.js"),__vite__mapDeps([129,1,2,33,34,3,130,39,56,57,20,21,27])),meta:{title:"添加用户",icon:"Plus"}}]},{path:"/substation",component:ca,redirect:"/substation/list",name:"Substation",meta:{title:"分站管理",icon:"OfficeBuilding"},children:[{path:"list",name:"SubstationList",component:()=>We(()=>import("./SubstationList-DDQ-erKb.js"),__vite__mapDeps([131,1,2,132,48,121,80,81,37,20,21,3,133,36,59,24,92,41,28,25,26,27,39])),meta:{title:"分站列表",icon:"List"}},{path:"finance",name:"SubstationFinance",component:()=>We(()=>import("./SubstationFinance-Co9hI-Gm.js"),__vite__mapDeps([134,2,1,89,28,90,7,8,9,10,11,132,48,3,135,36,39,20,21,45,37,25,26,56,58,27])),meta:{title:"分站财务",icon:"Money"}},{path:"permissions",name:"SubstationPermissions",component:()=>We(()=>import("./SubstationPermissions-D2vRH3n1.js"),__vite__mapDeps([136,2,1,3,137,20,21,39,41,28,24,26,27])),meta:{title:"权限配置",icon:"Lock"}},{path:"analytics",name:"SubstationAnalytics",component:()=>We(()=>import("./SubstationAnalytics-Bd6Snhpj.js"),__vite__mapDeps([138,33,2,1,34,3,139,36,28,37,20,21,25,26])),meta:{title:"分站分析",icon:"DataAnalysis"}}]},{path:"/agent",component:ca,redirect:"/agent/dashboard",name:"Agent",meta:{title:"代理商管理",icon:"Avatar"},children:[{path:"dashboard",name:"AgentDashboard",component:()=>We(()=>import("./AgentDashboard-XqAlNjir.js"),__vite__mapDeps([140,2,1,89,28,90,7,8,9,10,11,141,98,3,142,59,24,92,25,26,20,56,58,27])),meta:{title:"代理商工作台",icon:"Monitor"}},{path:"list",name:"AgentList",component:()=>We(()=>import("./AgentList-Dkj2HjhV.js"),__vite__mapDeps([143,2,1,89,28,90,141,3,144,36,45,41,56,57,37,20,21,25,26,39,27])),meta:{title:"代理商列表",icon:"List"}},{path:"applications",name:"AgentApplications",component:()=>We(()=>import("./AgentApplications-ByLKtHvk.js"),__vite__mapDeps([145,2,1,89,28,90,141,3,146,36,70,37,20,21,25,26,92,39,27])),meta:{title:"申请管理",icon:"Document"}},{path:"commission",name:"AgentCommission",component:()=>We(()=>import("./AgentCommission-Cz2P9BmQ.js"),__vite__mapDeps([147,33,2,1,34,3,148,36,28,37,20,21,25,26])),meta:{title:"佣金管理",icon:"Money"}},{path:"hierarchy",name:"AgentHierarchy",component:()=>We(()=>import("./AgentHierarchy-CVuo509U.js"),__vite__mapDeps([149,33,2,1,34,3,150,36,28,37,20,21,25,26])),meta:{title:"代理商层级",icon:"Connection"}},{path:"performance",name:"AgentPerformance",component:()=>We(()=>import("./AgentPerformance-BDMi80cu.js"),__vite__mapDeps([151,33,2,1,34,3,152,36,28,37,20,21,25,26])),meta:{title:"绩效分析",icon:"TrendCharts"}}]},{path:"/anti-block",component:ca,redirect:"/anti-block/dashboard",name:"AntiBlock",meta:{title:"防红系统",icon:"Tools"},children:[{path:"dashboard",name:"AntiBlockDashboard",component:()=>We(()=>import("./Dashboard-CnERKQsZ.js"),__vite__mapDeps([153,154,2,1,3,155,92,39,101,41,20,21,25,26,19,28,27])),meta:{title:"系统概览",icon:"DataLine"}},{path:"enhanced",name:"AntiBlockEnhanced",component:()=>We(()=>import("./EnhancedDashboard-jrKvGisZ.js"),__vite__mapDeps([156,2,1,12,77,3,157,36,70,39,41,28,37,20,21,25,26,19,27])),meta:{title:"增强管理",icon:"Monitor"}},{path:"domains",name:"DomainList",component:()=>We(()=>import("./DomainList-Cl7hEoti.js"),__vite__mapDeps([158,1,2,154,80,81,37,20,21,3,159,36,41,56,57,25,26,27,28,39])),meta:{title:"域名管理",icon:"Connection"}},{path:"short-links",name:"ShortLinkList",component:()=>We(()=>import("./ShortLinkList-Co1FXK9v.js"),__vite__mapDeps([160,154,2,1,3,161,36,92,24,37,20,21,25,26,39,45,28])),meta:{title:"短链接管理",icon:"Link"}},{path:"analytics",name:"AntiBlockAnalytics",component:()=>We(()=>import("./Analytics-BGcX8c47.js"),__vite__mapDeps([162,154,2,1,3,163,36,39,41,40,25,26,20,19,28,56,58,27,45])),meta:{title:"统计分析",icon:"DataAnalysis"}}]},{path:"/content",component:ca,redirect:"/content/management",name:"Content",meta:{title:"内容管理",icon:"Document"},children:[{path:"management",name:"ContentManagement",component:()=>We(()=>import("./ContentManagement-BFW8lonL.js"),__vite__mapDeps([164,1,2,89,28,90,165,50,3,166,36,37,20,21,25,26,27,39,41,56,57,70,45,38])),meta:{title:"内容管理",icon:"Edit"}},{path:"templates",name:"ContentTemplates",component:()=>We(()=>import("./ContentTemplates-DiI83_TV.js"),__vite__mapDeps([167,1,2,165,3,168,37,20,21,39,40,45,56,57,38,26])),meta:{title:"内容模板",icon:"DocumentCopy"}},{path:"ai-generator",name:"AIContentGenerator",component:()=>We(()=>import("./AIGenerator-CA6nj_Cf.js"),__vite__mapDeps([169,33,2,1,34,3,170,36,28,37,20,21,25,26])),meta:{title:"AI内容生成",icon:"MagicStick"}}]},{path:"/permission",component:ca,redirect:"/permission/roles",name:"Permission",meta:{title:"权限管理",icon:"Lock"},children:[{path:"roles",name:"RoleManagement",component:()=>We(()=>import("./RoleManagement-DVE5qPyC.js"),__vite__mapDeps([171,1,2,89,28,90,50,3,172,36,37,20,21,25,26,27,39,56,57,173,71])),meta:{title:"角色管理",icon:"UserFilled"}},{path:"permissions",name:"PermissionManagement",component:()=>We(()=>import("./PermissionManagement-woFlq7pl.js"),__vite__mapDeps([174,33,2,1,34,3,175,36,28,37,20,21,25,26])),meta:{title:"权限配置",icon:"Key"}}]},{path:"/promotion",component:ca,redirect:"/promotion/links",name:"Promotion",meta:{title:"推广管理",icon:"Share"},children:[{path:"links",name:"PromotionLinks",component:()=>We(()=>import("./LinkManagement-DdLYLk7s.js"),__vite__mapDeps([176,1,2,89,28,90,177,50,3,178,36,37,20,21,25,26,104,27,39,56,57,41,45,58,92,38])),meta:{title:"推广链接",icon:"Link"}},{path:"landing-pages",name:"LandingPages",component:()=>We(()=>import("./LandingPages-B02a-OJB.js"),__vite__mapDeps([179,1,2,177,50,3,180,36,37,20,21,25,26,56,58,28,45,27,39,40,41,100,57,38,19])),meta:{title:"落地页管理",icon:"Document"}},{path:"analytics",name:"PromotionAnalytics",component:()=>We(()=>import("./Analytics-CaYNhaAK.js"),__vite__mapDeps([181,33,2,1,34,3,182,36,28,37,20,21,25,26])),meta:{title:"推广分析",icon:"DataAnalysis"}}]},{path:"/orders",component:ca,redirect:"/orders/list",name:"Orders",meta:{title:"订单管理",icon:"Tickets"},children:[{path:"list",name:"OrderList",component:()=>We(()=>import("./OrderList-DbvoxFwQ.js"),__vite__mapDeps([183,1,2,89,28,90,50,3,184,36,37,20,21,25,26,27,45,70,92,39,56,57,41,31])),meta:{title:"订单列表",icon:"List"}},{path:"analytics",name:"OrderAnalytics",component:()=>We(()=>import("./OrderAnalytics-DnQftSh4.js"),__vite__mapDeps([185,33,2,1,34,3,186,36,28,37,20,21,25,26])),meta:{title:"订单分析",icon:"DataAnalysis"}},{path:"detail/:id",name:"OrderDetail",component:()=>We(()=>import("./OrderDetail-BhfV-_yQ.js"),__vite__mapDeps([187,1,2,33,34,3,188,31,25,26,20,28,27])),meta:{title:"订单详情",icon:"View",hidden:!0}}]},{path:"/system",component:ca,redirect:"/system/settings",name:"System",meta:{title:"系统管理",icon:"Setting"},children:[{path:"settings",name:"SystemSettings",component:()=>We(()=>import("./Settings-mvAAbHcQ.js"),__vite__mapDeps([189,1,2,52,53,54,19,190,50,3,191,27,41,38,26,40,39,28,36,44,173,71,25,20,37,21,45,192])),meta:{title:"系统设置",icon:"Tools"}},{path:"monitor",name:"SystemMonitor",component:()=>We(()=>import("./SystemMonitor-Cyso9dJg.js"),__vite__mapDeps([193,190,8,2,1,3,194,25,26,104,20,92,40,28,27])),meta:{title:"系统监控",icon:"Monitor"}},{path:"deployment",name:"DeploymentMonitor",component:()=>We(()=>import("./DeploymentMonitor-DllIuJ0F.js"),__vite__mapDeps([195,190,2,1,3,196,25,26,20,44,59,27,28])),meta:{title:"部署监控",icon:"Upload"}},{path:"export",name:"DataExport",component:()=>We(()=>import("./DataExport-BtOv5d6l.js"),__vite__mapDeps([197,86,2,1,3,198,40,192,37,20,21,25,26,19,39,38,41,45,56,57,28,27])),meta:{title:"数据导出",icon:"Download"}},{path:"notifications",name:"SystemNotifications",component:()=>We(()=>import("./Notifications-DIfDlwOJ.js"),__vite__mapDeps([199,12,1,2,3,200,39,45,38,26,56,57,37,20,21,25,27,28])),meta:{title:"通知管理",icon:"Bell"}},{path:"operation-logs",name:"OperationLogs",component:()=>We(()=>import("./OperationLogs-7VE7c_my.js"),__vite__mapDeps([201,12,1,2,3,202,36,41,56,57,92,70,37,20,21,25,26,39,45,27,28])),meta:{title:"操作日志",icon:"Document"}},{path:"function-test",name:"FunctionTest",component:()=>We(()=>import("./FunctionTest-Dz1mkhiN.js"),__vite__mapDeps([203,2,1,3,204,27,20,21,28,92])),meta:{title:"功能测试",icon:"Cpu"}},{path:"user-guide",name:"UserGuide",component:()=>We(()=>import("./UserGuide-WSTSMlFE.js"),__vite__mapDeps([205,2,1,3,206,59,92,20,28,27])),meta:{title:"使用指南",icon:"InfoFilled"}},{path:"payment-channels",name:"PaymentChannels",component:()=>We(()=>import("./PaymentChannels-CbTpmc2p.js"),__vite__mapDeps([207,33,2,1,34,3,208,36,28,37,20,21,25,26])),meta:{title:"支付渠道",icon:"CreditCard"}},{path:"file-management",name:"FileManagement",component:()=>We(()=>import("./FileManagement-CkXECOgY.js"),__vite__mapDeps([209,33,2,1,34,3,210,36,28,37,20,21,25,26])),meta:{title:"文件管理",icon:"Folder"}},{path:"permission-logs",name:"PermissionLogs",component:()=>We(()=>import("./PermissionLogs-BG7jSSWT.js"),__vite__mapDeps([211,33,2,1,34,3,212,36,28,37,20,21,25,26])),meta:{title:"权限日志",icon:"View"}}]},{path:"/payment",component:ca,redirect:"/payment/settings",name:"Payment",meta:{title:"支付管理",icon:"CreditCard"},children:[{path:"settings",name:"PaymentSettings",component:()=>We(()=>import("./PaymentSettings-CSwx3iS5.js"),__vite__mapDeps([213,2,1,33,34,214,3,215,27,20,28,24,41,38,26,54,19,39,21,40])),meta:{title:"支付设置",icon:"Setting"}},{path:"orders",name:"PaymentOrders",component:()=>We(()=>import("./PaymentOrders-D2erkauk.js"),__vite__mapDeps([216,1,2,33,34,214,3,217,36,37,20,21,25,26,28,45,27,70,39,41])),meta:{title:"支付订单",icon:"Tickets"}},{path:"refunds",name:"PaymentRefunds",component:()=>We(()=>import("./PaymentRefunds-BZXvU9S8.js"),__vite__mapDeps([218,2,1,33,34,214,3,219,36,70,37,20,21,25,26,28,39,45])),meta:{title:"退款管理",icon:"RefreshLeft"}}]},{path:"/security",component:ca,redirect:"/security/management",name:"Security",meta:{title:"安全管理",icon:"Lock"},children:[{path:"management",name:"SecurityManagement",component:()=>We(()=>import("./SecurityManagement-DqGJdqK5.js"),__vite__mapDeps([32,2,1,33,34,3,35,36,37,20,21,25,26,28,24,38,39,40,41,27])),meta:{title:"安全管理",icon:"Shield"}}]},{path:"/403",name:"Forbidden",component:()=>We(()=>import("./403-CtjY_-j_.js"),__vite__mapDeps([220,1,2,3,221])),meta:{title:"无权限",hidden:!0}},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>We(()=>import("./NotFound-0frYsztm.js"),__vite__mapDeps([222,1,2,3,223])),meta:{title:"404",hidden:!0}}],ha=w({history:A("/admin/"),routes:_a});ha.beforeEach(async(e,t,a)=>{O.start();const o=ot(),n=o.token;if("true"===new URLSearchParams(window.location.search).get("preview")||"true"===localStorage.getItem("preview-mode")){console.log("🎭 启用预览模式：",e.path);const t=`preview-mode-token-${Date.now()}`;o.setToken(t);const n={id:"preview-user",username:"admin",nickname:"超级管理员 (预览)",name:"预览用户",email:"<EMAIL>",avatar:"/default-avatar.png",role:"admin",roles:["admin"],permissions:["*"]};return o.setUserInfo(n),localStorage.setItem("preview-mode","true"),console.log("✅ 预览模式用户信息已设置:",n),void a()}if(n&&n.startsWith("preview-mode-token-"))return console.log("🎭 预览模式路由守卫：允许访问",e.path),void a();if(n)if("/login"===e.path)a({path:"/dashboard"});else{if(!o.userInfo)try{await o.getUserInfo()}catch(s){return await o.logout(),void a(`/login?redirect=${e.path}`)}a()}else"/login"!==e.path?a(`/login?redirect=${e.path}`):a()}),ha.afterEach(()=>{O.done()});const ga=R.create({baseURL:"/api/v1",timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});ga.interceptors.request.use(e=>{const t=ot().token;return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>(console.error("请求错误:",e),Promise.reject(e))),ga.interceptors.response.use(e=>{const{data:t}=e;return void 0!==t.code?200===t.code||0===t.code?t:(V.error(t.message||"请求失败"),Promise.reject(new Error(t.message||"请求失败"))):t},e=>{if(console.error("响应错误:",e),e.response){const{status:t,data:a}=e.response;switch(t){case 401:V.error("登录已过期，请重新登录");ot().logout(),ha.push("/login");break;case 403:V.error("没有权限访问");break;case 404:V.error("请求的资源不存在");break;case 422:const e=a.errors;if(e){const t=Object.values(e)[0];V.error(Array.isArray(t)?t[0]:t)}else V.error(a.message||"请求参数错误");break;case 500:V.error("服务器内部错误");break;default:V.error(a.message||`请求失败 (${t})`)}}else e.request?V.error("网络连接失败，请检查网络"):V.error("请求配置错误");return Promise.reject(e)});const fa={install(e){e.config.globalProperties.$api=ga,e.provide("$api",ga)}};function va(e){const t=ot(),a=t.userInfo?.role;return!!a&&(Array.isArray(e)?e.includes(a):a===e)}function ba(e){const t=ot(),a=t.userInfo?.permissions||[];return Array.isArray(e)?e.some(e=>a.includes(e)):a.includes(e)}const ya=Object.freeze(Object.defineProperty({__proto__:null,checkRoutePermission:function(e,t=null){const a=ot(),o=t||a.userInfo?.role;if(!o)return!1;const{meta:n}=e;return!n||!(n.roles&&n.roles.length>0&&!n.roles.includes(o))&&(!(n.permissions&&n.permissions.length>0&&!ba(n.permissions))&&st(e,o))},hasPermission:ba,hasRole:va},Symbol.toStringTag,{value:"Module"})),Ea={mounted(e,t){const{value:a}=t;if(a){va(a)||e.parentNode&&e.parentNode.removeChild(e)}else console.error("v-role指令需要指定角色参数")}},Da={mounted(e,t){const{value:a}=t;if(a){ba(a)||e.parentNode&&e.parentNode.removeChild(e)}else console.error("v-permission指令需要指定权限参数")}},ka={mounted(e,t){const{value:a,modifiers:o}=t;if(!a)return void console.error("v-auth指令需要指定权限参数");let n=!1;n=o.role?va(a):o.permission?ba(a):va(a),n||(o.hide?e.style.display="none":o.disable?(e.disabled=!0,e.classList.add("is-disabled")):e.parentNode&&e.parentNode.removeChild(e))}};(new Date).toISOString(),(new Date).toISOString(),(new Date).toISOString();("true"===new URLSearchParams(window.location.search).get("preview")||"true"===localStorage.getItem("preview-mode"))&&(console.log("🎭 预览模式已启用"),localStorage.setItem("preview-mode","true"));const La=I(ze);for(const[Ta,Pa]of Object.entries(qe))La.component(Ta,Pa);La.use(T()),La.use(ha),La.use(fa),function(e){e.directive("role",Ea),e.directive("permission",Da),e.directive("auth",ka)}(La),window.addEventListener("sessionExpired",()=>{const e=ot();V.warning({message:"会话已过期，请重新登录",duration:5e3}),ua(e.userInfo?.username||"unknown","session_expired",da.getCurrentSession()?.id,"自动会话过期检测"),e.logout().then(()=>{ha.push("/login")})}),document.addEventListener("visibilitychange",()=>{!document.hidden&&da.getCurrentSession()&&da.updateActivity()});let wa=null;const Aa=()=>{wa||(wa=setTimeout(()=>{da.getCurrentSession()&&da.updateActivity(),wa=null},3e4))};["mousedown","mousemove","keypress","scroll","touchstart","click"].forEach(e=>{document.addEventListener(e,Aa,{passive:!0})}),La.config.errorHandler=(e,t,a)=>{console.error("Vue应用错误:",e,a),V.error("应用出现错误，请刷新页面重试")},window.addEventListener("unhandledrejection",e=>{console.error("未处理的Promise错误:",e.reason),e.preventDefault()});try{La.mount("#app"),console.log("✅ 应用挂载成功")}catch(Ia){console.error("❌ 应用挂载失败:",Ia)}export{mt as _,We as a,ua as b,Ze as c,ga as d,et as e,tt as f,Xe as g,at as h,pa as l,ct as n,ya as p,ha as r,da as s,ot as u};
