#!/bin/bash

# 晨鑫流量变现系统 - 宝塔环境一键部署脚本
# 版本: 2.0.0
# 适用: CentOS 7/8, Ubuntu 18/20 + 宝塔面板

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目配置
PROJECT_NAME="晨鑫流量变现系统"
PROJECT_DIR="/www/wwwroot/ffjq"
DB_NAME="ffjq"
DB_USER="ffjq_user"

# 日志函数
print_header() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "  $PROJECT_NAME - 宝塔环境一键部署脚本"
    echo "=================================================="
    echo -e "${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 检查运行环境
check_environment() {
    log_step "检查运行环境..."
    
    # 检查是否为root用户
    if [[ $EUID -ne 0 ]]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查宝塔面板
    if [ ! -f "/www/server/panel/BT-Panel" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        log_info "安装命令: curl -sSO http://download.bt.cn/install/install_panel.sh && bash install_panel.sh"
        exit 1
    fi
    
    # 检查系统版本
    if [ -f /etc/redhat-release ]; then
        OS="CentOS"
        log_info "检测到系统: $OS"
    elif [ -f /etc/lsb-release ]; then
        OS="Ubuntu"
        log_info "检测到系统: $OS"
    else
        log_warning "未能识别系统版本，继续执行..."
        OS="Unknown"
    fi
    
    log_success "环境检查完成"
}

# 检查必需软件
check_requirements() {
    log_step "检查必需软件..."

    # 检查PHP版本
    if ! command -v php &> /dev/null; then
        log_error "PHP未安装，请先通过宝塔面板安装PHP 8.1或更高版本"
        exit 1
    fi

    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$PHP_VERSION >= 8.1" | bc -l) -eq 0 ]]; then
        log_error "PHP版本过低($PHP_VERSION)，需要8.1或更高版本"
        exit 1
    fi
    log_success "PHP版本检查通过: $PHP_VERSION"

    # 检查Composer
    if ! command -v composer &> /dev/null; then
        log_info "安装Composer..."
        curl -sS https://getcomposer.org/installer | php
        mv composer.phar /usr/local/bin/composer
        chmod +x /usr/local/bin/composer
    fi
    log_success "Composer检查通过"

    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_info "安装Node.js 18..."
        curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
        if [ "$OS" = "CentOS" ]; then
            yum install -y nodejs
        else
            apt-get install -y nodejs
        fi
    fi
    log_success "Node.js检查通过"
}

# 配置PHP扩展
configure_php() {
    log_step "配置PHP扩展..."

    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    PHP_INI="/www/server/php/${PHP_VERSION//.}/etc/php.ini"

    if [ -f "$PHP_INI" ]; then
        log_info "优化PHP配置..."

        # 备份原配置
        cp "$PHP_INI" "${PHP_INI}.backup.$(date +%Y%m%d_%H%M%S)"

        # 修改PHP配置
        sed -i 's/memory_limit = .*/memory_limit = 512M/' "$PHP_INI"
        sed -i 's/max_execution_time = .*/max_execution_time = 300/' "$PHP_INI"
        sed -i 's/upload_max_filesize = .*/upload_max_filesize = 100M/' "$PHP_INI"
        sed -i 's/post_max_size = .*/post_max_size = 100M/' "$PHP_INI"
        sed -i 's/max_input_vars = .*/max_input_vars = 3000/' "$PHP_INI"

        # 重启PHP-FPM
        systemctl restart php-fpm

        log_success "PHP配置优化完成"
    else
        log_warning "未找到PHP配置文件: $PHP_INI"
        log_info "请通过宝塔面板手动配置PHP参数"
    fi
}

# 创建数据库
create_database() {
    log_step "创建数据库..."
    
    # 生成随机密码
    DB_PASSWORD=$(openssl rand -base64 12)
    
    # 创建数据库和用户
    mysql -u root -p << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    # 保存数据库信息
    echo "数据库信息:" > /root/ffjq_db_info.txt
    echo "数据库名: $DB_NAME" >> /root/ffjq_db_info.txt
    echo "用户名: $DB_USER" >> /root/ffjq_db_info.txt
    echo "密码: $DB_PASSWORD" >> /root/ffjq_db_info.txt
    
    log_success "数据库创建完成，信息已保存到 /root/ffjq_db_info.txt"
}

# 部署项目代码
deploy_project() {
    log_step "部署项目代码..."
    
    # 创建项目目录
    mkdir -p "$PROJECT_DIR"
    cd "$PROJECT_DIR"
    
    # 如果当前目录就是项目目录，则复制文件
    if [ "$(pwd)" != "$PROJECT_DIR" ]; then
        log_info "复制项目文件..."
        cp -r /path/to/source/* "$PROJECT_DIR/"
    fi
    
    # 设置目录权限
    chown -R www:www "$PROJECT_DIR"
    chmod -R 755 "$PROJECT_DIR"
    chmod -R 777 "$PROJECT_DIR/storage"
    chmod -R 777 "$PROJECT_DIR/bootstrap/cache"
    
    log_success "项目代码部署完成"
}

# 安装依赖
install_dependencies() {
    log_step "安装项目依赖..."

    cd "$PROJECT_DIR"

    # 清理旧依赖
    log_info "清理旧依赖..."
    rm -rf vendor/ composer.lock

    # 安装PHP依赖
    log_info "安装PHP依赖..."
    composer config --global repo.packagist composer https://mirrors.aliyun.com/composer/
    if ! composer install --no-dev --optimize-autoloader --ignore-platform-reqs; then
        log_warning "标准安装失败，尝试强制安装..."
        composer install --no-dev --optimize-autoloader --ignore-platform-reqs --no-scripts
    fi

    # 安装管理后台依赖
    if [ -d "admin" ]; then
        log_info "构建管理后台..."
        cd admin
        rm -rf node_modules package-lock.json dist
        npm config set registry https://registry.npmmirror.com
        npm install --legacy-peer-deps || npm install --force
        npm run build
        cd ..
    fi

    # 安装用户前端依赖
    if [ -d "frontend" ]; then
        log_info "构建用户前端..."
        cd frontend
        rm -rf node_modules package-lock.json .nuxt .output
        npm config set registry https://registry.npmmirror.com
        npm install --legacy-peer-deps || npm install --force
        npm run build
        cd ..
    fi

    log_success "依赖安装完成"
}

# 配置应用
configure_application() {
    log_step "配置应用..."
    
    cd "$PROJECT_DIR"
    
    # 创建环境配置文件
    if [ ! -f ".env" ]; then
        cp .env.example .env
    fi
    
    # 读取数据库密码
    DB_PASSWORD=$(grep "密码:" /root/ffjq_db_info.txt | cut -d' ' -f2)
    
    # 更新环境配置
    sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env
    sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" .env
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env
    
    # 生成应用密钥
    php artisan key:generate --force
    php artisan jwt:secret --force
    
    # 执行数据库迁移
    php artisan migrate --force
    php artisan db:seed --force
    
    # 清理和生成缓存
    php artisan cache:clear
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    log_success "应用配置完成"
}

# 配置Nginx
configure_nginx() {
    log_step "配置Nginx..."
    
    NGINX_CONF="/www/server/panel/vhost/nginx/ffjq.conf"
    
    cat > "$NGINX_CONF" << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    root /www/wwwroot/ffjq/public;
    index index.php index.html;
    
    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    
    # 前端路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 管理后台
    location /admin {
        alias /www/wwwroot/ffjq/public/admin;
        try_files $uri $uri/ /admin/index.html;
    }
    
    # API路由
    location /api {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-82.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ /(storage|bootstrap|config|database|resources|routes|tests|vendor) {
        deny all;
    }
}
EOF
    
    # 重启Nginx
    systemctl restart nginx
    
    log_success "Nginx配置完成"
}

# 设置定时任务
setup_cron() {
    log_step "设置定时任务..."
    
    # 添加Laravel定时任务
    (crontab -l 2>/dev/null; echo "* * * * * cd $PROJECT_DIR && php artisan schedule:run >> /dev/null 2>&1") | crontab -
    
    log_success "定时任务设置完成"
}

# 显示部署结果
show_result() {
    log_success "=========================================="
    log_success "🎉 $PROJECT_NAME 部署完成！"
    log_success "=========================================="
    
    echo -e "${GREEN}项目信息:${NC}"
    echo "项目目录: $PROJECT_DIR"
    echo "数据库信息: /root/ffjq_db_info.txt"
    echo ""
    
    echo -e "${GREEN}访问地址:${NC}"
    echo "前端地址: http://your-domain.com"
    echo "管理后台: http://your-domain.com/admin"
    echo "API文档: http://your-domain.com/api-docs.html"
    echo ""
    
    echo -e "${YELLOW}后续操作:${NC}"
    echo "1. 修改Nginx配置中的域名"
    echo "2. 配置SSL证书"
    echo "3. 设置防火墙规则"
    echo "4. 配置队列服务"
    echo ""
    
    log_success "=========================================="
}

# 主函数
main() {
    print_header
    
    check_environment
    check_requirements
    configure_php
    create_database
    deploy_project
    install_dependencies
    configure_application
    configure_nginx
    setup_cron
    show_result
}

# 执行主函数
main "$@"
