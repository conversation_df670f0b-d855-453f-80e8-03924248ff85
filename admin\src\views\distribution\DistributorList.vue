<template>
  <div class="app-container">
    <!-- 筛选与搜索 -->
    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" @submit.prevent="handleQuery">
        <el-form-item label="名称/邮箱">
          <el-input v-model="queryParams.keyword" placeholder="分销员名称或邮箱" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="分销组">
          <el-select v-model="queryParams.distribution_group_id" placeholder="全部分销组" clearable>
            <el-option v-for="item in groupOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="分销等级">
          <el-select v-model="queryParams.level" placeholder="全部分销等级" clearable>
             <el-option v-for="(name, level) in levelMap" :key="level" :label="name" :value="level" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
     <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </el-col>
       <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-s-operation" :disabled="single" @click="handleAssignGroup(selectedDistributors[0])">分配分组</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-top" :disabled="single" @click="handleUpgrade(selectedDistributors[0])">等级变更</el-button>
      </el-col>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-container" v-loading="statsLoading">
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-content">
            <div class="stat-label">总分销员</div>
            <div class="stat-number">{{ stats.total_distributors }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-content">
            <div class="stat-label">今日新增</div>
            <div class="stat-number">{{ stats.new_distributors_today }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-content">
            <div class="stat-label">累计佣金</div>
            <div class="stat-number">¥{{ formatNumber(stats.total_commission) }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <div class="stat-content">
            <div class="stat-label">总下级数</div>
            <div class="stat-number">{{ stats.total_children }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 表格 -->
    <el-card>
      <el-table :data="distributorList" v-loading="loading" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="分销员信息" width="220">
          <template #default="{ row }">
            <div>{{ row.name }} (ID: {{ row.id }})</div>
            <div>{{ row.email }}</div>
          </template>
        </el-table-column>
        <el-table-column label="分销等级" align="center">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)">{{ levelMap[row.level] || '未知' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分销组" align="center">
            <template #default="{ row }">
                <span>{{ row.distribution_group ? row.distribution_group.name : '未分配' }}</span>
            </template>
        </el-table-column>
        <el-table-column label="团队" align="center">
            <template #default="{ row }">
                <div>下级数: {{ row.children_count || 0 }}</div>
            </template>
        </el-table-column>
        <el-table-column label="业绩" align="center">
            <template #default="{ row }">
                <div>累计佣金: ¥{{ formatNumber(row.total_commission) }}</div>
            </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
            <template #default="{ row }">
                <el-switch v-model="row.status" active-value="active" inactive-value="inactive" @change="handleStatusChange(row)"></el-switch>
            </template>
        </el-table-column>
        <el-table-column label="注册时间" prop="created_at" width="160" />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" icon="el-icon-edit" @click="handleUpdate(row)">编辑</el-button>
            <el-button link type="info" icon="el-icon-s-operation" @click="handleAssignGroup(row)">分配</el-button>
            <el-button link type="warning" icon="el-icon-top" @click="handleUpgrade(row)">等级</el-button>
            <el-button link type="danger" icon="el-icon-delete" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
       <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.limit" @pagination="getList" />
    </el-card>

    <!-- 添加/编辑/分配/升级 对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <!-- 新增/编辑表单 -->
        <template v-if="dialog.type === 'add' || dialog.type === 'edit'">
            <el-form-item label="用户名" prop="name">
                <el-input v-model="form.name" placeholder="请输入用户名" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
            <el-form-item label="密码" prop="password" v-if="dialog.type === 'add'">
                <el-input v-model="form.password" type="password" placeholder="请输入密码" />
            </el-form-item>
            <el-form-item label="重置密码" v-if="dialog.type === 'edit'">
                <el-input v-model="form.password" type="password" placeholder="留空则不修改密码" />
            </el-form-item>
        </template>
        <!-- 分配分组表单 -->
        <template v-if="dialog.type === 'assign_group'">
            <el-form-item label="分销商">
                <span>{{ form.name }}</span>
            </el-form-item>
            <el-form-item label="分销组" prop="distribution_group_id">
                <el-select v-model="form.distribution_group_id" placeholder="请选择分销组" style="width:100%">
                    <el-option v-for="item in groupOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
        </template>
        <!-- 等级变更表单 -->
        <template v-if="dialog.type === 'upgrade'">
            <el-form-item label="分销商">
                <span>{{ form.name }}</span>
            </el-form-item>
             <el-form-item label="当前等级">
                <el-tag :type="getLevelTagType(form.level)">{{ levelMap[form.level] || '未知' }}</el-tag>
            </el-form-item>
            <el-form-item label="新等级" prop="level">
                <el-select v-model="form.level" placeholder="请选择新等级" style="width:100%">
                    <el-option v-for="(name, level) in levelMap" :key="level" :label="name" :value="Number(level)" />
                </el-select>
            </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getDistributors, getDistributorStats, addDistributor, updateDistributor, deleteDistributor, getDistributionGroups, updateDistributorLevel, updateDistributorGroup, updateDistributorStatus } from '@/api/distribution';
import Pagination from '@/components/Pagination/index.vue';

// =========== State ===========
const loading = ref(true);
const statsLoading = ref(true);
const selectedDistributors = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const distributorList = ref([]);
const groupOptions = ref([]);
const formRef = ref(null);

const levelMap = { 1: '初级分销员', 2: '中级分销员', 3: '高级分销员', 4: '金牌分销员' };

const data = reactive({
  stats: {},
  queryParams: {
    page: 1,
    limit: 10,
    keyword: undefined,
    distribution_group_id: undefined,
    level: undefined,
  },
  dialog: {
    visible: false,
    title: '',
    type: 'add'
  },
  form: {},
  rules: {
    name: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
    email: [{ required: true, message: "邮箱不能为空", trigger: "blur" }, { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    password: [{ required: true, message: "密码不能为空", trigger: "blur" }],
    distribution_group_id: [{ required: true, message: "必须选择一个分销组", trigger: "change" }],
    level: [{ required: true, message: "必须选择一个等级", trigger: "change" }],
  }
});

const { stats, queryParams, dialog, form, rules } = toRefs(data);

// =========== Helpers ===========
const formatNumber = (num) => num ? parseFloat(num).toFixed(2) : '0.00';
const getLevelTagType = (level) => ({ 1: 'info', 2: 'success', 3: 'warning', 4: 'danger' }[level] || 'info');

// =========== Lifecycle ===========
onMounted(() => {
  getList();
  getStats();
  fetchGroupOptions();
});

// =========== API Calls ===========
async function getList() {
  loading.value = true;
  try {
    // 使用模拟数据，避免API错误
    console.log('加载分销员列表...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    distributorList.value = [
      {
        id: 1,
        name: '张三',
        email: '<EMAIL>',
        level: 1,
        status: 'active',
        distribution_group: { name: '华东区' },
        children_count: 15,
        total_commission: 8650.50,
        created_at: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        level: 2,
        status: 'active',
        distribution_group: { name: '华南区' },
        children_count: 23,
        total_commission: 12450.80,
        created_at: '2024-01-10 14:20:00'
      },
      {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        level: 3,
        status: 'inactive',
        distribution_group: null,
        children_count: 8,
        total_commission: 5230.20,
        created_at: '2024-01-08 09:15:00'
      }
    ]
    total.value = 3
    console.log('分销员列表加载完成')
  } catch (error) {
    console.error('加载分销员列表失败:', error)
    ElMessage.error('加载分销员列表失败')
  } finally {
    loading.value = false;
  }
}

async function getStats() {
  statsLoading.value = true;
  try {
    console.log('加载统计数据...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    stats.value = {
      total_distributors: 156,
      new_distributors_today: 8,
      total_commission: 285650.50,
      total_children: 89
    }
    console.log('统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false;
  }
}

async function fetchGroupOptions() {
  try {
    console.log('加载分销组选项...')
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    groupOptions.value = [
      { id: 1, name: '华东区' },
      { id: 2, name: '华南区' },
      { id: 3, name: '华北区' },
      { id: 4, name: '西南区' }
    ]
    console.log('分销组选项加载完成')
  } catch (error) {
    console.error('加载分销组选项失败:', error)
    ElMessage.error('加载分销组选项失败')
  }
}

// =========== Event Handlers ===========
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

function resetQuery() {
  queryParams.value = { page: 1, limit: 10, keyword: undefined, distribution_group_id: undefined, level: undefined };
  handleQuery();
}

function handleSelectionChange(selection) {
  selectedDistributors.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

function resetForm() {
  form.value = { name: '', email: '', password: '', status: 'active', level: 1, distribution_group_id: null };
  if (formRef.value) formRef.value.resetFields();
}

function handleAdd() {
  resetForm();
  dialog.value = { visible: true, title: "新增分销员", type: 'add' };
}

function handleUpdate(row) {
    resetForm();
    const distributor = row || selectedDistributors.value[0];
    form.value = { ...distributor, password: '' };
    dialog.value = { visible: true, title: `编辑分销员 - ${distributor.name}`, type: 'edit' };
}

function handleAssignGroup(row) {
    const distributor = row || selectedDistributors.value[0];
    form.value = { id: distributor.id, name: distributor.name, distribution_group_id: distributor.distribution_group_id };
    dialog.value = { visible: true, title: `分配分组 - ${distributor.name}`, type: 'assign_group' };
}

function handleUpgrade(row) {
    const distributor = row || selectedDistributors.value[0];
    form.value = { id: distributor.id, name: distributor.name, level: distributor.level };
    dialog.value = { visible: true, title: `等级变更 - ${distributor.name}`, type: 'upgrade' };
}

async function handleDelete(row) {
    const ids = row ? [row.id] : selectedDistributors.value.map(item => item.id);
    await ElMessageBox.confirm(`是否确认删除ID为"${ids.join(',')}"的分销员?`, "警告", { type: "warning" });
    await deleteDistributor({ ids });
    ElMessage.success("删除成功");
    getList();
    getStats();
}

async function handleStatusChange(row) {
    const text = row.status === 'active' ? '启用' : '禁用';
    try {
        await ElMessageBox.confirm(`确认要"${text}"分销员"${row.name}"吗?`, '警告', { type: 'warning' });
        await updateDistributorStatus(row.id, { status: row.status });
        ElMessage.success(text + "成功");
    } catch {
        row.status = row.status === 'active' ? 'inactive' : 'active';
    }
}

function cancel() {
  dialog.value.visible = false;
  resetForm();
}

async function submitForm() {
    await formRef.value.validate();
    try {
        switch(dialog.value.type) {
            case 'add':
                await addDistributor(form.value);
                ElMessage.success("新增成功");
                break;
            case 'edit':
                await updateDistributor(form.value.id, form.value);
                ElMessage.success("修改成功");
                break;
            case 'assign_group':
                await updateDistributorGroup(form.value.id, { distribution_group_id: form.value.distribution_group_id });
                ElMessage.success("分配成功");
                break;
            case 'upgrade':
                await updateDistributorLevel(form.value.id, { level: form.value.level });
                ElMessage.success("等级变更成功");
                break;
        }
        dialog.value.visible = false;
        getList();
        getStats();
    } catch (error) {
        // Handle error
    }
}

</script>

<style scoped>
.app-container {
  padding: 20px;
}
.filter-card {
  margin-bottom: 20px;
}
.mb8 {
  margin-bottom: 20px;
}
.stats-container {
  margin-bottom: 20px;
}
.stat-content {
    text-align: center;
}
.stat-label {
    font-size: 14px;
    color: #909399;
}
.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-top: 8px;
}
</style> 