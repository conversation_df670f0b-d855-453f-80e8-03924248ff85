/**
 * 权限指令
 * 用于在模板中控制元素的显示和隐藏
 */

import { hasRole, hasPermission } from '@/utils/permission'

// v-role 指令 - 基于角色控制
export const roleDirective = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value) {
      const hasRolePermission = hasRole(value)
      
      if (!hasRolePermission) {
        // 移除元素
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      console.error('v-role指令需要指定角色参数')
    }
  }
}

// v-permission 指令 - 基于权限控制
export const permissionDirective = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value) {
      const hasPermissionAccess = hasPermission(value)
      
      if (!hasPermissionAccess) {
        // 移除元素
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      console.error('v-permission指令需要指定权限参数')
    }
  }
}

// v-auth 指令 - 综合权限控制
export const authDirective = {
  mounted(el, binding) {
    const { value, modifiers } = binding
    
    if (!value) {
      console.error('v-auth指令需要指定权限参数')
      return
    }
    
    let hasAccess = false
    
    // 检查角色权限
    if (modifiers.role) {
      hasAccess = hasRole(value)
    }
    // 检查具体权限
    else if (modifiers.permission) {
      hasAccess = hasPermission(value)
    }
    // 默认检查角色
    else {
      hasAccess = hasRole(value)
    }
    
    if (!hasAccess) {
      // 根据修饰符决定处理方式
      if (modifiers.hide) {
        // 隐藏元素但保留在DOM中
        el.style.display = 'none'
      } else if (modifiers.disable) {
        // 禁用元素
        el.disabled = true
        el.classList.add('is-disabled')
      } else {
        // 默认移除元素
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}

// 安装权限指令
export function setupPermissionDirectives(app) {
  app.directive('role', roleDirective)
  app.directive('permission', permissionDirective)
  app.directive('auth', authDirective)
}

// 权限检查函数 - 用于组合式API
export function usePermission() {
  return {
    hasRole,
    hasPermission,
    checkRole: hasRole,
    checkPermission: hasPermission
  }
}