<template>
  <div class="group-content-manager">
    <!-- 内容管理工具栏 -->
    <div class="content-toolbar">
      <div class="toolbar-left">
        <h4 class="content-title">
          <el-icon><Document /></el-icon>
          群组内容管理
        </h4>
        <p class="content-desc">管理群组的展示内容、FAQ、用户评论等信息</p>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleSave" :loading="saving">
          <el-icon><Check /></el-icon>
          保存内容
        </el-button>
        <el-button @click="handlePreview">
          <el-icon><View /></el-icon>
          预览效果
        </el-button>
        <el-button @click="handleReset">
          <el-icon><RefreshRight /></el-icon>
          重置
        </el-button>
      </div>
    </div>

    <!-- 智能助手工具栏 -->
    <div class="smart-tools-bar">
      <el-button-group>
        <el-button @click="showAIGenerator = !showAIGenerator">
          <el-icon><MagicStick /></el-icon>
          AI生成助手
        </el-button>
        <el-button @click="showTemplateLibrary = !showTemplateLibrary">
          <el-icon><Collection /></el-icon>
          模板库
        </el-button>
        <el-button @click="analyzeContent">
          <el-icon><TrendCharts /></el-icon>
          内容分析
        </el-button>
        <el-button @click="optimizeContent">
          <el-icon><TrendCharts /></el-icon>
          智能优化
        </el-button>
      </el-button-group>
    </div>

    <!-- AI内容生成器 -->
    <el-collapse-transition>
      <div v-show="showAIGenerator" class="ai-generator-panel">
        <AIContentGenerator @content-generated="handleAIGenerated" />
      </div>
    </el-collapse-transition>

    <!-- 模板库 -->
    <el-collapse-transition>
      <div v-show="showTemplateLibrary" class="template-library-panel">
        <ContentTemplateLibrary @template-selected="handleTemplateSelected" />
      </div>
    </el-collapse-transition>

    <!-- 内容编辑区域 -->
    <div class="content-editor" v-loading="loading">
      <el-form :model="contentForm" :rules="contentRules" ref="contentFormRef" label-width="120px">
        
        <!-- 基础设置区块 -->
        <el-card class="content-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">
                <el-icon><Setting /></el-icon>
                基础设置
              </span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="群组头像">
                <ImageUploader 
                  v-model="contentForm.cover_image"
                  :limit="1"
                  accept="image/*"
                  list-type="picture-card"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成员图">
                <ImageUploader 
                  v-model="contentForm.member_image"
                  :limit="1"
                  accept="image/*"
                  list-type="picture-card"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="入群费用" prop="price">
                <el-input-number
                  v-model="contentForm.price"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  placeholder="0表示免费"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="群名称" prop="title">
                <el-input 
                  v-model="contentForm.title" 
                  placeholder="请输入群名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="副标题">
                <el-input 
                  v-model="contentForm.subtitle" 
                  placeholder="请输入副标题"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="阅读数">
                <el-input 
                  v-model="contentForm.read_count" 
                  placeholder="如：10万+"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="点赞数">
                <el-input-number 
                  v-model="contentForm.like_count" 
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="想看数">
                <el-input-number 
                  v-model="contentForm.want_see_count" 
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="头像库选择">
                <el-select v-model="contentForm.avatar_library" placeholder="选择头像库">
                  <el-option label="默认头像" value="default" />
                  <el-option label="商务头像" value="business" />
                  <el-option label="交友头像" value="dating" />
                  <el-option label="征婚头像" value="marriage" />
                  <el-option label="健身头像" value="fitness" />
                  <el-option label="家庭头像" value="family" />
                  <el-option label="扩列头像" value="qq" />
                  <el-option label="综合头像" value="za" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="按键名称">
            <el-input 
              v-model="contentForm.button_title" 
              placeholder="如：加入群，学习更多副业知识"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="引导浏览器打开">
                <el-radio-group v-model="contentForm.wx_accessible">
                  <el-radio :label="1">微信能打开</el-radio>
                  <el-radio :label="2">微信内不能打开</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注信息">
                <el-input 
                  v-model="contentForm.remark" 
                  placeholder="备注信息"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 内容设置区块 -->
        <el-card class="content-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">
                <el-icon><InfoFilled /></el-icon>
                内容设置
              </span>
            </div>
          </template>
          
          <el-form-item label="区块一标题">
            <el-input 
              v-model="contentForm.group_intro_title" 
              placeholder="如：群简介"
            />
          </el-form-item>
          
          <el-form-item label="区块一内容">
            <el-input 
              type="textarea"
              v-model="contentForm.group_intro_content"
              :rows="4"
              placeholder="输入群组介绍内容"
            />
          </el-form-item>
        </el-card>

        <!-- FAQ区块 -->
        <el-card class="content-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">
                <el-icon><QuestionFilled /></el-icon>
                常见问题 (FAQ)
              </span>
              <el-switch 
                v-model="contentForm.show_faq" 
                active-text="显示" 
                inactive-text="隐藏"
              />
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="FAQ标题" prop="faq_title">
                <el-input 
                  v-model="contentForm.faq_title" 
                  placeholder="如：常见问题、FAQ"
                  maxlength="60"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="展示样式">
                <el-select v-model="contentForm.faq_style" placeholder="选择展示样式">
                  <el-option label="折叠面板" value="collapse" />
                  <el-option label="列表展示" value="list" />
                  <el-option label="卡片展示" value="card" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- FAQ列表管理 -->
          <el-form-item label="FAQ内容">
            <div class="faq-manager">
              <div class="faq-header">
                <span>问答列表</span>
                <el-button type="primary" size="small" @click="addFaqItem">
                  <el-icon><Plus /></el-icon>
                  添加问答
                </el-button>
              </div>
              
              <div class="faq-list">
                <div 
                  v-for="(item, index) in faqList" 
                  :key="index" 
                  class="faq-item"
                >
                  <div class="faq-item-header">
                    <span class="faq-index">{{ index + 1 }}</span>
                    <el-input 
                      v-model="item.question" 
                      placeholder="输入问题"
                      class="faq-question"
                    />
                    <el-button 
                      type="danger" 
                      size="small" 
                      circle 
                      @click="removeFaqItem(index)"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                  <el-input 
                    type="textarea" 
                    v-model="item.answer" 
                    placeholder="输入答案"
                    :rows="3"
                    class="faq-answer"
                  />
                </div>
                
                <el-empty v-if="faqList.length === 0" description="暂无FAQ内容" :image-size="80" />
              </div>
            </div>
          </el-form-item>
        </el-card>

        <!-- 用户评论区块 -->
        <el-card class="content-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">
                <el-icon><ChatDotRound /></el-icon>
                用户评论
              </span>
              <el-switch 
                v-model="contentForm.show_reviews" 
                active-text="显示" 
                inactive-text="隐藏"
              />
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="评论标题">
                <el-input 
                  v-model="contentForm.reviews_title" 
                  placeholder="如：用户评价、群友反馈"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示数量">
                <el-input-number 
                  v-model="contentForm.reviews_count" 
                  :min="1" 
                  :max="20"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="评论样式">
                <el-select v-model="contentForm.reviews_style" placeholder="选择样式">
                  <el-option label="卡片样式" value="card" />
                  <el-option label="列表样式" value="list" />
                  <el-option label="轮播样式" value="carousel" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- 评论列表管理 -->
          <el-form-item label="评论内容">
            <div class="reviews-manager">
              <div class="reviews-header">
                <span>评论列表</span>
                <el-button type="primary" size="small" @click="addReviewItem">
                  <el-icon><Plus /></el-icon>
                  添加评论
                </el-button>
              </div>
              
              <div class="reviews-list">
                <div 
                  v-for="(review, index) in reviewsList" 
                  :key="index" 
                  class="review-item"
                >
                  <div class="review-header">
                    <el-avatar :src="review.avatar" :size="40">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <div class="review-user">
                      <el-input 
                        v-model="review.username" 
                        placeholder="用户名"
                        size="small"
                      />
                      <el-rate v-model="review.rating" size="small" />
                    </div>
                    <el-button 
                      type="danger" 
                      size="small" 
                      circle 
                      @click="removeReviewItem(index)"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                  <el-input 
                    type="textarea" 
                    v-model="review.content" 
                    placeholder="输入评论内容"
                    :rows="2"
                  />
                  <div class="review-meta">
                    <el-input 
                      v-model="review.avatar" 
                      placeholder="头像URL（可选）"
                      size="small"
                    />
                    <el-date-picker 
                      v-model="review.created_at" 
                      type="datetime" 
                      placeholder="评论时间"
                      size="small"
                      format="YYYY-MM-DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm:ss"
                    />
                  </div>
                </div>
                
                <el-empty v-if="reviewsList.length === 0" description="暂无用户评论" :image-size="80" />
              </div>
            </div>
          </el-form-item>
        </el-card>

        <!-- 扩展内容区块 -->
        <el-card class="content-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">
                <el-icon><Grid /></el-icon>
                扩展内容区块
              </span>
              <el-switch 
                v-model="contentForm.show_extra" 
                active-text="显示" 
                inactive-text="隐藏"
              />
            </div>
          </template>
          
          <!-- 扩展区块1 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="区块1标题">
                <el-input 
                  v-model="contentForm.extra_title1" 
                  placeholder="如：服务优势、特色功能"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="区块2标题">
                <el-input 
                  v-model="contentForm.extra_title2" 
                  placeholder="如：联系方式、注意事项"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="区块1内容">
                <RichTextEditor 
                  v-model="contentForm.extra_content1"
                  :height="150"
                  placeholder="输入扩展区块1的内容"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="区块2内容">
                <RichTextEditor 
                  v-model="contentForm.extra_content2"
                  :height="150"
                  placeholder="输入扩展区块2的内容"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 展示广告区块 -->
        <el-card class="content-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">
                <el-icon><Picture /></el-icon>
                展示广告
              </span>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客服二维码">
                <ImageUploader 
                  v-model="contentForm.customer_service_qr"
                  :limit="1"
                  accept="image/*"
                  list-type="picture-card"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="广告二维码">
                <ImageUploader 
                  v-model="contentForm.ad_qr_code"
                  :limit="1"
                  accept="image/*"
                  list-type="picture-card"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 客服信息设置 -->
          <el-divider content-position="left">客服信息设置</el-divider>
          
          <el-form-item label="客服信息显示">
            <el-radio-group v-model="contentForm.show_customer_service">
              <el-radio :label="1">不显示</el-radio>
              <el-radio :label="2">显示</el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="contentForm.show_customer_service === 2">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客服头像">
                  <ImageUploader 
                    v-model="contentForm.customer_service_avatar"
                    :limit="1"
                    accept="image/*"
                    list-type="picture-card"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服标题">
                  <el-input 
                    v-model="contentForm.customer_service_title" 
                    placeholder="如：VIP专属客服"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="客服描述">
              <el-input 
                v-model="contentForm.customer_service_desc" 
                placeholder="如：出现不能付款，不能入群等问题，请联系我！看到信息发回"
              />
            </el-form-item>
          </div>

          <!-- 区块三设置 -->
          <el-divider content-position="left">区块三设置</el-divider>
          
          <el-form-item label="区块三标题">
            <el-input 
              v-model="contentForm.extra_title3" 
              placeholder="区块三标题"
            />
          </el-form-item>
          
          <el-form-item label="区块三图片">
            <RichTextEditor 
              v-model="contentForm.extra_content3"
              :height="200"
              placeholder="可以插入图片和富文本内容"
            />
          </el-form-item>
        </el-card>

      </el-form>
    </div>

    <!-- 内容预览对话框 -->
    <ContentPreviewDialog 
      v-model="previewVisible"
      :content-data="contentForm"
      :group-data="groupData"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Check,
  View,
  RefreshRight,
  InfoFilled,
  QuestionFilled,
  ChatDotRound,
  Grid,
  Picture,
  Plus,
  Delete,
  User,
  Setting
} from '@element-plus/icons-vue'
import RichTextEditor from '@/components/RichTextEditor.vue'
import ImageUploader from '@/components/Upload/ImageUpload.vue'
import ContentPreviewDialog from './ContentPreviewDialog.vue'
import { getGroupContent, updateGroupContent } from '@/api/community'

const props = defineProps({
  groupId: {
    type: [Number, String],
    required: true
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['content-updated'])

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const previewVisible = ref(false)
const contentFormRef = ref(null)

// 内容表单数据 - 基于源码分析完善
const contentForm = reactive({
  // 基础设置字段
  cover_image: '',           // 群组头像 (wxg_img)
  member_image: '',          // 成员图 (wxg_memimg)
  price: 0,                  // 入群费用 (wxg_money)
  title: '',                 // 群名称 (wxg_title)
  subtitle: '',              // 副标题 (wxg_subtitle)
  read_count: '10万+',       // 阅读数 (wxg_redcount)
  like_count: 3659,          // 点赞数 (wxg_dzcount)
  want_see_count: 665,       // 想看数 (wxg_xxcount)
  button_title: '加入群，学习更多副业知识', // 按键名称 (wxg_buttitle)
  avatar_library: 'default', // 头像库选择 (headimg)
  wx_accessible: 1,          // 微信内是否可访问 (wxonoff)
  remark: '',                // 备注信息 (wxg_content)
  
  // 内容设置字段
  group_intro_title: '群简介',    // 区块一标题 (wxg_qjj_title)
  group_intro_content: '',       // 区块一内容 (wxg_qjj_content)
  faq_title: '常见问题',         // 区块二标题 (wxg_kuai_title)
  faq_content: '',               // 区块二内容 (wxg_kuai_content)
  user_reviews: '',              // 群友评论 (wxg_qunyou_content)
  
  // 展示广告字段
  customer_service_qr: '',       // 客服二维码 (wxg_kefu)
  ad_qr_code: '',               // 广告二维码 (wxg_adurl)
  show_customer_service: 1,      // 客服信息显示 (wxg_jhao)
  customer_service_avatar: '',   // 客服头像 (wxg_jhaoimg)
  customer_service_title: 'VIP专属客服', // 客服标题 (wxg_jhaotitle)
  customer_service_desc: '出现不能付款，不能入群等问题，请联系我！看到信息发回', // 客服描述 (wxg_jhaocontent)
  
  // 区块三设置
  extra_title3: '',              // 区块三标题 (wxg_kuai_title1)
  extra_content3: '',            // 区块三图片内容 (wxg_kuai_imgs1)
  
  // 支付设置
  payment_methods: [],           // 支付方式 (paylists)
  
  // 状态设置
  status: 1,                     // 群组状态
  
  // UI控制字段
  show_faq: true,
  faq_style: 'collapse',
  show_reviews: true,
  reviews_title: '用户评价',
  reviews_count: 6,
  reviews_style: 'card',
  show_extra: true,
  extra_title1: '',
  extra_content1: '',
  extra_title2: '',
  extra_content2: ''
})

// FAQ列表
const faqList = ref([])

// 评论列表
const reviewsList = ref([])

// 表单验证规则
const contentRules = {
  group_intro_title: [
    { required: true, message: '请输入群组介绍标题', trigger: 'blur' },
    { max: 60, message: '标题长度不能超过60个字符', trigger: 'blur' }
  ],
  faq_title: [
    { required: true, message: '请输入FAQ标题', trigger: 'blur' },
    { max: 60, message: '标题长度不能超过60个字符', trigger: 'blur' }
  ]
}

// 监听群组ID变化
watch(() => props.groupId, (newVal) => {
  if (newVal) {
    fetchGroupContent()
  }
}, { immediate: true })

// 方法
const fetchGroupContent = async () => {
  if (!props.groupId) return
  
  loading.value = true
  try {
    const { data } = await getGroupContent(props.groupId)
    
    // 填充表单数据
    Object.assign(contentForm, data.content || {})
    
    // 解析FAQ数据
    if (data.faq_content) {
      faqList.value = parseFaqContent(data.faq_content)
    }
    
    // 解析评论数据
    if (data.user_reviews) {
      reviewsList.value = parseReviewsContent(data.user_reviews)
    }
    
  } catch (error) {
    console.error('获取群组内容失败:', error)
    ElMessage.error('获取群组内容失败')
  } finally {
    loading.value = false
  }
}

const handleSave = async () => {
  if (!contentFormRef.value) return
  
  await contentFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    saving.value = true
    try {
      // 构建保存数据
      const saveData = {
        ...contentForm,
        faq_content: formatFaqContent(faqList.value),
        user_reviews: formatReviewsContent(reviewsList.value)
      }
      
      await updateGroupContent(props.groupId, saveData)
      ElMessage.success('内容保存成功')
      emit('content-updated')
      
    } catch (error) {
      console.error('保存内容失败:', error)
      ElMessage.error('保存内容失败')
    } finally {
      saving.value = false
    }
  })
}

const handlePreview = () => {
  previewVisible.value = true
}

const handleReset = () => {
  ElMessageBox.confirm('确定要重置所有内容吗？此操作不可恢复。', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    fetchGroupContent()
    ElMessage.success('内容已重置')
  })
}

// FAQ管理方法
const addFaqItem = () => {
  faqList.value.push({
    question: '',
    answer: ''
  })
}

const removeFaqItem = (index) => {
  faqList.value.splice(index, 1)
}

// 评论管理方法
const addReviewItem = () => {
  reviewsList.value.push({
    username: '',
    content: '',
    rating: 5,
    avatar: '',
    created_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
  })
}

const removeReviewItem = (index) => {
  reviewsList.value.splice(index, 1)
}

// 数据格式化方法
const parseFaqContent = (content) => {
  if (!content) return []
  
  const items = content.split('\n').filter(line => line.includes('----'))
  return items.map(item => {
    const [question, answer] = item.split('----')
    return { question: question?.trim() || '', answer: answer?.trim() || '' }
  })
}

const formatFaqContent = (list) => {
  return list
    .filter(item => item.question && item.answer)
    .map(item => `${item.question}----${item.answer}`)
    .join('\n')
}

const parseReviewsContent = (content) => {
  if (!content) return []
  
  const items = content.split('\n').filter(line => line.includes('----'))
  return items.map(item => {
    const parts = item.split('----')
    return {
      username: parts[0]?.trim() || '',
      content: parts[1]?.trim() || '',
      rating: parseInt(parts[2]) || 5,
      avatar: parts[3]?.trim() || '',
      created_at: parts[4]?.trim() || new Date().toISOString().slice(0, 19).replace('T', ' ')
    }
  })
}

const formatReviewsContent = (list) => {
  return list
    .filter(item => item.username && item.content)
    .map(item => `${item.username}----${item.content}----${item.rating}----${item.avatar}----${item.created_at}`)
    .join('\n')
}

// 生命周期
onMounted(() => {
  if (props.groupId) {
    fetchGroupContent()
  }
})
</script>

<style lang="scss" scoped>
.group-content-manager {
  .content-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    
    .toolbar-left {
      .content-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .content-desc {
        margin: 0;
        opacity: 0.9;
        font-size: 14px;
      }
    }
    
    .toolbar-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .content-editor {
    .content-section {
      margin-bottom: 24px;
      
      :deep(.el-card__header) {
        padding: 16px 20px;
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
      }
      
      :deep(.el-card__body) {
        padding: 24px;
      }
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #1e293b;
      }
    }
  }
  
  // FAQ管理样式
  .faq-manager {
    .faq-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 16px;
      background: #f1f5f9;
      border-radius: 8px;
      
      span {
        font-weight: 600;
        color: #475569;
      }
    }
    
    .faq-list {
      .faq-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        
        .faq-item-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          
          .faq-index {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 600;
          }
          
          .faq-question {
            flex: 1;
          }
        }
        
        .faq-answer {
          margin-left: 36px;
        }
      }
    }
  }
  
  // 评论管理样式
  .reviews-manager {
    .reviews-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 16px;
      background: #f1f5f9;
      border-radius: 8px;
      
      span {
        font-weight: 600;
        color: #475569;
      }
    }
    
    .reviews-list {
      .review-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        
        .review-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          
          .review-user {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
        }
        
        .review-meta {
          display: flex;
          gap: 12px;
          margin-top: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .group-content-manager {
    .content-toolbar {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .toolbar-right {
        justify-content: center;
      }
    }
    
    .faq-manager,
    .reviews-manager {
      .faq-item-header,
      .review-header {
        flex-wrap: wrap;
        gap: 8px;
      }
      
      .review-meta {
        flex-direction: column;
      }
    }
  }
}
</style>