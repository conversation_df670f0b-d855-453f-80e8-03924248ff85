<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserBehaviorStat extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'date',
        'login_count',
        'page_views',
        'session_duration',
        'actions_count',
        'orders_count',
        'commission_earned',
        'invite_count',
        'last_activity_at',
        'device_type',
        'browser',
        'platform',
        'ip_address',
        'location',
    ];

    protected $casts = [
        'date' => 'date',
        'commission_earned' => 'decimal:2',
        'session_duration' => 'integer',
        'last_activity_at' => 'datetime',
    ];

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取用户活跃度评分
     */
    public function getActivityScoreAttribute(): float
    {
        $score = 0;
        
        // 登录次数权重
        $score += $this->login_count * 10;
        
        // 页面浏览权重
        $score += $this->page_views * 2;
        
        // 会话时长权重（分钟）
        $score += ($this->session_duration / 60) * 5;
        
        // 操作次数权重
        $score += $this->actions_count * 3;
        
        // 订单数权重
        $score += $this->orders_count * 20;
        
        // 邀请数权重
        $score += $this->invite_count * 15;
        
        return round($score, 2);
    }

    /**
     * 获取用户价值评分
     */
    public function getValueScoreAttribute(): float
    {
        $score = 0;
        
        // 佣金收入权重
        $score += (float) $this->commission_earned * 0.1;
        
        // 订单数权重
        $score += $this->orders_count * 10;
        
        // 邀请数权重
        $score += $this->invite_count * 5;
        
        return round($score, 2);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeOfUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * 作用域：按设备类型筛选
     */
    public function scopeByDevice($query, $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    /**
     * 作用域：活跃用户
     */
    public function scopeActive($query)
    {
        return $query->where('login_count', '>', 0);
    }

    /**
     * 作用域：高价值用户
     */
    public function scopeHighValue($query)
    {
        return $query->where('commission_earned', '>', 0);
    }

    /**
     * 创建或更新统计数据
     */
    public static function updateOrCreateStat(int $userId, string $date, array $data): self
    {
        return self::updateOrCreate(
            ['user_id' => $userId, 'date' => $date],
            $data
        );
    }

    /**
     * 获取用户最近活跃度
     */
    public static function getRecentActivity(int $userId, int $days = 30): array
    {
        $stats = self::where('user_id', $userId)
            ->where('date', '>=', now()->subDays($days))
            ->orderBy('date', 'desc')
            ->get();

        return [
            'total_logins' => $stats->sum('login_count'),
            'total_page_views' => $stats->sum('page_views'),
            'total_session_duration' => $stats->sum('session_duration'),
            'total_actions' => $stats->sum('actions_count'),
            'total_orders' => $stats->sum('orders_count'),
            'total_commission' => $stats->sum('commission_earned'),
            'total_invites' => $stats->sum('invite_count'),
            'average_score' => $stats->avg('activity_score'),
            'active_days' => $stats->where('login_count', '>', 0)->count(),
        ];
    }
} 