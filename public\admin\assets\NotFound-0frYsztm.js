import{_ as t}from"./index-D2bI4m-v.js";import{ag as a,k as s,l as o,t as e,ai as c,E as n,z as d,D as i}from"./vue-vendor-DGsK9sC4.js";import{as as l}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";const r={class:"not-found-container"},f={class:"not-found-content"},v={class:"not-found-actions"},u=t({__name:"NotFound",setup(t){const u=a(),p=()=>{u.push("/")},m=()=>{u.go(-1)};return(t,a)=>{const u=l;return o(),s("div",r,[e("div",f,[a[2]||(a[2]=c('<div class="not-found-image" data-v-75c2aebc><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300" data-v-75c2aebc><circle cx="200" cy="150" r="80" fill="#f0f0f0" stroke="#ddd" stroke-width="4" data-v-75c2aebc></circle><text x="200" y="170" text-anchor="middle" font-size="60" font-weight="bold" fill="#999" data-v-75c2aebc>404</text></svg></div><h1 class="not-found-title" data-v-75c2aebc>页面未找到</h1><p class="not-found-description" data-v-75c2aebc> 抱歉，您访问的页面不存在或已被删除。 </p>',3)),e("div",v,[n(u,{type:"primary",onClick:p},{default:d(()=>a[0]||(a[0]=[i("返回首页",-1)])),_:1,__:[0]}),n(u,{onClick:m},{default:d(()=>a[1]||(a[1]=[i("返回上一页",-1)])),_:1,__:[1]})])])])}}},[["__scopeId","data-v-75c2aebc"]]);export{u as default};
