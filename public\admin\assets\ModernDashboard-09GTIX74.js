import{_ as e,u as a}from"./index-D2bI4m-v.js";/* empty css                    *//* empty css               *//* empty css                  */import{ag as s,r as t,c as l,L as i,e as c,n,a4 as r,k as d,l as o,t as u,E as v,u as m,z as g,D as p,F as y,Y as h,ai as f,y as b,C as k}from"./vue-vendor-DGsK9sC4.js";import{an as w,a7 as _,ai as C,a6 as D,ao as A,ae as N,a0 as T,aT as V,am as x,U as $,T as j,aU as M,as as U,aM as z,aV as G,aW as q,aX as L,aY as S,aI as F,aZ as I,p as O,au as R,o as W,a_ as Y,aO as E,aN as H,a$ as P,b0 as Q,$ as X,Q as Z}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";const B={class:"modern-dashboard"},J={class:"welcome-banner"},K={class:"banner-content"},ee={class:"welcome-text"},ae={class:"welcome-title"},se={class:"welcome-subtitle"},te={class:"banner-actions"},le={class:"metrics-grid"},ie={class:"metric-content"},ce={class:"metric-value"},ne={class:"metric-label"},re={class:"metric-chart"},de={class:"dashboard-content"},oe={class:"content-left"},ue={class:"chart-card"},ve={class:"card-header"},me={class:"card-actions"},ge={class:"chart-container"},pe={class:"activity-card"},ye={class:"activity-heatmap"},he={class:"heatmap-grid"},fe=["title"],be={class:"orders-card"},ke={class:"card-header"},we={class:"orders-list"},_e=["onClick"],Ce={class:"order-avatar"},De={class:"order-info"},Ae={class:"order-title"},Ne={class:"order-meta"},Te={class:"order-amount"},Ve={class:"popular-groups-card"},xe={class:"card-header"},$e={class:"groups-ranking"},je=["onClick"],Me={class:"group-info"},Ue={class:"group-name"},ze={class:"group-stats"},Ge={class:"member-count"},qe={class:"revenue"},Le={class:"group-trend"},Se={class:"trend-value"},Fe={class:"recent-activities-card"},Ie={class:"card-header"},Oe={class:"activity-filters"},Re={class:"activities-timeline"},We={class:"timeline-content"},Ye={class:"activity-header"},Ee={class:"activity-user"},He={class:"activity-action"},Pe={class:"activity-target"},Qe={class:"activity-time"},Xe={class:"content-right"},Ze={class:"quick-stats-card"},Be={class:"stats-grid"},Je={class:"stat-content"},Ke={class:"stat-value"},ea={class:"stat-label"},aa={class:"notifications-card"},sa={class:"card-header"},ta={class:"notifications-list"},la={class:"notification-content"},ia={class:"notification-title"},ca={class:"notification-time"},na={class:"notification-actions"},ra={class:"system-status-card"},da={class:"status-metrics"},oa={class:"status-item"},ua={class:"status-progress"},va={class:"status-value"},ma={class:"status-item"},ga={class:"status-progress"},pa={class:"status-value"},ya={class:"status-item"},ha={class:"status-progress"},fa={class:"status-value"},ba={class:"status-item"},ka={class:"status-progress"},wa={class:"status-value"},_a={class:"quick-actions-card"},Ca={class:"actions-grid"},Da=["onClick"],Aa={class:"action-label"},Na={class:"quick-actions-content"},Ta={class:"category-title"},Va={class:"category-actions"},xa=["onClick"],$a={class:"action-info"},ja={class:"action-name"},Ma={class:"action-desc"},Ua=e({__name:"ModernDashboard",setup(e){const Ua=s(),za=a(),Ga=t(!1),qa=t("today"),La=t(null),Sa=l(()=>(new Date).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})),Fa=()=>{const e=(new Date).getHours();return e<6?"夜深了，注意休息":e<12?"早上好":e<18?"下午好":"晚上好"},Ia=i([{key:"users",label:"总用户数",value:12580,change:"+12.5%",changeType:"positive",icon:w,iconClass:"user-icon",chartGradient:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"},{key:"orders",label:"总订单数",value:8964,change:"+8.2%",changeType:"positive",icon:_,iconClass:"order-icon",chartGradient:"linear-gradient(135deg, #10b981 0%, #059669 100%)"},{key:"revenue",label:"总收入",value:256780,change:"+15.3%",changeType:"positive",icon:C,iconClass:"revenue-icon",chartGradient:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"},{key:"conversion",label:"转化率",value:68.5,change:"-2.1%",changeType:"negative",icon:D,iconClass:"conversion-icon",chartGradient:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)"}]),Oa=i({cpu:45,memory:62,disk:78,latency:23}),Ra=i([{key:"todayUsers",label:"今日新增用户",value:156,icon:A,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"},{key:"todayOrders",label:"今日订单",value:89,icon:_,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)"},{key:"todayRevenue",label:"今日收入",value:"¥12,580",icon:C,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"},{key:"activeUsers",label:"在线用户",value:1,icon:w,color:"linear-gradient(135deg, #ef4444 0%, #dc2626 100%)"}]),Wa=i([{id:1,groupName:"高端投资理财群",userName:"张三",userAvatar:"/avatars/user1.jpg",amount:299,status:"paid",createdAt:new Date(Date.now()-3e5)},{id:2,groupName:"股票交流群",userName:"李四",userAvatar:"/avatars/user2.jpg",amount:199,status:"pending",createdAt:new Date(Date.now()-9e5)},{id:3,groupName:"创业交流群",userName:"王五",userAvatar:"/avatars/user3.jpg",amount:99,status:"paid",createdAt:new Date(Date.now()-18e5)}]),Ya=i([{id:1,title:"系统维护通知",type:"warning",read:!1,createdAt:new Date(Date.now()-6e5)},{id:2,title:"新用户注册",type:"info",read:!1,createdAt:new Date(Date.now()-12e5)},{id:3,title:"订单支付成功",type:"success",read:!0,createdAt:new Date(Date.now()-21e5)}]),Ea=i([{key:"addUser",label:"添加用户",icon:A,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",action:()=>Ua.push("/user/add")},{key:"createGroup",label:"创建群组",icon:N,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)",action:()=>Ua.push("/community/groups/add")},{key:"viewReports",label:"查看报表",icon:D,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",action:()=>Ua.push("/dashboard/reports")},{key:"systemSettings",label:"系统设置",icon:T,color:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",action:()=>Ua.push("/system/settings")}]),Ha=i([{name:"用户管理",actions:[{key:"addUser",label:"添加用户",description:"快速添加新用户到系统",icon:A,color:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",action:()=>Ua.push("/user/add")},{key:"userAnalytics",label:"用户分析",description:"查看用户行为和统计数据",icon:V,color:"linear-gradient(135deg, #10b981 0%, #059669 100%)",action:()=>Ua.push("/user/analytics")}]},{name:"内容管理",actions:[{key:"createGroup",label:"创建群组",description:"创建新的微信群组",icon:N,color:"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",action:()=>Ua.push("/community/groups/add")},{key:"contentManage",label:"内容管理",description:"管理群组内容和模板",icon:x,color:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",action:()=>Ua.push("/content/management")}]}]),Pa=i([]),Qa=i([{id:1,name:"高端投资理财群",memberCount:2580,revenue:125600,trendType:"up",trendValue:"+15.2%"},{id:2,name:"股票交流群",memberCount:1890,revenue:89400,trendType:"up",trendValue:"+8.7%"},{id:3,name:"创业交流群",memberCount:1456,revenue:67200,trendType:"down",trendValue:"-2.3%"},{id:4,name:"技术分享群",memberCount:1234,revenue:45600,trendType:"up",trendValue:"+5.1%"},{id:5,name:"职场发展群",memberCount:987,revenue:32100,trendType:"flat",trendValue:"0%"}]),Xa=t("all"),Za=i([{key:"all",label:"全部"},{key:"user",label:"用户"},{key:"order",label:"订单"},{key:"system",label:"系统"}]),Ba=i([{id:1,userName:"张三",action:"加入了",target:"高端投资理财群",type:"user",createdAt:new Date(Date.now()-12e4)},{id:2,userName:"李四",action:"完成了订单支付",target:"¥299",type:"order",createdAt:new Date(Date.now()-48e4)},{id:3,userName:"系统",action:"自动备份了",target:"数据库",type:"system",createdAt:new Date(Date.now()-9e5)},{id:4,userName:"王五",action:"创建了",target:"新的群组",type:"user",createdAt:new Date(Date.now()-15e5)},{id:5,userName:"赵六",action:"申请了",target:"提现 ¥1200",type:"order",createdAt:new Date(Date.now()-21e5)}]),Ja=l(()=>"all"===Xa.value?Ba:Ba.filter(e=>e.type===Xa.value)),Ka=e=>{const a=new Date-e,s=Math.floor(a/6e4),t=Math.floor(a/36e5),l=Math.floor(a/864e5);return s<1?"刚刚":s<60?`${s}分钟前`:t<24?`${t}小时前`:`${l}天前`},es=()=>{Z.success("数据已刷新")},as=()=>{Ua.push("/system/notifications")},ss=e=>{e.action&&(e.action(),Ga.value=!1)},ts=e=>{const a=["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #f093fb 0%, #f5576c 100%)","linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)","linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)","linear-gradient(135deg, #fa709a 0%, #fee140 100%)","linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)","linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)","linear-gradient(135deg, #ff8a80 0%, #ea6100 100%)","linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)"];let s=0;for(let t=0;t<e.length;t++)s=e.charCodeAt(t)+((s<<5)-s);return a[Math.abs(s)%a.length]};return c(()=>{(()=>{const e=[],a=new Date;for(let s=364;s>=0;s--){const t=new Date(a.getTime()-24*s*60*60*1e3),l=Math.floor(20*Math.random());let i="none";l>15?i="high":l>8?i="medium":l>3&&(i="low"),e.push({date:t.toLocaleDateString(),count:l,level:i})}Pa.splice(0,Pa.length,...e)})(),n(()=>{})}),(e,a)=>{const s=j,t=U,l=G,i=q,c=H,n=r("Minus"),w=S,_=F,C=I,D=R;return o(),d("div",B,[u("div",J,[u("div",K,[u("div",ee,[u("h1",ae," 欢迎回来，"+$(m(za).nickname||"管理员")+"！ ",1),u("p",se," 今天是 "+$(Sa.value)+"，"+$(Fa()),1)]),u("div",te,[v(t,{type:"primary",class:"modern-btn primary",onClick:a[0]||(a[0]=e=>Ga.value=!0)},{default:g(()=>[v(s,null,{default:g(()=>[v(m(M))]),_:1}),a[5]||(a[5]=p(" 快速操作 ",-1))]),_:1,__:[5]}),v(t,{class:"modern-btn secondary",onClick:es},{default:g(()=>[v(s,null,{default:g(()=>[v(m(z))]),_:1}),a[6]||(a[6]=p(" 刷新数据 ",-1))]),_:1,__:[6]})])]),a[7]||(a[7]=u("div",{class:"banner-decoration"},[u("div",{class:"decoration-circle circle-1"}),u("div",{class:"decoration-circle circle-2"}),u("div",{class:"decoration-circle circle-3"})],-1))]),u("div",le,[(o(!0),d(y,null,h(Ia,e=>{return o(),d("div",{class:"metric-card",key:e.key},[u("div",{class:W(["metric-icon",e.iconClass])},[v(s,{size:24},{default:g(()=>[(o(),b(k(e.icon)))]),_:2},1024)],2),u("div",ie,[u("div",ce,$((a=e.value,a>=1e4?(a/1e4).toFixed(1)+"w":a.toLocaleString())),1),u("div",ne,$(e.label),1),u("div",{class:W(["metric-change",e.changeType])},[v(s,{size:12},{default:g(()=>["positive"===e.changeType?(o(),b(m(Y),{key:0})):(o(),b(m(E),{key:1}))]),_:2},1024),p(" "+$(e.change),1)],2)]),u("div",re,[u("div",{class:"mini-chart",style:O({background:e.chartGradient})},null,4)])]);var a}),128))]),u("div",de,[u("div",oe,[u("div",ue,[u("div",ve,[a[8]||(a[8]=u("h3",{class:"card-title"},"实时数据概览",-1)),u("div",me,[v(i,{modelValue:qa.value,"onUpdate:modelValue":a[1]||(a[1]=e=>qa.value=e),size:"small",class:"time-selector"},{default:g(()=>[v(l,{label:"今日",value:"today"}),v(l,{label:"本周",value:"week"}),v(l,{label:"本月",value:"month"}),v(l,{label:"本年",value:"year"})]),_:1},8,["modelValue"])])]),u("div",ge,[u("div",{ref_key:"mainChart",ref:La,class:"main-chart"},null,512)])]),u("div",pe,[a[9]||(a[9]=f('<div class="card-header" data-v-2b507339><h3 class="card-title" data-v-2b507339>用户活动热力图</h3><div class="activity-legend" data-v-2b507339><span class="legend-item" data-v-2b507339><div class="legend-color low" data-v-2b507339></div> 低活跃 </span><span class="legend-item" data-v-2b507339><div class="legend-color medium" data-v-2b507339></div> 中活跃 </span><span class="legend-item" data-v-2b507339><div class="legend-color high" data-v-2b507339></div> 高活跃 </span></div></div>',1)),u("div",ye,[u("div",he,[(o(!0),d(y,null,h(Pa,(e,a)=>(o(),d("div",{key:a,class:W(["heatmap-cell",e.level]),title:`${e.date}: ${e.count} 活跃用户`},null,10,fe))),128))])])]),u("div",be,[u("div",ke,[a[11]||(a[11]=u("h3",{class:"card-title"},"最新订单",-1)),v(t,{text:"",onClick:a[2]||(a[2]=a=>e.$router.push("/orders/list"))},{default:g(()=>[a[10]||(a[10]=p(" 查看全部 ",-1)),v(s,null,{default:g(()=>[v(m(L))]),_:1})]),_:1,__:[10]})]),u("div",we,[(o(!0),d(y,null,h(Wa,e=>{return o(),d("div",{key:e.id,class:"order-item",onClick:a=>{return s=e.id,void Ua.push(`/orders/detail/${s}`);var s}},[u("div",Ce,[v(c,{size:40,src:e.userAvatar,style:O({background:ts(e.userName),color:"white",fontWeight:"600"})},{default:g(()=>{return[p($((a=e.userName,a?/[\u4e00-\u9fa5]/.test(a)?a.slice(-1):a.charAt(0).toUpperCase():"?")),1)];var a}),_:2},1032,["src","style"])]),u("div",De,[u("div",Ae,$(e.groupName),1),u("div",Ne,$(e.userName)+" · "+$(Ka(e.createdAt)),1)]),u("div",Te," ¥"+$(e.amount),1),u("div",{class:W(["order-status",e.status])},$((a=e.status,{paid:"已支付",pending:"待支付",cancelled:"已取消",refunded:"已退款"}[a]||"未知")),3)],8,_e);var a}),128))])]),u("div",Ve,[u("div",xe,[a[13]||(a[13]=u("h3",{class:"card-title"},"热门群组排行",-1)),v(t,{text:"",onClick:a[3]||(a[3]=a=>e.$router.push("/community/groups"))},{default:g(()=>[a[12]||(a[12]=p(" 查看全部 ",-1)),v(s,null,{default:g(()=>[v(m(L))]),_:1})]),_:1,__:[12]})]),u("div",$e,[(o(!0),d(y,null,h(Qa,(e,t)=>(o(),d("div",{key:e.id,class:"ranking-item",onClick:a=>{return s=e.id,void Ua.push(`/community/groups/detail/${s}`);var s}},[u("div",{class:W(["ranking-number",{"top-three":t<3}])},$(t+1),3),u("div",Me,[u("div",Ue,$(e.name),1),u("div",ze,[u("span",Ge,$(e.memberCount)+"人",1),a[14]||(a[14]=u("span",{class:"separator"},"·",-1)),u("span",qe,"¥"+$(e.revenue),1)])]),u("div",Le,[u("div",{class:W(["trend-icon",e.trendType])},[v(s,null,{default:g(()=>["up"===e.trendType?(o(),b(m(Y),{key:0})):"down"===e.trendType?(o(),b(m(E),{key:1})):(o(),b(n,{key:2}))]),_:2},1024)],2),u("span",Se,$(e.trendValue),1)])],8,je))),128))])]),u("div",Fe,[u("div",Ie,[a[15]||(a[15]=u("h3",{class:"card-title"},"最新动态",-1)),u("div",Oe,[v(w,{size:"small"},{default:g(()=>[(o(!0),d(y,null,h(Za,e=>(o(),b(t,{key:e.key,type:Xa.value===e.key?"primary":"",onClick:a=>Xa.value=e.key},{default:g(()=>[p($(e.label),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})])]),u("div",Re,[(o(!0),d(y,null,h(Ja.value,e=>(o(),d("div",{key:e.id,class:"timeline-item"},[u("div",{class:W(["timeline-dot",e.type])},null,2),u("div",We,[u("div",Ye,[u("span",Ee,$(e.userName),1),u("span",He,$(e.action),1),u("span",Pe,$(e.target),1)]),u("div",Qe,$(Ka(e.createdAt)),1)])]))),128))])])]),u("div",Xe,[u("div",Ze,[a[16]||(a[16]=u("div",{class:"card-header"},[u("h3",{class:"card-title"},"今日统计")],-1)),u("div",Be,[(o(!0),d(y,null,h(Ra,e=>(o(),d("div",{class:"stat-item",key:e.key},[u("div",{class:"stat-icon",style:O({background:e.color})},[v(s,null,{default:g(()=>[(o(),b(k(e.icon)))]),_:2},1024)],4),u("div",Je,[u("div",Ke,$(e.value),1),u("div",ea,$(e.label),1)])]))),128))])]),u("div",aa,[u("div",sa,[a[18]||(a[18]=u("h3",{class:"card-title"},"通知中心",-1)),v(_,{value:Ya.length,hidden:0===Ya.length},{default:g(()=>[v(t,{text:"",onClick:as},{default:g(()=>a[17]||(a[17]=[p(" 查看全部 ",-1)])),_:1,__:[17]})]),_:1},8,["value","hidden"])]),u("div",ta,[(o(!0),d(y,null,h(Ya.slice(0,5),e=>(o(),d("div",{key:e.id,class:W(["notification-item",{unread:!e.read}])},[u("div",{class:W(["notification-icon",e.type])},[v(s,null,{default:g(()=>{return[(o(),b(k((a=e.type,{warning:P,info:X,success:Q,error:P}[a]||X))))];var a}),_:2},1024)],2),u("div",la,[u("div",ia,$(e.title),1),u("div",ca,$(Ka(e.createdAt)),1)]),u("div",na,[v(t,{text:"",size:"small",onClick:a=>(e=>{const a=Ya.find(a=>a.id===e);a&&(a.read=!0,Z.success("已标记为已读"))})(e.id)},{default:g(()=>a[19]||(a[19]=[p(" 标记已读 ",-1)])),_:2,__:[19]},1032,["onClick"])])],2))),128))])]),u("div",ra,[a[24]||(a[24]=u("div",{class:"card-header"},[u("h3",{class:"card-title"},"系统状态"),u("div",{class:"status-indicator online"},[u("div",{class:"status-dot"}),p(" 运行正常 ")])],-1)),u("div",da,[u("div",oa,[a[20]||(a[20]=u("div",{class:"status-label"},"CPU 使用率",-1)),u("div",ua,[v(C,{percentage:Oa.cpu,"show-text":!1},null,8,["percentage"]),u("span",va,$(Oa.cpu)+"%",1)])]),u("div",ma,[a[21]||(a[21]=u("div",{class:"status-label"},"内存使用率",-1)),u("div",ga,[v(C,{percentage:Oa.memory,"show-text":!1,color:"#10b981"},null,8,["percentage"]),u("span",pa,$(Oa.memory)+"%",1)])]),u("div",ya,[a[22]||(a[22]=u("div",{class:"status-label"},"磁盘使用率",-1)),u("div",ha,[v(C,{percentage:Oa.disk,"show-text":!1,color:"#f59e0b"},null,8,["percentage"]),u("span",fa,$(Oa.disk)+"%",1)])]),u("div",ba,[a[23]||(a[23]=u("div",{class:"status-label"},"网络延迟",-1)),u("div",ka,[u("span",wa,$(Oa.latency)+"ms",1)])])])]),u("div",_a,[a[25]||(a[25]=u("div",{class:"card-header"},[u("h3",{class:"card-title"},"快捷操作")],-1)),u("div",Ca,[(o(!0),d(y,null,h(Ea,e=>(o(),d("div",{key:e.key,class:"action-item",onClick:a=>ss(e)},[u("div",{class:"action-icon",style:O({background:e.color})},[v(s,null,{default:g(()=>[(o(),b(k(e.icon)))]),_:2},1024)],4),u("div",Aa,$(e.label),1)],8,Da))),128))])])])]),v(D,{modelValue:Ga.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Ga.value=e),title:"快速操作",width:"600px",class:"quick-actions-dialog"},{default:g(()=>[u("div",Na,[(o(!0),d(y,null,h(Ha,e=>(o(),d("div",{class:"action-category",key:e.name},[u("h4",Ta,$(e.name),1),u("div",Va,[(o(!0),d(y,null,h(e.actions,e=>(o(),d("div",{key:e.key,class:"category-action-item",onClick:a=>ss(e)},[u("div",{class:"action-icon",style:O({background:e.color})},[v(s,null,{default:g(()=>[(o(),b(k(e.icon)))]),_:2},1024)],4),u("div",$a,[u("div",ja,$(e.label),1),u("div",Ma,$(e.description),1)])],8,xa))),128))])]))),128))])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-2b507339"]]);export{Ua as default};
