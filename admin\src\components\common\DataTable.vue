<template>
  <div class="data-table-container">
    <!-- 表格工具栏 -->
    <div class="table-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <!-- 批量操作 -->
        <div class="batch-actions" v-if="selection.length > 0">
          <span class="selection-info">已选择 {{ selection.length }} 项</span>
          <el-button 
            v-for="action in batchActions" 
            :key="action.key"
            :type="action.type || 'default'"
            :icon="action.icon"
            size="small"
            @click="handleBatchAction(action)"
          >
            {{ action.label }}
          </el-button>
        </div>
        
        <!-- 普通操作 -->
        <div class="normal-actions" v-else>
          <el-button 
            v-for="action in toolbarActions" 
            :key="action.key"
            :type="action.type || 'default'"
            :icon="action.icon"
            size="small"
            @click="handleToolbarAction(action)"
          >
            {{ action.label }}
          </el-button>
        </div>
      </div>
      
      <div class="toolbar-right">
        <!-- 搜索框 -->
        <div class="search-box" v-if="searchable">
          <el-input
            v-model="searchKeyword"
            :placeholder="searchPlaceholder"
            size="small"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <i class="el-icon-search"></i>
            </template>
          </el-input>
        </div>
        
        <!-- 筛选器 -->
        <div class="filters" v-if="filters && filters.length">
          <el-select
            v-for="filter in filters"
            :key="filter.key"
            v-model="filterValues[filter.key]"
            :placeholder="filter.placeholder"
            size="small"
            clearable
            @change="handleFilter"
          >
            <el-option
              v-for="option in filter.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
        
        <!-- 表格设置 -->
        <el-dropdown trigger="click" v-if="showSettings">
          <el-button size="small" type="text">
            <i class="el-icon-setting"></i>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="exportData">
                <i class="el-icon-download"></i> 导出数据
              </el-dropdown-item>
              <el-dropdown-item @click="refreshData">
                <i class="el-icon-refresh"></i> 刷新数据
              </el-dropdown-item>
              <el-dropdown-item divided @click="showColumnSettings = true">
                <i class="el-icon-s-tools"></i> 列设置
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        :height="height"
        :max-height="maxHeight"
        :stripe="stripe"
        :border="border"
        :size="size"
        :empty-text="emptyText"
        :row-key="rowKey"
        :default-sort="defaultSort"
        :highlight-current-row="highlightCurrentRow"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
        @row-dblclick="handleRowDblClick"
        class="modern-table"
      >
        <!-- 选择列 -->
        <el-table-column
          v-if="selectable"
          type="selection"
          width="55"
          align="center"
          fixed="left"
        />
        
        <!-- 序号列 -->
        <el-table-column
          v-if="showIndex"
          type="index"
          label="序号"
          width="80"
          align="center"
          fixed="left"
        />
        
        <!-- 数据列 -->
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :align="column.align || 'left'"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template #default="scope" v-if="column.slot">
            <slot :name="column.slot" :row="scope.row" :column="column" :index="scope.$index"></slot>
          </template>
          
          <template #default="scope" v-else-if="column.formatter">
            <span v-html="column.formatter(scope.row, column, scope.row[column.prop], scope.$index)"></span>
          </template>
          
          <template #default="scope" v-else-if="column.type === 'tag'">
            <el-tag
              :type="getTagType(scope.row[column.prop], column.tagMap)"
              size="small"
            >
              {{ getTagText(scope.row[column.prop], column.tagMap) }}
            </el-tag>
          </template>
          
          <template #default="scope" v-else-if="column.type === 'image'">
            <el-image
              :src="scope.row[column.prop]"
              :preview-src-list="[scope.row[column.prop]]"
              fit="cover"
              style="width: 40px; height: 40px; border-radius: 4px;"
            />
          </template>
          
          <template #default="scope" v-else-if="column.type === 'date'">
            {{ formatDate(scope.row[column.prop], column.dateFormat) }}
          </template>
          
          <template #default="scope" v-else-if="column.type === 'currency'">
            ¥{{ formatCurrency(scope.row[column.prop]) }}
          </template>
          
          <template #default="scope" v-else-if="column.type === 'actions'">
            <div class="table-actions">
              <el-button
                v-for="action in getRowActions(scope.row, column.actions)"
                :key="action.key"
                :type="action.type || 'text'"
                :size="action.size || 'small'"
                :icon="action.icon"
                :disabled="action.disabled"
                @click="handleRowAction(action, scope.row, scope.$index)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页器 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 列设置对话框 -->
    <el-dialog
      v-model="showColumnSettings"
      title="列设置"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="column-settings">
        <el-checkbox-group v-model="selectedColumns">
          <div
            v-for="column in allColumns"
            :key="column.prop"
            class="column-item"
          >
            <el-checkbox :label="column.prop">
              {{ column.label }}
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      
      <template #footer>
        <el-button @click="showColumnSettings = false">取消</el-button>
        <el-button type="primary" @click="applyColumnSettings">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
// import { formatDistanceToNow } from 'date-fns'
// import { zhCN } from 'date-fns/locale'

// Props 定义
const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 表格列配置
  columns: {
    type: Array,
    required: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 表格高度
  height: {
    type: [String, Number],
    default: undefined
  },
  // 最大高度
  maxHeight: {
    type: [String, Number],
    default: undefined
  },
  // 是否显示斑马纹
  stripe: {
    type: Boolean,
    default: true
  },
  // 是否显示边框
  border: {
    type: Boolean,
    default: false
  },
  // 表格尺寸
  size: {
    type: String,
    default: 'default'
  },
  // 空数据文本
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  // 行数据的 Key
  rowKey: {
    type: String,
    default: 'id'
  },
  // 默认排序
  defaultSort: {
    type: Object,
    default: () => ({})
  },
  // 是否高亮当前行
  highlightCurrentRow: {
    type: Boolean,
    default: false
  },
  // 是否可选择
  selectable: {
    type: Boolean,
    default: false
  },
  // 是否显示序号
  showIndex: {
    type: Boolean,
    default: false
  },
  // 是否显示工具栏
  showToolbar: {
    type: Boolean,
    default: true
  },
  // 工具栏操作
  toolbarActions: {
    type: Array,
    default: () => []
  },
  // 批量操作
  batchActions: {
    type: Array,
    default: () => []
  },
  // 是否可搜索
  searchable: {
    type: Boolean,
    default: true
  },
  // 搜索占位符
  searchPlaceholder: {
    type: String,
    default: '请输入关键词搜索'
  },
  // 筛选器
  filters: {
    type: Array,
    default: () => []
  },
  // 是否显示设置
  showSettings: {
    type: Boolean,
    default: true
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: true
  },
  // 分页布局
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  // 每页显示条数选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  // 总条数
  total: {
    type: Number,
    default: 0
  }
})

// Emits 定义
const emit = defineEmits([
  'selection-change',
  'sort-change',
  'row-click',
  'row-dblclick',
  'toolbar-action',
  'batch-action',
  'row-action',
  'search',
  'filter',
  'page-change',
  'size-change',
  'refresh',
  'export'
])

// 响应式数据
const tableRef = ref()
const selection = ref([])
const searchKeyword = ref('')
const filterValues = ref({})
const currentPage = ref(1)
const pageSize = ref(20)
const showColumnSettings = ref(false)
const selectedColumns = ref([])
const allColumns = ref([])

// 计算属性
const tableData = computed(() => props.data)

const visibleColumns = computed(() => {
  if (selectedColumns.value.length === 0) {
    return props.columns
  }
  return props.columns.filter(column => selectedColumns.value.includes(column.prop))
})

// 生命周期
onMounted(() => {
  allColumns.value = props.columns.map(col => ({ prop: col.prop, label: col.label }))
  selectedColumns.value = props.columns.map(col => col.prop)
})

// 方法
const handleSelectionChange = (val) => {
  selection.value = val
  emit('selection-change', val)
}

const handleSortChange = (sort) => {
  emit('sort-change', sort)
}

const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event)
}

const handleRowDblClick = (row, column, event) => {
  emit('row-dblclick', row, column, event)
}

const handleToolbarAction = (action) => {
  emit('toolbar-action', action)
}

const handleBatchAction = (action) => {
  emit('batch-action', action, selection.value)
}

const handleRowAction = (action, row, index) => {
  emit('row-action', action, row, index)
}

const handleSearch = () => {
  emit('search', searchKeyword.value)
}

const handleFilter = () => {
  emit('filter', filterValues.value)
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('page-change', page, pageSize.value)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  emit('size-change', size, currentPage.value)
}

const refreshData = () => {
  emit('refresh')
}

const exportData = () => {
  emit('export')
}

const applyColumnSettings = () => {
  showColumnSettings.value = false
  ElMessage.success('列设置已应用')
}

// 工具方法
const getTagType = (value, tagMap) => {
  if (!tagMap) return 'default'
  const item = tagMap.find(item => item.value === value)
  return item ? item.type : 'default'
}

const getTagText = (value, tagMap) => {
  if (!tagMap) return value
  const item = tagMap.find(item => item.value === value)
  return item ? item.text : value
}

const formatDate = (date, format = 'relative') => {
  if (!date) return '-'
  const dateObj = new Date(date)
  if (format === 'relative') {
    const now = new Date()
    const diffInMs = now - dateObj
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))
    if (diffInDays === 0) return '今天'
    if (diffInDays === 1) return '昨天'
    if (diffInDays < 7) return `${diffInDays}天前`
    return dateObj.toLocaleDateString('zh-CN')
  }
  return dateObj.toLocaleString('zh-CN')
}

const formatCurrency = (amount) => {
  if (amount == null) return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getRowActions = (row, actions) => {
  if (!actions) return []
  return actions.filter(action => {
    if (typeof action.show === 'function') {
      return action.show(row)
    }
    return action.show !== false
  })
}

// 暴露方法
defineExpose({
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row, selected) => tableRef.value?.toggleRowSelection(row, selected),
  toggleAllSelection: () => tableRef.value?.toggleAllSelection(),
  setCurrentRow: (row) => tableRef.value?.setCurrentRow(row),
  clearSort: () => tableRef.value?.clearSort(),
  doLayout: () => tableRef.value?.doLayout()
})
</script>

<style scoped lang="scss">
.data-table-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.table-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
  background: rgba(255, 255, 255, 0.5);
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  
  @media (max-width: 768px) {
    justify-content: space-between;
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.selection-info {
  font-size: var(--font-size-sm);
  color: var(--primary-600);
  font-weight: var(--font-weight-medium);
}

.search-box {
  width: 250px;
  
  @media (max-width: 768px) {
    width: 100%;
  }
}

.filters {
  display: flex;
  gap: var(--spacing-sm);
  
  @media (max-width: 768px) {
    flex-wrap: wrap;
  }
}

.table-wrapper {
  :deep(.modern-table) {
    .el-table__header {
      background: var(--gray-50);
      
      th {
        background: transparent;
        border-bottom: 2px solid var(--gray-200);
        font-weight: var(--font-weight-semibold);
        color: var(--gray-700);
      }
    }
    
    .el-table__body {
      tr {
        transition: all var(--transition-fast);
        
        &:hover {
          background: var(--primary-50);
        }
      }
      
      td {
        border-bottom: 1px solid var(--gray-100);
      }
    }
  }
}

.table-actions {
  display: flex;
  gap: var(--spacing-xs);
  
  .el-button {
    margin: 0;
  }
}

.table-pagination {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
  background: rgba(255, 255, 255, 0.5);
  display: flex;
  justify-content: center;
  
  :deep(.el-pagination) {
    .el-pager li {
      background: transparent;
      border-radius: var(--radius-md);
      margin: 0 2px;
      
      &.active {
        background: var(--primary-500);
        color: white;
      }
      
      &:hover:not(.active) {
        background: var(--primary-100);
        color: var(--primary-600);
      }
    }
    
    .btn-prev,
    .btn-next {
      background: transparent;
      border-radius: var(--radius-md);
      
      &:hover {
        background: var(--primary-100);
        color: var(--primary-600);
      }
    }
  }
}

.column-settings {
  max-height: 400px;
  overflow-y: auto;
}

.column-item {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--gray-100);
  
  &:last-child {
    border-bottom: none;
  }
}
</style>
