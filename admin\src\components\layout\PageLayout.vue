<template>
  <div class="page-layout" :class="{ 'full-screen': fullScreen }">
    <!-- 页面头部 -->
    <div class="page-header" v-if="!hideHeader">
      <div class="page-header-content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-section" v-if="breadcrumb && breadcrumb.length">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item 
              v-for="(item, index) in breadcrumb" 
              :key="index"
              :to="item.path"
            >
              <i :class="item.icon" v-if="item.icon"></i>
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <!-- 页面标题区域 -->
        <div class="title-section">
          <div class="title-content">
            <div class="title-icon" v-if="icon">
              <i :class="icon" class="page-icon"></i>
            </div>
            <div class="title-text">
              <h1 class="page-title">{{ title }}</h1>
              <p class="page-subtitle" v-if="subtitle">{{ subtitle }}</p>
            </div>
          </div>
          
          <!-- 页面操作按钮 -->
          <div class="page-actions" v-if="$slots.actions">
            <slot name="actions"></slot>
          </div>
        </div>
        
        <!-- 页面统计信息 -->
        <div class="stats-section" v-if="stats && stats.length">
          <div class="stats-grid">
            <div 
              v-for="(stat, index) in stats" 
              :key="index"
              class="stat-card animate-fade-in-up"
              :style="{ animationDelay: `${index * 100}ms` }"
            >
              <div class="stat-icon" :class="stat.color">
                <i :class="stat.icon"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-change" v-if="stat.change" :class="stat.changeType">
                  <span class="change-indicator">{{ stat.changeType === 'increase' ? '↑' : '↓' }}</span>
                  {{ stat.change }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 页面内容 -->
    <div class="page-content" :class="contentClass">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <div class="spinner"></div>
          <p class="loading-text">{{ loadingText || '加载中...' }}</p>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-content">
          <div class="error-icon">
            ⚠️
          </div>
          <h3 class="error-title">{{ error.title || '出现错误' }}</h3>
          <p class="error-message">{{ error.message || '请稍后重试' }}</p>
          <div class="error-actions" v-if="error.actions">
            <el-button 
              v-for="action in error.actions" 
              :key="action.key"
              :type="action.type || 'primary'"
              @click="action.handler"
            >
              {{ action.text }}
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="empty" class="empty-container">
        <div class="empty-content">
          <div class="empty-icon">
            📦
          </div>
          <h3 class="empty-title">{{ empty.title || '暂无数据' }}</h3>
          <p class="empty-message">{{ empty.message || '暂时没有相关数据' }}</p>
          <div class="empty-actions" v-if="empty.actions">
            <el-button 
              v-for="action in empty.actions" 
              :key="action.key"
              :type="action.type || 'primary'"
              @click="action.handler"
            >
              {{ action.text }}
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 正常内容 -->
      <div v-else class="content-wrapper">
        <slot></slot>
      </div>
    </div>
    
    <!-- 页面底部 -->
    <div class="page-footer" v-if="$slots.footer && !hideFooter">
      <slot name="footer"></slot>
    </div>
    
    <!-- 浮动操作按钮 -->
    <div class="fab-container" v-if="$slots.fab">
      <slot name="fab"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props 定义
const props = defineProps({
  // 页面标题
  title: {
    type: String,
    required: true
  },
  // 页面副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 页面图标
  icon: {
    type: String,
    default: ''
  },
  // 面包屑导航
  breadcrumb: {
    type: Array,
    default: () => []
  },
  // 统计数据
  stats: {
    type: Array,
    default: () => []
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 加载文本
  loadingText: {
    type: String,
    default: '加载中...'
  },
  // 错误状态
  error: {
    type: Object,
    default: null
  },
  // 空状态
  empty: {
    type: Object,
    default: null
  },
  // 隐藏头部
  hideHeader: {
    type: Boolean,
    default: false
  },
  // 隐藏底部
  hideFooter: {
    type: Boolean,
    default: false
  },
  // 全屏模式
  fullScreen: {
    type: Boolean,
    default: false
  },
  // 内容区域样式类
  contentClass: {
    type: String,
    default: ''
  }
})

// 计算属性
const contentClass = computed(() => {
  return [
    props.contentClass,
    {
      'has-header': !props.hideHeader,
      'has-footer': !props.hideFooter,
      'is-loading': props.loading,
      'has-error': props.error,
      'is-empty': props.empty
    }
  ]
})
</script>

<style scoped lang="scss">
// CSS变量定义
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --gray-200: #e5e7eb;
  --gray-400: #9ca3af;
  --gray-600: #4b5563;
  --gray-900: #111827;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --success-600: #059669;
  --error-600: #dc2626;
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --z-modal: 1000;
  --z-sticky: 100;
  --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
}
.page-layout {
  min-height: calc(100vh - 88px); // 调整高度计算
  height: auto; // 允许内容自适应高度
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  margin: 24px;
  overflow: visible; // 允许内容溢出显示

  &.full-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-modal);
    margin: 0;
    border-radius: 0;
    overflow: auto; // 全屏模式允许滚动
  }
}

// 页面头部
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.page-header-content {
  padding: 24px 32px;
}

.breadcrumb-section {
  margin-bottom: 16px;

  :deep(.el-breadcrumb__item) {
    .el-breadcrumb__inner {
      color: #4b5563;
      font-weight: 500;

      &:hover {
        color: #2563eb;
      }
    }

    &:last-child .el-breadcrumb__inner {
      color: #111827;
      font-weight: 600;
    }
  }
}

.title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.title-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  width: 60px;
  height: 60px;
  border-radius: 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  .page-icon {
    font-size: 24px;
    color: white;
  }
}

.title-text {
  .page-title {
    font-size: 30px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 4px 0;
    line-height: 1.25;
  }

  .page-subtitle {
    font-size: 18px;
    color: #4b5563;
    margin: 0;
    line-height: 1.5;
  }
}

.page-actions {
  display: flex;
  align-items: center;
  gap: 12px;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-start;
  }
}

// 统计卡片
.stats-section {
  margin-top: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;

  &.primary { background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%); }
  &.success { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
  &.warning { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
  &.error { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
  &.info { background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  line-height: 1.25;
}

.stat-label {
  font-size: 14px;
  color: #4b5563;
  margin-top: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;

  &.increase {
    color: #059669;
  }

  &.decrease {
    color: #dc2626;
  }
}

// 页面内容
.page-content {
  flex: 1;
  padding: 24px 32px 32px 32px;
  overflow: visible; // 允许内容完全显示
  min-height: 0; // 确保flex子元素可以收缩

  &.has-header {
    padding-top: 24px;
  }

  &.has-footer {
    padding-bottom: 24px;
  }
}

.content-wrapper {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px auto;
}

.loading-text {
  color: #4b5563;
  font-size: 18px;
}

// 错误状态
.error-container,
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.error-content,
.empty-content {
  text-align: center;
  max-width: 400px;
}

.error-icon,
.empty-icon {
  font-size: 4rem;
  color: #9ca3af;
  margin-bottom: 16px;
}

.error-title,
.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
}

.error-message,
.empty-message {
  color: #4b5563;
  margin-bottom: 24px;
  line-height: 1.6;
}

.error-actions,
.empty-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

// 页面底部
.page-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px 16px;
  margin-top: auto;
}

// 浮动操作按钮
.fab-container {
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 1000;
}

// 响应式设计
@media (max-width: 768px) {
  .page-layout {
    margin: 16px;
    min-height: calc(100vh - 96px); // 移动端高度调整
  }

  .page-header-content {
    padding: 16px;
  }

  .page-content {
    padding: 16px;
    overflow: visible; // 移动端也允许内容完全显示
  }

  .title-text .page-title {
    font-size: 24px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .fab-container {
    bottom: 16px;
    right: 16px;
  }
}

// 动画关键帧
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
