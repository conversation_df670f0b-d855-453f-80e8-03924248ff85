import{_ as e}from"./index-D2bI4m-v.js";/* empty css               *//* empty css               *//* empty css                  *//* empty css                *//* empty css                 */import{bR as s,b4 as a,b2 as t,b3 as l,as as c,b8 as r,U as u,aW as i,aV as n,Q as p,bQ as o}from"./element-plus-DcSKpKA8.js";import{r as d,L as m,c as v,k as y,l as h,E as g,z as f,y as _,B as w,t as b,D as x}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const D={class:"function-test-page"},E={class:"test-content"},k={class:"test-buttons"},j={key:0,class:"test-result"},z={class:"test-content"},T={class:"test-buttons"},C={key:0,class:"test-result"},P={class:"test-content"},V={class:"test-buttons"},Q={key:0,class:"test-result"},U={class:"test-content"},q={class:"test-buttons"},B={key:0,class:"test-result"},F={class:"test-summary"},I={class:"summary-item"},L={class:"summary-number"},M={class:"summary-item success"},R={class:"summary-number"},W={class:"summary-item error"},A={class:"summary-number"},G={class:"summary-item"},H={class:"summary-number"},J=e({__name:"FunctionTest",setup(e){const J=d(""),K=m({userExport:!1,transactionExport:!1,distributorExport:!1,preview:!1,batch:!1,alert:!1}),N=m({export:null,preview:null,batch:null,alert:null}),O=d(!1),S=d([]),X=v(()=>S.value.length),Y=v(()=>S.value.filter(e=>e.success).length),Z=v(()=>S.value.filter(e=>!e.success).length),$=v(()=>X.value>0?Y.value/X.value*100:0),ee=async()=>{K.userExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),N.export={success:!0,message:"用户导出功能测试成功"},S.value.push({type:"export",success:!0,time:new Date}),p.success("用户导出功能测试成功")}catch(e){N.export={success:!1,message:"用户导出功能测试失败"},S.value.push({type:"export",success:!1,time:new Date}),p.error("用户导出功能测试失败")}finally{K.userExport=!1,O.value=!0}},se=async()=>{K.transactionExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),N.export={success:!0,message:"交易导出功能测试成功"},S.value.push({type:"export",success:!0,time:new Date}),p.success("交易导出功能测试成功")}catch(e){N.export={success:!1,message:"交易导出功能测试失败"},S.value.push({type:"export",success:!1,time:new Date}),p.error("交易导出功能测试失败")}finally{K.transactionExport=!1,O.value=!0}},ae=async()=>{K.distributorExport=!0;try{await new Promise(e=>setTimeout(e,1e3)),N.export={success:!0,message:"分销商导出功能测试成功"},S.value.push({type:"export",success:!0,time:new Date}),p.success("分销商导出功能测试成功")}catch(e){N.export={success:!1,message:"分销商导出功能测试失败"},S.value.push({type:"export",success:!1,time:new Date}),p.error("分销商导出功能测试失败")}finally{K.distributorExport=!1,O.value=!0}},te=async()=>{K.preview=!0;try{J.value;await new Promise(e=>setTimeout(e,800)),N.preview={success:!0,message:"数据预览功能测试成功"},S.value.push({type:"preview",success:!0,time:new Date}),o({title:"测试成功",message:"数据预览功能工作正常",type:"success"})}catch(e){N.preview={success:!1,message:"数据预览功能测试失败"},S.value.push({type:"preview",success:!1,time:new Date}),o({title:"测试失败",message:"数据预览功能异常",type:"error"})}finally{K.preview=!1,O.value=!0}},le=async()=>{K.batch=!0;try{await new Promise(e=>setTimeout(e,1200)),N.batch={success:!0,message:"批量处理功能测试成功"},S.value.push({type:"batch",success:!0,time:new Date}),p.success("批量处理功能测试成功")}catch(e){N.batch={success:!1,message:"批量处理功能测试失败"},S.value.push({type:"batch",success:!1,time:new Date}),p.error("批量处理功能测试失败")}finally{K.batch=!1,O.value=!0}},ce=async()=>{K.alert=!0;try{await new Promise(e=>setTimeout(e,1e3)),N.alert={success:!0,message:"告警设置功能测试成功"},S.value.push({type:"alert",success:!0,time:new Date}),p.success("告警设置功能测试成功")}catch(e){N.alert={success:!1,message:"告警设置功能测试失败"},S.value.push({type:"alert",success:!1,time:new Date}),p.error("告警设置功能测试失败")}finally{K.alert=!1,O.value=!0}};return(e,p)=>{const o=s,d=c,m=r,v=l,S=t,re=n,ue=i,ie=a;return h(),y("div",D,[g(v,null,{header:f(()=>p[1]||(p[1]=[b("div",{class:"card-header"},[b("span",null,"🧪 功能测试页面"),b("small",{style:{color:"#666","font-size":"12px","margin-left":"10px"}}," 验证所有已完善的后台功能 ")],-1)])),default:f(()=>[g(o,{title:"测试说明",type:"info",description:"此页面用于测试管理后台的各项功能是否正常工作。点击下方按钮即可测试对应功能。",style:{"margin-bottom":"20px"}}),g(ie,{gutter:20},{default:f(()=>[g(S,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[2]||(p[2]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-download"}),b("span",null,"导出功能测试")],-1)])),default:f(()=>[b("div",E,[p[6]||(p[6]=b("p",{class:"test-desc"},"测试用户列表、财务记录、分销商等数据的导出功能",-1)),b("div",k,[g(d,{type:"primary",size:"small",onClick:ee,loading:K.userExport},{default:f(()=>p[3]||(p[3]=[x(" 测试用户导出 ",-1)])),_:1,__:[3]},8,["loading"]),g(d,{type:"success",size:"small",onClick:se,loading:K.transactionExport},{default:f(()=>p[4]||(p[4]=[x(" 测试交易导出 ",-1)])),_:1,__:[4]},8,["loading"]),g(d,{type:"warning",size:"small",onClick:ae,loading:K.distributorExport},{default:f(()=>p[5]||(p[5]=[x(" 测试分销商导出 ",-1)])),_:1,__:[5]},8,["loading"])]),N.export?(h(),y("div",j,[g(m,{type:N.export.success?"success":"danger"},{default:f(()=>[x(u(N.export.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1}),g(S,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[7]||(p[7]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-view"}),b("span",null,"数据预览功能测试")],-1)])),default:f(()=>[b("div",z,[p[9]||(p[9]=b("p",{class:"test-desc"},"测试数据导出前的预览功能",-1)),b("div",T,[g(ue,{modelValue:J.value,"onUpdate:modelValue":p[0]||(p[0]=e=>J.value=e),placeholder:"选择数据类型",size:"small"},{default:f(()=>[g(re,{label:"用户数据",value:"users"}),g(re,{label:"订单数据",value:"orders"}),g(re,{label:"财务数据",value:"finance"})]),_:1},8,["modelValue"]),g(d,{type:"primary",size:"small",onClick:te,loading:K.preview,disabled:!J.value},{default:f(()=>p[8]||(p[8]=[x(" 测试预览功能 ",-1)])),_:1,__:[8]},8,["loading","disabled"])]),N.preview?(h(),y("div",C,[g(m,{type:N.preview.success?"success":"danger"},{default:f(()=>[x(u(N.preview.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1}),g(S,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[10]||(p[10]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-s-operation"}),b("span",null,"批量处理功能测试")],-1)])),default:f(()=>[b("div",P,[p[12]||(p[12]=b("p",{class:"test-desc"},"测试订单等数据的批量处理功能",-1)),b("div",V,[g(d,{type:"primary",size:"small",onClick:le,loading:K.batch},{default:f(()=>p[11]||(p[11]=[x(" 测试批量处理 ",-1)])),_:1,__:[11]},8,["loading"])]),N.batch?(h(),y("div",Q,[g(m,{type:N.batch.success?"success":"danger"},{default:f(()=>[x(u(N.batch.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1}),g(S,{span:12},{default:f(()=>[g(v,{class:"test-card"},{header:f(()=>p[13]||(p[13]=[b("div",{class:"test-header"},[b("i",{class:"el-icon-warning"}),b("span",null,"告警设置功能测试")],-1)])),default:f(()=>[b("div",U,[p[15]||(p[15]=b("p",{class:"test-desc"},"测试防红系统的告警设置功能",-1)),b("div",q,[g(d,{type:"primary",size:"small",onClick:ce,loading:K.alert},{default:f(()=>p[14]||(p[14]=[x(" 测试告警设置 ",-1)])),_:1,__:[14]},8,["loading"])]),N.alert?(h(),y("div",B,[g(m,{type:N.alert.success?"success":"danger"},{default:f(()=>[x(u(N.alert.message),1)]),_:1},8,["type"])])):w("",!0)])]),_:1})]),_:1})]),_:1}),O.value?(h(),_(v,{key:0,style:{"margin-top":"20px"}},{header:f(()=>p[16]||(p[16]=[b("div",{class:"card-header"},[b("span",null,"📊 测试结果汇总")],-1)])),default:f(()=>[b("div",F,[g(ie,{gutter:20},{default:f(()=>[g(S,{span:6},{default:f(()=>[b("div",I,[b("div",L,u(X.value),1),p[17]||(p[17]=b("div",{class:"summary-label"},"总测试数",-1))])]),_:1}),g(S,{span:6},{default:f(()=>[b("div",M,[b("div",R,u(Y.value),1),p[18]||(p[18]=b("div",{class:"summary-label"},"成功测试",-1))])]),_:1}),g(S,{span:6},{default:f(()=>[b("div",W,[b("div",A,u(Z.value),1),p[19]||(p[19]=b("div",{class:"summary-label"},"失败测试",-1))])]),_:1}),g(S,{span:6},{default:f(()=>[b("div",G,[b("div",H,u(Math.round($.value))+"%",1),p[20]||(p[20]=b("div",{class:"summary-label"},"成功率",-1))])]),_:1})]),_:1})])]),_:1})):w("",!0)]),_:1})])}}},[["__scopeId","data-v-cd1e52ad"]]);export{J as default};
