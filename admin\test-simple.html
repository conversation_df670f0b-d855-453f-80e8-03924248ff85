<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-title {
            font-size: 2rem;
            margin-bottom: 20px;
        }
        .test-info {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 8px;
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔧 系统测试页面</h1>
        <div class="test-info">
            <p><strong>当前时间:</strong> <span id="current-time"></span></p>
            <p><strong>屏幕分辨率:</strong> <span id="screen-resolution"></span></p>
            <p><strong>浏览器:</strong> <span id="browser-info"></span></p>
        </div>
        
        <button class="test-button" onclick="testLogin()">测试登录页面</button>
        
        <div class="status" id="status" style="display: none;">
            <p id="status-text"></p>
        </div>
    </div>

    <script>
        // 更新页面信息
        function updateInfo() {
            document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
            document.getElementById('screen-resolution').textContent = `${window.screen.width}×${window.screen.height}`;
            document.getElementById('browser-info').textContent = navigator.userAgent.split(' ')[0];
        }
        
        // 测试登录页面
        function testLogin() {
            const status = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            
            status.style.display = 'block';
            statusText.innerHTML = '正在跳转到登录页面...';
            
            setTimeout(() => {
                window.location.href = '/admin/';
            }, 1000);
        }
        
        // 初始化
        updateInfo();
        setInterval(updateInfo, 1000);
    </script>
</body>
</html>
