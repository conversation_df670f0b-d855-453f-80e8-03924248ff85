<template>
  <div class="rich-text-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar" v-show="true">
      <!-- 格式化按钮 -->
      <el-button-group>
        <el-button size="small" type="default" @click="execCommand('bold')" title="粗体">
          <strong>B</strong>
        </el-button>
        <el-button size="small" type="default" @click="execCommand('italic')" title="斜体">
          <em>I</em>
        </el-button>
        <el-button size="small" type="default" @click="execCommand('underline')" title="下划线">
          <u>U</u>
        </el-button>
      </el-button-group>

      <div class="toolbar-divider"></div>

      <!-- 对齐按钮 -->
      <el-button-group>
        <el-button size="small" type="default" @click="execCommand('justifyLeft')" title="左对齐">
          ←
        </el-button>
        <el-button size="small" type="default" @click="execCommand('justifyCenter')" title="居中">
          ↔
        </el-button>
        <el-button size="small" type="default" @click="execCommand('justifyRight')" title="右对齐">
          →
        </el-button>
      </el-button-group>

      <div class="toolbar-divider"></div>

      <!-- 列表按钮 -->
      <el-button-group>
        <el-button size="small" type="default" @click="execCommand('insertUnorderedList')" title="无序列表">
          •
        </el-button>
        <el-button size="small" type="default" @click="execCommand('insertOrderedList')" title="有序列表">
          1.
        </el-button>
      </el-button-group>

      <div class="toolbar-divider"></div>

      <!-- 插入按钮 -->
      <el-button-group>
        <el-button size="small" type="default" @click="insertLink" title="插入链接">
          🔗
        </el-button>
        <el-button size="small" type="default" @click="insertImage" title="插入图片">
          🖼️
        </el-button>
        <el-button size="small" type="default" @click="insertVideo" title="插入视频">
          🎥
        </el-button>
      </el-button-group>

      <div class="toolbar-divider"></div>

      <!-- 其他按钮 -->
      <el-button size="small" type="default" @click="execCommand('removeFormat')" title="清除格式">
        清除
      </el-button>
    </div>
    
    <div
      ref="editorRef"
      class="editor-content"
      :style="{ height: height + 'px' }"
      contenteditable="true"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @paste="handlePaste"
      v-html="content"
    ></div>
    
    <div class="editor-footer">
      <span class="word-count">{{ wordCount }} 字</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 200
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  maxLength: {
    type: Number,
    default: 10000
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur'])

const editorRef = ref(null)
const content = ref('')
const isFocused = ref(false)

// 字数统计
const wordCount = computed(() => {
  const text = editorRef.value?.innerText || ''
  return text.length
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== content.value) {
    content.value = newVal
    if (editorRef.value) {
      editorRef.value.innerHTML = newVal
    }
  }
}, { immediate: true })

// 执行编辑命令
const execCommand = (command, value = null) => {
  document.execCommand(command, false, value)
  editorRef.value?.focus()
  handleInput()
}

// 检查命令是否激活
const isActive = (command) => {
  return document.queryCommandState(command)
}

// 插入链接
const insertLink = async () => {
  try {
    const { value: url } = await ElMessageBox.prompt('请输入链接地址', '插入链接', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^https?:\/\/.+/,
      inputErrorMessage: '请输入有效的链接地址'
    })
    
    if (url) {
      execCommand('createLink', url)
    }
  } catch (error) {
    // 用户取消
  }
}

// 插入图片
const insertImage = async () => {
  try {
    const { value: url } = await ElMessageBox.prompt('请输入图片地址', '插入图片', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i,
      inputErrorMessage: '请输入有效的图片地址'
    })
    
    if (url) {
      execCommand('insertImage', url)
    }
  } catch (error) {
    // 用户取消
  }
}

// 插入视频
const insertVideo = async () => {
  try {
    const { value: url } = await ElMessageBox.prompt('请输入视频地址', '插入视频', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^https?:\/\/.+/,
      inputErrorMessage: '请输入有效的视频地址',
      inputPlaceholder: '支持 MP4、优酷、腾讯视频、B站等链接'
    })
    
    if (url) {
      insertVideoElement(url)
    }
  } catch (error) {
    // 用户取消
  }
}

// 插入视频元素
const insertVideoElement = (url) => {
  let videoHtml = ''
  
  // 检测视频类型并生成相应的HTML
  if (isDirectVideoUrl(url)) {
    // 直接视频文件
    videoHtml = `<video controls style="max-width: 100%; height: auto; margin: 8px 0;">
      <source src="${url}" type="video/mp4">
      您的浏览器不支持视频播放。
    </video>`
  } else if (isBilibiliUrl(url)) {
    // B站视频
    const bvid = extractBilibiliId(url)
    if (bvid) {
      videoHtml = `<iframe src="//player.bilibili.com/player.html?bvid=${bvid}" 
        scrolling="no" border="0" frameborder="no" framespacing="0" 
        allowfullscreen="true" style="width: 100%; height: 400px; margin: 8px 0;">
      </iframe>`
    }
  } else if (isYouTubeUrl(url)) {
    // YouTube视频
    const videoId = extractYouTubeId(url)
    if (videoId) {
      videoHtml = `<iframe src="https://www.youtube.com/embed/${videoId}" 
        frameborder="0" allowfullscreen style="width: 100%; height: 400px; margin: 8px 0;">
      </iframe>`
    }
  } else {
    // 其他视频平台或嵌入代码
    videoHtml = `<div class="video-container" style="margin: 8px 0;">
      <p>视频链接：<a href="${url}" target="_blank">${url}</a></p>
      <p style="color: #909399; font-size: 12px;">点击链接观看视频</p>
    </div>`
  }
  
  if (videoHtml) {
    // 插入HTML内容
    document.execCommand('insertHTML', false, videoHtml)
    handleInput()
  }
}

// 检测是否为直接视频文件URL
const isDirectVideoUrl = (url) => {
  return /\.(mp4|webm|ogg|avi|mov|wmv|flv)$/i.test(url)
}

// 检测是否为B站URL
const isBilibiliUrl = (url) => {
  return /bilibili\.com\/video\/(BV\w+|av\d+)/i.test(url)
}

// 提取B站视频ID
const extractBilibiliId = (url) => {
  const match = url.match(/bilibili\.com\/video\/(BV\w+)/i)
  return match ? match[1] : null
}

// 检测是否为YouTube URL
const isYouTubeUrl = (url) => {
  return /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/i.test(url)
}

// 提取YouTube视频ID
const extractYouTubeId = (url) => {
  const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/i)
  return match ? match[1] : null
}

// 处理输入
const handleInput = () => {
  const html = editorRef.value?.innerHTML || ''
  content.value = html
  emit('update:modelValue', html)
  
  // 检查字数限制
  if (wordCount.value > props.maxLength) {
    ElMessage.warning(`内容长度不能超过 ${props.maxLength} 字`)
  }
}

// 处理焦点
const handleFocus = () => {
  isFocused.value = true
  emit('focus')
}

// 处理失焦
const handleBlur = () => {
  isFocused.value = false
  emit('blur')
}

// 处理粘贴
const handlePaste = (e) => {
  e.preventDefault()
  
  // 获取粘贴的文本
  const text = e.clipboardData.getData('text/plain')
  
  // 清理HTML标签，只保留纯文本
  const cleanText = text.replace(/<[^>]*>/g, '')
  
  // 插入文本
  document.execCommand('insertText', false, cleanText)
  handleInput()
}

// 设置占位符
const setPlaceholder = () => {
  if (editorRef.value) {
    if (!content.value || content.value.trim() === '') {
      editorRef.value.innerHTML = `<p style="color: #c0c4cc; pointer-events: none;">${props.placeholder}</p>`
    }
  }
}

// 清除占位符
const clearPlaceholder = () => {
  if (editorRef.value && editorRef.value.innerHTML.includes(props.placeholder)) {
    editorRef.value.innerHTML = ''
  }
}

onMounted(() => {
  if (editorRef.value) {
    // 设置初始内容
    editorRef.value.innerHTML = content.value || ''
    
    // 如果没有内容，显示占位符
    if (!content.value) {
      setPlaceholder()
    }
    
    // 监听焦点事件来处理占位符
    editorRef.value.addEventListener('focus', clearPlaceholder)
    editorRef.value.addEventListener('blur', () => {
      if (!editorRef.value.innerText.trim()) {
        setPlaceholder()
      }
    })
  }
})

// 暴露方法给父组件
defineExpose({
  focus: () => editorRef.value?.focus(),
  blur: () => editorRef.value?.blur(),
  clear: () => {
    if (editorRef.value) {
      editorRef.value.innerHTML = ''
      handleInput()
    }
  },
  insertText: (text) => {
    execCommand('insertText', text)
  },
  getContent: () => content.value,
  getTextContent: () => editorRef.value?.innerText || ''
})
</script>

<style lang="scss" scoped>
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  transition: border-color 0.3s;
  
  &:hover {
    border-color: #c0c4cc;
  }
  
  &:focus-within {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

.editor-toolbar {
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  
  .el-button-group {
    .el-button {
      padding: 4px 8px;
      border: 1px solid #dcdfe6;
      background: #fff;
      color: #606266;
      font-size: 12px;
      
      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background: #ecf5ff;
      }
      
      &.active {
        color: #409eff;
        border-color: #409eff;
        background: #ecf5ff;
      }
    }
  }
}

.editor-content {
  padding: 12px;
  min-height: 100px;
  overflow-y: auto;
  outline: none;
  line-height: 1.6;
  font-size: 14px;
  color: #606266;
  
  // 编辑器内容样式
  :deep(p) {
    margin: 0 0 8px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  :deep(ul), :deep(ol) {
    margin: 8px 0;
    padding-left: 20px;
  }
  
  :deep(li) {
    margin: 4px 0;
  }
  
  :deep(a) {
    color: #409eff;
    text-decoration: underline;
    
    &:hover {
      color: #66b1ff;
    }
  }
  
  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }
  
  // 视频样式
  :deep(video) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
    background: #000;
  }
  
  :deep(iframe) {
    max-width: 100%;
    border-radius: 4px;
    margin: 8px 0;
    border: 1px solid #e4e7ed;
  }
  
  :deep(.video-container) {
    padding: 12px;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin: 8px 0;
    
    p {
      margin: 4px 0;
      
      &:first-child {
        font-weight: 500;
        color: #303133;
      }
    }
    
    a {
      word-break: break-all;
    }
  }
  
  :deep(strong) {
    font-weight: 600;
  }
  
  :deep(em) {
    font-style: italic;
  }
  
  :deep(u) {
    text-decoration: underline;
  }
  
  // 占位符样式
  :deep(p[style*="color: #c0c4cc"]) {
    margin: 0;
    user-select: none;
  }
}

.editor-footer {
  padding: 8px 12px;
  border-top: 1px solid #e4e7ed;
  background: #f5f7fa;
  display: flex;
  justify-content: flex-end;
  
  .word-count {
    font-size: 12px;
    color: #909399;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .editor-toolbar {
    padding: 6px 8px;
    
    .el-button-group .el-button {
      padding: 3px 6px;
      font-size: 11px;
    }
  }
  
  .editor-content {
    padding: 8px;
    font-size: 13px;
  }
  
  .editor-footer {
    padding: 6px 8px;
  }
}

// 自定义滚动条
.editor-content::-webkit-scrollbar {
  width: 6px;
}

.editor-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.editor-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}
</style>