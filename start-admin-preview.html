<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨鑫流量变现系统 - 管理后台预览启动器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .launcher-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 50px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 2.5rem;
            color: #1f2937;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #6b7280;
            margin-bottom: 30px;
        }
        
        .description {
            color: #4b5563;
            margin-bottom: 40px;
            line-height: 1.6;
            font-size: 1.1rem;
        }
        
        .features {
            background: #f9fafb;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 40px;
            text-align: left;
        }
        
        .features h3 {
            color: #1f2937;
            margin-bottom: 20px;
            font-size: 1.3rem;
            text-align: center;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            color: #4b5563;
        }
        
        .feature-icon {
            font-size: 1.2rem;
            margin-right: 10px;
        }
        
        .launch-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .launch-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 12px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .launch-btn.secondary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .launch-btn.secondary:hover {
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }
        
        .info-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            color: #92400e;
        }
        
        .info-box h4 {
            margin-bottom: 10px;
            color: #92400e;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            background: #dcfce7;
            color: #166534;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #22c55e;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 768px) {
            .launcher-container {
                padding: 30px 20px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .feature-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <div class="logo">🚀</div>
        <h1 class="title">晨鑫流量变现系统</h1>
        <p class="subtitle">智能社群营销与多级分销平台</p>
        
        <div class="status-indicator">
            <div class="status-dot"></div>
            系统就绪 - 预览模式可用
        </div>
        
        <p class="description">
            欢迎体验我们的管理后台系统！预览模式让您无需登录即可查看所有功能模块，
            包括数据看板、社群管理、分销系统、财务管理等完整功能。
        </p>
        
        <div class="features">
            <h3>🎯 核心功能预览</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <span>实时数据看板</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">👥</span>
                    <span>社群管理系统</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔗</span>
                    <span>多级分销管理</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">💰</span>
                    <span>财务管理中心</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🛡️</span>
                    <span>防红链接系统</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⚙️</span>
                    <span>系统配置管理</span>
                </div>
            </div>
        </div>
        
        <div class="launch-buttons">
            <a href="http://localhost:3001/?preview=true" class="launch-btn" onclick="startPreview()" target="_blank">
                🎭 启动预览模式 (开发版)
            </a>
            <a href="http://localhost:4173/admin/?preview=true" class="launch-btn secondary" target="_blank">
                🏗️ 启动预览模式 (构建版)
            </a>
            <a href="admin/preview.html" class="launch-btn secondary">
                📋 查看功能说明
            </a>
        </div>
        
        <div class="info-box">
            <h4>💡 预览模式说明</h4>
            <p>
                预览模式使用模拟数据展示系统功能，所有操作都是安全的，不会影响真实数据。
                您可以自由探索各个功能模块，体验完整的管理后台操作流程。
            </p>
        </div>
    </div>

    <script>
        function startPreview() {
            // 设置预览模式标识
            localStorage.setItem('preview-mode', 'true');

            // 显示启动提示
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '🚀 正在启动预览模式...';
            btn.style.pointerEvents = 'none';

            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.pointerEvents = 'auto';
            }, 2000);

            // 不阻止默认行为，让链接正常跳转
            return true;
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            // 这里可以添加系统状态检查逻辑
            console.log('系统状态检查完成');
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            
            // 添加一些动画效果
            const container = document.querySelector('.launcher-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
