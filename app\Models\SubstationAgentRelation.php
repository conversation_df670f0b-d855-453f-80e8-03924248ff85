<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubstationAgentRelation extends Model
{
    use HasFactory;

    protected $fillable = [
        'substation_id',
        'agent_id',
        'relation_type',
        'commission_rate',
        'permissions',
        'status',
        'started_at',
        'ended_at'
    ];

    protected $casts = [
        'permissions' => 'array',
        'commission_rate' => 'decimal:2',
        'started_at' => 'datetime',
        'ended_at' => 'datetime'
    ];

    /**
     * 关联分站
     */
    public function substation()
    {
        return $this->belongsTo(Substation::class);
    }

    /**
     * 关联代理商
     */
    public function agent()
    {
        return $this->belongsTo(AgentAccount::class);
    }

    /**
     * 检查关系是否有效
     */
    public function isActive()
    {
        if ($this->status !== 'active') {
            return false;
        }

        if ($this->ended_at && $this->ended_at->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * 获取关系类型标签
     */
    public function getRelationTypeLabelAttribute()
    {
        $labels = [
            'primary' => '<span class="badge badge-primary">主要合作</span>',
            'secondary' => '<span class="badge badge-info">次要合作</span>',
            'cooperative' => '<span class="badge badge-success">协作关系</span>'
        ];

        return $labels[$this->relation_type] ?? '<span class="badge badge-secondary">未知</span>';
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        $labels = [
            'active' => '<span class="badge badge-success">活跃</span>',
            'inactive' => '<span class="badge badge-secondary">未激活</span>',
            'suspended' => '<span class="badge badge-warning">暂停</span>'
        ];

        return $labels[$this->status] ?? '<span class="badge badge-secondary">未知</span>';
    }

    /**
     * 作用域：活跃关系
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('ended_at')
                          ->orWhere('ended_at', '>', now());
                    });
    }

    /**
     * 作用域：按分站筛选
     */
    public function scopeBySubstation($query, $substationId)
    {
        return $query->where('substation_id', $substationId);
    }

    /**
     * 作用域：按代理商筛选
     */
    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }
}