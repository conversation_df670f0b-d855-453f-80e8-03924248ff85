import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                    *//* empty css                  *//* empty css                    *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                        *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                     */import{T as l,a0 as a,as as t,aM as o,bc as r,bd as u,aH as i,aW as s,aV as d,b3 as _,b6 as n,b7 as c,U as p,b8 as m,bj as v,bk as f,b9 as b,b5 as y,b4 as g,b2 as w,be as V,bo as h,bp as k,bf as q,bn as U,aU as x,au as j,Q as C,R as z}from"./element-plus-DcSKpKA8.js";import{m as T}from"./marketing-D5ylsfMy.js";import{r as $,L as Z,e as P,k as I,l as Q,t as L,E as M,z as S,D as B,u as D,A as E,y as H,B as O,F as W,Y as A}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const F={class:"group-marketing-management"},G={class:"page-header"},J={class:"header-content"},R={class:"header-actions"},Y={class:"filter-section"},K={class:"table-section"},N={class:"group-title"},X={class:"price"},ee={class:"virtual-stats"},le={class:"pagination-wrapper"},ae={key:0},te=["src"],oe=["src"],re=["src"],ue={class:"dialog-footer"},ie={key:0,class:"preview-content"},se={class:"preview-header"},de={class:"preview-stats"},_e={key:0,class:"preview-members"},ne={class:"member-list"},ce=["src","alt"],pe=e({__name:"GroupMarketing",setup(e){const pe=$(!1),me=$(!1),ve=$([]),fe=$([]),be=$(!1),ye=$(!1),ge=$(!1),we=$("basic"),Ve=$("北京"),he=$(""),ke=$(13),qe=$(null),Ue=$([]),xe=Z({title:"",auto_city_replace:"",display_type:""}),je=Z({page:1,size:20,total:0}),Ce=Z({read_count_display:"",like_count:0,want_see_count:0,button_title:"",avatar_library:"qq",wx_accessible:1,display_type:1,group_intro_title:"",group_intro_content:"",faq_title:"",faq_content:"",member_reviews:"",auto_city_replace:0,city_insert_strategy:"auto",virtual_members:0,virtual_orders:0,virtual_income:0,today_views:0,show_customer_service:1,customer_service_avatar:"",customer_service_title:"",customer_service_desc:"",customer_service_qr:"",ad_qr_code:""}),ze=Z({template_id:"",scope:"selected"}),Te=$("/api/upload/image"),$e=$(null),Ze=async()=>{pe.value=!0;try{await new Promise(e=>setTimeout(e,500));let e=[...[{id:1,title:"北京商务精英交流群",price:99,avatar_library:"qq",display_type:1,virtual_members:328,virtual_orders:89,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-15T10:00:00Z"},{id:2,title:"上海副业赚钱交流群",price:58,avatar_library:"default",display_type:1,virtual_members:267,virtual_orders:156,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-10T14:30:00Z"},{id:3,title:"深圳学习成长群",price:29,avatar_library:"qq",display_type:2,virtual_members:145,virtual_orders:67,wx_accessible:0,auto_city_replace:0,created_at:"2024-01-20T09:15:00Z"},{id:4,title:"广州健身运动群",price:39,avatar_library:"default",display_type:1,virtual_members:189,virtual_orders:78,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-25T16:45:00Z"},{id:5,title:"杭州创业交流群",price:88,avatar_library:"qq",display_type:1,virtual_members:234,virtual_orders:123,wx_accessible:1,auto_city_replace:1,created_at:"2024-01-12T11:20:00Z"}]];xe.title&&(e=e.filter(e=>e.title.toLowerCase().includes(xe.title.toLowerCase()))),""!==xe.auto_city_replace&&(e=e.filter(e=>e.auto_city_replace==xe.auto_city_replace)),""!==xe.display_type&&(e=e.filter(e=>e.display_type==xe.display_type)),ve.value=e,je.total=e.length,console.log("✅ 群组数据加载成功:",ve.value)}catch(e){console.error("获取群组列表失败:",e),C.error("获取群组列表失败")}finally{pe.value=!1}},Pe=()=>{je.page=1,Ze()},Ie=()=>{Object.keys(xe).forEach(e=>{xe[e]=""}),Pe()},Qe=e=>{fe.value=e},Le=async()=>{if($e.value&&Ve.value)try{const e=await T.testCityLocation($e.value,Ve.value);he.value=e.data.replaced_title}catch(e){C.error("测试失败")}},Me=async()=>{if($e.value)try{const e=await T.generateVirtualMembers($e.value,ke.value);C.success(`成功生成 ${e.data.length} 个虚拟成员`)}catch(e){C.error("生成虚拟成员失败")}},Se=async()=>{if($e.value){me.value=!0;try{await new Promise(e=>setTimeout(e,800)),console.log("✅ 营销配置保存成功:",{groupId:$e.value,config:Ce}),C.success("营销配置保存成功"),be.value=!1,Ze()}catch(e){console.error("保存配置失败:",e),C.error("保存配置失败")}finally{me.value=!1}}},Be=async()=>{if(ze.template_id)if("selected"!==ze.scope||0!==fe.value.length)try{const e="selected"===ze.scope?fe.value.map(e=>e.id):ve.value.map(e=>e.id);await new Promise(e=>setTimeout(e,1e3)),console.log("✅ 批量配置应用成功:",{templateId:ze.template_id,groupIds:e,scope:ze.scope,affectedCount:e.length}),C.success(`批量配置应用成功，共影响 ${e.length} 个群组`),ge.value=!1,Ze()}catch(e){console.error("批量配置失败:",e),C.error("批量配置失败")}else C.warning("请选择要配置的群组");else C.warning("请选择营销模板")},De=e=>{je.size=e,Ze()},Ee=e=>{je.page=e,Ze()},He=()=>{Ze()},Oe=e=>{Ce.customer_service_avatar=e.data.url},We=e=>{Ce.customer_service_qr=e.data.url},Ae=e=>{Ce.ad_qr_code=e.data.url},Fe=e=>{const l=e.type.startsWith("image/"),a=e.size/1024/1024<2;return l?!!a||(C.error("图片大小不能超过 2MB!"),!1):(C.error("只能上传图片文件!"),!1)},Ge=Fe;return P(()=>{Ze(),(async()=>{try{await new Promise(e=>setTimeout(e,300));const e=[{id:1,name:"商务交流模板",description:"适用于商务人士交流的营销模板",config:{read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入商务群",group_intro_title:"商务交流群简介",group_intro_content:"专业的商务交流平台，汇聚各行业精英",virtual_members:150,virtual_orders:80},created_at:"2024-01-01T00:00:00Z"},{id:2,name:"社交娱乐模板",description:"适用于社交娱乐的营销模板",config:{read_count_display:"3万+",like_count:800,want_see_count:600,button_title:"加入娱乐群",group_intro_title:"娱乐交流群",group_intro_content:"轻松愉快的社交环境，分享生活乐趣",virtual_members:120,virtual_orders:60},created_at:"2024-01-02T00:00:00Z"},{id:3,name:"学习教育模板",description:"适用于学习教育的营销模板",config:{read_count_display:"8万+",like_count:1500,want_see_count:1e3,button_title:"加入学习群",group_intro_title:"学习交流群",group_intro_content:"专业的学习交流平台，共同进步成长",virtual_members:180,virtual_orders:90},created_at:"2024-01-03T00:00:00Z"},{id:4,name:"副业赚钱模板",description:"适用于副业创业的营销模板",config:{read_count_display:"12万+",like_count:3659,want_see_count:665,button_title:"加入群，学习更多副业知识",group_intro_title:"副业赚钱交流群",group_intro_content:"分享副业项目，交流赚钱经验，实现财务自由",virtual_members:267,virtual_orders:156},created_at:"2024-01-04T00:00:00Z"},{id:5,name:"健身运动模板",description:"适用于健身运动的营销模板",config:{read_count_display:"6万+",like_count:2234,want_see_count:445,button_title:"加入群，一起健身运动",group_intro_title:"健身运动交流群",group_intro_content:"分享健身经验，制定运动计划，一起变得更健康",virtual_members:189,virtual_orders:78},created_at:"2024-01-05T00:00:00Z"}];Ue.value=e,console.log("✅ 营销模板加载成功:",Ue.value)}catch(e){console.error("获取营销模板失败:",e),C.error("获取营销模板失败")}})()}),(e,T)=>{const $=l,Z=t,P=i,fe=u,Ze=d,Je=s,Re=r,Ye=_,Ke=c,Ne=m,Xe=n,el=f,ll=w,al=V,tl=g,ol=k,rl=h,ul=y,il=q,sl=U,dl=b,_l=j,nl=v;return Q(),I("div",F,[L("div",G,[L("div",J,[T[39]||(T[39]=L("div",{class:"header-left"},[L("h1",{class:"page-title"},"群组营销配置"),L("p",{class:"page-subtitle"},"管理群组的营销展示和推广功能")],-1)),L("div",R,[M(Z,{type:"primary",onClick:T[0]||(T[0]=e=>ge.value=!0)},{default:S(()=>[M($,null,{default:S(()=>[M(D(a))]),_:1}),T[37]||(T[37]=B(" 批量配置 ",-1))]),_:1,__:[37]}),M(Z,{onClick:He},{default:S(()=>[M($,null,{default:S(()=>[M(D(o))]),_:1}),T[38]||(T[38]=B(" 刷新 ",-1))]),_:1,__:[38]})])])]),L("div",Y,[M(Ye,null,{default:S(()=>[M(Re,{model:xe,inline:""},{default:S(()=>[M(fe,{label:"群组名称"},{default:S(()=>[M(P,{modelValue:xe.title,"onUpdate:modelValue":T[1]||(T[1]=e=>xe.title=e),placeholder:"请输入群组名称",clearable:""},null,8,["modelValue"])]),_:1}),M(fe,{label:"城市定位"},{default:S(()=>[M(Je,{modelValue:xe.auto_city_replace,"onUpdate:modelValue":T[2]||(T[2]=e=>xe.auto_city_replace=e),placeholder:"请选择",clearable:""},{default:S(()=>[M(Ze,{label:"已启用",value:1}),M(Ze,{label:"未启用",value:0})]),_:1},8,["modelValue"])]),_:1}),M(fe,{label:"展示类型"},{default:S(()=>[M(Je,{modelValue:xe.display_type,"onUpdate:modelValue":T[3]||(T[3]=e=>xe.display_type=e),placeholder:"请选择",clearable:""},{default:S(()=>[M(Ze,{label:"文字+图片",value:1}),M(Ze,{label:"纯图片",value:2})]),_:1},8,["modelValue"])]),_:1}),M(fe,null,{default:S(()=>[M(Z,{type:"primary",onClick:Pe},{default:S(()=>T[40]||(T[40]=[B("搜索",-1)])),_:1,__:[40]}),M(Z,{onClick:Ie},{default:S(()=>T[41]||(T[41]=[B("重置",-1)])),_:1,__:[41]})]),_:1})]),_:1},8,["model"])]),_:1})]),L("div",K,[M(Ye,null,{default:S(()=>[E((Q(),H(Xe,{data:ve.value,onSelectionChange:Qe},{default:S(()=>[M(Ke,{type:"selection",width:"55"}),M(Ke,{prop:"id",label:"ID",width:"80"}),M(Ke,{prop:"title",label:"群组名称","min-width":"200"},{default:S(({row:e})=>[L("div",N,[L("span",null,p(e.title),1),e.auto_city_replace?(Q(),H(Ne,{key:0,size:"small",type:"success"},{default:S(()=>T[42]||(T[42]=[B("智能定位",-1)])),_:1,__:[42]})):O("",!0)])]),_:1}),M(Ke,{prop:"price",label:"价格",width:"100"},{default:S(({row:e})=>[L("span",X,"¥"+p(e.price),1)]),_:1}),M(Ke,{prop:"avatar_library",label:"头像库",width:"100"},{default:S(({row:e})=>[M(Ne,{type:"qq"===e.avatar_library?"primary":"warning",size:"small"},{default:S(()=>[B(p("qq"===e.avatar_library?"QQ风格":"综合随机"),1)]),_:2},1032,["type"])]),_:1}),M(Ke,{prop:"display_type",label:"展示类型",width:"120"},{default:S(({row:e})=>[M(Ne,{type:1===e.display_type?"success":"info",size:"small"},{default:S(()=>[B(p(1===e.display_type?"文字+图片":"纯图片"),1)]),_:2},1032,["type"])]),_:1}),M(Ke,{prop:"virtual_stats",label:"虚拟数据",width:"150"},{default:S(({row:e})=>[L("div",ee,[L("div",null,"成员: "+p(e.virtual_members||0),1),L("div",null,"订单: "+p(e.virtual_orders||0),1)])]),_:1}),M(Ke,{prop:"wx_accessible",label:"微信访问",width:"100"},{default:S(({row:e})=>[M(Ne,{type:1===e.wx_accessible?"success":"danger",size:"small"},{default:S(()=>[B(p(1===e.wx_accessible?"允许":"限制"),1)]),_:2},1032,["type"])]),_:1}),M(Ke,{label:"操作",width:"200",fixed:"right"},{default:S(({row:e})=>[M(Z,{size:"small",onClick:l=>(async e=>{$e.value=e.id;try{await new Promise(e=>setTimeout(e,300));const l={basic:{template_id:1,read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入群聊",group_intro_title:`${e.title}简介`,group_intro_content:`欢迎加入${e.title}，这里有最新的资讯和优质的服务！`,virtual_members:e.virtual_members||150,virtual_orders:e.virtual_orders||80},content:{marketing_content:`${e.title}是一个专业的交流平台，汇聚了众多行业精英。在这里，您可以：\n\n1. 获取最新的行业资讯\n2. 与同行专家深度交流\n3. 发现更多商业机会\n4. 提升个人专业技能\n\n加入我们，开启您的成功之路！`,highlights:["专业交流","行业资讯","商业机会","技能提升"],features:["实时互动","专家指导","资源共享","定期活动"]},service:{contact_info:"wechat123456",service_qr_code:"https://picsum.photos/200/200?random="+e.id,customer_service_hours:"9:00-18:00",response_time:"5分钟内回复"},advanced:{auto_reply_enabled:!0,welcome_message:`欢迎加入${e.title}！请先阅读群规，有任何问题可以联系管理员。`,keywords:["交流","学习","分享","成长"],target_audience:"专业人士",promotion_strategy:"口碑推广"}};Object.assign(Ce,l.basic,l.content,l.service,l.advanced),be.value=!0,console.log("✅ 营销配置加载成功:",Ce)}catch(l){console.error("获取营销配置失败:",l),C.error("获取营销配置失败")}})(e)},{default:S(()=>T[43]||(T[43]=[B("配置",-1)])),_:2,__:[43]},1032,["onClick"]),M(Z,{size:"small",type:"success",onClick:l=>(async e=>{try{await new Promise(e=>setTimeout(e,500));const l={group_info:{title:e.title,description:`${e.title}是一个专业的交流平台`,price:e.price,member_count:e.virtual_members,order_count:e.virtual_orders},marketing_config:{read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入群聊"},preview_url:`https://preview.example.com/group/${e.id}`,qr_code:`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://preview.example.com/group/${e.id}`,landing_page:{title:e.title,content:`欢迎加入${e.title}！这里有最新的资讯和优质的服务。`,features:["专业交流","行业资讯","商业机会","技能提升"],contact_info:"wechat123456"}};qe.value=l,ye.value=!0,console.log("✅ 预览数据加载成功:",qe.value)}catch(l){console.error("获取预览数据失败:",l),C.error("获取预览数据失败")}})(e)},{default:S(()=>T[44]||(T[44]=[B("预览",-1)])),_:2,__:[44]},1032,["onClick"]),M(Z,{size:"small",type:"warning",onClick:l=>(async e=>{try{await new Promise(e=>setTimeout(e,300));const l={original_title:e.title,replaced_title:Ve.value?`[${Ve.value}]${e.title}`:e.title,strategy_name:"城市前缀策略",test_city:Ve.value||"北京",replacement_rules:["在标题前添加城市前缀","保持原有标题内容","使用方括号格式"]};z.alert(`原标题: ${l.original_title}\n替换后: ${l.replaced_title}\n策略: ${l.strategy_name}\n测试城市: ${l.test_city}`,"城市定位测试结果",{type:"info"}),console.log("✅ 城市定位测试完成:",l)}catch(l){console.error("城市定位测试失败:",l),C.error("城市定位测试失败")}})(e)},{default:S(()=>T[45]||(T[45]=[B("测试",-1)])),_:2,__:[45]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[nl,pe.value]]),L("div",le,[M(el,{"current-page":je.page,"onUpdate:currentPage":T[4]||(T[4]=e=>je.page=e),"page-size":je.size,"onUpdate:pageSize":T[5]||(T[5]=e=>je.size=e),total:je.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:De,onCurrentChange:Ee},null,8,["current-page","page-size","total"])])]),_:1})]),M(_l,{modelValue:be.value,"onUpdate:modelValue":T[31]||(T[31]=e=>be.value=e),title:"群组营销配置",width:"80%","close-on-click-modal":!1},{footer:S(()=>[L("div",ue,[M(Z,{onClick:T[30]||(T[30]=e=>be.value=!1)},{default:S(()=>T[55]||(T[55]=[B("取消",-1)])),_:1,__:[55]}),M(Z,{type:"primary",onClick:Se,loading:me.value},{default:S(()=>T[56]||(T[56]=[B("保存配置",-1)])),_:1,__:[56]},8,["loading"])])]),default:S(()=>[M(dl,{modelValue:we.value,"onUpdate:modelValue":T[29]||(T[29]=e=>we.value=e),type:"card"},{default:S(()=>[M(ul,{label:"基础设置",name:"basic"},{default:S(()=>[M(Re,{model:Ce,"label-width":"120px"},{default:S(()=>[M(tl,{gutter:24},{default:S(()=>[M(ll,{span:12},{default:S(()=>[M(fe,{label:"阅读数显示"},{default:S(()=>[M(P,{modelValue:Ce.read_count_display,"onUpdate:modelValue":T[6]||(T[6]=e=>Ce.read_count_display=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),M(ll,{span:12},{default:S(()=>[M(fe,{label:"点赞数"},{default:S(()=>[M(al,{modelValue:Ce.like_count,"onUpdate:modelValue":T[7]||(T[7]=e=>Ce.like_count=e),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),M(tl,{gutter:24},{default:S(()=>[M(ll,{span:12},{default:S(()=>[M(fe,{label:"想看数"},{default:S(()=>[M(al,{modelValue:Ce.want_see_count,"onUpdate:modelValue":T[8]||(T[8]=e=>Ce.want_see_count=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),M(ll,{span:12},{default:S(()=>[M(fe,{label:"入群按钮文案"},{default:S(()=>[M(P,{modelValue:Ce.button_title,"onUpdate:modelValue":T[9]||(T[9]=e=>Ce.button_title=e),placeholder:"如：立即加入群聊"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),M(tl,{gutter:24},{default:S(()=>[M(ll,{span:12},{default:S(()=>[M(fe,{label:"头像库选择"},{default:S(()=>[M(Je,{modelValue:Ce.avatar_library,"onUpdate:modelValue":T[10]||(T[10]=e=>Ce.avatar_library=e)},{default:S(()=>[M(Ze,{label:"QQ风格（扩列交友）",value:"qq"}),M(Ze,{label:"综合随机",value:"za"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),M(ll,{span:12},{default:S(()=>[M(fe,{label:"微信访问控制"},{default:S(()=>[M(rl,{modelValue:Ce.wx_accessible,"onUpdate:modelValue":T[11]||(T[11]=e=>Ce.wx_accessible=e)},{default:S(()=>[M(ol,{label:1},{default:S(()=>T[46]||(T[46]=[B("微信能打开",-1)])),_:1,__:[46]}),M(ol,{label:2},{default:S(()=>T[47]||(T[47]=[B("微信内不能打开",-1)])),_:1,__:[47]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),M(fe,{label:"展示类型"},{default:S(()=>[M(rl,{modelValue:Ce.display_type,"onUpdate:modelValue":T[12]||(T[12]=e=>Ce.display_type=e)},{default:S(()=>[M(ol,{label:1},{default:S(()=>T[48]||(T[48]=[B("文字+图片",-1)])),_:1,__:[48]}),M(ol,{label:2},{default:S(()=>T[49]||(T[49]=[B("纯图片",-1)])),_:1,__:[49]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),M(ul,{label:"内容设置",name:"content"},{default:S(()=>[M(Re,{model:Ce,"label-width":"120px"},{default:S(()=>[M(fe,{label:"群简介标题"},{default:S(()=>[M(P,{modelValue:Ce.group_intro_title,"onUpdate:modelValue":T[13]||(T[13]=e=>Ce.group_intro_title=e),placeholder:"如：群简介"},null,8,["modelValue"])]),_:1}),M(fe,{label:"群简介内容"},{default:S(()=>[M(P,{type:"textarea",modelValue:Ce.group_intro_content,"onUpdate:modelValue":T[14]||(T[14]=e=>Ce.group_intro_content=e),rows:4},null,8,["modelValue"])]),_:1}),M(fe,{label:"常见问题标题"},{default:S(()=>[M(P,{modelValue:Ce.faq_title,"onUpdate:modelValue":T[15]||(T[15]=e=>Ce.faq_title=e),placeholder:"如：常见问题"},null,8,["modelValue"])]),_:1}),M(fe,{label:"常见问题内容"},{default:S(()=>[M(P,{type:"textarea",modelValue:Ce.faq_content,"onUpdate:modelValue":T[16]||(T[16]=e=>Ce.faq_content=e),rows:6,placeholder:"格式：问题1----答案1\n问题2----答案2"},null,8,["modelValue"])]),_:1}),M(fe,{label:"群友评论"},{default:S(()=>[M(P,{type:"textarea",modelValue:Ce.member_reviews,"onUpdate:modelValue":T[17]||(T[17]=e=>Ce.member_reviews=e),rows:6,placeholder:"格式：评论内容1----点赞数1\n评论内容2----点赞数2"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),M(ul,{label:"城市定位",name:"location"},{default:S(()=>[M(Re,{model:Ce,"label-width":"120px"},{default:S(()=>[M(fe,{label:"自动城市替换"},{default:S(()=>[M(il,{modelValue:Ce.auto_city_replace,"onUpdate:modelValue":T[18]||(T[18]=e=>Ce.auto_city_replace=e),"active-value":1,"inactive-value":0},null,8,["modelValue"]),T[50]||(T[50]=L("span",{class:"form-tip"},'启用后，标题中的"xxx"将自动替换为用户所在城市',-1))]),_:1,__:[50]}),Ce.auto_city_replace?(Q(),H(fe,{key:0,label:"城市插入策略"},{default:S(()=>[M(Je,{modelValue:Ce.city_insert_strategy,"onUpdate:modelValue":T[19]||(T[19]=e=>Ce.city_insert_strategy=e)},{default:S(()=>[M(Ze,{label:"智能判断",value:"auto"}),M(Ze,{label:"前缀模式",value:"prefix"}),M(Ze,{label:"后缀模式",value:"suffix"}),M(Ze,{label:"自然插入",value:"natural"}),M(Ze,{label:"不插入",value:"none"})]),_:1},8,["modelValue"])]),_:1})):O("",!0),Ce.auto_city_replace?(Q(),H(fe,{key:1,label:"测试城市替换"},{default:S(()=>[M(tl,{gutter:12},{default:S(()=>[M(ll,{span:8},{default:S(()=>[M(P,{modelValue:Ve.value,"onUpdate:modelValue":T[20]||(T[20]=e=>Ve.value=e),placeholder:"输入测试城市"},null,8,["modelValue"])]),_:1}),M(ll,{span:8},{default:S(()=>[M(Z,{onClick:Le},{default:S(()=>T[51]||(T[51]=[B("测试替换",-1)])),_:1,__:[51]})]),_:1}),M(ll,{span:8},{default:S(()=>[he.value?(Q(),I("span",ae,p(he.value),1)):O("",!0)]),_:1})]),_:1})]),_:1})):O("",!0)]),_:1},8,["model"])]),_:1}),M(ul,{label:"虚拟数据",name:"virtual"},{default:S(()=>[M(Re,{model:Ce,"label-width":"120px"},{default:S(()=>[M(tl,{gutter:24},{default:S(()=>[M(ll,{span:12},{default:S(()=>[M(fe,{label:"虚拟成员数"},{default:S(()=>[M(al,{modelValue:Ce.virtual_members,"onUpdate:modelValue":T[21]||(T[21]=e=>Ce.virtual_members=e),min:0},null,8,["modelValue"])]),_:1})]),_:1}),M(ll,{span:12},{default:S(()=>[M(fe,{label:"虚拟订单数"},{default:S(()=>[M(al,{modelValue:Ce.virtual_orders,"onUpdate:modelValue":T[22]||(T[22]=e=>Ce.virtual_orders=e),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),M(tl,{gutter:24},{default:S(()=>[M(ll,{span:12},{default:S(()=>[M(fe,{label:"虚拟收入"},{default:S(()=>[M(al,{modelValue:Ce.virtual_income,"onUpdate:modelValue":T[23]||(T[23]=e=>Ce.virtual_income=e),min:0,precision:2},null,8,["modelValue"])]),_:1})]),_:1}),M(ll,{span:12},{default:S(()=>[M(fe,{label:"今日浏览量"},{default:S(()=>[M(al,{modelValue:Ce.today_views,"onUpdate:modelValue":T[24]||(T[24]=e=>Ce.today_views=e),min:0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),M(fe,{label:"生成虚拟成员"},{default:S(()=>[M(tl,{gutter:12},{default:S(()=>[M(ll,{span:8},{default:S(()=>[M(al,{modelValue:ke.value,"onUpdate:modelValue":T[25]||(T[25]=e=>ke.value=e),min:1,max:50},null,8,["modelValue"])]),_:1}),M(ll,{span:8},{default:S(()=>[M(Z,{onClick:Me},{default:S(()=>T[52]||(T[52]=[B("生成成员数据",-1)])),_:1,__:[52]})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),M(ul,{label:"客服广告",name:"service"},{default:S(()=>[M(Re,{model:Ce,"label-width":"120px"},{default:S(()=>[M(fe,{label:"显示客服信息"},{default:S(()=>[M(rl,{modelValue:Ce.show_customer_service,"onUpdate:modelValue":T[26]||(T[26]=e=>Ce.show_customer_service=e)},{default:S(()=>[M(ol,{label:1},{default:S(()=>T[53]||(T[53]=[B("不显示",-1)])),_:1,__:[53]}),M(ol,{label:2},{default:S(()=>T[54]||(T[54]=[B("显示",-1)])),_:1,__:[54]})]),_:1},8,["modelValue"])]),_:1}),2===Ce.show_customer_service?(Q(),I(W,{key:0},[M(fe,{label:"客服头像"},{default:S(()=>[M(sl,{class:"avatar-uploader",action:Te.value,"show-file-list":!1,"on-success":Oe,"before-upload":Fe},{default:S(()=>[Ce.customer_service_avatar?(Q(),I("img",{key:0,src:Ce.customer_service_avatar,class:"avatar"},null,8,te)):(Q(),H($,{key:1,class:"avatar-uploader-icon"},{default:S(()=>[M(D(x))]),_:1}))]),_:1},8,["action"])]),_:1}),M(fe,{label:"客服标题"},{default:S(()=>[M(P,{modelValue:Ce.customer_service_title,"onUpdate:modelValue":T[27]||(T[27]=e=>Ce.customer_service_title=e),placeholder:"如：VIP专属客服"},null,8,["modelValue"])]),_:1}),M(fe,{label:"客服描述"},{default:S(()=>[M(P,{modelValue:Ce.customer_service_desc,"onUpdate:modelValue":T[28]||(T[28]=e=>Ce.customer_service_desc=e),placeholder:"如：出现不能付款，不能入群等问题，请联系我！"},null,8,["modelValue"])]),_:1})],64)):O("",!0),M(fe,{label:"客服二维码"},{default:S(()=>[M(sl,{class:"qr-uploader",action:Te.value,"show-file-list":!1,"on-success":We,"before-upload":D(Ge)},{default:S(()=>[Ce.customer_service_qr?(Q(),I("img",{key:0,src:Ce.customer_service_qr,class:"qr-code"},null,8,oe)):(Q(),H($,{key:1,class:"qr-uploader-icon"},{default:S(()=>[M(D(x))]),_:1}))]),_:1},8,["action","before-upload"])]),_:1}),M(fe,{label:"广告二维码"},{default:S(()=>[M(sl,{class:"qr-uploader",action:Te.value,"show-file-list":!1,"on-success":Ae,"before-upload":D(Ge)},{default:S(()=>[Ce.ad_qr_code?(Q(),I("img",{key:0,src:Ce.ad_qr_code,class:"qr-code"},null,8,re)):(Q(),H($,{key:1,class:"qr-uploader-icon"},{default:S(()=>[M(D(x))]),_:1}))]),_:1},8,["action","before-upload"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]),M(_l,{modelValue:ye.value,"onUpdate:modelValue":T[32]||(T[32]=e=>ye.value=e),title:"群组预览",width:"60%"},{default:S(()=>[qe.value?(Q(),I("div",ie,[L("div",se,[L("h3",null,p(qe.value.title),1),L("div",de,[L("span",null,"阅读 "+p(qe.value.read_count_display),1),L("span",null,"点赞 "+p(qe.value.like_count),1),L("span",null,"想看 "+p(qe.value.want_see_count),1)])]),qe.value.virtual_members?(Q(),I("div",_e,[T[57]||(T[57]=L("h4",null,"群成员",-1)),L("div",ne,[(Q(!0),I(W,null,A(qe.value.virtual_members.slice(0,6),e=>(Q(),I("div",{key:e.nickname,class:"member-item"},[L("img",{src:e.avatar,alt:e.nickname},null,8,ce),L("span",null,p(e.nickname),1)]))),128))])])):O("",!0)])):O("",!0)]),_:1},8,["modelValue"]),M(_l,{modelValue:ge.value,"onUpdate:modelValue":T[36]||(T[36]=e=>ge.value=e),title:"批量营销配置",width:"50%"},{footer:S(()=>[M(Z,{onClick:T[35]||(T[35]=e=>ge.value=!1)},{default:S(()=>T[60]||(T[60]=[B("取消",-1)])),_:1,__:[60]}),M(Z,{type:"primary",onClick:Be},{default:S(()=>T[61]||(T[61]=[B("应用配置",-1)])),_:1,__:[61]})]),default:S(()=>[M(Re,{model:ze,"label-width":"120px"},{default:S(()=>[M(fe,{label:"选择模板"},{default:S(()=>[M(Je,{modelValue:ze.template_id,"onUpdate:modelValue":T[33]||(T[33]=e=>ze.template_id=e),placeholder:"请选择营销模板"},{default:S(()=>[(Q(!0),I(W,null,A(Ue.value,e=>(Q(),H(Ze,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),M(fe,{label:"应用范围"},{default:S(()=>[M(rl,{modelValue:ze.scope,"onUpdate:modelValue":T[34]||(T[34]=e=>ze.scope=e)},{default:S(()=>[M(ol,{value:"selected"},{default:S(()=>T[58]||(T[58]=[B("仅选中的群组",-1)])),_:1,__:[58]}),M(ol,{value:"all"},{default:S(()=>T[59]||(T[59]=[B("所有群组",-1)])),_:1,__:[59]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-2cc4d472"]]);export{pe as default};
