import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    */import{r as a,L as t,e as l,k as n,l as o,E as s,z as r,t as i,A as d,y as p,B as u,D as c,u as m,a2 as g}from"./vue-vendor-DGsK9sC4.js";import{av as v,as as _,U as h,T as f,aO as y,ax as b,ay as j,aH as w,aW as x,aV as k,b1 as C,b9 as z,b5 as $,b6 as V,b7 as S,ao as T,b8 as D,bk as I,bj as U,Q as A,R as O}from"./element-plus-DcSKpKA8.js";import{P as B}from"./PageLayout-OFR6SHfu.js";import{f as P}from"./format-3eU4VJ9V.js";import"./utils-4VKArNEK.js";const E={class:"app-container"},L={class:"toolbar-container"},q={class:"toolbar-left"},H={class:"toolbar-right"},K={class:"content-list"},M={class:"content-cell"},Q={class:"content-text"},R={class:"content-meta"},W={key:0},F={class:"pagination-container"},G=e({__name:"ContentModeration",setup(e){const G=a(!1),J=a("pending"),N=a(""),X=t({contentType:""}),Y=a([]),Z=a([]),ee=t({page:1,limit:10,total:0}),ae=t({pending:0,approved:0,rejected:0}),te={pending:{text:"待审核",type:"warning"},approved:{text:"已批准",type:"success"},rejected:{text:"已拒绝",type:"danger"}},le={pending:Array.from({length:5}).map((e,a)=>({id:100+a,content:`这是第${a+1}条待审核的内容，请管理员尽快处理。`,author:{id:201,name:"用户A"},group:{id:301,name:"技术交流群"},type:"comment",status:"pending",created_at:(new Date).toISOString()})),approved:Array.from({length:20}).map((e,a)=>({id:200+a,content:`这是第${a+1}条已批准的内容。`,author:{id:202,name:"用户B"},group:{id:302,name:"产品设计圈"},type:"post",status:"approved",created_at:(new Date).toISOString()})),rejected:Array.from({length:8}).map((e,a)=>({id:300+a,content:`这是第${a+1}条已拒绝的内容，包含不当言论。`,author:{id:203,name:"用户C"},group:{id:301,name:"技术交流群"},type:"comment",status:"rejected",created_at:(new Date).toISOString()}))},ne=()=>{G.value=!0,setTimeout(()=>{const e=le[J.value];ee.total=e.length,ae.pending=le.pending.length,ae.approved=le.approved.length,ae.rejected=le.rejected.length;const a=(ee.page-1)*ee.limit,t=a+ee.limit;Y.value=e.slice(a,t),G.value=!1},500)},oe=()=>{ee.page=1,ne()},se=e=>{Z.value=e},re=e=>{const a=Z.value.map(e=>e.id);"approve"===e?A.success(`已批量批准 ${a.length} 条内容`):"reject"===e&&A.warning(`已批量拒绝 ${a.length} 条内容`),ne(),Z.value=[]};return l(()=>{ne()}),(e,a)=>{const t=f,l=_,le=j,ie=b,de=v,pe=w,ue=k,ce=x,me=$,ge=z,ve=S,_e=D,he=V,fe=I,ye=U;return o(),n("div",E,[s(B,null,{header:r(()=>a[5]||(a[5]=[i("div",{class:"page-header"},[i("h1",null,"内容审核与管理"),i("p",null,"审核用户在社群中发布的内容，确保社区环境健康、合规。")],-1)])),default:r(()=>[i("div",L,[i("div",q,[Z.value.length>0?(o(),p(de,{key:0,onCommand:re},{dropdown:r(()=>[s(ie,null,{default:r(()=>[s(le,{command:"approve"},{default:r(()=>a[6]||(a[6]=[c("批量批准",-1)])),_:1,__:[6]}),s(le,{command:"reject"},{default:r(()=>a[7]||(a[7]=[c("批量拒绝",-1)])),_:1,__:[7]})]),_:1})]),default:r(()=>[s(l,{type:"primary"},{default:r(()=>[c(" 批量操作 ("+h(Z.value.length)+")",1),s(t,{class:"el-icon--right"},{default:r(()=>[s(m(y))]),_:1})]),_:1})]),_:1})):u("",!0)]),i("div",H,[s(pe,{modelValue:N.value,"onUpdate:modelValue":a[0]||(a[0]=e=>N.value=e),placeholder:"搜索内容、作者",style:{width:"250px","margin-right":"10px"},clearable:"",onKeyup:g(ne,["enter"])},null,8,["modelValue"]),s(ce,{modelValue:X.contentType,"onUpdate:modelValue":a[1]||(a[1]=e=>X.contentType=e),placeholder:"内容类型",clearable:"",style:{width:"120px","margin-right":"10px"}},{default:r(()=>[s(ue,{label:"全部类型",value:""}),s(ue,{label:"帖子",value:"post"}),s(ue,{label:"评论",value:"comment"})]),_:1},8,["modelValue"]),s(l,{type:"primary",onClick:ne},{default:r(()=>[s(t,null,{default:r(()=>[s(m(C))]),_:1}),a[8]||(a[8]=c(" 搜索 ",-1))]),_:1,__:[8]})])]),s(ge,{modelValue:J.value,"onUpdate:modelValue":a[2]||(a[2]=e=>J.value=e),onTabClick:oe,class:"content-tabs"},{default:r(()=>[s(me,{label:`待审核 (${ae.pending})`,name:"pending"},null,8,["label"]),s(me,{label:`已批准 (${ae.approved})`,name:"approved"},null,8,["label"]),s(me,{label:`已拒绝 (${ae.rejected})`,name:"rejected"},null,8,["label"])]),_:1},8,["modelValue"]),d((o(),n("div",K,[s(he,{data:Y.value,style:{width:"100%"},onSelectionChange:se},{default:r(()=>[s(ve,{type:"selection",width:"55"}),s(ve,{label:"内容","min-width":"300"},{default:r(({row:e})=>[i("div",M,[i("div",Q,h(e.content),1),i("div",R,[i("span",null,[s(t,null,{default:r(()=>[s(m(T))]),_:1}),c(" "+h(e.author.name),1)]),i("span",null,"群组: "+h(e.group.name),1),i("span",null,"时间: "+h(m(P)(e.created_at)),1)])])]),_:1}),s(ve,{label:"类型",width:"100"},{default:r(({row:e})=>[s(_e,{size:"small"},{default:r(()=>[c(h("post"===e.type?"帖子":"评论"),1)]),_:2},1024)]),_:1}),s(ve,{label:"状态",width:"100"},{default:r(({row:e})=>[s(_e,{type:te[e.status].type},{default:r(()=>[c(h(te[e.status].text),1)]),_:2},1032,["type"])]),_:1}),s(ve,{label:"操作",width:"200",fixed:"right"},{default:r(({row:e})=>["pending"===e.status?(o(),n("div",W,[s(l,{link:"",type:"primary",size:"small",onClick:a=>{return t=e,A.success(`内容(ID: ${t.id}) 已批准`),void ne();var t}},{default:r(()=>a[9]||(a[9]=[c("批准",-1)])),_:2,__:[9]},1032,["onClick"]),s(l,{link:"",type:"danger",size:"small",onClick:a=>{return t=e,void O.prompt("请输入拒绝原因（可选）","确认拒绝",{confirmButtonText:"确定",cancelButtonText:"取消"}).then(({value:e})=>{A.warning(`内容(ID: ${t.id}) 已拒绝，原因: ${e||"无"}`),ne()}).catch(()=>{});var t}},{default:r(()=>a[10]||(a[10]=[c("拒绝",-1)])),_:2,__:[10]},1032,["onClick"])])):u("",!0),s(l,{link:"",type:"info",size:"small",onClick:a=>{return t=e,void A.info(`查看内容(ID: ${t.id})的详情...`);var t}},{default:r(()=>a[11]||(a[11]=[c("查看详情",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),i("div",F,[s(fe,{"current-page":ee.page,"onUpdate:currentPage":a[3]||(a[3]=e=>ee.page=e),"page-size":ee.limit,"onUpdate:pageSize":a[4]||(a[4]=e=>ee.limit=e),total:ee.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ne,onCurrentChange:ne},null,8,["current-page","page-size","total"])])])),[[ye,G.value]])]),_:1})])}}},[["__scopeId","data-v-bb7bc39a"]]);export{G as default};
