<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\WechatGroup;
use App\Models\GroupAccessLog;
use App\Models\IpCityCache;
use App\Services\IPLocationService;
use App\Services\BrowserDetectionService;
use App\Services\GroupAccessValidationService;

/**
 * 营销功能测试脚本
 * 测试新增的营销功能是否正常工作
 */

echo "🚀 FFJQ营销功能测试脚本\n";
echo "================================\n\n";

try {
    // 初始化Laravel应用
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    // 测试1: 数据库连接和表结构
    echo "📊 测试1: 数据库连接和表结构\n";
    testDatabaseStructure();

    // 测试2: IP城市定位功能
    echo "\n🌍 测试2: IP城市定位功能\n";
    testIPLocationService();

    // 测试3: 浏览器检测功能
    echo "\n🌐 测试3: 浏览器检测功能\n";
    testBrowserDetectionService();

    // 测试4: 群组营销数据
    echo "\n📈 测试4: 群组营销数据\n";
    testGroupMarketingData();

    // 测试5: 群组访问验证
    echo "\n🔒 测试5: 群组访问验证\n";
    testGroupAccessValidation();

    // 测试6: 虚拟数据生成
    echo "\n🎭 测试6: 虚拟数据生成\n";
    testVirtualDataGeneration();

    echo "\n✅ 所有测试完成！\n";
    echo "================================\n";

} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

/**
 * 测试数据库结构
 */
function testDatabaseStructure()
{
    try {
        // 检查新增的表是否存在
        $tables = [
            'group_access_logs',
            'ip_city_cache'
        ];

        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                echo "  ✅ 表 {$table} 存在\n";
            } else {
                echo "  ❌ 表 {$table} 不存在\n";
            }
        }

        // 检查wechat_groups表的新字段
        $newColumns = [
            'read_count_display',
            'like_count',
            'want_see_count',
            'button_title',
            'group_intro_title',
            'faq_content',
            'member_reviews',
            'customer_service_qr',
            'avatar_library',
            'wx_accessible',
            'auto_city_replace',
            'virtual_members',
            'marketing_tags',
            'view_count'
        ];

        $existingColumns = DB::getSchemaBuilder()->getColumnListing('wechat_groups');
        
        foreach ($newColumns as $column) {
            if (in_array($column, $existingColumns)) {
                echo "  ✅ 字段 wechat_groups.{$column} 存在\n";
            } else {
                echo "  ❌ 字段 wechat_groups.{$column} 不存在\n";
            }
        }

    } catch (Exception $e) {
        echo "  ❌ 数据库结构测试失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 测试IP城市定位服务
 */
function testIPLocationService()
{
    try {
        $ipService = new IPLocationService();
        
        // 测试获取客户端IP
        $clientIP = $ipService->getClientIP();
        echo "  📍 客户端IP: {$clientIP}\n";
        
        // 测试城市定位
        $testIPs = ['*******', '***************', '*********'];
        
        foreach ($testIPs as $ip) {
            $city = $ipService->getCity($ip);
            echo "  🏙️ IP {$ip} -> 城市: {$city}\n";
            
            // 测试缓存功能
            $cachedCity = IpCityCache::getCachedCity($ip);
            if ($cachedCity) {
                echo "    💾 缓存命中: {$cachedCity}\n";
            }
        }

        // 测试常用城市列表
        $commonCities = $ipService->getCommonCities();
        echo "  🌆 常用城市数量: " . count($commonCities) . "\n";

    } catch (Exception $e) {
        echo "  ❌ IP城市定位测试失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 测试浏览器检测服务
 */
function testBrowserDetectionService()
{
    try {
        $browserService = new BrowserDetectionService();
        
        // 模拟不同的User-Agent
        $testUserAgents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1 MicroMessenger/8.0.0',
            'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ];

        foreach ($testUserAgents as $index => $userAgent) {
            // 临时设置User-Agent
            $_SERVER['HTTP_USER_AGENT'] = $userAgent;
            
            $browserType = $browserService->getBrowserType();
            $isWechat = $browserService->isWechatBrowser();
            $isMobile = $browserService->isMobile();
            
            echo "  🔍 测试 " . ($index + 1) . ":\n";
            echo "    浏览器类型: {$browserType}\n";
            echo "    是否微信: " . ($isWechat ? '是' : '否') . "\n";
            echo "    是否移动端: " . ($isMobile ? '是' : '否') . "\n";
        }

        // 测试浏览器引导页面生成
        $guidePage = $browserService->generateBrowserGuidePage('https://example.com', '测试群组');
        echo "  📄 浏览器引导页面长度: " . strlen($guidePage) . " 字符\n";

    } catch (Exception $e) {
        echo "  ❌ 浏览器检测测试失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 测试群组营销数据
 */
function testGroupMarketingData()
{
    try {
        // 获取第一个群组进行测试
        $group = WechatGroup::first();
        
        if (!$group) {
            echo "  ⚠️ 没有找到群组数据，跳过测试\n";
            return;
        }

        echo "  📊 测试群组ID: {$group->id}\n";
        echo "  📝 群组标题: {$group->title}\n";
        
        // 测试营销字段
        echo "  👍 点赞数: " . ($group->like_count ?? 0) . "\n";
        echo "  👀 想看数: " . ($group->want_see_count ?? 0) . "\n";
        echo "  📖 阅读数显示: " . ($group->read_count_display ?? '未设置') . "\n";
        echo "  🔘 按钮标题: " . ($group->button_title ?? '未设置') . "\n";
        
        // 测试城市替换功能
        $cityReplacedTitle = $group->getCityReplacedTitle('北京');
        echo "  🏙️ 城市替换后标题: {$cityReplacedTitle}\n";
        
        // 测试虚拟统计数据
        $virtualStats = $group->virtual_stats;
        echo "  🎭 虚拟统计数据:\n";
        echo "    虚拟成员: " . ($virtualStats['members'] ?? 0) . "\n";
        echo "    虚拟订单: " . ($virtualStats['orders'] ?? 0) . "\n";
        echo "    虚拟收入: " . ($virtualStats['income'] ?? 0) . "\n";
        
        // 测试格式化内容
        $formattedFAQ = $group->formatted_faq_content;
        echo "  ❓ FAQ条目数: " . count($formattedFAQ) . "\n";
        
        $formattedReviews = $group->formatted_member_reviews;
        echo "  💬 评价条目数: " . count($formattedReviews) . "\n";

    } catch (Exception $e) {
        echo "  ❌ 群组营销数据测试失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 测试群组访问验证
 */
function testGroupAccessValidation()
{
    try {
        $group = WechatGroup::first();
        
        if (!$group) {
            echo "  ⚠️ 没有找到群组数据，跳过测试\n";
            return;
        }

        $ipService = new IPLocationService();
        $browserService = new BrowserDetectionService();
        $validationService = new GroupAccessValidationService($ipService, $browserService);
        
        // 测试访问验证
        $validation = $validationService->validateGroupAccess($group->id);
        
        echo "  🔒 访问验证结果: " . ($validation['valid'] ? '通过' : '失败') . "\n";
        
        if (!$validation['valid']) {
            echo "    失败原因: " . $validation['message'] . "\n";
        } else {
            echo "    检测城市: " . ($validation['detected_city'] ?? '未知') . "\n";
            echo "    浏览器类型: " . ($validation['browser_info']['type'] ?? '未知') . "\n";
        }
        
        // 测试安全链接生成
        $secureLink = $validationService->generateSecureGroupLink($group);
        echo "  🔗 安全链接: " . substr($secureLink, 0, 50) . "...\n";
        
        // 测试访问统计
        $stats = $validationService->getGroupAccessStats($group->id, 7);
        echo "  📈 7天访问统计:\n";
        echo "    总访问: " . $stats['total_visits'] . "\n";
        echo "    成功访问: " . $stats['successful_visits'] . "\n";
        echo "    独立IP: " . $stats['unique_ips'] . "\n";

    } catch (Exception $e) {
        echo "  ❌ 群组访问验证测试失败: " . $e->getMessage() . "\n";
    }
}

/**
 * 测试虚拟数据生成
 */
function testVirtualDataGeneration()
{
    try {
        $group = WechatGroup::first();
        
        if (!$group) {
            echo "  ⚠️ 没有找到群组数据，跳过测试\n";
            return;
        }

        // 测试虚拟成员生成
        $virtualMembers = $group->generateVirtualMembers(10);
        echo "  👥 生成虚拟成员数: " . count($virtualMembers) . "\n";
        
        if (!empty($virtualMembers)) {
            $firstMember = $virtualMembers[0];
            echo "    示例成员: " . $firstMember['nickname'] . "\n";
            echo "    头像路径: " . $firstMember['avatar'] . "\n";
        }
        
        // 测试虚拟评价生成
        $virtualReviews = $group->generateVirtualReviews();
        echo "  💬 生成虚拟评价数: " . count($virtualReviews) . "\n";
        
        if (!empty($virtualReviews)) {
            $firstReview = $virtualReviews[0];
            echo "    示例评价: " . substr($firstReview['content'], 0, 20) . "...\n";
            echo "    评价者: " . $firstReview['nickname'] . "\n";
        }

    } catch (Exception $e) {
        echo "  ❌ 虚拟数据生成测试失败: " . $e->getMessage() . "\n";
    }
}