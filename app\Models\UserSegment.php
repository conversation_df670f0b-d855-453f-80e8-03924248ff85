<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class UserSegment extends Model
{
    protected $fillable = [
        'name',
        'description',
        'conditions',
        'type',
        'status',
        'auto_update',
        'created_by',
        'member_count',
        'last_updated_at',
    ];

    protected $casts = [
        'conditions' => 'array',
        'auto_update' => 'boolean',
        'last_updated_at' => 'datetime',
    ];

    /**
     * 分群成员关联
     */
    public function members(): HasMany
    {
        return $this->hasMany(UserSegmentMember::class, 'segment_id');
    }

    /**
     * 分群统计关联
     */
    public function statistics(): HasMany
    {
        return $this->hasMany(UserSegmentStat::class, 'segment_id');
    }

    /**
     * 分群用户关联（通过中间表）
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_segment_members', 'segment_id', 'user_id')
            ->withPivot(['added_at', 'score'])
            ->withTimestamps();
    }

    /**
     * 创建者关联
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 分群类型范围
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 活跃分群范围
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 自动更新分群范围
     */
    public function scopeAutoUpdate($query)
    {
        return $query->where('auto_update', true);
    }

    /**
     * 更新分群成员
     */
    public function updateMembers()
    {
        if (!$this->auto_update) {
            return false;
        }

        // 根据条件查询符合的用户
        $userIds = $this->queryUsersBasedOnConditions();

        // 清除现有成员
        $this->members()->delete();

        // 添加新成员
        $members = [];
        foreach ($userIds as $userId) {
            $members[] = [
                'segment_id' => $this->id,
                'user_id' => $userId,
                'added_at' => now(),
                'score' => $this->calculateUserScore($userId),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        UserSegmentMember::insert($members);

        // 更新成员数量
        $this->update([
            'member_count' => count($members),
            'last_updated_at' => now(),
        ]);

        return true;
    }

    /**
     * 根据条件查询用户
     */
    private function queryUsersBasedOnConditions()
    {
        $query = User::query();
        $conditions = $this->conditions;

        if (empty($conditions)) {
            return [];
        }

        foreach ($conditions as $condition) {
            $field = $condition['field'] ?? '';
            $operator = $condition['operator'] ?? '=';
            $value = $condition['value'] ?? '';

            switch ($field) {
                case 'registration_date':
                    $this->applyDateCondition($query, 'created_at', $operator, $value);
                    break;
                case 'last_login':
                    $this->applyDateCondition($query, 'last_login_at', $operator, $value);
                    break;
                case 'role':
                    $query->where('role', $operator, $value);
                    break;
                case 'level':
                    $query->where('level', $operator, $value);
                    break;
                case 'status':
                    $query->where('status', $operator, $value);
                    break;
                case 'balance':
                    $query->where('balance', $operator, $value);
                    break;
                case 'order_count':
                    $query->whereHas('orders', function ($q) use ($operator, $value) {
                        // 这里需要根据操作符进行特殊处理
                    });
                    break;
                case 'total_spent':
                    $query->whereHas('orders', function ($q) use ($operator, $value) {
                        $q->where('status', 'paid');
                    })->having(DB::raw('SUM(orders.amount)'), $operator, $value);
                    break;
                default:
                    // 自定义字段处理
                    break;
            }
        }

        return $query->pluck('id')->toArray();
    }

    /**
     * 应用日期条件
     */
    private function applyDateCondition($query, $field, $operator, $value)
    {
        switch ($operator) {
            case 'days_ago':
                $date = now()->subDays($value);
                $query->where($field, '>=', $date);
                break;
            case 'before':
                $query->where($field, '<', $value);
                break;
            case 'after':
                $query->where($field, '>', $value);
                break;
            case 'between':
                if (is_array($value) && count($value) == 2) {
                    $query->whereBetween($field, $value);
                }
                break;
            default:
                $query->where($field, $operator, $value);
                break;
        }
    }

    /**
     * 计算用户分数
     */
    private function calculateUserScore($userId)
    {
        // 这里可以实现复杂的评分逻辑
        // 例如基于用户活跃度、消费金额、注册时间等
        
        $user = User::find($userId);
        if (!$user) {
            return 0;
        }

        $score = 0;

        // 基础分数
        $score += 10;

        // 注册时间分数（越早注册分数越高）
        $daysSinceRegistration = $user->created_at->diffInDays(now());
        $score += max(0, 100 - $daysSinceRegistration / 10);

        // 活跃度分数
        if ($user->last_login_at) {
            $daysSinceLastLogin = $user->last_login_at->diffInDays(now());
            $score += max(0, 50 - $daysSinceLastLogin);
        }

        // 消费分数
        $totalSpent = $user->orders()->where('status', 'paid')->sum('amount');
        $score += min(100, $totalSpent / 100); // 每100元1分，最高100分

        return round($score, 2);
    }

    /**
     * 获取分群统计信息
     */
    public function getStatistics()
    {
        $stats = [
            'member_count' => $this->member_count,
            'avg_score' => $this->members()->avg('score'),
            'top_users' => $this->members()
                ->with('user')
                ->orderBy('score', 'desc')
                ->limit(10)
                ->get(),
            'recent_growth' => $this->getRecentGrowth(),
        ];

        return $stats;
    }

    /**
     * 获取最近增长情况
     */
    private function getRecentGrowth()
    {
        $days = 7;
        $growth = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            $nextDate = $date->copy()->addDay();

            $count = $this->members()
                ->where('added_at', '>=', $date)
                ->where('added_at', '<', $nextDate)
                ->count();

            $growth[] = [
                'date' => $date->format('Y-m-d'),
                'count' => $count,
            ];
        }

        return $growth;
    }

    /**
     * 导出分群成员
     */
    public function exportMembers($format = 'excel')
    {
        $members = $this->users()->get();

        $data = $members->map(function ($user) {
            return [
                'ID' => $user->id,
                '用户名' => $user->name,
                '邮箱' => $user->email,
                '手机号' => $user->phone,
                '角色' => $user->role,
                '状态' => $user->status,
                '分数' => $user->pivot->score,
                '加入时间' => $user->pivot->added_at,
            ];
        })->toArray();

        // 这里可以调用导出服务进行实际导出
        return $data;
    }

    /**
     * 批量添加用户到分群
     */
    public function addUsers($userIds, $score = null)
    {
        $members = [];
        foreach ($userIds as $userId) {
            // 检查用户是否已存在
            if (!$this->members()->where('user_id', $userId)->exists()) {
                $members[] = [
                    'segment_id' => $this->id,
                    'user_id' => $userId,
                    'added_at' => now(),
                    'score' => $score ?? $this->calculateUserScore($userId),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        if (!empty($members)) {
            UserSegmentMember::insert($members);
            $this->increment('member_count', count($members));
        }

        return count($members);
    }

    /**
     * 批量移除用户
     */
    public function removeUsers($userIds)
    {
        $removedCount = $this->members()->whereIn('user_id', $userIds)->delete();
        $this->decrement('member_count', $removedCount);
        
        return $removedCount;
    }

    /**
     * 检查用户是否在分群中
     */
    public function hasUser($userId)
    {
        return $this->members()->where('user_id', $userId)->exists();
    }
} 