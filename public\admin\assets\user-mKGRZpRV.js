import"./index-D2bI4m-v.js";import{f as e}from"./chunk-KZPPZA2C-BZQYgWVq.js";const t=Array.from({length:150},(t,n)=>({id:n+1,username:e.internet.userName(),name:e.person.fullName(),email:e.internet.email(),phone:e.phone.number(),avatar:e.image.avatar(),role:e.helpers.arrayElement(["user","agent","group_owner"]),status:e.helpers.arrayElement([0,1]),vip_level:e.helpers.arrayElement([0,1,2,3,4,5]),balance:e.finance.amount(0,1e4,2),points:e.number.int({min:0,max:1e5}),created_at:e.date.past({years:2}),last_login_at:e.date.recent(),bio:e.lorem.paragraph()})),n=Array.from({length:500},(t,n)=>({id:n+1,user_id:e.number.int({min:1,max:150}),order_no:e.string.uuid(),amount:e.finance.amount(10,500,2),status:e.helpers.arrayElement(["pending","paid","cancelled","refunded"]),created_at:e.date.past({years:1}),wechat_group:{title:e.lorem.words(3)}})),a=Array.from({length:1e3},(t,n)=>({id:n+1,user_id:e.number.int({min:1,max:150}),type:e.helpers.arrayElement(["add","subtract"]),points:e.number.int({min:10,max:1e3}),description:e.lorem.sentence(),created_at:e.date.past({years:1})})),r=()=>Promise.resolve({code:0,data:t[0],message:"成功"}),s=()=>Promise.resolve({code:0,data:{total_orders:e.number.int({min:50,max:200}),total_spent:e.finance.amount(5e3,5e4,2),points:t[0].points,coupons:e.number.int({min:0,max:20})},message:"成功"}),o=e=>{const{limit:a=5}=e,r=n.filter(e=>e.user_id===t[0].id);return Promise.resolve({code:0,data:r.slice(0,a),message:"成功"})},i=e=>{const{limit:n=5}=e,r=a.filter(e=>e.user_id===t[0].id);return Promise.resolve({code:0,data:r.slice(0,n),message:"成功"})},m=t=>{const{period:n="month"}=t,a=Array.from({length:30},(e,t)=>`Day ${t+1}`),r=Array.from({length:30},()=>e.finance.amount(0,1e3,2));return Promise.resolve({code:0,data:{summary:{total_amount:r.reduce((e,t)=>e+parseFloat(t),0).toFixed(2),total_orders:e.number.int({min:10,max:50}),avg_amount:(r.reduce((e,t)=>e+parseFloat(t),0)/r.length).toFixed(2),max_amount:Math.max(...r).toFixed(2)},chart:{labels:a,data:r}},message:"成功"})},d=e=>{const n=t.findIndex(e=>e.id===t[0].id);return-1!==n&&(t[n]={...t[n],...e}),Promise.resolve({code:0,data:null,message:"更新成功"})},u=e=>{const{page:n=1,limit:a=10}=e,r=(n-1)*a,s=n*a;return Promise.resolve({code:0,data:{list:t.slice(r,s),total:t.length},message:"成功"})},l=()=>Promise.resolve({code:0,data:{total_users:t.length,active_today:e.number.int({min:100,max:500}),new_today:e.number.int({min:10,max:50}),banned_count:t.filter(e=>0===e.status).length},message:"成功"}),c=e=>Promise.resolve({code:0,data:t.find(t=>t.id===e),message:"成功"}),g=e=>{const n={id:t.length+1,...e,created_at:(new Date).toISOString(),last_login_at:(new Date).toISOString()};return t.push(n),Promise.resolve({code:0,data:n,message:"创建成功"})},p=(e,n)=>{const a=t.findIndex(t=>t.id===e);return-1!==a&&(t[a]={...t[a],...n}),Promise.resolve({code:0,data:null,message:"更新成功"})},f=e=>{const n=t.findIndex(t=>t.id===e);return-1!==n&&t.splice(n,1),Promise.resolve({code:0,data:null,message:"删除成功"})},_=(e,n)=>{const a=t.findIndex(t=>t.id===e);return-1!==a&&(t[a].status=n),Promise.resolve({code:0,data:null,message:"状态更新成功"})},h=(e,n)=>{const a=t.findIndex(t=>t.id===e);return-1!==a&&(t[a].balance=(parseFloat(t[a].balance)+parseFloat(n.amount)).toFixed(2)),Promise.resolve({code:0,data:null,message:"余额调整成功"})};function x(){return r()}function v(){return s()}function b(e){return o(e)}function y(e){return i(e)}function P(e){return m(e)}function F(e){return d(e)}function I(e){return u(e)}function w(){return l()}function A(e){return c(e)}function E(e){return g(e)}function S(e,t){return p(e,t)}function j(e){return f(e)}function D(e,t){return _(e,t)}function k(e,t){return h(e,t)}export{v as a,b,y as c,P as d,S as e,E as f,x as g,k as h,A as i,I as j,w as k,j as l,D as m,F as u};
