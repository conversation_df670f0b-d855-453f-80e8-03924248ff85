import{_ as e}from"./index-D2bI4m-v.js";/* empty css                         *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                       *//* empty css                        *//* empty css                *//* empty css               */import{U as l,b8 as a,T as t,aQ as s,as as n,b2 as i,b4 as u,al as o,ae as r,ai as d,c2 as c,a6 as p,a0 as _,b3 as m,bo as v,bv as f,aN as h,aH as b,ab as g,au as y,b6 as w,b7 as C,b9 as k,b5 as x,bR as V,by as j,bz as q,Q as D}from"./element-plus-DcSKpKA8.js";import{S as F}from"./StatCard-WpSR56Tk.js";import{L as A}from"./LineChart-Ba008-uu.js";import{D as U}from"./DoughnutChart-JEDVUFw0.js";import{a as E}from"./agent-SmxfvIrI.js";import{b as z}from"./browser-DJkR4j8n.js";import{r as M,e as Q,k as S,l as L,t as R,E as T,z as $,D as H,u as N,F as I,Y as W,y as X,C as Y,n as Z}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";/* empty css                                                                 */import"./chart-Bup65vvO.js";const B={class:"agent-dashboard"},G={class:"page-header"},J={class:"header-right"},K={class:"header-info"},O={class:"agent-code"},P={class:"header-actions"},ee={class:"card-header"},le={class:"card-header"},ae={class:"team-overview"},te={class:"team-stats"},se={class:"team-stat-item"},ne={class:"value"},ie={class:"team-stat-item"},ue={class:"value"},oe={class:"team-stat-item"},re={class:"value"},de={class:"recent-members"},ce={class:"member-info"},pe={class:"member-name"},_e={class:"member-time"},me={class:"card-header"},ve={class:"activities-list"},fe={class:"activity-icon"},he={class:"activity-content"},be={class:"activity-title"},ge={class:"activity-desc"},ye={class:"activity-time"},we={class:"qr-code-container"},Ce={class:"qr-code-tips"},ke={class:"help-content"},xe={class:"help-section"},Ve={class:"feature-item"},je={class:"feature-icon"},qe={class:"feature-item"},De={class:"feature-icon"},Fe={class:"feature-item"},Ae={class:"feature-icon"},Ue={class:"feature-item"},Ee={class:"feature-icon"},ze={class:"feature-item"},Me={class:"feature-icon"},Qe={class:"feature-item"},Se={class:"feature-icon"},Le={class:"help-section"},Re={class:"help-section"},Te={class:"commission-rules"},$e={class:"rule-item"},He={class:"example"},Ne={class:"rule-item"},Ie={class:"example"},We={class:"rule-item"},Xe={class:"help-section"},Ye={class:"tips-content"},Ze={class:"tips-content"},Be={class:"help-section"},Ge={class:"guide-content"},Je={class:"guide-content"},Ke={class:"guide-content"},Oe={class:"help-section"},Pe=e({__name:"AgentDashboard",setup(e){M(!1);const Pe=M("month"),el=M(!1),ll=M(!1),al=M(),tl=M({}),sl=M({}),nl=M({}),il=M([]),ul=M([]),ol=M(""),rl=M("channels"),dl=M(["link-generation"]),cl=M([]),pl=M([{level:"初级代理",color:"info",name:"新手代理",requirements:"注册成功，完成实名认证",commission_rate:"5%",benefits:"基础推广工具、新手培训"},{level:"中级代理",color:"primary",name:"进阶代理",requirements:"推广用户≥10人，月佣金≥500元",commission_rate:"8%",benefits:"专属客服、营销素材、数据分析"},{level:"高级代理",color:"warning",name:"资深代理",requirements:"推广用户≥50人，团队≥5人，月佣金≥2000元",commission_rate:"12%",benefits:"优先结算、专属培训、活动优先权"},{level:"金牌代理",color:"danger",name:"顶级代理",requirements:"推广用户≥200人，团队≥20人，月佣金≥10000元",commission_rate:"15%",benefits:"专属经理、定制服务、年度奖励"}]),_l=M({direct:10,team:3}),ml=M({max_levels:5}),vl=M([{level:"1级",rate:"10%",description:"直接推广用户"},{level:"2级",rate:"3%",description:"下级代理推广用户"},{level:"3级",rate:"1%",description:"三级代理推广用户"},{level:"4级",rate:"0.5%",description:"四级代理推广用户"},{level:"5级",rate:"0.2%",description:"五级代理推广用户"}]),fl=M({labels:[],datasets:[{label:"佣金收入",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}),hl=M({labels:["直接推广","团队推广","活动推广","其他"],datasets:[{data:[0,0,0,0],backgroundColor:["#409EFF","#67C23A","#E6A23C","#F56C6C"]}]}),bl={responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},gl={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}},yl=async()=>{try{const e={week:{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[120,190,300,500,200,300,450]},month:{labels:Array.from({length:30},(e,l)=>`${l+1}日`),data:Array.from({length:30},()=>Math.floor(1e3*Math.random()))},quarter:{labels:["1月","2月","3月"],data:[8e3,12e3,15e3]}}[Pe.value];fl.value={labels:e.labels,datasets:[{label:"佣金收入",data:e.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}}catch(e){D.error("加载佣金趋势失败")}},wl=async()=>{try{ul.value=[{id:1,type:"commission",title:"佣金到账",description:"您获得了 ¥150.00 的推广佣金",created_at:new Date},{id:2,type:"user",title:"新用户注册",description:'通过您的推广链接，新增用户"王五"',created_at:new Date(Date.now()-36e5)},{id:3,type:"team",title:"团队成员升级",description:'下级代理商"赵六"升级为二级代理',created_at:new Date(Date.now()-72e5)}]}catch(e){D.error("加载活动数据失败")}},Cl=()=>{tl.value.agent_code&&(ol.value=`${window.location.origin}/register?agent=${tl.value.agent_code}`)},kl=async()=>{try{await navigator.clipboard.writeText(ol.value),D.success("推广链接已复制到剪贴板")}catch(e){D.error("复制失败，请手动复制")}},xl=async()=>{try{el.value=!0,await Z();await z.toCanvas(al.value,ol.value,{width:200,margin:2})}catch(e){D.error("生成二维码失败")}},Vl=()=>{const e=al.value.querySelector("canvas");if(e){const l=document.createElement("a");l.download=`推广二维码-${tl.value.agent_code}.png`,l.href=e.toDataURL(),l.click()}},jl=()=>{wl(),D.success("动态已刷新")},ql=()=>{D.info("推广工具页面开发中...")},Dl=()=>{D.info("团队管理页面开发中...")},Fl=()=>{D.info("佣金中心页面开发中...")},Al=()=>{D.info("培训中心页面开发中...")},Ul=()=>{D.info("绩效分析页面开发中...")},El=()=>{D.info("设置页面开发中...")},zl=e=>new Date(e).toLocaleString("zh-CN"),Ml=e=>({commission:"Money",user:"User",team:"Connection"}[e]||"InfoFilled");return Q(()=>{(async()=>{try{const e=await E.getMy();tl.value=e.data,Cl()}catch(e){D.error("加载代理商信息失败")}})(),(async()=>{try{const e=await E.getMyStats();sl.value=e.data}catch(e){D.error("加载统计数据失败")}})(),yl(),(async()=>{try{nl.value={direct_children:12,total_children:45,active_members:38},il.value=[{id:1,name:"张三",avatar:"",agent_type:"individual",agent_type_text:"个人代理",created_at:new Date},{id:2,name:"李四",avatar:"",agent_type:"enterprise",agent_type_text:"企业代理",created_at:new Date(Date.now()-864e5)}]}catch(e){D.error("加载团队数据失败")}})(),wl()}),(e,D)=>{const E=a,z=t,M=n,Q=i,Z=u,wl=m,Cl=f,Ql=v,Sl=h,Ll=b,Rl=y,Tl=C,$l=w,Hl=V,Nl=x,Il=k,Wl=q,Xl=j;return L(),S("div",B,[R("div",G,[D[9]||(D[9]=R("div",{class:"header-left"},[R("h2",null,"代理商工作台"),R("p",{class:"page-description"},"管理您的推广业务，跟踪团队业绩，提升佣金收入")],-1)),R("div",J,[R("div",K,[T(E,{type:"primary"},{default:$(()=>[H(l(tl.value.agent_level_text),1)]),_:1}),T(E,{type:"success"},{default:$(()=>[H(l(tl.value.agent_type_text),1)]),_:1}),R("span",O,"编码: "+l(tl.value.agent_code),1)]),R("div",P,[T(M,{type:"info",onClick:D[0]||(D[0]=e=>ll.value=!0)},{default:$(()=>[T(z,null,{default:$(()=>[T(N(s))]),_:1}),D[8]||(D[8]=H(" 功能说明 ",-1))]),_:1,__:[8]})])])]),T(Z,{gutter:20,class:"stats-row"},{default:$(()=>[T(Q,{span:6},{default:$(()=>[T(F,{title:"推广用户数",value:sl.value.total_users||0,icon:"User",color:"#409EFF",trend:sl.value.user_growth_rate},null,8,["value","trend"])]),_:1}),T(Q,{span:6},{default:$(()=>[T(F,{title:"总佣金收入",value:sl.value.total_commission||0,icon:"Money",color:"#67C23A",prefix:"¥",trend:sl.value.commission_growth_rate},null,8,["value","trend"])]),_:1}),T(Q,{span:6},{default:$(()=>[T(F,{title:"本月佣金",value:sl.value.monthly_commission||0,icon:"Coin",color:"#E6A23C",prefix:"¥"},null,8,["value"])]),_:1}),T(Q,{span:6},{default:$(()=>[T(F,{title:"下级代理商",value:sl.value.child_agents_count||0,icon:"Connection",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),T(wl,{class:"quick-actions-card"},{header:$(()=>D[10]||(D[10]=[R("span",null,"快捷操作",-1)])),default:$(()=>[T(Z,{gutter:15},{default:$(()=>[T(Q,{span:4},{default:$(()=>[R("div",{class:"action-item",onClick:ql},[T(z,{class:"action-icon"},{default:$(()=>[T(N(o))]),_:1}),D[11]||(D[11]=R("span",null,"推广工具",-1))])]),_:1}),T(Q,{span:4},{default:$(()=>[R("div",{class:"action-item",onClick:Dl},[T(z,{class:"action-icon"},{default:$(()=>[T(N(r))]),_:1}),D[12]||(D[12]=R("span",null,"团队管理",-1))])]),_:1}),T(Q,{span:4},{default:$(()=>[R("div",{class:"action-item",onClick:Fl},[T(z,{class:"action-icon"},{default:$(()=>[T(N(d))]),_:1}),D[13]||(D[13]=R("span",null,"佣金中心",-1))])]),_:1}),T(Q,{span:4},{default:$(()=>[R("div",{class:"action-item",onClick:Al},[T(z,{class:"action-icon"},{default:$(()=>[T(N(c))]),_:1}),D[14]||(D[14]=R("span",null,"培训中心",-1))])]),_:1}),T(Q,{span:4},{default:$(()=>[R("div",{class:"action-item",onClick:Ul},[T(z,{class:"action-icon"},{default:$(()=>[T(N(p))]),_:1}),D[15]||(D[15]=R("span",null,"绩效分析",-1))])]),_:1}),T(Q,{span:4},{default:$(()=>[R("div",{class:"action-item",onClick:El},[T(z,{class:"action-icon"},{default:$(()=>[T(N(_))]),_:1}),D[16]||(D[16]=R("span",null,"账户设置",-1))])]),_:1})]),_:1})]),_:1}),T(Z,{gutter:20,class:"charts-row"},{default:$(()=>[T(Q,{span:12},{default:$(()=>[T(wl,null,{header:$(()=>[R("div",ee,[D[20]||(D[20]=R("span",null,"佣金收入趋势",-1)),T(Ql,{modelValue:Pe.value,"onUpdate:modelValue":D[1]||(D[1]=e=>Pe.value=e),size:"small",onChange:yl},{default:$(()=>[T(Cl,{label:"week"},{default:$(()=>D[17]||(D[17]=[H("近7天",-1)])),_:1,__:[17]}),T(Cl,{label:"month"},{default:$(()=>D[18]||(D[18]=[H("近30天",-1)])),_:1,__:[18]}),T(Cl,{label:"quarter"},{default:$(()=>D[19]||(D[19]=[H("近3个月",-1)])),_:1,__:[19]})]),_:1},8,["modelValue"])])]),default:$(()=>[T(A,{data:fl.value,options:bl,height:"300px"},null,8,["data"])]),_:1})]),_:1}),T(Q,{span:12},{default:$(()=>[T(wl,null,{header:$(()=>D[21]||(D[21]=[R("span",null,"用户来源分析",-1)])),default:$(()=>[T(U,{data:hl.value,options:gl,height:"300px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),T(Z,{gutter:20,class:"info-row"},{default:$(()=>[T(Q,{span:12},{default:$(()=>[T(wl,null,{header:$(()=>[R("div",le,[D[23]||(D[23]=R("span",null,"我的团队",-1)),T(M,{size:"small",onClick:Dl},{default:$(()=>D[22]||(D[22]=[H("查看全部",-1)])),_:1,__:[22]})])]),default:$(()=>[R("div",ae,[R("div",te,[R("div",se,[D[24]||(D[24]=R("span",{class:"label"},"直属下级:",-1)),R("span",ne,l(nl.value.direct_children||0),1)]),R("div",ie,[D[25]||(D[25]=R("span",{class:"label"},"团队总人数:",-1)),R("span",ue,l(nl.value.total_children||0),1)]),R("div",oe,[D[26]||(D[26]=R("span",{class:"label"},"活跃成员:",-1)),R("span",re,l(nl.value.active_members||0),1)])]),R("div",de,[D[27]||(D[27]=R("h4",null,"最新加入成员",-1)),(L(!0),S(I,null,W(il.value,e=>{return L(),S("div",{key:e.id,class:"member-item"},[T(Sl,{size:32,src:e.avatar},null,8,["src"]),R("div",ce,[R("div",pe,l(e.name),1),R("div",_e,l(zl(e.created_at)),1)]),T(E,{size:"small",type:(a=e.agent_type,{individual:"primary",enterprise:"success",channel:"warning"}[a]||"info")},{default:$(()=>[H(l(e.agent_type_text),1)]),_:2},1032,["type"])]);var a}),128))])])]),_:1})]),_:1}),T(Q,{span:12},{default:$(()=>[T(wl,null,{header:$(()=>[R("div",me,[D[29]||(D[29]=R("span",null,"最新动态",-1)),T(M,{size:"small",onClick:jl},{default:$(()=>D[28]||(D[28]=[H("刷新",-1)])),_:1,__:[28]})])]),default:$(()=>[R("div",ve,[(L(!0),S(I,null,W(ul.value,e=>{return L(),S("div",{key:e.id,class:"activity-item"},[R("div",fe,[T(z,{color:(a=e.type,{commission:"#67C23A",user:"#409EFF",team:"#E6A23C"}[a]||"#909399")},{default:$(()=>[(L(),X(Y(Ml(e.type))))]),_:2},1032,["color"])]),R("div",he,[R("div",be,l(e.title),1),R("div",ge,l(e.description),1),R("div",ye,l(zl(e.created_at)),1)])]);var a}),128))])]),_:1})]),_:1})]),_:1}),T(wl,{class:"promotion-card"},{header:$(()=>D[30]||(D[30]=[R("span",null,"推广链接快速生成",-1)])),default:$(()=>[T(Z,{gutter:20},{default:$(()=>[T(Q,{span:16},{default:$(()=>[T(Ll,{modelValue:ol.value,"onUpdate:modelValue":D[2]||(D[2]=e=>ol.value=e),placeholder:"您的专属推广链接",readonly:""},{prepend:$(()=>D[31]||(D[31]=[H("推广链接",-1)])),append:$(()=>[T(M,{onClick:kl},{default:$(()=>[T(z,null,{default:$(()=>[T(N(g))]),_:1}),D[32]||(D[32]=H(" 复制 ",-1))]),_:1,__:[32]})]),_:1},8,["modelValue"])]),_:1}),T(Q,{span:8},{default:$(()=>[T(M,{type:"primary",onClick:xl},{default:$(()=>[T(z,null,{default:$(()=>[T(N(_))]),_:1}),D[33]||(D[33]=H(" 生成二维码 ",-1))]),_:1,__:[33]}),T(M,{onClick:ql},{default:$(()=>[T(z,null,{default:$(()=>[T(N(_))]),_:1}),D[34]||(D[34]=H(" 更多工具 ",-1))]),_:1,__:[34]})]),_:1})]),_:1})]),_:1}),T(Rl,{modelValue:el.value,"onUpdate:modelValue":D[3]||(D[3]=e=>el.value=e),title:"推广二维码",width:"400px"},{default:$(()=>[R("div",we,[R("div",{ref_key:"qrCodeRef",ref:al,class:"qr-code"},null,512),R("div",Ce,[D[36]||(D[36]=R("p",null,"扫描二维码或分享链接进行推广",-1)),T(M,{type:"primary",onClick:Vl},{default:$(()=>D[35]||(D[35]=[H("下载二维码",-1)])),_:1,__:[35]})])])]),_:1},8,["modelValue"]),T(Rl,{modelValue:ll.value,"onUpdate:modelValue":D[7]||(D[7]=e=>ll.value=e),title:"代理商工作台功能说明",width:"1000px",class:"help-dialog"},{default:$(()=>[R("div",ke,[D[77]||(D[77]=R("div",{class:"help-section"},[R("h3",null,"🎯 功能概述"),R("p",null,"代理商工作台是您管理推广业务的核心平台，提供全面的数据统计、团队管理、佣金跟踪等功能，帮助您高效开展推广业务，最大化收益。")],-1)),R("div",xe,[D[43]||(D[43]=R("h3",null,"🚀 核心功能模块",-1)),T(Z,{gutter:20},{default:$(()=>[T(Q,{span:8},{default:$(()=>[R("div",Ve,[R("div",je,[T(z,null,{default:$(()=>[T(N(o))]),_:1})]),D[37]||(D[37]=R("div",{class:"feature-content"},[R("h4",null,"推广工具"),R("p",null,"专属推广链接、二维码生成、推广素材管理")],-1))])]),_:1}),T(Q,{span:8},{default:$(()=>[R("div",qe,[R("div",De,[T(z,null,{default:$(()=>[T(N(r))]),_:1})]),D[38]||(D[38]=R("div",{class:"feature-content"},[R("h4",null,"团队管理"),R("p",null,"下级代理商管理、团队业绩统计、层级关系维护")],-1))])]),_:1}),T(Q,{span:8},{default:$(()=>[R("div",Fe,[R("div",Ae,[T(z,null,{default:$(()=>[T(N(d))]),_:1})]),D[39]||(D[39]=R("div",{class:"feature-content"},[R("h4",null,"佣金中心"),R("p",null,"佣金收入统计、提现申请、收益明细查询")],-1))])]),_:1}),T(Q,{span:8},{default:$(()=>[R("div",Ue,[R("div",Ee,[T(z,null,{default:$(()=>[T(N(p))]),_:1})]),D[40]||(D[40]=R("div",{class:"feature-content"},[R("h4",null,"绩效分析"),R("p",null,"推广数据分析、转化率统计、业绩趋势图表")],-1))])]),_:1}),T(Q,{span:8},{default:$(()=>[R("div",ze,[R("div",Me,[T(z,null,{default:$(()=>[T(N(c))]),_:1})]),D[41]||(D[41]=R("div",{class:"feature-content"},[R("h4",null,"培训中心"),R("p",null,"推广技巧学习、产品知识培训、营销资料下载")],-1))])]),_:1}),T(Q,{span:8},{default:$(()=>[R("div",Qe,[R("div",Se,[T(z,null,{default:$(()=>[T(N(_))]),_:1})]),D[42]||(D[42]=R("div",{class:"feature-content"},[R("h4",null,"账户设置"),R("p",null,"个人信息管理、收款账户设置、通知偏好配置")],-1))])]),_:1})]),_:1})]),R("div",Le,[D[44]||(D[44]=R("h3",null,"🏆 代理商等级体系",-1)),T($l,{data:pl.value,style:{width:"100%"}},{default:$(()=>[T(Tl,{prop:"level",label:"等级",width:"100"},{default:$(({row:e})=>[T(E,{type:e.color},{default:$(()=>[H(l(e.level),1)]),_:2},1032,["type"])]),_:1}),T(Tl,{prop:"name",label:"等级名称",width:"120"}),T(Tl,{prop:"requirements",label:"升级条件"}),T(Tl,{prop:"commission_rate",label:"佣金比例",width:"100"}),T(Tl,{prop:"benefits",label:"专属权益"})]),_:1},8,["data"])]),R("div",Re,[D[56]||(D[56]=R("h3",null,"💰 佣金计算规则",-1)),R("div",Te,[R("div",$e,[D[48]||(D[48]=R("h4",null,"🔸 直推佣金",-1)),R("p",null,[D[45]||(D[45]=H("直接推广用户产生的订单，您可获得 ",-1)),R("strong",null,l(_l.value.direct)+"%",1),D[46]||(D[46]=H(" 的佣金",-1))]),R("div",He,[D[47]||(D[47]=R("span",{class:"example-label"},"示例：",-1)),H(" 用户通过您的链接购买100元产品，您获得"+l(_l.value.direct)+"元佣金 ",1)])]),R("div",Ne,[D[52]||(D[52]=R("h4",null,"🔸 团队佣金",-1)),R("p",null,[D[49]||(D[49]=H("下级代理商推广产生的订单，您可获得 ",-1)),R("strong",null,l(_l.value.team)+"%",1),D[50]||(D[50]=H(" 的团队佣金",-1))]),R("div",Ie,[D[51]||(D[51]=R("span",{class:"example-label"},"示例：",-1)),H(" 下级代理推广100元订单，您获得"+l(_l.value.team)+"元团队佣金 ",1)])]),R("div",We,[D[55]||(D[55]=R("h4",null,"🔸 层级佣金",-1)),R("p",null,[D[53]||(D[53]=H("支持多层级佣金分配，最多支持 ",-1)),R("strong",null,l(ml.value.max_levels),1),D[54]||(D[54]=H(" 级分佣",-1))]),T($l,{data:vl.value,size:"small",style:{"margin-top":"10px"}},{default:$(()=>[T(Tl,{prop:"level",label:"层级",width:"80"}),T(Tl,{prop:"rate",label:"佣金比例",width:"100"}),T(Tl,{prop:"description",label:"说明"})]),_:1},8,["data"])])])]),R("div",Xe,[D[64]||(D[64]=R("h3",null,"📈 推广技巧与建议",-1)),T(Il,{modelValue:rl.value,"onUpdate:modelValue":D[4]||(D[4]=e=>rl.value=e),type:"card"},{default:$(()=>[T(Nl,{label:"推广渠道",name:"channels"},{default:$(()=>[R("div",Ye,[D[58]||(D[58]=R("h4",null,"🌟 推荐推广渠道",-1)),D[59]||(D[59]=R("ul",null,[R("li",null,[R("strong",null,"社交媒体"),H("：微信朋友圈、QQ空间、微博等社交平台分享")]),R("li",null,[R("strong",null,"社群营销"),H("：微信群、QQ群、论坛等社群推广")]),R("li",null,[R("strong",null,"内容营销"),H("：撰写产品评测、使用心得等优质内容")]),R("li",null,[R("strong",null,"线下推广"),H("：朋友推荐、活动宣传等线下渠道")]),R("li",null,[R("strong",null,"短视频平台"),H("：抖音、快手等短视频平台推广")])],-1)),T(Hl,{type:"success",closable:!1,style:{"margin-top":"15px"}},{default:$(()=>D[57]||(D[57]=[H(" 💡 建议：多渠道组合推广，提高覆盖面和转化率 ",-1)])),_:1,__:[57]})])]),_:1}),T(Nl,{label:"推广话术",name:"scripts"},{default:$(()=>D[60]||(D[60]=[R("div",{class:"tips-content"},[R("h4",null,"💬 推广话术模板"),R("div",{class:"script-item"},[R("h5",null,"朋友圈推广"),R("div",{class:"script-text"},[H(' "发现一个不错的平台，可以通过推广赚取佣金💰'),R("br"),H(" 产品质量有保障，佣金结算及时✅"),R("br"),H(" 感兴趣的朋友可以了解一下👇"),R("br"),H(' [推广链接]" ')])]),R("div",{class:"script-item"},[R("h5",null,"私聊推广"),R("div",{class:"script-text"},[H(' "Hi，最近在做一个项目，产品不错，佣金也挺可观的。'),R("br"),H(" 如果你有兴趣了解或者想要产品的话，可以通过我的链接购买，"),R("br"),H(" 这样我也能获得一些佣金收入😊"),R("br"),H(' 链接：[推广链接]" ')])])],-1)])),_:1,__:[60]}),T(Nl,{label:"注意事项",name:"notes"},{default:$(()=>[R("div",Ze,[D[62]||(D[62]=R("h4",null,"⚠️ 推广注意事项",-1)),D[63]||(D[63]=R("ul",null,[R("li",null,[R("strong",null,"诚信推广"),H("：如实介绍产品特点，不夸大宣传")]),R("li",null,[R("strong",null,"合规操作"),H("：遵守平台规则，不进行违规推广")]),R("li",null,[R("strong",null,"用户体验"),H("：关注用户反馈，提供优质服务")]),R("li",null,[R("strong",null,"持续学习"),H("：关注产品更新，学习推广技巧")]),R("li",null,[R("strong",null,"数据分析"),H("：定期分析推广数据，优化推广策略")])],-1)),T(Hl,{type:"warning",closable:!1,style:{"margin-top":"15px"}},{default:$(()=>D[61]||(D[61]=[H(" ⚠️ 警告：严禁虚假宣传、恶意刷单等违规行为，一经发现将取消代理资格 ",-1)])),_:1,__:[61]})])]),_:1})]),_:1},8,["modelValue"])]),R("div",Be,[D[71]||(D[71]=R("h3",null,"📝 操作指南",-1)),T(Xl,{modelValue:dl.value,"onUpdate:modelValue":D[5]||(D[5]=e=>dl.value=e)},{default:$(()=>[T(Wl,{title:"如何生成推广链接？",name:"link-generation"},{default:$(()=>[R("div",Ge,[D[66]||(D[66]=R("ol",null,[R("li",null,'在工作台下方找到"推广链接快速生成"区域'),R("li",null,"系统会自动生成您的专属推广链接"),R("li",null,'点击"复制"按钮复制链接到剪贴板'),R("li",null,'也可以点击"生成二维码"创建推广二维码'),R("li",null,"将链接或二维码分享给潜在用户")],-1)),T(Hl,{type:"info",closable:!1},{default:$(()=>D[65]||(D[65]=[H(" 💡 提示：推广链接包含您的专属代理编码，用户通过此链接注册购买，您将获得相应佣金 ",-1)])),_:1,__:[65]})])]),_:1}),T(Wl,{title:"如何查看佣金收入？",name:"commission-check"},{default:$(()=>[R("div",Je,[D[68]||(D[68]=R("ol",null,[R("li",null,"在统计卡片中查看总佣金收入和本月佣金"),R("li",null,'点击"佣金中心"查看详细的佣金明细'),R("li",null,"在佣金收入趋势图中查看收入变化"),R("li",null,"可以按时间段筛选查看不同期间的收入")],-1)),T(Hl,{type:"success",closable:!1},{default:$(()=>D[67]||(D[67]=[H(" ✅ 说明：佣金每日结算，T+1到账，可在佣金中心申请提现 ",-1)])),_:1,__:[67]})])]),_:1}),T(Wl,{title:"如何管理我的团队？",name:"team-management"},{default:$(()=>[R("div",Ke,[D[70]||(D[70]=R("ol",null,[R("li",null,'在"我的团队"卡片中查看团队概况'),R("li",null,'点击"团队管理"进入详细的团队管理页面'),R("li",null,"可以查看下级代理商的业绩和状态"),R("li",null,"为团队成员提供培训和指导"),R("li",null,"关注团队成员的推广数据和收益情况")],-1)),T(Hl,{type:"info",closable:!1},{default:$(()=>D[69]||(D[69]=[H(" 💡 建议：定期与团队成员沟通，分享推广经验，共同提升业绩 ",-1)])),_:1,__:[69]})])]),_:1})]),_:1},8,["modelValue"])]),R("div",Oe,[D[76]||(D[76]=R("h3",null,"❓ 常见问题",-1)),T(Xl,{modelValue:cl.value,"onUpdate:modelValue":D[6]||(D[6]=e=>cl.value=e)},{default:$(()=>[T(Wl,{title:"佣金什么时候到账？",name:"faq1"},{default:$(()=>D[72]||(D[72]=[R("p",null,"佣金采用T+1结算模式，即今日产生的佣金将在明日到账。您可以在佣金中心查看详细的结算记录。",-1)])),_:1,__:[72]}),T(Wl,{title:"如何提升代理商等级？",name:"faq2"},{default:$(()=>D[73]||(D[73]=[R("p",null,"代理商等级根据您的推广业绩自动评定，包括推广用户数、团队规模、佣金收入等指标。持续推广并发展团队即可提升等级。",-1)])),_:1,__:[73]}),T(Wl,{title:"推广链接有有效期吗？",name:"faq3"},{default:$(()=>D[74]||(D[74]=[R("p",null,"推广链接长期有效，但建议定期更新推广素材。如果您的代理资格发生变化，系统会自动更新链接状态。",-1)])),_:1,__:[74]}),T(Wl,{title:"可以同时推广多个产品吗？",name:"faq4"},{default:$(()=>D[75]||(D[75]=[R("p",null,"可以的。您可以推广平台上的所有产品，每个产品的佣金比例可能不同，具体以产品页面显示为准。",-1)])),_:1,__:[75]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-4ae2df16"]]);export{Pe as default};
