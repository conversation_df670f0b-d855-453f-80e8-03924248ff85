<template>
  <div class="content-template-library">
    <el-card class="library-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><Collection /></el-icon>
            内容模板库
          </span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新建模板
            </el-button>
          </div>
        </div>
      </template>

      <!-- 模板分类和筛选 -->
      <div class="template-filters">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-select v-model="filterCategory" placeholder="选择分类" clearable>
              <el-option label="全部分类" value="" />
              <el-option label="群组标题" value="title" />
              <el-option label="群组描述" value="description" />
              <el-option label="FAQ问答" value="faq" />
              <el-option label="用户评论" value="reviews" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select v-model="filterIndustry" placeholder="选择行业" clearable>
              <el-option label="全部行业" value="" />
              <el-option label="互联网/科技" value="tech" />
              <el-option label="金融/投资" value="finance" />
              <el-option label="教育/培训" value="education" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索模板..."
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 模板列表 -->
      <div class="template-list">
        <el-row :gutter="20">
          <el-col 
            :span="8" 
            v-for="template in filteredTemplates" 
            :key="template.id"
          >
            <div class="template-card" @click="selectTemplate(template)">
              <div class="template-header">
                <el-tag :type="getCategoryTagType(template.category)" size="small">
                  {{ getCategoryLabel(template.category) }}
                </el-tag>
              </div>
              
              <div class="template-content">
                <h4 class="template-title">{{ template.title }}</h4>
                <p class="template-preview">{{ template.content.substring(0, 100) }}...</p>
              </div>
              
              <div class="template-footer">
                <span class="usage-count">使用 {{ template.usageCount }} 次</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Collection, Plus, Search } from '@element-plus/icons-vue'

const emit = defineEmits(['template-selected'])

// 响应式数据
const filterCategory = ref('')
const filterIndustry = ref('')
const searchKeyword = ref('')

// 模板数据
const templates = ref([
  {
    id: 1,
    title: '互联网技术交流群标题模板',
    category: 'title',
    industry: 'tech',
    content: '{city}程序员技术交流群 - 汇聚IT精英，分享前沿技术',
    usageCount: 156
  }
])

// 计算属性
const filteredTemplates = computed(() => {
  return templates.value.filter(template => {
    const categoryMatch = !filterCategory.value || template.category === filterCategory.value
    const industryMatch = !filterIndustry.value || template.industry === filterIndustry.value
    const keywordMatch = !searchKeyword.value || 
      template.title.includes(searchKeyword.value) ||
      template.content.includes(searchKeyword.value)
    
    return categoryMatch && industryMatch && keywordMatch
  })
})

// 方法
const selectTemplate = (template) => {
  emit('template-selected', template)
  ElMessage.success(`已选择模板：${template.title}`)
}

const showCreateDialog = () => {
  ElMessage.info('创建模板功能开发中')
}

const getCategoryTagType = (category) => {
  const types = {
    title: 'primary',
    description: 'success',
    faq: 'warning',
    reviews: 'info'
  }
  return types[category] || ''
}

const getCategoryLabel = (category) => {
  const labels = {
    title: '标题',
    description: '描述',
    faq: 'FAQ',
    reviews: '评论'
  }
  return labels[category] || category
}
</script>

<style lang="scss" scoped>
.content-template-library {
  .library-card {
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
  
  .template-filters {
    margin-bottom: 24px;
  }
  
  .template-list {
    .template-card {
      padding: 16px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 16px;
      
      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        transform: translateY(-2px);
      }
      
      .template-header {
        margin-bottom: 12px;
      }
      
      .template-content {
        margin-bottom: 12px;
        
        .template-title {
          margin: 0 0 8px 0;
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
        }
        
        .template-preview {
          margin: 0;
          font-size: 12px;
          color: #64748b;
          line-height: 1.5;
        }
      }
      
      .template-footer {
        .usage-count {
          font-size: 12px;
          color: #94a3b8;
        }
      }
    }
  }
}
</style>