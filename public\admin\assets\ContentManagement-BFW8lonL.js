import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css               */import{r as a,L as l,d as t,y as o,l as s,z as n,E as u,D as d,t as i,c,k as r,B as m,F as p,Y as v,u as _,e as f,a2 as y,A as b}from"./vue-vendor-DGsK9sC4.js";import{bc as g,bd as h,aH as w,aW as V,aV as k,bo as x,bp as C,be as U,as as j,U as T,au as z,Q as D,bM as I,bN as B,b8 as A,b4 as S,b2 as Y,T as $,aa as q,aM as O,ab as M,bi as H,b1 as P,bg as L,bh as G,bl as Q,R,am as W,Y as E,b6 as F,b7 as K,av as N,aO as X,ax as J,ay as Z,bj as ee,bk as ae,b3 as le}from"./element-plus-DcSKpKA8.js";import{S as te}from"./StatCard-WpSR56Tk.js";/* empty css                     *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                             *//* empty css                       *//* empty css                          */import{g as oe,a as se,d as ne,u as ue}from"./content-BaH0vSdJ.js";import{f as de}from"./format-3eU4VJ9V.js";import"./utils-4VKArNEK.js";/* empty css                                                                 */const ie={class:"dialog-footer"},ce=e({__name:"ContentDialog",props:{modelValue:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:c}){const r=e,m=c,p=a(!1),v=a(!1),_=a(),f=a(!1),y=l({id:null,title:"",type:"",content:"",keywords:"",status:"active",sort:0}),b={title:[{required:!0,message:"请输入内容标题",trigger:"blur"},{min:2,max:100,message:"标题长度在 2 到 100 个字符",trigger:"blur"}],type:[{required:!0,message:"请选择内容类型",trigger:"change"}],content:[{required:!0,message:"请输入内容",trigger:"blur"},{min:10,max:2e3,message:"内容长度在 10 到 2000 个字符",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]};t(()=>r.modelValue,e=>{p.value=e,e&&I()}),t(p,e=>{m("update:modelValue",e)});const I=()=>{r.data&&r.data.id?(f.value=!0,Object.assign(y,r.data)):(f.value=!1,B())},B=()=>{Object.assign(y,{id:null,title:"",type:"",content:"",keywords:"",status:"active",sort:0}),_.value?.clearValidate()},A=async()=>{try{await _.value.validate(),v.value=!0,await new Promise(e=>setTimeout(e,1e3)),D.success(f.value?"更新成功":"创建成功"),m("success"),S()}catch(e){console.error("提交失败:",e)}finally{v.value=!1}},S=()=>{p.value=!1,B()};return(e,a)=>{const l=w,t=h,c=k,r=V,m=C,D=x,I=U,B=g,Y=j,$=z;return s(),o($,{modelValue:p.value,"onUpdate:modelValue":a[6]||(a[6]=e=>p.value=e),title:f.value?"编辑内容":"创建内容",width:"800px","before-close":S},{footer:n(()=>[i("div",ie,[u(Y,{onClick:S},{default:n(()=>a[10]||(a[10]=[d("取消",-1)])),_:1,__:[10]}),u(Y,{type:"primary",loading:v.value,onClick:A},{default:n(()=>[d(T(f.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:n(()=>[u(B,{ref_key:"formRef",ref:_,model:y,rules:b,"label-width":"100px"},{default:n(()=>[u(t,{label:"内容标题",prop:"title"},{default:n(()=>[u(l,{modelValue:y.title,"onUpdate:modelValue":a[0]||(a[0]=e=>y.title=e),placeholder:"请输入内容标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),u(t,{label:"内容类型",prop:"type"},{default:n(()=>[u(r,{modelValue:y.type,"onUpdate:modelValue":a[1]||(a[1]=e=>y.type=e),placeholder:"请选择内容类型",style:{width:"100%"}},{default:n(()=>[u(c,{label:"群组公告",value:"announcement"}),u(c,{label:"欢迎语",value:"welcome"}),u(c,{label:"群规则",value:"rules"}),u(c,{label:"推广文案",value:"promotion"}),u(c,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),u(t,{label:"内容",prop:"content"},{default:n(()=>[u(l,{modelValue:y.content,"onUpdate:modelValue":a[2]||(a[2]=e=>y.content=e),type:"textarea",rows:8,placeholder:"请输入内容",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])]),_:1}),u(t,{label:"关键词",prop:"keywords"},{default:n(()=>[u(l,{modelValue:y.keywords,"onUpdate:modelValue":a[3]||(a[3]=e=>y.keywords=e),placeholder:"请输入关键词，多个关键词用逗号分隔",maxlength:"200"},null,8,["modelValue"])]),_:1}),u(t,{label:"状态",prop:"status"},{default:n(()=>[u(D,{modelValue:y.status,"onUpdate:modelValue":a[4]||(a[4]=e=>y.status=e)},{default:n(()=>[u(m,{label:"active"},{default:n(()=>a[7]||(a[7]=[d("启用",-1)])),_:1,__:[7]}),u(m,{label:"inactive"},{default:n(()=>a[8]||(a[8]=[d("禁用",-1)])),_:1,__:[8]}),u(m,{label:"draft"},{default:n(()=>a[9]||(a[9]=[d("草稿",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1}),u(t,{label:"排序",prop:"sort"},{default:n(()=>[u(I,{modelValue:y.sort,"onUpdate:modelValue":a[5]||(a[5]=e=>y.sort=e),min:0,max:999,placeholder:"排序值"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-8df54393"]]),re={key:0,class:"preview-container"},me={class:"info-section"},pe={class:"content-section"},ve={class:"content-preview"},_e={class:"content-text"},fe={key:0,class:"keywords-section"},ye={class:"keywords-container"},be={class:"stats-section"},ge={class:"stat-item"},he={class:"stat-value"},we={class:"stat-item"},Ve={class:"stat-value"},ke={class:"stat-item"},xe={class:"stat-value"},Ce={class:"dialog-footer"},Ue=e({__name:"ContentPreviewDialog",props:{modelValue:{type:Boolean,default:!1},data:{type:Object,default:()=>({})}},emits:["update:modelValue","edit"],setup(e,{emit:l}){const _=e,f=l,y=a(!1),b=c(()=>_.data.keywords?_.data.keywords.split(",").map(e=>e.trim()).filter(e=>e):[]);t(()=>_.modelValue,e=>{y.value=e}),t(y,e=>{f("update:modelValue",e)});const g=e=>({announcement:"群组公告",welcome:"欢迎语",rules:"群规则",promotion:"推广文案",other:"其他"}[e]||"未知"),h=e=>({active:"启用",inactive:"禁用",draft:"草稿"}[e]||"未知"),w=()=>{y.value=!1},V=()=>{f("edit",_.data),w()},k=async()=>{try{await navigator.clipboard.writeText(_.data.content),D.success("内容已复制到剪贴板")}catch(e){D.error("复制失败，请手动复制")}};return(a,l)=>{const t=B,c=A,_=I,f=Y,x=S,C=j,U=z;return s(),o(U,{modelValue:y.value,"onUpdate:modelValue":l[0]||(l[0]=e=>y.value=e),title:"内容预览",width:"700px","before-close":w},{footer:n(()=>[i("div",Ce,[u(C,{onClick:w},{default:n(()=>l[8]||(l[8]=[d("关闭",-1)])),_:1,__:[8]}),u(C,{type:"primary",onClick:V},{default:n(()=>l[9]||(l[9]=[d("编辑",-1)])),_:1,__:[9]}),u(C,{type:"success",onClick:k},{default:n(()=>l[10]||(l[10]=[d("复制内容",-1)])),_:1,__:[10]})])]),default:n(()=>[e.data?(s(),r("div",re,[i("div",me,[l[1]||(l[1]=i("h3",{class:"section-title"},"基本信息",-1)),u(_,{column:2,border:""},{default:n(()=>[u(t,{label:"标题"},{default:n(()=>[d(T(e.data.title),1)]),_:1}),u(t,{label:"类型"},{default:n(()=>{return[u(c,{type:(a=e.data.type,{announcement:"danger",welcome:"success",rules:"warning",promotion:"primary",other:"info"}[a]||"info")},{default:n(()=>[d(T(g(e.data.type)),1)]),_:1},8,["type"])];var a}),_:1}),u(t,{label:"状态"},{default:n(()=>{return[u(c,{type:(a=e.data.status,{active:"success",inactive:"danger",draft:"warning"}[a]||"info")},{default:n(()=>[d(T(h(e.data.status)),1)]),_:1},8,["type"])];var a}),_:1}),u(t,{label:"排序"},{default:n(()=>[d(T(e.data.sort),1)]),_:1}),u(t,{label:"创建时间"},{default:n(()=>[d(T(e.data.created_at),1)]),_:1}),u(t,{label:"更新时间"},{default:n(()=>[d(T(e.data.updated_at),1)]),_:1})]),_:1})]),i("div",pe,[l[2]||(l[2]=i("h3",{class:"section-title"},"内容预览",-1)),i("div",ve,[i("div",_e,T(e.data.content),1)])]),e.data.keywords?(s(),r("div",fe,[l[3]||(l[3]=i("h3",{class:"section-title"},"关键词",-1)),i("div",ye,[(s(!0),r(p,null,v(b.value,e=>(s(),o(c,{key:e,class:"keyword-tag",size:"small"},{default:n(()=>[d(T(e),1)]),_:2},1024))),128))])])):m("",!0),i("div",be,[l[7]||(l[7]=i("h3",{class:"section-title"},"使用统计",-1)),u(x,{gutter:20},{default:n(()=>[u(f,{span:8},{default:n(()=>[i("div",ge,[i("div",he,T(e.data.view_count||0),1),l[4]||(l[4]=i("div",{class:"stat-label"},"浏览次数",-1))])]),_:1}),u(f,{span:8},{default:n(()=>[i("div",we,[i("div",Ve,T(e.data.use_count||0),1),l[5]||(l[5]=i("div",{class:"stat-label"},"使用次数",-1))])]),_:1}),u(f,{span:8},{default:n(()=>[i("div",ke,[i("div",xe,T(e.data.group_count||0),1),l[6]||(l[6]=i("div",{class:"stat-label"},"应用群组",-1))])]),_:1})]),_:1})])])):m("",!0)]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-dc1502b8"]]),je={class:"ai-generate-container"},Te={class:"config-section"},ze={class:"generate-section"},De={key:0,class:"result-section"},Ie={class:"result-container"},Be={class:"result-actions"},Ae={key:1,class:"history-section"},Se={class:"history-list"},Ye=["onClick"],$e={class:"history-header"},qe={class:"history-type"},Oe={class:"history-time"},Me={class:"history-content"},He={class:"dialog-footer"},Pe=e({__name:"AIGenerateDialog",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","use-content"],setup(e,{emit:c}){const f=e,y=c,b=a(!1),x=a(!1),C=a(""),U=a([]),I=l({type:"",style:"friendly",length:"medium",keywords:"",requirements:""});t(()=>f.modelValue,e=>{b.value=e}),t(b,e=>{y("update:modelValue",e)});const B=async()=>{x.value=!0;try{await new Promise(e=>setTimeout(e,2e3));const e={announcement:"亲爱的群友们，为了维护良好的群聊环境，请大家遵守以下规定...",welcome:"欢迎新朋友加入我们的大家庭！在这里你可以...",rules:"群规则：1. 禁止发布广告信息 2. 保持友善交流 3. 尊重他人...",promotion:"限时优惠！现在加入我们的平台，享受专属福利...",other:"这是一段由AI生成的内容，根据您的要求定制..."};C.value=e[I.type]||"生成的内容将显示在这里",U.value.unshift({type:I.type,style:I.style,length:I.length,content:C.value,time:(new Date).toLocaleString()}),U.value.length>10&&(U.value=U.value.slice(0,10)),D.success("内容生成成功")}catch(e){D.error("生成失败，请重试")}finally{x.value=!1}},A=()=>{B()},S=async()=>{try{await navigator.clipboard.writeText(C.value),D.success("内容已复制到剪贴板")}catch(e){D.error("复制失败，请手动复制")}},Y=()=>{C.value?(y("use-content",{content:C.value,type:I.type,keywords:I.keywords}),P()):D.warning("请先生成内容")},P=()=>{b.value=!1,C.value="",Object.assign(I,{type:"",style:"friendly",length:"medium",keywords:"",requirements:""})};return(e,a)=>{const l=k,t=V,c=h,f=w,y=g,D=$,L=j,G=z;return s(),o(G,{modelValue:b.value,"onUpdate:modelValue":a[6]||(a[6]=e=>b.value=e),title:"AI内容生成",width:"800px","before-close":P},{footer:n(()=>[i("div",He,[u(L,{onClick:P},{default:n(()=>a[13]||(a[13]=[d("取消",-1)])),_:1,__:[13]}),C.value?(s(),o(L,{key:0,type:"primary",onClick:Y},{default:n(()=>a[14]||(a[14]=[d(" 使用内容 ",-1)])),_:1,__:[14]})):m("",!0)])]),default:n(()=>[i("div",je,[i("div",Te,[a[7]||(a[7]=i("h3",{class:"section-title"},"生成配置",-1)),u(y,{model:I,"label-width":"120px"},{default:n(()=>[u(c,{label:"内容类型"},{default:n(()=>[u(t,{modelValue:I.type,"onUpdate:modelValue":a[0]||(a[0]=e=>I.type=e),placeholder:"请选择内容类型",style:{width:"100%"}},{default:n(()=>[u(l,{label:"群组公告",value:"announcement"}),u(l,{label:"欢迎语",value:"welcome"}),u(l,{label:"群规则",value:"rules"}),u(l,{label:"推广文案",value:"promotion"}),u(l,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),u(c,{label:"内容风格"},{default:n(()=>[u(t,{modelValue:I.style,"onUpdate:modelValue":a[1]||(a[1]=e=>I.style=e),placeholder:"请选择内容风格",style:{width:"100%"}},{default:n(()=>[u(l,{label:"正式",value:"formal"}),u(l,{label:"友好",value:"friendly"}),u(l,{label:"活泼",value:"lively"}),u(l,{label:"专业",value:"professional"}),u(l,{label:"简洁",value:"concise"})]),_:1},8,["modelValue"])]),_:1}),u(c,{label:"内容长度"},{default:n(()=>[u(t,{modelValue:I.length,"onUpdate:modelValue":a[2]||(a[2]=e=>I.length=e),placeholder:"请选择内容长度",style:{width:"100%"}},{default:n(()=>[u(l,{label:"简短 (50-100字)",value:"short"}),u(l,{label:"中等 (100-300字)",value:"medium"}),u(l,{label:"详细 (300-500字)",value:"long"})]),_:1},8,["modelValue"])]),_:1}),u(c,{label:"关键词"},{default:n(()=>[u(f,{modelValue:I.keywords,"onUpdate:modelValue":a[3]||(a[3]=e=>I.keywords=e),placeholder:"请输入关键词，多个关键词用逗号分隔",maxlength:"200"},null,8,["modelValue"])]),_:1}),u(c,{label:"特殊要求"},{default:n(()=>[u(f,{modelValue:I.requirements,"onUpdate:modelValue":a[4]||(a[4]=e=>I.requirements=e),type:"textarea",rows:3,placeholder:"请输入特殊要求或补充说明",maxlength:"500"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),i("div",ze,[u(L,{type:"primary",size:"large",loading:x.value,onClick:B,disabled:!I.type},{default:n(()=>[u(D,null,{default:n(()=>[u(_(q))]),_:1}),d(" "+T(x.value?"生成中...":"开始生成"),1)]),_:1},8,["loading","disabled"])]),C.value?(s(),r("div",De,[a[11]||(a[11]=i("h3",{class:"section-title"},"生成结果",-1)),i("div",Ie,[u(f,{modelValue:C.value,"onUpdate:modelValue":a[5]||(a[5]=e=>C.value=e),type:"textarea",rows:8,placeholder:"生成的内容将显示在这里",readonly:""},null,8,["modelValue"]),i("div",Be,[u(L,{onClick:A,loading:x.value},{default:n(()=>[u(D,null,{default:n(()=>[u(_(O))]),_:1}),a[8]||(a[8]=d(" 重新生成 ",-1))]),_:1,__:[8]},8,["loading"]),u(L,{type:"success",onClick:S},{default:n(()=>[u(D,null,{default:n(()=>[u(_(M))]),_:1}),a[9]||(a[9]=d(" 复制内容 ",-1))]),_:1,__:[9]}),u(L,{type:"primary",onClick:Y},{default:n(()=>[u(D,null,{default:n(()=>[u(_(H))]),_:1}),a[10]||(a[10]=d(" 使用此内容 ",-1))]),_:1,__:[10]})])])])):m("",!0),U.value.length>0?(s(),r("div",Ae,[a[12]||(a[12]=i("h3",{class:"section-title"},"生成历史",-1)),i("div",Se,[(s(!0),r(p,null,v(U.value,(e,a)=>{return s(),r("div",{key:a,class:"history-item",onClick:a=>(e=>{C.value=e.content,I.type=e.type,I.style=e.style,I.length=e.length})(e)},[i("div",$e,[i("span",qe,T((l=e.type,{announcement:"群组公告",welcome:"欢迎语",rules:"群规则",promotion:"推广文案",other:"其他"}[l]||"未知")),1),i("span",Oe,T(e.time),1)]),i("div",Me,T(e.content.substring(0,100))+"...",1)],8,Ye);var l}),128))])])):m("",!0)])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-0da8c789"]]),Le={class:"apply-container"},Ge={class:"content-info"},Qe={class:"content-preview"},Re={class:"preview-text"},We={class:"group-selection"},Ee={class:"filter-bar"},Fe={class:"group-list"},Ke={class:"group-info"},Ne={class:"group-header"},Xe={class:"group-name"},Je={class:"group-details"},Ze={class:"detail-item"},ea={class:"detail-item"},aa={class:"detail-item"},la={class:"selection-stats"},ta={class:"apply-settings"},oa={class:"dialog-footer"},sa=e({__name:"ApplyToGroupDialog",props:{modelValue:{type:Boolean,default:!1},contentData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:f}){const y=e,b=f,U=a(!1),S=a(!1),Y=a(""),q=a(""),O=a([]),M=l({method:"replace",timing:"immediate",scheduledTime:"",notifications:["notify_admin"]}),H=a([{id:1,name:"产品推广群A",status:"active",member_count:156,type:"推广群",created_at:"2024-01-15"},{id:2,name:"客户服务群",status:"active",member_count:89,type:"服务群",created_at:"2024-01-10"},{id:3,name:"内部交流群",status:"paused",member_count:45,type:"内部群",created_at:"2024-01-08"}]),W=c(()=>{let e=H.value;return Y.value&&(e=e.filter(e=>e.name.toLowerCase().includes(Y.value.toLowerCase()))),q.value&&(e=e.filter(e=>e.status===q.value)),e});t(()=>y.modelValue,e=>{U.value=e,e&&X()}),t(U,e=>{b("update:modelValue",e)});const E=e=>({announcement:"群组公告",welcome:"欢迎语",rules:"群规则",promotion:"推广文案",other:"其他"}[e]||"未知"),F=()=>{O.value=W.value.map(e=>e.id)},K=()=>{O.value=[]},N=async()=>{if(0!==O.value.length)try{await R.confirm(`确定要将内容应用到 ${O.value.length} 个群组吗？`,"应用确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),S.value=!0,await new Promise(e=>setTimeout(e,2e3)),D.success(`成功应用到 ${O.value.length} 个群组`),b("success"),J()}catch{D.info("已取消应用")}finally{S.value=!1}else D.warning("请选择至少一个群组")},X=()=>{O.value=[],Y.value="",q.value="",Object.assign(M,{method:"replace",timing:"immediate",scheduledTime:"",notifications:["notify_admin"]})},J=()=>{U.value=!1,X()};return(a,l)=>{const t=B,c=A,f=I,y=$,b=w,D=k,H=V,R=j,X=G,Z=L,ee=C,ae=x,le=h,te=Q,oe=g,se=z;return s(),o(se,{modelValue:U.value,"onUpdate:modelValue":l[7]||(l[7]=e=>U.value=e),title:"应用到群组",width:"700px","before-close":J},{footer:n(()=>[i("div",oa,[u(R,{onClick:J},{default:n(()=>l[20]||(l[20]=[d("取消",-1)])),_:1,__:[20]}),u(R,{type:"primary",loading:S.value,disabled:0===O.value.length,onClick:N},{default:n(()=>[d(" 应用到群组 ("+T(O.value.length)+") ",1)]),_:1},8,["loading","disabled"])])]),default:n(()=>[i("div",Le,[i("div",Ge,[l[9]||(l[9]=i("h3",{class:"section-title"},"内容信息",-1)),u(f,{column:2,border:""},{default:n(()=>[u(t,{label:"标题"},{default:n(()=>[d(T(e.contentData.title),1)]),_:1}),u(t,{label:"类型"},{default:n(()=>{return[u(c,{type:(a=e.contentData.type,{announcement:"danger",welcome:"success",rules:"warning",promotion:"primary",other:"info"}[a]||"info")},{default:n(()=>[d(T(E(e.contentData.type)),1)]),_:1},8,["type"])];var a}),_:1})]),_:1}),i("div",Qe,[l[8]||(l[8]=i("div",{class:"preview-label"},"内容预览：",-1)),i("div",Re,T(e.contentData.content),1)])]),i("div",We,[l[12]||(l[12]=i("h3",{class:"section-title"},"选择群组",-1)),i("div",Ee,[u(b,{modelValue:Y.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Y.value=e),placeholder:"搜索群组名称",style:{width:"200px"},clearable:""},{prefix:n(()=>[u(y,null,{default:n(()=>[u(_(P))]),_:1})]),_:1},8,["modelValue"]),u(H,{modelValue:q.value,"onUpdate:modelValue":l[1]||(l[1]=e=>q.value=e),placeholder:"群组状态",style:{width:"120px"},clearable:""},{default:n(()=>[u(D,{label:"全部",value:""}),u(D,{label:"活跃",value:"active"}),u(D,{label:"暂停",value:"paused"})]),_:1},8,["modelValue"]),u(R,{onClick:F},{default:n(()=>l[10]||(l[10]=[d("全选",-1)])),_:1,__:[10]}),u(R,{onClick:K},{default:n(()=>l[11]||(l[11]=[d("取消全选",-1)])),_:1,__:[11]})]),i("div",Fe,[u(Z,{modelValue:O.value,"onUpdate:modelValue":l[2]||(l[2]=e=>O.value=e)},{default:n(()=>[(s(!0),r(p,null,v(W.value,e=>(s(),r("div",{key:e.id,class:"group-item"},[u(X,{label:e.id,class:"group-checkbox"},{default:n(()=>[i("div",Ke,[i("div",Ne,[i("span",Xe,T(e.name),1),u(c,{type:"active"===e.status?"success":"warning",size:"small"},{default:n(()=>[d(T("active"===e.status?"活跃":"暂停"),1)]),_:2},1032,["type"])]),i("div",Je,[i("span",Ze,"成员: "+T(e.member_count),1),i("span",ea,"类型: "+T(e.type),1),i("span",aa,"创建: "+T(e.created_at),1)])])]),_:2},1032,["label"])]))),128))]),_:1},8,["modelValue"])]),i("div",la,[i("span",null,"已选择 "+T(O.value.length)+" 个群组",1)])]),i("div",ta,[l[19]||(l[19]=i("h3",{class:"section-title"},"应用设置",-1)),u(oe,{model:M,"label-width":"120px"},{default:n(()=>[u(le,{label:"应用方式"},{default:n(()=>[u(ae,{modelValue:M.method,"onUpdate:modelValue":l[3]||(l[3]=e=>M.method=e)},{default:n(()=>[u(ee,{label:"replace"},{default:n(()=>l[13]||(l[13]=[d("替换现有内容",-1)])),_:1,__:[13]}),u(ee,{label:"append"},{default:n(()=>l[14]||(l[14]=[d("追加到现有内容",-1)])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1}),u(le,{label:"生效时间"},{default:n(()=>[u(ae,{modelValue:M.timing,"onUpdate:modelValue":l[4]||(l[4]=e=>M.timing=e)},{default:n(()=>[u(ee,{label:"immediate"},{default:n(()=>l[15]||(l[15]=[d("立即生效",-1)])),_:1,__:[15]}),u(ee,{label:"scheduled"},{default:n(()=>l[16]||(l[16]=[d("定时生效",-1)])),_:1,__:[16]})]),_:1},8,["modelValue"])]),_:1}),"scheduled"===M.timing?(s(),o(le,{key:0,label:"定时时间"},{default:n(()=>[u(te,{modelValue:M.scheduledTime,"onUpdate:modelValue":l[5]||(l[5]=e=>M.scheduledTime=e),type:"datetime",placeholder:"选择生效时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})):m("",!0),u(le,{label:"通知设置"},{default:n(()=>[u(Z,{modelValue:M.notifications,"onUpdate:modelValue":l[6]||(l[6]=e=>M.notifications=e)},{default:n(()=>[u(X,{label:"notify_admin"},{default:n(()=>l[17]||(l[17]=[d("通知管理员",-1)])),_:1,__:[17]}),u(X,{label:"notify_members"},{default:n(()=>l[18]||(l[18]=[d("通知群成员",-1)])),_:1,__:[18]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-8b105a28"]]),na={class:"app-container"},ua={class:"filter-container"},da={class:"card-header"},ia={class:"content-title"},ca=["onClick"],ra={class:"usage-count"},ma={class:"pagination-container"},pa=e({__name:"ContentManagement",setup(e){const t=a([]),c=a(0),p=a(!0),v=a(!1),g=a(!1),h=a(!1),x=a(!1),C=a({}),U=a([]),z=a({total_contents:0,active_contents:0,ai_generated:0,total_views:0}),I=l({page:1,limit:20,keyword:"",type:"",status:""}),B=async()=>{p.value=!0;try{const{data:e}=await oe(I);t.value=e.list,c.value=e.total}catch(e){console.error("获取内容列表失败:",e),D.error("获取内容列表失败")}finally{p.value=!1}},O=async()=>{try{const{data:e}=await se();z.value=e}catch(e){console.error("获取统计数据失败:",e)}},M=()=>{I.page=1,B()},P=()=>{C.value={},v.value=!0},L=e=>{C.value={...e},g.value=!0},G=()=>{h.value=!0},Q=async(e,a)=>{try{const l="inactive"===a?"禁用":"启用";await R.confirm(`确定要${l}这个内容吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ue(e,a),D.success(`${l}成功`),B()}catch(l){"cancel"!==l&&D.error("操作失败")}},ie=e=>{const[a,l]=e.split("-"),o=parseInt(l),s=t.value.find(e=>e.id===o);switch(a){case"preview":L(s);break;case"apply":n=s,C.value={...n},x.value=!0;break;case"optimize":(async()=>{try{await R.confirm("确定要使用AI优化这个内容吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),D.success("AI优化已开始，请稍后查看结果"),B()}catch(e){"cancel"!==e&&D.error("AI优化失败")}})();break;case"disable":Q(o,"inactive");break;case"enable":Q(o,"active");break;case"delete":(async e=>{try{await R.confirm("确定要删除这个内容吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await ne(e),D.success("删除成功"),B()}catch(a){"cancel"!==a&&D.error("删除失败")}})(o)}var n},re=()=>{0!==U.value.length?R.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"批量启用",cancelButtonText:"批量禁用"}).then(()=>{me()}).catch(e=>{"cancel"===e&&me()}):D.warning("请先选择要操作的内容")},me=async e=>{try{U.value.map(e=>e.id);D.success("批量操作成功"),B()}catch(a){D.error("批量操作失败")}},pe=e=>{U.value=e},ve=e=>{I.limit=e,B()},_e=e=>{I.page=e,B()},fe=()=>{B(),O()},ye=()=>{B(),O()},be=()=>{D.success("内容已成功应用到群组")},ge=()=>{B()},he=e=>({announcement:"群组公告",welcome:"欢迎语",rules:"群规则",promotion:"推广文案",other:"其他"}[e]||"未知"),we=e=>({active:"启用",inactive:"禁用",draft:"草稿"}[e]||"未知"),Ve=e=>{if(!e)return"";const a=e.replace(/<[^>]*>/g,"");return a.length>50?a.substring(0,50)+"...":a};return f(()=>{B(),O()}),(e,a)=>{const l=w,f=k,U=V,B=j,O=Y,Q=S,R=K,oe=$,se=A,ne=Z,ue=J,me=N,ke=F,xe=ae,Ce=le,je=ee;return s(),r("div",na,[i("div",ua,[u(l,{modelValue:I.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>I.keyword=e),placeholder:"搜索内容标题、关键词",style:{width:"200px"},class:"filter-item",onKeyup:y(M,["enter"]),clearable:""},null,8,["modelValue"]),u(U,{modelValue:I.type,"onUpdate:modelValue":a[1]||(a[1]=e=>I.type=e),placeholder:"内容类型",clearable:"",style:{width:"120px"},class:"filter-item"},{default:n(()=>[u(f,{label:"全部",value:""}),u(f,{label:"群组公告",value:"announcement"}),u(f,{label:"欢迎语",value:"welcome"}),u(f,{label:"群规则",value:"rules"}),u(f,{label:"推广文案",value:"promotion"}),u(f,{label:"其他",value:"other"})]),_:1},8,["modelValue"]),u(U,{modelValue:I.status,"onUpdate:modelValue":a[2]||(a[2]=e=>I.status=e),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:n(()=>[u(f,{label:"全部",value:""}),u(f,{label:"启用",value:"active"}),u(f,{label:"禁用",value:"inactive"}),u(f,{label:"草稿",value:"draft"})]),_:1},8,["modelValue"]),u(B,{class:"filter-item",type:"primary",icon:"Search",onClick:M},{default:n(()=>a[9]||(a[9]=[d(" 搜索 ",-1)])),_:1,__:[9]}),u(B,{class:"filter-item",type:"success",icon:"Plus",onClick:P},{default:n(()=>a[10]||(a[10]=[d(" 创建内容 ",-1)])),_:1,__:[10]}),u(B,{class:"filter-item",type:"warning",icon:"MagicStick",onClick:G},{default:n(()=>a[11]||(a[11]=[d(" AI生成 ",-1)])),_:1,__:[11]})]),u(Q,{gutter:20,class:"stats-row"},{default:n(()=>[u(O,{span:6},{default:n(()=>[u(te,{type:"primary",icon:_(W),value:z.value.total_contents,label:"总内容数",trend:{type:"up",value:"+12.5%",desc:"较上月"},clickable:"",onClick:ge},null,8,["icon","value"])]),_:1}),u(O,{span:6},{default:n(()=>[u(te,{type:"success",icon:_(H),value:z.value.active_contents,label:"启用内容",trend:{type:"up",value:"+8.3%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),u(O,{span:6},{default:n(()=>[u(te,{type:"warning",icon:_(q),value:z.value.ai_generated,label:"AI生成",trend:{type:"up",value:"+25.6%",desc:"较上月"}},null,8,["icon","value"])]),_:1}),u(O,{span:6},{default:n(()=>[u(te,{type:"danger",icon:_(E),value:z.value.total_views,label:"总浏览量",trend:{type:"up",value:"+18.9%",desc:"较上月"}},null,8,["icon","value"])]),_:1})]),_:1}),u(Ce,null,{header:n(()=>[i("div",da,[a[13]||(a[13]=i("h3",null,"内容列表",-1)),i("div",null,[u(B,{type:"primary",size:"small",onClick:re},{default:n(()=>a[12]||(a[12]=[d("批量操作",-1)])),_:1,__:[12]})])])]),default:n(()=>[b((s(),o(ke,{data:t.value,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",onSelectionChange:pe},{default:n(()=>[u(R,{type:"selection",width:"55"}),u(R,{label:"ID",prop:"id",width:"80"}),u(R,{label:"标题",width:"200"},{default:n(({row:e})=>[i("div",ia,[e.is_ai_generated?(s(),o(oe,{key:0,class:"ai-icon"},{default:n(()=>[u(_(q))]),_:1})):m("",!0),i("span",null,T(e.title),1)])]),_:1}),u(R,{label:"类型",width:"100"},{default:n(({row:e})=>{return[u(se,{type:(a=e.type,{announcement:"primary",welcome:"success",rules:"warning",promotion:"danger",other:"info"}[a]||"info")},{default:n(()=>[d(T(he(e.type)),1)]),_:2},1032,["type"])];var a}),_:1}),u(R,{label:"内容预览",width:"300"},{default:n(({row:e})=>[i("div",{class:"content-preview",onClick:a=>L(e)},T(Ve(e.content)),9,ca)]),_:1}),u(R,{label:"使用次数",width:"100"},{default:n(({row:e})=>[i("span",ra,T(e.usage_count||0),1)]),_:1}),u(R,{label:"状态",width:"100"},{default:n(({row:e})=>{return[u(se,{type:(a=e.status,{active:"success",inactive:"danger",draft:"warning"}[a]||"info")},{default:n(()=>[d(T(we(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),u(R,{label:"创建时间",width:"160"},{default:n(({row:e})=>[d(T(_(de)(e.created_at)),1)]),_:1}),u(R,{label:"操作",width:"200",fixed:"right"},{default:n(({row:e})=>[u(B,{type:"primary",size:"small",onClick:a=>(e=>{C.value={...e},v.value=!0})(e)},{default:n(()=>a[14]||(a[14]=[d(" 编辑 ",-1)])),_:2,__:[14]},1032,["onClick"]),u(B,{type:"success",size:"small",onClick:a=>(async e=>{try{await navigator.clipboard.writeText(e.content),D.success("内容已复制到剪贴板")}catch(a){D.error("复制失败")}})(e)},{default:n(()=>a[15]||(a[15]=[d(" 复制 ",-1)])),_:2,__:[15]},1032,["onClick"]),u(me,{onCommand:ie},{dropdown:n(()=>[u(ue,null,{default:n(()=>[u(ne,{command:`preview-${e.id}`},{default:n(()=>a[17]||(a[17]=[d("预览",-1)])),_:2,__:[17]},1032,["command"]),u(ne,{command:`apply-${e.id}`},{default:n(()=>a[18]||(a[18]=[d("应用到群组",-1)])),_:2,__:[18]},1032,["command"]),u(ne,{command:`optimize-${e.id}`},{default:n(()=>a[19]||(a[19]=[d("AI优化",-1)])),_:2,__:[19]},1032,["command"]),"active"===e.status?(s(),o(ne,{key:0,command:`disable-${e.id}`},{default:n(()=>a[20]||(a[20]=[d("禁用",-1)])),_:2,__:[20]},1032,["command"])):m("",!0),"inactive"===e.status?(s(),o(ne,{key:1,command:`enable-${e.id}`},{default:n(()=>a[21]||(a[21]=[d("启用",-1)])),_:2,__:[21]},1032,["command"])):m("",!0),u(ne,{command:`delete-${e.id}`,divided:""},{default:n(()=>a[22]||(a[22]=[d("删除",-1)])),_:2,__:[22]},1032,["command"])]),_:2},1024)]),default:n(()=>[u(B,{type:"info",size:"small"},{default:n(()=>[a[16]||(a[16]=d(" 更多",-1)),u(oe,{class:"el-icon--right"},{default:n(()=>[u(_(X))]),_:1})]),_:1,__:[16]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[je,p.value]]),i("div",ma,[u(xe,{"current-page":I.page,"onUpdate:currentPage":a[3]||(a[3]=e=>I.page=e),"page-size":I.limit,"onUpdate:pageSize":a[4]||(a[4]=e=>I.limit=e),"page-sizes":[10,20,30,50],total:c.value,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve,onCurrentChange:_e},null,8,["current-page","page-size","total"])])]),_:1}),u(ce,{modelValue:v.value,"onUpdate:modelValue":a[5]||(a[5]=e=>v.value=e),"content-data":C.value,onSuccess:fe},null,8,["modelValue","content-data"]),u(Ue,{modelValue:g.value,"onUpdate:modelValue":a[6]||(a[6]=e=>g.value=e),"content-data":C.value},null,8,["modelValue","content-data"]),u(Pe,{modelValue:h.value,"onUpdate:modelValue":a[7]||(a[7]=e=>h.value=e),onSuccess:ye},null,8,["modelValue"]),u(sa,{modelValue:x.value,"onUpdate:modelValue":a[8]||(a[8]=e=>x.value=e),"content-data":C.value,onSuccess:be},null,8,["modelValue","content-data"])])}}},[["__scopeId","data-v-861fbe44"]]);export{pa as default};
