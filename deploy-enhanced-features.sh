#!/bin/bash

# FFJQ项目增强功能部署脚本
# 部署城市定位、营销功能、防封系统等增强功能

set -e

echo "=== FFJQ项目增强功能部署开始 ==="

# 检查环境
echo "1. 检查部署环境..."
if [ ! -f "artisan" ]; then
    echo "错误: 请在Laravel项目根目录下运行此脚本"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "错误: .env文件不存在"
    exit 1
fi

# 备份数据库
echo "2. 备份数据库..."
DB_NAME=$(grep DB_DATABASE .env | cut -d '=' -f2)
BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"

if command -v mysqldump &> /dev/null; then
    mysqldump -u $(grep DB_USERNAME .env | cut -d '=' -f2) -p$(grep DB_PASSWORD .env | cut -d '=' -f2) $DB_NAME > $BACKUP_FILE
    echo "数据库已备份到: $BACKUP_FILE"
else
    echo "警告: mysqldump未找到，跳过数据库备份"
fi

# 安装依赖
echo "3. 更新Composer依赖..."
composer install --optimize-autoloader --no-dev

# 运行数据库迁移
echo "4. 执行数据库迁移..."
php artisan migrate --force

# 运行数据填充
echo "5. 填充营销数据..."
if php artisan db:seed --class=MarketingDataSeeder --force; then
    echo "营销数据填充完成"
else
    echo "警告: 营销数据填充失败，请手动检查"
fi

# 清除缓存
echo "6. 清除应用缓存..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 重新生成缓存
echo "7. 重新生成缓存..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 创建存储链接
echo "8. 创建存储链接..."
php artisan storage:link

# 设置文件权限
echo "9. 设置文件权限..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 755 public/storage

# 检查IP定位服务配置
echo "10. 检查IP定位服务配置..."
if ! grep -q "IP_LOCATION_SERVICE" .env; then
    echo "添加IP定位服务配置到.env文件..."
    cat >> .env << EOF

# IP定位服务配置
IP_LOCATION_SERVICE=ip2region
IP2REGION_DB_PATH=storage/app/ip2region/ip2region.xdb
AMAP_API_KEY=
BAIDU_API_KEY=
EOF
fi

# 下载IP数据库文件
echo "11. 下载IP地理位置数据库..."
mkdir -p storage/app/ip2region
if [ ! -f "storage/app/ip2region/ip2region.xdb" ]; then
    echo "下载ip2region数据库文件..."
    curl -L "https://github.com/lionsoul2014/ip2region/raw/master/data/ip2region.xdb" -o storage/app/ip2region/ip2region.xdb
    if [ $? -eq 0 ]; then
        echo "IP数据库下载完成"
    else
        echo "警告: IP数据库下载失败，请手动下载"
    fi
fi

# 创建头像目录
echo "12. 创建头像资源目录..."
mkdir -p public/face/qq
mkdir -p public/face/za

# 测试增强功能
echo "13. 测试增强功能..."
if php test-enhanced-features.php; then
    echo "功能测试通过"
else
    echo "警告: 部分功能测试失败，请检查日志"
fi

# 重启队列进程
echo "14. 重启队列进程..."
if command -v supervisorctl &> /dev/null; then
    supervisorctl restart laravel-worker:*
    echo "队列进程已重启"
else
    echo "提示: 请手动重启队列进程"
fi

# 检查Web服务器配置
echo "15. 检查Web服务器配置..."
if [ -f "/etc/nginx/sites-available/default" ]; then
    echo "检测到Nginx，请确保以下配置已添加:"
    echo "location /api/location {"
    echo "    try_files \$uri \$uri/ /index.php?\$query_string;"
    echo "}"
    echo ""
fi

# 生成部署报告
echo "16. 生成部署报告..."
REPORT_FILE="deployment_report_$(date +%Y%m%d_%H%M%S).txt"
cat > $REPORT_FILE << EOF
FFJQ项目增强功能部署报告
部署时间: $(date)
部署版本: Enhanced Features v2.0

已部署功能:
✓ 城市定位功能增强
✓ 群组营销功能优化
✓ 防封系统升级
✓ 浏览器检测服务
✓ 访问验证系统
✓ 域名池管理
✓ 虚拟数据生成
✓ 智能标题替换

数据库变更:
- 添加营销字段到wechat_groups表
- 创建群组访问日志表
- 创建IP城市缓存表
- 增强域名池表结构

新增API端点:
- /api/location/* - 城市定位API
- /api/groups/{id}/marketing-config - 营销配置API
- /api/groups/{id}/virtual-members - 虚拟成员API
- /api/groups/{id}/preview - 群组预览API

配置文件:
- config/ip_location.php - IP定位服务配置
- .env - 新增IP定位相关配置

注意事项:
1. 请配置高德地图API密钥以启用精确定位
2. 定期检查域名池健康状态
3. 监控访问日志和统计数据
4. 及时更新IP地理位置数据库

EOF

echo "部署报告已生成: $REPORT_FILE"

echo ""
echo "=== 增强功能部署完成 ==="
echo ""
echo "🎉 恭喜！FFJQ项目增强功能已成功部署"
echo ""
echo "接下来的步骤:"
echo "1. 配置高德地图API密钥 (可选，用于精确定位)"
echo "2. 上传群组头像资源到 public/face/ 目录"
echo "3. 在管理后台测试新增的营销功能"
echo "4. 检查防封系统的域名配置"
echo "5. 监控系统日志确保功能正常运行"
echo ""
echo "如有问题，请查看部署报告: $REPORT_FILE"
echo "技术支持: 请检查 storage/logs/laravel.log 获取详细错误信息"