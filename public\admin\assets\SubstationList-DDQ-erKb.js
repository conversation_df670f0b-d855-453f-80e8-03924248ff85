import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                         *//* empty css                    *//* empty css                 *//* empty css                        *//* empty css                *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               *//* empty css                     *//* empty css                  */import{r as l,L as a,e as t,k as n,l as i,t as o,E as s,z as u,D as d,u as c,A as r,y as p,G as m,F as _,Y as f,B as v}from"./vue-vendor-DGsK9sC4.js";import{g as h,d as b,u as g,c as y,r as V}from"./substation-Bk2UQYn7.js";import{j as w}from"./user-mKGRZpRV.js";import{T as k,aQ as x,as as C,aU as U,bc as j,bd as I,aH as D,aW as q,aV as P,b4 as S,b2 as $,b6 as A,b7 as T,U as E,b8 as N,bj as z,b3 as B,be as R,au as F,b9 as Y,b5 as H,bR as O,a0 as G,_ as L,ao as M,bH as J,ar as Q,by as Z,bz as K,R as W,Q as X,c1 as ee}from"./element-plus-DcSKpKA8.js";import{P as le}from"./index-Do9uvhBr.js";import"./utils-4VKArNEK.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";/* empty css                      */const ae={class:"app-container"},te={class:"page-header"},ne={class:"header-actions"},ie={class:"dialog-footer"},oe={class:"dialog-footer"},se={style:{display:"flex","justify-content":"space-between","align-items":"center"}},ue={class:"form-tip"},de={class:"dialog-footer"},ce={class:"help-content"},re={class:"help-section"},pe={class:"feature-item"},me={class:"feature-icon"},_e={class:"feature-item"},fe={class:"feature-icon"},ve={class:"feature-item"},he={class:"feature-icon"},be={class:"feature-item"},ge={class:"feature-icon"},ye={class:"feature-item"},Ve={class:"feature-icon"},we={class:"feature-item"},ke={class:"feature-icon"},xe={class:"help-section"},Ce={class:"help-section"},Ue={class:"commission-guide"},je={class:"guide-item"},Ie={class:"help-section"},De={class:"guide-content"},qe={class:"guide-content"},Pe={class:"guide-content"},Se={class:"guide-content"},$e={class:"help-section"},Ae=e({__name:"SubstationList",setup(e){const Ae=l(!0),Te=l([]),Ee=l(0),Ne=l(!1),ze=l([]),Be=l([]),Re=l(!1),Fe=l(["create-substation"]),Ye=l([]),He=l([{status:"正常",color:"success",description:"分站正常运行，所有功能可用",features:"无限制",action:"保持现状，定期监控"},{status:"禁用",color:"info",description:"分站被管理员暂停使用",features:"用户无法访问，管理员可登录",action:"检查禁用原因，解决后重新启用"},{status:"过期",color:"danger",description:"分站使用期限已到期",features:"所有功能停用，数据保留",action:"联系客户续费或备份数据"}]),Oe=l([{type:"新建分站",range:"5%-10%",reason:"吸引新客户，建立合作关系"},{type:"标准分站",range:"10%-15%",reason:"平衡收益与成本，维持正常运营"},{type:"高级分站",range:"15%-20%",reason:"提供更多服务，获得更高收益"},{type:"企业分站",range:"8%-12%",reason:"大客户优惠，长期合作考虑"}]),Ge=a({page:1,per_page:10,name:void 0,domain:void 0,status:void 0}),Le=a({visible:!1,title:""}),Me=a({visible:!1,substationName:"",currentExpireDate:""}),Je=a({visible:!1,title:"",substationId:null}),Qe=l("payoreo"),Ze=l([]),Ke=a({}),We=l(!1),Xe=l(!1),el=l(),ll=l(),al=l({}),tl=l({months:null}),nl=a({name:[{required:!0,message:"分站名称不能为空",trigger:"blur"}],domain:[{required:!0,message:"分站域名不能为空",trigger:"blur"}],user_id:[{required:!0,message:"管理员不能为空",trigger:"change"}],commission_rate:[{required:!0,message:"抽成比例不能为空",trigger:"blur"}],expire_months:[{required:!0,message:"有效期不能为空",trigger:"change"}]}),il=a({months:[{required:!0,message:"续费时长不能为空",trigger:"change"}]}),ol=a({config_name:[{required:!0,message:"配置名称不能为空",trigger:"blur"}],api_url:[{required:!0,message:"API地址不能为空",trigger:"blur"},{type:"url",message:"请输入正确的URL格式",trigger:"blur"}],pid:[{required:!0,message:"商户ID不能为空",trigger:"blur"}],key:[{required:!0,message:"商户秘钥不能为空",trigger:"blur"}]}),sl=async()=>{Ae.value=!0;const{data:e}=await h(Ge);Te.value=e.data,Ee.value=e.total,Ae.value=!1},ul=()=>{Ge.page=1,sl()},dl=()=>{Ge.page=1,Ge.name=void 0,Ge.domain=void 0,Ge.status=void 0,ul()},cl=async()=>{await vl(),al.value={commission_rate:.1,expire_months:12},Le.title="新增分站",Le.visible=!0},rl=e=>{const l=e.id?[e.id]:ze.value;W.confirm(`是否确认删除ID为"${l.join(",")}"的分站？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{for(const e of l)await b(e);sl(),X.success("删除成功")}).catch(()=>{})},pl=e=>{ze.value=e.map(e=>e.id),Ne.value=e.length>0},ml=()=>{Le.visible=!1,el.value.resetFields()},_l=async()=>{await el.value.validate(),al.value.id?(await g(al.value.id,al.value),X.success("修改成功")):(await y(al.value),X.success("新增成功")),Le.visible=!1,sl()},fl=async()=>{await ll.value.validate(),await V(tl.value.substation_id,tl.value.months),X.success("续费成功"),Me.visible=!1,sl()},vl=async()=>{const{data:e}=await w({role:"substation",per_page:1e3});Be.value=e.data},hl=e=>({1:"正常",2:"禁用",3:"过期"}[e]),bl=async e=>{try{const e=await fetch("/api/v1/api/v1/payment-channels/user-channels",{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}});if(e.ok){const l=await e.json();l.success&&(Ze.value=l.data,l.data.length>0&&(Qe.value=l.data[0].channel_code))}}catch(l){console.error("获取支付通道失败:",l),X.error("获取支付通道失败")}},gl=()=>{Ze.value.forEach(e=>{Ke[e.channel_code]||(Ke[e.channel_code]={config_name:`${e.channel_name}配置`,api_url:"payoreo"===e.channel_code?"https://api.payoreo.com":"",pid:"",key:"",notify_url:"",return_url:""}),e.has_config&&yl(e.channel_code)})},yl=async e=>{try{const l=await fetch(`/api/v1/api/v1/payment-channels/user-config/${e}`,{method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"}});if(l.ok){const a=await l.json();a.success&&a.data&&(Ke[e]={...Ke[e],...a.data.config_data})}}catch(l){console.error("加载支付配置失败:",l)}};return t(()=>{sl()}),(e,l)=>{const a=k,t=C,h=D,b=I,g=P,y=q,V=j,w=$,W=S,ze=T,Vl=N,wl=A,kl=B,xl=R,Cl=F,Ul=O,jl=H,Il=Y,Dl=K,ql=Z,Pl=z;return i(),n("div",ae,[o("div",te,[l[24]||(l[24]=o("div",{class:"header-left"},[o("h2",null,"分站管理"),o("p",{class:"page-description"},"管理平台分站，配置分站权限，监控分站运营状况")],-1)),o("div",ne,[s(t,{type:"info",onClick:l[0]||(l[0]=e=>Re.value=!0)},{default:u(()=>[s(a,null,{default:u(()=>[s(c(x))]),_:1}),l[22]||(l[22]=d(" 功能说明 ",-1))]),_:1,__:[22]}),s(t,{type:"primary",onClick:cl},{default:u(()=>[s(a,null,{default:u(()=>[s(c(U))]),_:1}),l[23]||(l[23]=d(" 新增分站 ",-1))]),_:1,__:[23]})])]),s(kl,null,{default:u(()=>[s(V,{inline:!0,model:Ge,class:"filter-container"},{default:u(()=>[s(b,{label:"分站名称"},{default:u(()=>[s(h,{modelValue:Ge.name,"onUpdate:modelValue":l[1]||(l[1]=e=>Ge.name=e),placeholder:"请输入分站名称",clearable:""},null,8,["modelValue"])]),_:1}),s(b,{label:"域名"},{default:u(()=>[s(h,{modelValue:Ge.domain,"onUpdate:modelValue":l[2]||(l[2]=e=>Ge.domain=e),placeholder:"请输入域名",clearable:""},null,8,["modelValue"])]),_:1}),s(b,{label:"状态"},{default:u(()=>[s(y,{modelValue:Ge.status,"onUpdate:modelValue":l[3]||(l[3]=e=>Ge.status=e),placeholder:"请选择状态",clearable:""},{default:u(()=>[s(g,{label:"正常",value:1}),s(g,{label:"禁用",value:2}),s(g,{label:"过期",value:3})]),_:1},8,["modelValue"])]),_:1}),s(b,null,{default:u(()=>[s(t,{type:"primary",icon:"Search",onClick:ul},{default:u(()=>l[25]||(l[25]=[d("搜索",-1)])),_:1,__:[25]}),s(t,{icon:"Refresh",onClick:dl},{default:u(()=>l[26]||(l[26]=[d("重置",-1)])),_:1,__:[26]})]),_:1})]),_:1},8,["model"]),s(W,{gutter:10,class:"mb-2"},{default:u(()=>[s(w,{span:1.5},{default:u(()=>[s(t,{type:"primary",plain:"",icon:"Plus",onClick:cl},{default:u(()=>l[27]||(l[27]=[d("新增分站",-1)])),_:1,__:[27]})]),_:1}),s(w,{span:1.5},{default:u(()=>[s(t,{type:"danger",plain:"",icon:"Delete",disabled:!Ne.value,onClick:rl},{default:u(()=>l[28]||(l[28]=[d("删除",-1)])),_:1,__:[28]},8,["disabled"])]),_:1})]),_:1}),r((i(),p(wl,{data:Te.value,onSelectionChange:pl},{default:u(()=>[s(ze,{type:"selection",width:"55",align:"center"}),s(ze,{label:"ID",prop:"id",width:"80"}),s(ze,{label:"分站名称",prop:"name","show-overflow-tooltip":""}),s(ze,{label:"域名",prop:"domain","show-overflow-tooltip":""}),s(ze,{label:"管理员",prop:"user.nickname"}),s(ze,{label:"抽成比例",align:"center"},{default:u(e=>[d(E((100*e.row.commission_rate).toFixed(1))+"% ",1)]),_:1}),s(ze,{label:"状态",align:"center"},{default:u(e=>{return[s(Vl,{type:(l=e.row.status,{1:"success",2:"info",3:"danger"}[l])},{default:u(()=>[d(E(hl(e.row.status)),1)]),_:2},1032,["type"])];var l}),_:1}),s(ze,{label:"到期时间",prop:"expire_at"}),s(ze,{label:"创建时间",prop:"created_at"}),s(ze,{label:"操作",width:"280",align:"center"},{default:u(e=>[s(t,{type:"primary",link:"",icon:"Edit",onClick:l=>(async e=>{await vl(),al.value={...e},Le.title="编辑分站",Le.visible=!0})(e.row)},{default:u(()=>l[29]||(l[29]=[d("编辑",-1)])),_:2,__:[29]},1032,["onClick"]),s(t,{type:"warning",link:"",icon:"CreditCard",onClick:l=>(async e=>{Je.title=`${e.name} - 支付配置`,Je.substationId=e.id,Je.visible=!0,await bl(e.id),gl()})(e.row)},{default:u(()=>l[30]||(l[30]=[d("支付配置",-1)])),_:2,__:[30]},1032,["onClick"]),s(t,{type:"success",link:"",icon:"Clock",onClick:l=>{return a=e.row,Me.substationName=a.name,Me.currentExpireDate=a.expire_at,tl.value={substation_id:a.id,current_expire_date:a.expire_at,months:null},void(Me.visible=!0);var a}},{default:u(()=>l[31]||(l[31]=[d("续费",-1)])),_:2,__:[31]},1032,["onClick"]),s(t,{type:"danger",link:"",icon:"Delete",onClick:l=>rl(e.row)},{default:u(()=>l[32]||(l[32]=[d("删除",-1)])),_:2,__:[32]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Pl,Ae.value]]),r(s(le,{total:Ee.value,page:Ge.page,"onUpdate:page":l[4]||(l[4]=e=>Ge.page=e),limit:Ge.per_page,"onUpdate:limit":l[5]||(l[5]=e=>Ge.per_page=e),onPagination:sl},null,8,["total","page","limit"]),[[m,Ee.value>0]])]),_:1}),s(Cl,{title:Le.title,modelValue:Le.visible,"onUpdate:modelValue":l[12]||(l[12]=e=>Le.visible=e),width:"600px","append-to-body":""},{footer:u(()=>[o("div",ie,[s(t,{onClick:ml},{default:u(()=>l[35]||(l[35]=[d("取 消",-1)])),_:1,__:[35]}),s(t,{type:"primary",onClick:_l},{default:u(()=>l[36]||(l[36]=[d("确 定",-1)])),_:1,__:[36]})])]),default:u(()=>[s(V,{ref_key:"formRef",ref:el,model:al.value,rules:nl,"label-width":"100px"},{default:u(()=>[s(b,{label:"分站名称",prop:"name"},{default:u(()=>[s(h,{modelValue:al.value.name,"onUpdate:modelValue":l[6]||(l[6]=e=>al.value.name=e),placeholder:"请输入分站名称"},null,8,["modelValue"])]),_:1}),s(b,{label:"分站域名",prop:"domain"},{default:u(()=>[s(h,{modelValue:al.value.domain,"onUpdate:modelValue":l[7]||(l[7]=e=>al.value.domain=e),placeholder:"请输入分站域名"},{append:u(()=>l[33]||(l[33]=[d(".linkhub.pro",-1)])),_:1},8,["modelValue"])]),_:1}),s(b,{label:"管理员",prop:"user_id"},{default:u(()=>[s(y,{modelValue:al.value.user_id,"onUpdate:modelValue":l[8]||(l[8]=e=>al.value.user_id=e),placeholder:"请选择管理员",filterable:""},{default:u(()=>[(i(!0),n(_,null,f(Be.value,e=>(i(),p(g,{key:e.id,label:`${e.nickname}(${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(b,{label:"抽成比例",prop:"commission_rate"},{default:u(()=>[s(xl,{modelValue:al.value.commission_rate,"onUpdate:modelValue":l[9]||(l[9]=e=>al.value.commission_rate=e),precision:3,step:.001,min:0,max:1},null,8,["modelValue"]),l[34]||(l[34]=o("span",{class:"ml-2"},"（0-1之间的小数，如0.1表示10%）",-1))]),_:1,__:[34]}),s(b,{label:"有效期",prop:"expire_months"},{default:u(()=>[s(y,{modelValue:al.value.expire_months,"onUpdate:modelValue":l[10]||(l[10]=e=>al.value.expire_months=e),placeholder:"请选择有效期"},{default:u(()=>[s(g,{label:"1个月",value:1}),s(g,{label:"3个月",value:3}),s(g,{label:"6个月",value:6}),s(g,{label:"12个月",value:12}),s(g,{label:"24个月",value:24}),s(g,{label:"36个月",value:36})]),_:1},8,["modelValue"])]),_:1}),s(b,{label:"描述",prop:"description"},{default:u(()=>[s(h,{type:"textarea",modelValue:al.value.description,"onUpdate:modelValue":l[11]||(l[11]=e=>al.value.description=e),placeholder:"请输入分站描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),s(Cl,{title:"分站续费",modelValue:Me.visible,"onUpdate:modelValue":l[15]||(l[15]=e=>Me.visible=e),width:"500px","append-to-body":""},{footer:u(()=>[o("div",oe,[s(t,{onClick:l[14]||(l[14]=e=>Me.visible=!1)},{default:u(()=>l[37]||(l[37]=[d("取 消",-1)])),_:1,__:[37]}),s(t,{type:"primary",onClick:fl},{default:u(()=>l[38]||(l[38]=[d("确 定",-1)])),_:1,__:[38]})])]),default:u(()=>[s(V,{ref_key:"renewFormRef",ref:ll,model:tl.value,rules:il,"label-width":"100px"},{default:u(()=>[s(b,{label:"分站名称"},{default:u(()=>[s(h,{value:Me.substationName,readonly:""},null,8,["value"])]),_:1}),s(b,{label:"当前到期时间"},{default:u(()=>[s(h,{value:Me.currentExpireDate,readonly:""},null,8,["value"])]),_:1}),s(b,{label:"续费时长",prop:"months"},{default:u(()=>[s(y,{modelValue:tl.value.months,"onUpdate:modelValue":l[13]||(l[13]=e=>tl.value.months=e),placeholder:"请选择续费时长"},{default:u(()=>[s(g,{label:"1个月",value:1}),s(g,{label:"3个月",value:3}),s(g,{label:"6个月",value:6}),s(g,{label:"12个月",value:12}),s(g,{label:"24个月",value:24})]),_:1},8,["modelValue"])]),_:1}),tl.value.months?(i(),p(b,{key:0,label:"续费后到期时间"},{default:u(()=>[s(h,{value:tl.value.months&&tl.value.current_expire_date?ee(tl.value.current_expire_date).add(tl.value.months,"month").format("YYYY-MM-DD HH:mm:ss"):"",readonly:""},null,8,["value"])]),_:1})):v("",!0)]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),s(Cl,{title:Je.title,modelValue:Je.visible,"onUpdate:modelValue":l[18]||(l[18]=e=>Je.visible=e),width:"800px","append-to-body":""},{footer:u(()=>[o("div",de,[s(t,{onClick:l[17]||(l[17]=e=>Je.visible=!1)},{default:u(()=>l[44]||(l[44]=[d("关 闭",-1)])),_:1,__:[44]})])]),default:u(()=>[s(Il,{modelValue:Qe.value,"onUpdate:modelValue":l[16]||(l[16]=e=>Qe.value=e),type:"card"},{default:u(()=>[(i(!0),n(_,null,f(Ze.value,e=>(i(),p(jl,{key:e.channel_code,label:e.channel_name,name:e.channel_code},{default:u(()=>[s(kl,{shadow:"never",style:{"margin-bottom":"20px"}},{header:u(()=>[o("div",se,[o("span",null,E(e.channel_name)+"配置",1),o("div",null,[e.has_config?(i(),p(Vl,{key:0,type:e.test_status?"success":"warning"},{default:u(()=>[d(E(e.test_status?"已测试通过":"未测试"),1)]),_:2},1032,["type"])):(i(),p(Vl,{key:1,type:"info"},{default:u(()=>l[39]||(l[39]=[d("未配置",-1)])),_:1,__:[39]}))])])]),default:u(()=>[s(V,{ref_for:!0,ref:`paymentForm_${e.channel_code}`,model:Ke[e.channel_code],rules:ol,"label-width":"120px"},{default:u(()=>["payoreo"===e.channel_code?(i(),n(_,{key:0},[s(b,{label:"配置名称",prop:"config_name"},{default:u(()=>[s(h,{modelValue:Ke[e.channel_code].config_name,"onUpdate:modelValue":l=>Ke[e.channel_code].config_name=l,placeholder:"请输入配置名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(b,{label:"API地址",prop:"api_url"},{default:u(()=>[s(h,{modelValue:Ke[e.channel_code].api_url,"onUpdate:modelValue":l=>Ke[e.channel_code].api_url=l,placeholder:"https://api.payoreo.com"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(b,{label:"商户ID",prop:"pid"},{default:u(()=>[s(h,{modelValue:Ke[e.channel_code].pid,"onUpdate:modelValue":l=>Ke[e.channel_code].pid=l,placeholder:"请输入易支付商户ID"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(b,{label:"商户秘钥",prop:"key"},{default:u(()=>[s(h,{modelValue:Ke[e.channel_code].key,"onUpdate:modelValue":l=>Ke[e.channel_code].key=l,type:"password","show-password":"",placeholder:"请输入易支付商户秘钥"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),s(b,{label:"异步通知地址"},{default:u(()=>[s(h,{modelValue:Ke[e.channel_code].notify_url,"onUpdate:modelValue":l=>Ke[e.channel_code].notify_url=l,placeholder:"留空则使用系统默认"},null,8,["modelValue","onUpdate:modelValue"]),o("div",ue,"系统默认: "+E(`${window.location.origin}/api/v1/payment/notify/payoreo`),1)]),_:2},1024),s(b,{label:"同步返回地址"},{default:u(()=>[s(h,{modelValue:Ke[e.channel_code].return_url,"onUpdate:modelValue":l=>Ke[e.channel_code].return_url=l,placeholder:"留空则使用系统默认"},null,8,["modelValue","onUpdate:modelValue"]),l[40]||(l[40]=o("div",{class:"form-tip"},"用户支付成功后的跳转页面",-1))]),_:2,__:[40]},1024)],64)):(i(),p(Ul,{key:1,title:`${e.channel_name}配置功能开发中`,type:"info","show-icon":"",closable:!1},null,8,["title"])),s(b,null,{default:u(()=>[s(t,{type:"primary",onClick:l=>(async e=>{try{We.value=!0;const l=await fetch("/api/v1/api/v1/payment-channels/user-config",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"},body:JSON.stringify({channel_code:e,config_name:Ke[e].config_name,config_data:Ke[e]})});if(l.ok){const e=await l.json();e.success?(X.success("支付配置保存成功"),await bl(Je.substationId)):X.error(e.message||"保存失败")}else X.error("保存失败")}catch(l){console.error("保存支付配置失败:",l),X.error("保存失败")}finally{We.value=!1}})(e.channel_code),loading:We.value},{default:u(()=>l[41]||(l[41]=[d(" 保存配置 ",-1)])),_:2,__:[41]},1032,["onClick","loading"]),s(t,{type:"success",onClick:l=>(async e=>{try{Xe.value=!0;const l=await fetch("/api/v1/api/v1/payment-channels/test-config",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"Content-Type":"application/json"},body:JSON.stringify({channel_code:e})});if(l.ok){const e=await l.json();e.success?(X.success("配置测试通过"),await bl(Je.substationId)):X.error(e.message||"测试失败")}else X.error("测试失败")}catch(l){console.error("测试支付配置失败:",l),X.error("测试失败")}finally{Xe.value=!1}})(e.channel_code),loading:Xe.value,disabled:!e.has_config},{default:u(()=>l[42]||(l[42]=[d(" 测试配置 ",-1)])),_:2,__:[42]},1032,["onClick","loading","disabled"]),e.has_config?(i(),p(t,{key:0,type:"info",onClick:l=>yl(e.channel_code)},{default:u(()=>l[43]||(l[43]=[d(" 重新加载 ",-1)])),_:2,__:[43]},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1032,["model","rules"])]),_:2},1024)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"]),s(Cl,{modelValue:Re.value,"onUpdate:modelValue":l[21]||(l[21]=e=>Re.value=e),title:"分站管理功能说明",width:"1000px",class:"help-dialog"},{default:u(()=>[o("div",ce,[l[70]||(l[70]=o("div",{class:"help-section"},[o("h3",null,"🏢 功能概述"),o("p",null,"分站管理系统是平台多站点运营的核心功能，支持创建和管理多个独立的分站点，每个分站拥有独立的域名、管理员和配置，实现平台的规模化扩展和区域化运营。")],-1)),o("div",re,[l[51]||(l[51]=o("h3",null,"🚀 核心功能模块",-1)),s(W,{gutter:20},{default:u(()=>[s(w,{span:8},{default:u(()=>[o("div",pe,[o("div",me,[s(a,null,{default:u(()=>[s(c(U))]),_:1})]),l[45]||(l[45]=o("div",{class:"feature-content"},[o("h4",null,"分站创建"),o("p",null,"创建新的分站点，配置基本信息和管理权限")],-1))])]),_:1}),s(w,{span:8},{default:u(()=>[o("div",_e,[o("div",fe,[s(a,null,{default:u(()=>[s(c(G))]),_:1})]),l[46]||(l[46]=o("div",{class:"feature-content"},[o("h4",null,"分站配置"),o("p",null,"设置分站域名、抽成比例、有效期等参数")],-1))])]),_:1}),s(w,{span:8},{default:u(()=>[o("div",ve,[o("div",he,[s(a,null,{default:u(()=>[s(c(L))]),_:1})]),l[47]||(l[47]=o("div",{class:"feature-content"},[o("h4",null,"支付配置"),o("p",null,"为分站配置独立的支付通道和参数")],-1))])]),_:1}),s(w,{span:8},{default:u(()=>[o("div",be,[o("div",ge,[s(a,null,{default:u(()=>[s(c(M))]),_:1})]),l[48]||(l[48]=o("div",{class:"feature-content"},[o("h4",null,"管理员管理"),o("p",null,"指定分站管理员，分配管理权限")],-1))])]),_:1}),s(w,{span:8},{default:u(()=>[o("div",ye,[o("div",Ve,[s(a,null,{default:u(()=>[s(c(J))]),_:1})]),l[49]||(l[49]=o("div",{class:"feature-content"},[o("h4",null,"续费管理"),o("p",null,"管理分站有效期，处理续费申请")],-1))])]),_:1}),s(w,{span:8},{default:u(()=>[o("div",we,[o("div",ke,[s(a,null,{default:u(()=>[s(c(Q))]),_:1})]),l[50]||(l[50]=o("div",{class:"feature-content"},[o("h4",null,"状态监控"),o("p",null,"实时监控分站运行状态和业务数据")],-1))])]),_:1})]),_:1})]),o("div",xe,[l[52]||(l[52]=o("h3",null,"📊 分站状态说明",-1)),s(wl,{data:He.value,style:{width:"100%"}},{default:u(()=>[s(ze,{prop:"status",label:"状态",width:"100"},{default:u(({row:e})=>[s(Vl,{type:e.color},{default:u(()=>[d(E(e.status),1)]),_:2},1032,["type"])]),_:1}),s(ze,{prop:"description",label:"状态描述"}),s(ze,{prop:"features",label:"功能限制"}),s(ze,{prop:"action",label:"处理建议"})]),_:1},8,["data"])]),o("div",Ce,[l[55]||(l[55]=o("h3",null,"💰 抽成比例设置指南",-1)),o("div",Ue,[l[54]||(l[54]=o("div",{class:"guide-item"},[o("h4",null,"🔸 抽成比例说明"),o("p",null,"抽成比例是平台从分站收入中提取的分成比例，用小数表示（如0.1表示10%）"),o("div",{class:"commission-examples"},[o("div",{class:"example-item"},[o("span",{class:"example-label"},"示例1："),o("span",null,"设置0.05，表示平台抽成5%，分站保留95%")]),o("div",{class:"example-item"},[o("span",{class:"example-label"},"示例2："),o("span",null,"设置0.15，表示平台抽成15%，分站保留85%")])])],-1)),o("div",je,[l[53]||(l[53]=o("h4",null,"🔸 推荐设置范围",-1)),s(wl,{data:Oe.value,size:"small"},{default:u(()=>[s(ze,{prop:"type",label:"分站类型",width:"120"}),s(ze,{prop:"range",label:"推荐比例",width:"120"}),s(ze,{prop:"reason",label:"设置理由"})]),_:1},8,["data"])])])]),l[71]||(l[71]=o("div",{class:"help-section"},[o("h3",null,"💳 支付配置指南"),o("div",{class:"payment-guide"},[o("div",{class:"guide-step"},[o("h4",null,"📋 配置步骤"),o("ol",null,[o("li",null,'点击分站列表中的"支付配置"按钮'),o("li",null,"选择要配置的支付通道（如易支付）"),o("li",null,"填写支付通道的配置信息"),o("li",null,"测试配置是否正确"),o("li",null,"保存配置并启用")])]),o("div",{class:"guide-step"},[o("h4",null,"⚙️ 易支付配置说明"),o("div",{class:"config-fields"},[o("div",{class:"field-item"},[o("strong",null,"API地址："),d("易支付平台的API接口地址 ")]),o("div",{class:"field-item"},[o("strong",null,"商户ID："),d("在易支付平台注册的商户标识 ")]),o("div",{class:"field-item"},[o("strong",null,"商户秘钥："),d("用于签名验证的密钥，请妥善保管 ")]),o("div",{class:"field-item"},[o("strong",null,"通知地址："),d("支付成功后的异步通知地址 ")]),o("div",{class:"field-item"},[o("strong",null,"返回地址："),d("支付成功后用户跳转的页面 ")])])])])],-1)),o("div",Ie,[l[64]||(l[64]=o("h3",null,"📝 操作指南",-1)),s(ql,{modelValue:Fe.value,"onUpdate:modelValue":l[19]||(l[19]=e=>Fe.value=e)},{default:u(()=>[s(Dl,{title:"如何创建新分站？",name:"create-substation"},{default:u(()=>[o("div",De,[l[57]||(l[57]=o("ol",null,[o("li",null,'点击页面右上角的"新增分站"按钮'),o("li",null,[d("填写分站基本信息： "),o("ul",null,[o("li",null,"分站名称：建议使用有意义的名称"),o("li",null,"分站域名：输入二级域名（系统自动添加后缀）"),o("li",null,"管理员：选择负责该分站的管理员"),o("li",null,"抽成比例：设置平台分成比例"),o("li",null,"有效期：选择分站的使用期限")])]),o("li",null,"填写分站描述（可选）"),o("li",null,'点击"确定"完成创建')],-1)),s(Ul,{type:"info",closable:!1},{default:u(()=>l[56]||(l[56]=[d(" 💡 提示：域名创建后需要进行DNS解析配置才能正常访问 ",-1)])),_:1,__:[56]})])]),_:1}),s(Dl,{title:"如何配置分站支付？",name:"payment-config"},{default:u(()=>[o("div",qe,[l[59]||(l[59]=o("ol",null,[o("li",null,"在分站列表中找到目标分站"),o("li",null,'点击"支付配置"按钮'),o("li",null,"选择支付通道标签页"),o("li",null,[d("填写支付配置信息： "),o("ul",null,[o("li",null,"配置名称：便于识别的名称"),o("li",null,"API地址：支付平台的接口地址"),o("li",null,"商户信息：商户ID和密钥"),o("li",null,"回调地址：通知和返回地址")])]),o("li",null,'点击"测试配置"验证设置'),o("li",null,'测试通过后点击"保存配置"')],-1)),s(Ul,{type:"warning",closable:!1},{default:u(()=>l[58]||(l[58]=[d(" ⚠️ 注意：商户密钥等敏感信息请妥善保管，不要泄露 ",-1)])),_:1,__:[58]})])]),_:1}),s(Dl,{title:"如何为分站续费？",name:"renew-substation"},{default:u(()=>[o("div",Pe,[l[61]||(l[61]=o("ol",null,[o("li",null,"在分站列表中找到需要续费的分站"),o("li",null,'点击"续费"按钮'),o("li",null,"查看当前到期时间"),o("li",null,"选择续费时长（1-24个月）"),o("li",null,"确认续费后的到期时间"),o("li",null,'点击"确定"完成续费')],-1)),s(Ul,{type:"success",closable:!1},{default:u(()=>l[60]||(l[60]=[d(" ✅ 说明：续费后分站状态会自动更新，有效期延长 ",-1)])),_:1,__:[60]})])]),_:1}),s(Dl,{title:"如何管理分站状态？",name:"manage-status"},{default:u(()=>[o("div",Se,[l[63]||(l[63]=o("ol",null,[o("li",null,"在分站列表中查看各分站的状态"),o("li",null,"正常状态：分站正常运行，所有功能可用"),o("li",null,"禁用状态：分站被暂停，用户无法访问"),o("li",null,"过期状态：分站已过期，需要续费才能恢复"),o("li",null,"可以通过编辑功能修改分站状态")],-1)),s(Ul,{type:"info",closable:!1},{default:u(()=>l[62]||(l[62]=[d(" 💡 建议：定期检查分站状态，及时处理过期和异常情况 ",-1)])),_:1,__:[62]})])]),_:1})]),_:1},8,["modelValue"])]),l[72]||(l[72]=o("div",{class:"help-section"},[o("h3",null,"💡 最佳实践建议"),o("div",{class:"best-practices"},[o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"🎯"),o("div",{class:"practice-content"},[o("h4",null,"合理规划分站"),o("p",null,"根据业务需求和地域特点合理规划分站数量和分布，避免过度分散或集中")])]),o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"👥"),o("div",{class:"practice-content"},[o("h4",null,"选择合适管理员"),o("p",null,"为每个分站选择有经验、负责任的管理员，确保分站正常运营")])]),o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"💰"),o("div",{class:"practice-content"},[o("h4",null,"合理设置抽成"),o("p",null,"根据分站规模、运营成本和市场情况合理设置抽成比例，平衡各方利益")])]),o("div",{class:"practice-item"},[o("div",{class:"practice-icon"},"🔒"),o("div",{class:"practice-content"},[o("h4",null,"加强安全管理"),o("p",null,"定期检查分站安全状况，及时更新配置，防范安全风险")])])])],-1)),o("div",$e,[l[69]||(l[69]=o("h3",null,"❓ 常见问题",-1)),s(ql,{modelValue:Ye.value,"onUpdate:modelValue":l[20]||(l[20]=e=>Ye.value=e)},{default:u(()=>[s(Dl,{title:"分站域名如何解析？",name:"faq1"},{default:u(()=>l[65]||(l[65]=[o("p",null,"分站创建后，需要在DNS服务商处添加CNAME记录，将分站域名指向主站域名。具体操作请联系技术支持。",-1)])),_:1,__:[65]}),s(Dl,{title:"抽成比例可以随时修改吗？",name:"faq2"},{default:u(()=>l[66]||(l[66]=[o("p",null,"可以的。管理员可以随时编辑分站信息修改抽成比例，修改后立即生效，影响后续的收入分成计算。",-1)])),_:1,__:[66]}),s(Dl,{title:"分站过期后数据会丢失吗？",name:"faq3"},{default:u(()=>l[67]||(l[67]=[o("p",null,"分站过期后数据不会立即删除，但用户无法访问。建议在过期前及时续费，或联系管理员备份重要数据。",-1)])),_:1,__:[67]}),s(Dl,{title:"如何批量管理多个分站？",name:"faq4"},{default:u(()=>l[68]||(l[68]=[o("p",null,"可以使用表格上方的批量操作功能，选中多个分站后进行批量删除等操作。更多批量功能正在开发中。",-1)])),_:1,__:[68]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-22159dbd"]]);export{Ae as default};
