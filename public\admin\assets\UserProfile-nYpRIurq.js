import{_ as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                         *//* empty css                *//* empty css               *//* empty css               */import{b3 as e,aN as s,U as l,b8 as t,aP as n,b4 as i,b2 as u,T as d,ao as r,bI as o,bw as p,al as v,bE as c,bb as _,ba as m,ai as f,bJ as g,bj as h,at as b}from"./element-plus-DcSKpKA8.js";import{f as y}from"./format-3eU4VJ9V.js";import{r as j,d as I,e as k,y as w,l as x,z as T,A as z,k as Z,E as A,t as F,D as U,u as D,F as E,Y as N}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const P={class:"user-profile-container"},$={class:"card-header"},q={class:"basic-info"},B={class:"info-text"},J={class:"tags"},O={class:"stat-item"},S={class:"stat-item"},V={class:"stat-item"},Y={class:"stat-item"},C={class:"card-header"},G={class:"interaction-list"},H={class:"topic-tags"},K={class:"card-header"},L={class:"stat-item"},M={class:"stat-item"},Q={class:"stat-item"},R={class:"card-header"},W={class:"stat-item"},X={class:"stat-item"},aa={class:"stat-item"},ea=a({__name:"UserProfile",props:{visible:Boolean,userId:{type:[Number,String],default:null}},emits:["update:visible"],setup(a,{emit:ea}){const sa=a,la=ea,ta=j(!1),na=j({}),ia=j({}),ua=j({}),da=j({}),ra=j({}),oa=async a=>{a&&(ta.value=!0,setTimeout(()=>{na.value={id:a,nickname:"张三",avatar:"https://i.pravatar.cc/150?u=zhangsan",role:"核心用户",joined_at:"2024-01-15T10:30:00Z",tags:["技术控","早期用户","高价值"]},ia.value={login_days_30d:25,post_count:188,avg_online_time:2.5,last_active:"2小时前"},ua.value={likes_given:512,favorites_given:98,shares_given:45,popular_topics:["AI技术","产品设计","创业心得"]},da.value={total_spent:1280.5,order_count:15,avg_order_value:85.37,conversion_path:[{timestamp:"2024-05-10T10:00:00Z",description:"通过分享链接进入落地页"},{timestamp:"2024-05-10T10:05:00Z",description:'浏览了 "AI大模型" 课程'},{timestamp:"2024-05-11T14:20:00Z",description:'下单购买 "AI大模型" 课程',type:"order"},{timestamp:"2024-05-20T18:30:00Z",description:'复购 "产品设计" 课程',type:"order"}]},ra.value={invited_users:12,downline_users:5,commission_earned:350.8},ta.value=!1},800))};I(()=>sa.userId,a=>{oa(a)});const pa=()=>{la("update:visible",!1)};return k(()=>{sa.userId&&oa(sa.userId)}),(j,I)=>{const k=d,ea=s,sa=t,la=n,oa=u,va=i,ca=e,_a=m,ma=_,fa=b,ga=h;return x(),w(fa,{"model-value":a.visible,title:`用户画像 - ${na.value.nickname}`,direction:"rtl",size:"50%","onUpdate:modelValue":I[0]||(I[0]=a=>j.$emit("update:visible",a)),"before-close":pa},{default:T(()=>[z((x(),Z("div",P,[A(ca,{class:"profile-section",shadow:"never"},{header:T(()=>[F("div",$,[F("span",null,[A(k,null,{default:T(()=>[A(D(r))]),_:1}),I[1]||(I[1]=U(" 基本信息与活跃度",-1))])])]),default:T(()=>[F("div",q,[A(ea,{size:80,src:na.value.avatar},null,8,["src"]),F("div",B,[F("h2",null,[U(l(na.value.nickname)+" ",1),A(sa,{size:"small"},{default:T(()=>[U(l(na.value.role||"普通成员"),1)]),_:1})]),F("p",null,"ID: "+l(na.value.id)+" | 加入时间: "+l(D(y)(na.value.joined_at)),1),F("div",J,[(x(!0),Z(E,null,N(na.value.tags,a=>(x(),w(sa,{key:a,class:"profile-tag"},{default:T(()=>[U(l(a),1)]),_:2},1024))),128))])])]),A(la),A(va,{gutter:20,class:"activity-stats"},{default:T(()=>[A(oa,{span:6},{default:T(()=>[F("div",O,[F("strong",null,l(ia.value.login_days_30d),1),I[2]||(I[2]=F("span",null,"近30日登录",-1))])]),_:1}),A(oa,{span:6},{default:T(()=>[F("div",S,[F("strong",null,l(ia.value.post_count),1),I[3]||(I[3]=F("span",null,"累计发言",-1))])]),_:1}),A(oa,{span:6},{default:T(()=>[F("div",V,[F("strong",null,l(ia.value.avg_online_time)+"h",1),I[4]||(I[4]=F("span",null,"日均在线",-1))])]),_:1}),A(oa,{span:6},{default:T(()=>[F("div",Y,[F("strong",null,l(ia.value.last_active),1),I[5]||(I[5]=F("span",null,"最后活跃",-1))])]),_:1})]),_:1})]),_:1}),A(ca,{class:"profile-section",shadow:"never"},{header:T(()=>[F("div",C,[F("span",null,[A(k,null,{default:T(()=>[A(D(c))]),_:1}),I[6]||(I[6]=U(" 内容偏好与互动",-1))])])]),default:T(()=>[A(va,{gutter:20},{default:T(()=>[A(oa,{span:12},{default:T(()=>[I[7]||(I[7]=F("h4",null,"互动行为统计",-1)),F("ul",G,[F("li",null,[A(k,null,{default:T(()=>[A(D(o))]),_:1}),U(" 点赞数: "+l(ua.value.likes_given),1)]),F("li",null,[A(k,null,{default:T(()=>[A(D(p))]),_:1}),U(" 收藏数: "+l(ua.value.favorites_given),1)]),F("li",null,[A(k,null,{default:T(()=>[A(D(v))]),_:1}),U(" 分享数: "+l(ua.value.shares_given),1)])])]),_:1,__:[7]}),A(oa,{span:12},{default:T(()=>[I[8]||(I[8]=F("h4",null,"常参与的话题",-1)),F("div",H,[(x(!0),Z(E,null,N(ua.value.popular_topics,a=>(x(),w(sa,{key:a,type:"success",class:"topic-tag"},{default:T(()=>[U(l(a),1)]),_:2},1024))),128))])]),_:1,__:[8]})]),_:1})]),_:1}),A(ca,{class:"profile-section",shadow:"never"},{header:T(()=>[F("div",K,[F("span",null,[A(k,null,{default:T(()=>[A(D(f))]),_:1}),I[9]||(I[9]=U(" 消费能力与转化",-1))])])]),default:T(()=>[A(va,{gutter:20},{default:T(()=>[A(oa,{span:8},{default:T(()=>[F("div",L,[F("strong",null,"¥"+l(da.value.total_spent.toFixed(2)),1),I[10]||(I[10]=F("span",null,"累计消费",-1))])]),_:1}),A(oa,{span:8},{default:T(()=>[F("div",M,[F("strong",null,l(da.value.order_count),1),I[11]||(I[11]=F("span",null,"订单数",-1))])]),_:1}),A(oa,{span:8},{default:T(()=>[F("div",Q,[F("strong",null,"¥"+l(da.value.avg_order_value.toFixed(2)),1),I[12]||(I[12]=F("span",null,"客单价",-1))])]),_:1})]),_:1}),A(la),I[13]||(I[13]=F("h4",null,"转化路径分析",-1)),A(ma,null,{default:T(()=>[(x(!0),Z(E,null,N(da.value.conversion_path,(a,e)=>(x(),w(_a,{key:e,timestamp:D(y)(a.timestamp),type:"order"===a.type?"primary":"success"},{default:T(()=>[U(l(a.description),1)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1,__:[13]}),A(ca,{class:"profile-section",shadow:"never"},{header:T(()=>[F("div",R,[F("span",null,[A(k,null,{default:T(()=>[A(D(g))]),_:1}),I[14]||(I[14]=U(" 社交网络与影响力",-1))])])]),default:T(()=>[A(va,{gutter:20},{default:T(()=>[A(oa,{span:8},{default:T(()=>[F("div",W,[F("strong",null,l(ra.value.invited_users),1),I[15]||(I[15]=F("span",null,"邀请好友数",-1))])]),_:1}),A(oa,{span:8},{default:T(()=>[F("div",X,[F("strong",null,l(ra.value.downline_users),1),I[16]||(I[16]=F("span",null,"下线成员数",-1))])]),_:1}),A(oa,{span:8},{default:T(()=>[F("div",aa,[F("strong",null,"¥"+l(ra.value.commission_earned.toFixed(2)),1),I[17]||(I[17]=F("span",null,"赚取佣金",-1))])]),_:1})]),_:1})]),_:1})])),[[ga,ta.value]])]),_:1},8,["model-value","title"])}}},[["__scopeId","data-v-feccc22c"]]);export{ea as default};
