import{_ as e,u as a}from"./index-D2bI4m-v.js";import{P as l}from"./PageLayout-OFR6SHfu.js";/* empty css                          *//* empty css                    *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        */import{U as t,as as r,aH as s,aV as o,aW as u,ax as n,ay as i,av as d,b7 as c,b8 as p,c0 as m,b6 as b,bk as v,bg as f,bh as g,au as y,Q as h,bc as _,b4 as w,b2 as k,bd as V,be as x,aP as z,bn as U,T as C,aU as j,aN as S,bo as B,bp as D,o as T,R as A,bM as P,bN as I,bj as $,at as F}from"./element-plus-DcSKpKA8.js";import{r as q,c as R,e as L,k as N,l as M,B as O,t as H,E,F as K,Y as W,y as Z,z as Q,D as J,a3 as Y,m as G,L as X,d as ee,u as ae,A as le}from"./vue-vendor-DGsK9sC4.js";/* empty css                     *//* empty css                  *//* empty css                    *//* empty css                        *//* empty css               */import{e as te,f as re,h as se,i as oe,j as ue,k as ne,l as ie,m as de}from"./user-mKGRZpRV.js";/* empty css                       *//* empty css                 *//* empty css                   *//* empty css                             */import{f as ce}from"./format-3eU4VJ9V.js";import"./utils-4VKArNEK.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const pe={class:"data-table-container"},me={key:0,class:"table-toolbar"},be={class:"toolbar-left"},ve={key:0,class:"batch-actions"},fe={class:"selection-info"},ge={key:1,class:"normal-actions"},ye={class:"toolbar-right"},he={key:0,class:"search-box"},_e={key:1,class:"filters"},we={class:"table-wrapper"},ke=["innerHTML"],Ve={class:"table-actions"},xe={key:1,class:"table-pagination"},ze={class:"column-settings"},Ue={__name:"DataTable",props:{data:{type:Array,default:()=>[]},columns:{type:Array,required:!0},loading:{type:Boolean,default:!1},height:{type:[String,Number],default:void 0},maxHeight:{type:[String,Number],default:void 0},stripe:{type:Boolean,default:!0},border:{type:Boolean,default:!1},size:{type:String,default:"default"},emptyText:{type:String,default:"暂无数据"},rowKey:{type:String,default:"id"},defaultSort:{type:Object,default:()=>({})},highlightCurrentRow:{type:Boolean,default:!1},selectable:{type:Boolean,default:!1},showIndex:{type:Boolean,default:!1},showToolbar:{type:Boolean,default:!0},toolbarActions:{type:Array,default:()=>[]},batchActions:{type:Array,default:()=>[]},searchable:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:"请输入关键词搜索"},filters:{type:Array,default:()=>[]},showSettings:{type:Boolean,default:!0},showPagination:{type:Boolean,default:!0},paginationLayout:{type:String,default:"total, sizes, prev, pager, next, jumper"},pageSizes:{type:Array,default:()=>[10,20,50,100]},total:{type:Number,default:0}},emits:["selection-change","sort-change","row-click","row-dblclick","toolbar-action","batch-action","row-action","search","filter","page-change","size-change","refresh","export"],setup(e,{expose:a,emit:l}){const _=e,w=l,k=q(),V=q([]),x=q(""),z=q({}),U=q(1),C=q(20),j=q(!1),S=q([]),B=q([]),D=R(()=>_.data),T=R(()=>0===S.value.length?_.columns:_.columns.filter(e=>S.value.includes(e.prop)));L(()=>{B.value=_.columns.map(e=>({prop:e.prop,label:e.label})),S.value=_.columns.map(e=>e.prop)});const A=e=>{V.value=e,w("selection-change",e)},P=e=>{w("sort-change",e)},I=(e,a,l)=>{w("row-click",e,a,l)},$=(e,a,l)=>{w("row-dblclick",e,a,l)},F=()=>{w("search",x.value)},X=()=>{w("filter",z.value)},ee=e=>{U.value=e,w("page-change",e,C.value)},ae=e=>{C.value=e,U.value=1,w("size-change",e,U.value)},le=()=>{w("refresh")},te=()=>{w("export")},re=()=>{j.value=!1,h.success("列设置已应用")},se=(e,a)=>{if(!a)return"default";const l=a.find(a=>a.value===e);return l?l.type:"default"},oe=(e,a)=>{if(!a)return e;const l=a.find(a=>a.value===e);return l?l.text:e},ue=(e,a="relative")=>{if(!e)return"-";const l=new Date(e);if("relative"===a){const e=new Date-l,a=Math.floor(e/864e5);return 0===a?"今天":1===a?"昨天":a<7?`${a}天前`:l.toLocaleDateString("zh-CN")}return l.toLocaleString("zh-CN")};return a({clearSelection:()=>k.value?.clearSelection(),toggleRowSelection:(e,a)=>k.value?.toggleRowSelection(e,a),toggleAllSelection:()=>k.value?.toggleAllSelection(),setCurrentRow:e=>k.value?.setCurrentRow(e),clearSort:()=>k.value?.clearSort(),doLayout:()=>k.value?.doLayout()}),(a,l)=>{const h=r,_=s,q=o,R=u,L=i,ne=n,ie=d,de=c,ce=p,Ue=m,Ce=b,je=v,Se=g,Be=f,De=y;return M(),N("div",pe,[e.showToolbar?(M(),N("div",me,[H("div",be,[V.value.length>0?(M(),N("div",ve,[H("span",fe,"已选择 "+t(V.value.length)+" 项",1),(M(!0),N(K,null,W(e.batchActions,e=>(M(),Z(h,{key:e.key,type:e.type||"default",icon:e.icon,size:"small",onClick:a=>(e=>{w("batch-action",e,V.value)})(e)},{default:Q(()=>[J(t(e.label),1)]),_:2},1032,["type","icon","onClick"]))),128))])):(M(),N("div",ge,[(M(!0),N(K,null,W(e.toolbarActions,e=>(M(),Z(h,{key:e.key,type:e.type||"default",icon:e.icon,size:"small",onClick:a=>(e=>{w("toolbar-action",e)})(e)},{default:Q(()=>[J(t(e.label),1)]),_:2},1032,["type","icon","onClick"]))),128))]))]),H("div",ye,[e.searchable?(M(),N("div",he,[E(_,{modelValue:x.value,"onUpdate:modelValue":l[0]||(l[0]=e=>x.value=e),placeholder:e.searchPlaceholder,size:"small",clearable:"",onInput:F},{prefix:Q(()=>l[7]||(l[7]=[H("i",{class:"el-icon-search"},null,-1)])),_:1},8,["modelValue","placeholder"])])):O("",!0),e.filters&&e.filters.length?(M(),N("div",_e,[(M(!0),N(K,null,W(e.filters,e=>(M(),Z(R,{key:e.key,modelValue:z.value[e.key],"onUpdate:modelValue":a=>z.value[e.key]=a,placeholder:e.placeholder,size:"small",clearable:"",onChange:X},{default:Q(()=>[(M(!0),N(K,null,W(e.options,e=>(M(),Z(q,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"]))),128))])):O("",!0),e.showSettings?(M(),Z(ie,{key:2,trigger:"click"},{dropdown:Q(()=>[E(ne,null,{default:Q(()=>[E(L,{onClick:te},{default:Q(()=>l[9]||(l[9]=[H("i",{class:"el-icon-download"},null,-1),J(" 导出数据 ",-1)])),_:1,__:[9]}),E(L,{onClick:le},{default:Q(()=>l[10]||(l[10]=[H("i",{class:"el-icon-refresh"},null,-1),J(" 刷新数据 ",-1)])),_:1,__:[10]}),E(L,{divided:"",onClick:l[1]||(l[1]=e=>j.value=!0)},{default:Q(()=>l[11]||(l[11]=[H("i",{class:"el-icon-s-tools"},null,-1),J(" 列设置 ",-1)])),_:1,__:[11]})]),_:1})]),default:Q(()=>[E(h,{size:"small",type:"text"},{default:Q(()=>l[8]||(l[8]=[H("i",{class:"el-icon-setting"},null,-1)])),_:1,__:[8]})]),_:1})):O("",!0)])])):O("",!0),H("div",we,[E(Ce,{ref_key:"tableRef",ref:k,data:D.value,loading:e.loading,height:e.height,"max-height":e.maxHeight,stripe:e.stripe,border:e.border,size:e.size,"empty-text":e.emptyText,"row-key":e.rowKey,"default-sort":e.defaultSort,"highlight-current-row":e.highlightCurrentRow,onSelectionChange:A,onSortChange:P,onRowClick:I,onRowDblclick:$,class:"modern-table"},{default:Q(()=>[e.selectable?(M(),Z(de,{key:0,type:"selection",width:"55",align:"center",fixed:"left"})):O("",!0),e.showIndex?(M(),Z(de,{key:1,type:"index",label:"序号",width:"80",align:"center",fixed:"left"})):O("",!0),(M(!0),N(K,null,W(T.value,e=>(M(),Z(de,{key:e.prop,prop:e.prop,label:e.label,width:e.width,"min-width":e.minWidth,fixed:e.fixed,align:e.align||"left",sortable:e.sortable,"show-overflow-tooltip":!1!==e.showOverflowTooltip},Y({_:2},[e.slot?{name:"default",fn:Q(l=>[G(a.$slots,e.slot,{row:l.row,column:e,index:l.$index},void 0,!0)]),key:"0"}:e.formatter?{name:"default",fn:Q(a=>[H("span",{innerHTML:e.formatter(a.row,e,a.row[e.prop],a.$index)},null,8,ke)]),key:"1"}:"tag"===e.type?{name:"default",fn:Q(a=>[E(ce,{type:se(a.row[e.prop],e.tagMap),size:"small"},{default:Q(()=>[J(t(oe(a.row[e.prop],e.tagMap)),1)]),_:2},1032,["type"])]),key:"2"}:"image"===e.type?{name:"default",fn:Q(a=>[E(Ue,{src:a.row[e.prop],"preview-src-list":[a.row[e.prop]],fit:"cover",style:{width:"40px",height:"40px","border-radius":"4px"}},null,8,["src","preview-src-list"])]),key:"3"}:"date"===e.type?{name:"default",fn:Q(a=>[J(t(ue(a.row[e.prop],e.dateFormat)),1)]),key:"4"}:"currency"===e.type?{name:"default",fn:Q(a=>{return[J(" ¥"+t((l=a.row[e.prop],null==l?"0.00":Number(l).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}))),1)];var l}),key:"5"}:"actions"===e.type?{name:"default",fn:Q(a=>{return[H("div",Ve,[(M(!0),N(K,null,W((l=a.row,r=e.actions,r?r.filter(e=>"function"==typeof e.show?e.show(l):!1!==e.show):[]),e=>(M(),Z(h,{key:e.key,type:e.type||"text",size:e.size||"small",icon:e.icon,disabled:e.disabled,onClick:l=>((e,a,l)=>{w("row-action",e,a,l)})(e,a.row,a.$index)},{default:Q(()=>[J(t(e.label),1)]),_:2},1032,["type","size","icon","disabled","onClick"]))),128))])];var l,r}),key:"6"}:void 0]),1032,["prop","label","width","min-width","fixed","align","sortable","show-overflow-tooltip"]))),128))]),_:3},8,["data","loading","height","max-height","stripe","border","size","empty-text","row-key","default-sort","highlight-current-row"])]),e.showPagination?(M(),N("div",xe,[E(je,{"current-page":U.value,"onUpdate:currentPage":l[2]||(l[2]=e=>U.value=e),"page-size":C.value,"onUpdate:pageSize":l[3]||(l[3]=e=>C.value=e),"page-sizes":e.pageSizes,total:e.total,layout:e.paginationLayout,background:!0,onSizeChange:ae,onCurrentChange:ee},null,8,["current-page","page-size","page-sizes","total","layout"])])):O("",!0),E(De,{modelValue:j.value,"onUpdate:modelValue":l[6]||(l[6]=e=>j.value=e),title:"列设置",width:"500px","close-on-click-modal":!1},{footer:Q(()=>[E(h,{onClick:l[5]||(l[5]=e=>j.value=!1)},{default:Q(()=>l[12]||(l[12]=[J("取消",-1)])),_:1,__:[12]}),E(h,{type:"primary",onClick:re},{default:Q(()=>l[13]||(l[13]=[J("确定",-1)])),_:1,__:[13]})]),default:Q(()=>[H("div",ze,[E(Be,{modelValue:S.value,"onUpdate:modelValue":l[4]||(l[4]=e=>S.value=e)},{default:Q(()=>[(M(!0),N(K,null,W(B.value,e=>(M(),N("div",{key:e.prop,class:"column-item"},[E(Se,{label:e.prop},{default:Q(()=>[J(t(e.label),1)]),_:2},1032,["label"])]))),128))]),_:1},8,["modelValue"])])]),_:1},8,["modelValue"])])}}},Ce=e(Ue,[["__scopeId","data-v-d88b4c9c"]]),je={class:"avatar-upload"},Se=["src"],Be={class:"dialog-footer"},De=e({__name:"UserDialog",props:{modelValue:{type:Boolean,default:!1},userData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:l}){const n=e,i=l,d=a(),c=q(null),p=q(!1),m=q([]),b=R({get:()=>n.modelValue,set:e=>i("update:modelValue",e)}),v=R(()=>n.userData&&n.userData.id),f=R(()=>"/api/v1/upload/avatar"),g=R(()=>({Authorization:`Bearer ${d.token}`})),S=X({username:"",name:"",email:"",phone:"",role:"user",status:1,password:"",confirmPassword:"",balance:0,frozen_balance:0,avatar:"",remark:"",distributor_account:"",distributor_phone:"",distributor_alipay:"",distributor_wechat:"",distributor_real_name:"",parent_distributor_id:null}),B=(e,a,l)=>{a!==S.password?l(new Error("两次输入的密码不一致")):l()},D=R(()=>{const e={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:50,message:"姓名长度在 2 到 50 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],role:[{required:!0,message:"请选择用户角色",trigger:"change"}],status:[{required:!0,message:"请选择用户状态",trigger:"change"}]};return v.value||(e.password=[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],e.confirmPassword=[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:B,trigger:"blur"}]),e});ee(()=>n.userData,e=>{e&&Object.keys(e).length>0?Object.assign(S,{username:e.username||"",name:e.name||"",email:e.email||"",phone:e.phone||"",role:e.role||"user",status:void 0!==e.status?e.status:1,balance:e.balance||0,frozen_balance:e.frozen_balance||0,avatar:e.avatar||"",remark:e.remark||"",distributor_account:e.distributor_account||"",distributor_phone:e.distributor_phone||"",distributor_alipay:e.distributor_alipay||"",distributor_wechat:e.distributor_wechat||"",distributor_real_name:e.distributor_real_name||"",parent_distributor_id:e.parent_distributor_id||null}):Object.assign(S,{username:"",name:"",email:"",phone:"",role:"user",status:1,password:"",confirmPassword:"",balance:0,frozen_balance:0,avatar:"",remark:"",distributor_account:"",distributor_phone:"",distributor_alipay:"",distributor_wechat:"",distributor_real_name:"",parent_distributor_id:null})},{immediate:!0,deep:!0});const T=e=>{e.success?(S.avatar=e.data.url,h.success("头像上传成功")):h.error("头像上传失败")},A=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<2;return a?!!l||(h.error("图片大小不能超过 2MB!"),!1):(h.error("只能上传图片文件!"),!1)},P=async()=>{if(c.value)try{await c.value.validate(),p.value=!0;const e={...S};delete e.confirmPassword,v.value&&!e.password&&delete e.password,v.value?(await te(n.userData.id,e),h.success("用户更新成功")):(await re(e),h.success("用户创建成功")),i("success"),I()}catch(e){console.error("提交失败:",e),e.response?.data?.message?h.error(e.response.data.message):h.error(v.value?"更新失败":"创建失败")}finally{p.value=!1}},I=()=>{c.value?.resetFields(),b.value=!1};return L(()=>{(async()=>{try{m.value=[]}catch(e){console.error("获取分销员列表失败:",e)}})()}),(e,a)=>{const l=s,n=V,i=k,d=w,h=o,B=u,$=x,F=z,q=C,R=U,L=_,Y=r,G=y;return M(),Z(G,{modelValue:b.value,"onUpdate:modelValue":a[17]||(a[17]=e=>b.value=e),title:v.value?"编辑用户":"创建用户",width:"700px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:I},{footer:Q(()=>[H("div",Be,[E(Y,{onClick:I},{default:Q(()=>a[20]||(a[20]=[J("取消",-1)])),_:1,__:[20]}),E(Y,{type:"primary",loading:p.value,onClick:P},{default:Q(()=>[J(t(v.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:Q(()=>[E(L,{ref_key:"formRef",ref:c,model:S,rules:D.value,"label-width":"100px",class:"user-form"},{default:Q(()=>[E(d,{gutter:20},{default:Q(()=>[E(i,{span:12},{default:Q(()=>[E(n,{label:"用户名",prop:"username"},{default:Q(()=>[E(l,{modelValue:S.username,"onUpdate:modelValue":a[0]||(a[0]=e=>S.username=e),placeholder:"请输入用户名",maxlength:"50",disabled:v.value},null,8,["modelValue","disabled"])]),_:1})]),_:1}),E(i,{span:12},{default:Q(()=>[E(n,{label:"姓名",prop:"name"},{default:Q(()=>[E(l,{modelValue:S.name,"onUpdate:modelValue":a[1]||(a[1]=e=>S.name=e),placeholder:"请输入真实姓名",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),E(d,{gutter:20},{default:Q(()=>[E(i,{span:12},{default:Q(()=>[E(n,{label:"邮箱",prop:"email"},{default:Q(()=>[E(l,{modelValue:S.email,"onUpdate:modelValue":a[2]||(a[2]=e=>S.email=e),placeholder:"请输入邮箱地址",type:"email"},null,8,["modelValue"])]),_:1})]),_:1}),E(i,{span:12},{default:Q(()=>[E(n,{label:"手机号",prop:"phone"},{default:Q(()=>[E(l,{modelValue:S.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>S.phone=e),placeholder:"请输入手机号",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),E(d,{gutter:20},{default:Q(()=>[E(i,{span:12},{default:Q(()=>[E(n,{label:"用户角色",prop:"role"},{default:Q(()=>[E(B,{modelValue:S.role,"onUpdate:modelValue":a[4]||(a[4]=e=>S.role=e),placeholder:"请选择角色",style:{width:"100%"}},{default:Q(()=>[E(h,{label:"普通用户",value:"user"}),E(h,{label:"分销员",value:"distributor"}),E(h,{label:"代理商",value:"agent"}),E(h,{label:"分站管理员",value:"substation"}),E(h,{label:"管理员",value:"admin"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),E(i,{span:12},{default:Q(()=>[E(n,{label:"用户状态",prop:"status"},{default:Q(()=>[E(B,{modelValue:S.status,"onUpdate:modelValue":a[5]||(a[5]=e=>S.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:Q(()=>[E(h,{label:"正常",value:1}),E(h,{label:"禁用",value:0}),E(h,{label:"待审核",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),v.value?O("",!0):(M(),Z(d,{key:0,gutter:20},{default:Q(()=>[E(i,{span:12},{default:Q(()=>[E(n,{label:"密码",prop:"password"},{default:Q(()=>[E(l,{modelValue:S.password,"onUpdate:modelValue":a[6]||(a[6]=e=>S.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),E(i,{span:12},{default:Q(()=>[E(n,{label:"确认密码",prop:"confirmPassword"},{default:Q(()=>[E(l,{modelValue:S.confirmPassword,"onUpdate:modelValue":a[7]||(a[7]=e=>S.confirmPassword=e),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})),E(d,{gutter:20},{default:Q(()=>[E(i,{span:12},{default:Q(()=>[E(n,{label:"账户余额",prop:"balance"},{default:Q(()=>[E($,{modelValue:S.balance,"onUpdate:modelValue":a[8]||(a[8]=e=>S.balance=e),min:0,precision:2,style:{width:"100%"},placeholder:"0.00"},null,8,["modelValue"])]),_:1})]),_:1}),E(i,{span:12},{default:Q(()=>[E(n,{label:"冻结余额",prop:"frozen_balance"},{default:Q(()=>[E($,{modelValue:S.frozen_balance,"onUpdate:modelValue":a[9]||(a[9]=e=>S.frozen_balance=e),min:0,precision:2,style:{width:"100%"},placeholder:"0.00"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),"distributor"===S.role?(M(),N(K,{key:1},[E(F,{"content-position":"left"},{default:Q(()=>a[18]||(a[18]=[J("分销员信息",-1)])),_:1,__:[18]}),E(d,{gutter:20},{default:Q(()=>[E(i,{span:12},{default:Q(()=>[E(n,{label:"分销员账号",prop:"distributor_account"},{default:Q(()=>[E(l,{modelValue:S.distributor_account,"onUpdate:modelValue":a[10]||(a[10]=e=>S.distributor_account=e),placeholder:"请输入分销员账号",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),E(i,{span:12},{default:Q(()=>[E(n,{label:"分销员手机",prop:"distributor_phone"},{default:Q(()=>[E(l,{modelValue:S.distributor_phone,"onUpdate:modelValue":a[11]||(a[11]=e=>S.distributor_phone=e),placeholder:"请输入分销员手机号",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),E(d,{gutter:20},{default:Q(()=>[E(i,{span:12},{default:Q(()=>[E(n,{label:"支付宝账号",prop:"distributor_alipay"},{default:Q(()=>[E(l,{modelValue:S.distributor_alipay,"onUpdate:modelValue":a[12]||(a[12]=e=>S.distributor_alipay=e),placeholder:"请输入支付宝账号"},null,8,["modelValue"])]),_:1})]),_:1}),E(i,{span:12},{default:Q(()=>[E(n,{label:"微信号",prop:"distributor_wechat"},{default:Q(()=>[E(l,{modelValue:S.distributor_wechat,"onUpdate:modelValue":a[13]||(a[13]=e=>S.distributor_wechat=e),placeholder:"请输入微信号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),E(n,{label:"真实姓名",prop:"distributor_real_name"},{default:Q(()=>[E(l,{modelValue:S.distributor_real_name,"onUpdate:modelValue":a[14]||(a[14]=e=>S.distributor_real_name=e),placeholder:"请输入真实姓名",maxlength:"50"},null,8,["modelValue"])]),_:1}),E(n,{label:"上级分销员",prop:"parent_distributor_id"},{default:Q(()=>[E(B,{modelValue:S.parent_distributor_id,"onUpdate:modelValue":a[15]||(a[15]=e=>S.parent_distributor_id=e),placeholder:"请选择上级分销员",style:{width:"100%"},filterable:"",clearable:""},{default:Q(()=>[(M(!0),N(K,null,W(m.value,e=>(M(),Z(h,{key:e.id,label:`${e.name} (${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})],64)):O("",!0),E(n,{label:"头像"},{default:Q(()=>[H("div",je,[E(R,{class:"avatar-uploader",action:f.value,headers:g.value,"show-file-list":!1,"on-success":T,"before-upload":A,accept:"image/*"},{default:Q(()=>[S.avatar?(M(),N("img",{key:0,src:S.avatar,class:"avatar",alt:"用户头像"},null,8,Se)):(M(),Z(q,{key:1,class:"avatar-uploader-icon"},{default:Q(()=>[E(ae(j))]),_:1}))]),_:1},8,["action","headers"]),a[19]||(a[19]=H("div",{class:"upload-tip"},"建议尺寸：200x200px",-1))])]),_:1}),E(n,{label:"备注"},{default:Q(()=>[E(l,{modelValue:S.remark,"onUpdate:modelValue":a[16]||(a[16]=e=>S.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-05469c10"]]),Te={class:"balance-info"},Ae={class:"user-info"},Pe={class:"user-details"},Ie={class:"username"},$e={class:"name"},Fe={class:"current-balance"},qe={class:"balance-item"},Re={class:"value"},Le={class:"balance-item"},Ne={class:"value"},Me={class:"preview-balance"},Oe={key:0,class:"warning-text"},He={class:"dialog-footer"},Ee=e({__name:"BalanceDialog",props:{modelValue:{type:Boolean,default:!1},userData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:a}){const l=e,n=a,i=q(null),d=q(!1),c=R({get:()=>l.modelValue,set:e=>n("update:modelValue",e)}),p=X({type:"increase",amount:0,reason:"",remark:""}),m={type:[{required:!0,message:"请选择调整类型",trigger:"change"}],amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{type:"number",min:.01,message:"调整金额必须大于0.01",trigger:"blur"}],reason:[{required:!0,message:"请选择调整原因",trigger:"change"}],remark:[{required:!0,message:"请输入调整说明",trigger:"blur"},{min:5,max:200,message:"说明长度在 5 到 200 个字符",trigger:"blur"}]},b=R(()=>{const e=l.userData.balance||0,a=p.amount||0;switch(p.type){case"increase":return e+a;case"decrease":return e-a;case"set":return a;default:return e}});ee(c,e=>{e&&Object.assign(p,{type:"increase",amount:0,reason:"",remark:""})});const v=async()=>{if(i.value)try{await i.value.validate();const e=`确定要${g(p.type)} ¥${p.amount.toFixed(2)} 吗？`;await A.confirm(e,"确认调整",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),d.value=!0;const a={type:p.type,amount:p.amount,reason:p.reason,remark:p.remark};await se(l.userData.id,a),h.success("余额调整成功"),n("success"),f()}catch(e){"cancel"!==e&&(console.error("调整失败:",e),e.response?.data?.message?h.error(e.response.data.message):h.error("余额调整失败"))}finally{d.value=!1}},f=()=>{i.value?.resetFields(),c.value=!1},g=e=>({increase:"增加余额",decrease:"减少余额",set:"设置余额"}[e]||"调整余额");return(a,l)=>{const n=S,g=D,h=B,w=V,k=x,z=o,U=u,C=s,j=_,A=r,P=y;return M(),Z(P,{modelValue:c.value,"onUpdate:modelValue":l[4]||(l[4]=e=>c.value=e),title:"调整用户余额",width:"500px","close-on-click-modal":!1,onClose:f},{footer:Q(()=>[H("div",He,[E(A,{onClick:f},{default:Q(()=>l[10]||(l[10]=[J("取消",-1)])),_:1,__:[10]}),E(A,{type:"primary",loading:d.value,disabled:b.value<0,onClick:v},{default:Q(()=>l[11]||(l[11]=[J(" 确认调整 ",-1)])),_:1,__:[11]},8,["loading","disabled"])])]),default:Q(()=>[H("div",Te,[H("div",Ae,[E(n,{src:e.userData.avatar,alt:e.userData.username,size:"large"},{default:Q(()=>[J(t(e.userData.username?.charAt(0).toUpperCase()),1)]),_:1},8,["src","alt"]),H("div",Pe,[H("div",Ie,t(e.userData.username),1),H("div",$e,t(e.userData.name),1)])]),H("div",Fe,[H("div",qe,[l[5]||(l[5]=H("span",{class:"label"},"当前余额：",-1)),H("span",Re,"¥"+t((e.userData.balance||0).toFixed(2)),1)]),H("div",Le,[l[6]||(l[6]=H("span",{class:"label"},"冻结余额：",-1)),H("span",Ne,"¥"+t((e.userData.frozen_balance||0).toFixed(2)),1)])])]),E(j,{ref_key:"formRef",ref:i,model:p,rules:m,"label-width":"100px",class:"balance-form"},{default:Q(()=>[E(w,{label:"调整类型",prop:"type"},{default:Q(()=>[E(h,{modelValue:p.type,"onUpdate:modelValue":l[0]||(l[0]=e=>p.type=e)},{default:Q(()=>[E(g,{label:"increase"},{default:Q(()=>l[7]||(l[7]=[J("增加余额",-1)])),_:1,__:[7]}),E(g,{label:"decrease"},{default:Q(()=>l[8]||(l[8]=[J("减少余额",-1)])),_:1,__:[8]}),E(g,{label:"set"},{default:Q(()=>l[9]||(l[9]=[J("设置余额",-1)])),_:1,__:[9]})]),_:1},8,["modelValue"])]),_:1}),E(w,{label:"调整金额",prop:"amount"},{default:Q(()=>[E(k,{modelValue:p.amount,"onUpdate:modelValue":l[1]||(l[1]=e=>p.amount=e),min:.01,max:999999,precision:2,style:{width:"100%"},placeholder:"请输入调整金额"},null,8,["modelValue"])]),_:1}),E(w,{label:"调整原因",prop:"reason"},{default:Q(()=>[E(U,{modelValue:p.reason,"onUpdate:modelValue":l[2]||(l[2]=e=>p.reason=e),placeholder:"请选择调整原因",style:{width:"100%"}},{default:Q(()=>[E(z,{label:"系统调整",value:"system_adjust"}),E(z,{label:"充值",value:"recharge"}),E(z,{label:"提现",value:"withdraw"}),E(z,{label:"佣金结算",value:"commission"}),E(z,{label:"退款",value:"refund"}),E(z,{label:"奖励",value:"reward"}),E(z,{label:"惩罚",value:"penalty"}),E(z,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),E(w,{label:"备注说明",prop:"remark"},{default:Q(()=>[E(C,{modelValue:p.remark,"onUpdate:modelValue":l[3]||(l[3]=e=>p.remark=e),type:"textarea",rows:3,placeholder:"请输入调整说明",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),E(w,{label:"调整后余额"},{default:Q(()=>[H("div",Me,[H("span",{class:T(["preview-value",{negative:b.value<0}])}," ¥"+t(b.value.toFixed(2)),3),b.value<0?(M(),N("span",Oe," (余额不足，请检查调整金额) ")):O("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-4717f023"]]),Ke={class:"user-detail"},We={key:0,class:"detail-content"},Ze={class:"info-section"},Qe={class:"user-header"},Je={class:"user-info"},Ye={class:"username"},Ge={class:"user-id"},Xe={class:"money-text"},ea={class:"info-section"},aa={class:"stat-card"},la={class:"stat-value"},ta={class:"stat-card"},ra={class:"stat-value"},sa={class:"stat-card"},oa={class:"stat-value"},ua={class:"info-section"},na={class:"money-text"},ia={class:"info-section"},da={class:"money-text"},ca=e({__name:"UserDetailDrawer",props:{modelValue:{type:Boolean,default:!1},userId:{type:[String,Number],default:null}},emits:["update:modelValue"],setup(e,{emit:a}){const l=e,r=a,s=R({get:()=>l.modelValue,set:e=>r("update:modelValue",e)}),o=q(!1),u=q(null);ee(()=>l.userId,e=>{e&&s.value&&n()}),ee(s,e=>{e&&l.userId&&n()});const n=async()=>{if(l.userId){o.value=!0;try{const{data:e}=await oe(l.userId);u.value=e}catch(e){console.error("获取用户详情失败:",e)}finally{o.value=!1}}},i=e=>({user:"普通用户",distributor:"分销员",agent:"代理商",admin:"管理员"}[e]||"未知"),d=e=>({active:"正常",disabled:"禁用",pending:"待审核"}[e]||"未知"),m=e=>({paid:"success",pending:"warning",cancelled:"danger"}[e]||""),v=e=>({paid:"已支付",pending:"待支付",cancelled:"已取消"}[e]||"未知"),f=e=>({recharge:"充值",consume:"消费",refund:"退款",reward:"奖励",deduct:"扣费"}[e]||"未知"),g=()=>{s.value=!1,u.value=null};return(e,a)=>{const l=S,r=p,n=I,y=P,h=k,_=w,V=c,x=b,z=F,U=$;return M(),Z(z,{modelValue:s.value,"onUpdate:modelValue":a[0]||(a[0]=e=>s.value=e),title:"用户详情",size:"600px",onClose:g},{default:Q(()=>{return[le((M(),N("div",Ke,[u.value?(M(),N("div",We,[H("div",Ze,[a[1]||(a[1]=H("h3",null,"基本信息",-1)),H("div",Qe,[E(l,{size:80,src:u.value.avatar,alt:u.value.username},{default:Q(()=>[J(t(u.value.username?.charAt(0).toUpperCase()),1)]),_:1},8,["src","alt"]),H("div",Je,[H("div",Ye,t(u.value.username),1),H("div",Ge,"ID: "+t(u.value.id),1),E(r,{type:(e=u.value.status,{active:"success",disabled:"danger",pending:"warning"}[e]||"")},{default:Q(()=>[J(t(d(u.value.status)),1)]),_:1},8,["type"])])]),E(y,{column:2,border:""},{default:Q(()=>[E(n,{label:"邮箱"},{default:Q(()=>[J(t(u.value.email),1)]),_:1}),E(n,{label:"手机号"},{default:Q(()=>[J(t(u.value.phone),1)]),_:1}),E(n,{label:"角色"},{default:Q(()=>{return[E(r,{type:(e=u.value.role,{user:"",distributor:"success",agent:"warning",admin:"danger"}[e]||"")},{default:Q(()=>[J(t(i(u.value.role)),1)]),_:1},8,["type"])];var e}),_:1}),E(n,{label:"余额"},{default:Q(()=>[H("span",Xe,"¥"+t(u.value.balance?.toFixed(2)),1)]),_:1}),E(n,{label:"注册时间"},{default:Q(()=>[J(t(ae(ce)(u.value.created_at)),1)]),_:1}),E(n,{label:"最后登录"},{default:Q(()=>[J(t(ae(ce)(u.value.last_login)),1)]),_:1})]),_:1})]),H("div",ea,[a[5]||(a[5]=H("h3",null,"统计信息",-1)),E(_,{gutter:16},{default:Q(()=>[E(h,{span:8},{default:Q(()=>[H("div",aa,[H("div",la,t(u.value.stats?.total_orders||0),1),a[2]||(a[2]=H("div",{class:"stat-label"},"总订单数",-1))])]),_:1}),E(h,{span:8},{default:Q(()=>[H("div",ta,[H("div",ra,"¥"+t((u.value.stats?.total_amount||0).toFixed(2)),1),a[3]||(a[3]=H("div",{class:"stat-label"},"总消费金额",-1))])]),_:1}),E(h,{span:8},{default:Q(()=>[H("div",sa,[H("div",oa,t(u.value.stats?.total_groups||0),1),a[4]||(a[4]=H("div",{class:"stat-label"},"加入群组数",-1))])]),_:1})]),_:1})]),H("div",ua,[a[6]||(a[6]=H("h3",null,"最近订单",-1)),E(x,{data:u.value.recent_orders,size:"small"},{default:Q(()=>[E(V,{label:"订单号",prop:"order_no",width:"150"}),E(V,{label:"金额",width:"100"},{default:Q(({row:e})=>[H("span",na,"¥"+t(e.amount?.toFixed(2)),1)]),_:1}),E(V,{label:"状态",width:"80"},{default:Q(({row:e})=>[E(r,{type:m(e.status),size:"small"},{default:Q(()=>[J(t(v(e.status)),1)]),_:2},1032,["type"])]),_:1}),E(V,{label:"时间"},{default:Q(({row:e})=>[J(t(ae(ce)(e.created_at)),1)]),_:1})]),_:1},8,["data"])]),H("div",ia,[a[7]||(a[7]=H("h3",null,"余额变动记录",-1)),E(x,{data:u.value.balance_logs,size:"small"},{default:Q(()=>[E(V,{label:"类型",width:"80"},{default:Q(({row:e})=>{return[E(r,{type:(a=e.type,{recharge:"success",consume:"warning",refund:"info",reward:"success",deduct:"danger"}[a]||""),size:"small"},{default:Q(()=>[J(t(f(e.type)),1)]),_:2},1032,["type"])];var a}),_:1}),E(V,{label:"金额",width:"100"},{default:Q(({row:e})=>[H("span",{class:T(e.amount>0?"positive-amount":"negative-amount")},t(e.amount>0?"+":"")+"¥"+t(e.amount?.toFixed(2)),3)]),_:1}),E(V,{label:"余额",width:"100"},{default:Q(({row:e})=>[H("span",da,"¥"+t(e.balance?.toFixed(2)),1)]),_:1}),E(V,{label:"备注",prop:"remark"}),E(V,{label:"时间",width:"150"},{default:Q(({row:e})=>[J(t(ae(ce)(e.created_at)),1)]),_:1})]),_:1},8,["data"])])])):O("",!0)])),[[U,o.value]])];var e}),_:1},8,["modelValue"])}}},[["__scopeId","data-v-b19c969b"]]),pa=e({__name:"UserList",setup(e){const a=q(!1),t=q([]),s=q(!1),o=q(!1),u=q(!1),n=q(!1),i=q({}),d=q(null),c=q({total_users:1234,active_users:1156,distributors:89,agents:23}),p=q({current:1,size:20,total:0}),m=R(()=>[{icon:"el-icon-user",label:"总用户数",value:c.value.total_users,color:"primary",change:"+12.5%",changeType:"increase"},{icon:"el-icon-user-solid",label:"活跃用户",value:c.value.active_users,color:"success",change:"+8.3%",changeType:"increase"},{icon:"el-icon-s-custom",label:"分销员",value:c.value.distributors,color:"warning",change:"+15.2%",changeType:"increase"},{icon:"el-icon-medal",label:"代理商",value:c.value.agents,color:"info",change:"****%",changeType:"increase"}]),b=q([{type:"selection",width:55},{prop:"id",label:"用户ID",width:80},{prop:"avatar",label:"头像",width:80,type:"image",fallback:e=>e.username?.charAt(0)?.toUpperCase()||"U"},{prop:"username",label:"用户名",width:120},{prop:"email",label:"邮箱",width:180},{prop:"phone",label:"手机号",width:120},{prop:"role",label:"角色",width:100,type:"tag",tagMap:{user:{text:"普通用户",type:"info"},distributor:{text:"分销员",type:"success"},agent:{text:"代理商",type:"warning"},admin:{text:"管理员",type:"danger"}}},{prop:"balance",label:"余额",width:100,type:"currency"},{prop:"status",label:"状态",width:100,type:"tag",tagMap:{active:{text:"正常",type:"success"},disabled:{text:"禁用",type:"danger"},pending:{text:"待审核",type:"warning"}}},{prop:"created_at",label:"注册时间",width:160,type:"date"},{prop:"last_login",label:"最后登录",width:160,type:"date"},{type:"actions",label:"操作",width:200,fixed:"right",actions:[{key:"edit",label:"编辑",type:"primary",size:"small"},{key:"balance",label:"调整余额",type:"warning",size:"small"},{key:"more",label:"更多",type:"info",size:"small",dropdown:!0,dropdownItems:[{key:"view",label:"查看详情"},{key:"reset",label:"重置密码"},{key:"toggle",label:"切换状态"},{key:"delete",label:"删除用户",divided:!0,danger:!0}]}]}]),v=q({fields:[{key:"keyword",type:"input",placeholder:"搜索用户名、邮箱、手机号",width:200},{key:"role",type:"select",placeholder:"用户角色",width:120,options:[{label:"全部",value:""},{label:"普通用户",value:"user"},{label:"分销员",value:"distributor"},{label:"代理商",value:"agent"},{label:"管理员",value:"admin"}]},{key:"status",type:"select",placeholder:"用户状态",width:120,options:[{label:"全部",value:""},{label:"正常",value:"active"},{label:"禁用",value:"disabled"},{label:"待审核",value:"pending"}]}]}),f=q([{key:"enable",label:"批量启用",type:"success",icon:"el-icon-check"},{key:"disable",label:"批量禁用",type:"warning",icon:"el-icon-close"},{key:"delete",label:"批量删除",type:"danger",icon:"el-icon-delete"}]),g=async(e={})=>{s.value=!0;try{const a={page:p.value.current,limit:p.value.size,...e},{data:l}=await ue(a);t.value=l.list||[],p.value.total=l.total||0}catch(a){console.error("获取用户列表失败:",a),h.error("获取用户列表失败")}finally{s.value=!1}},y=async()=>{try{const{data:e}=await ne();c.value=e}catch(e){console.error("获取统计数据失败:",e)}},_=e=>{p.value.current=1,g(e)},w=e=>{p.value.current=e,g()},k=e=>{p.value.size=e,p.value.current=1,g()},V=(e,a)=>{switch(e.key){case"edit":U(a);break;case"balance":C(a);break;case"view":j(a.id);break;case"reset":S(a.id);break;case"toggle":B(a);break;case"delete":D(a.id)}},x=(e,a)=>{switch(e.key){case"enable":T(a,"active");break;case"disable":T(a,"disabled");break;case"delete":P(a)}},z=()=>{i.value={},o.value=!0},U=e=>{i.value={...e},o.value=!0},C=e=>{i.value={...e},u.value=!0},j=e=>{d.value=e,n.value=!0},S=async e=>{try{await A.confirm("确定要重置该用户的密码吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),h.success("密码重置成功，新密码已发送到用户邮箱"),g()}catch(a){"cancel"!==a&&h.error("重置密码失败")}},B=async e=>{const a="active"===e.status?"disabled":"active",l="disabled"===a?"禁用":"启用";try{await A.confirm(`确定要${l}该用户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await de(e.id,a),h.success(`${l}成功`),g()}catch(t){"cancel"!==t&&h.error("操作失败")}},D=async e=>{try{await A.confirm("确定要删除该用户吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await ie(e),h.success("删除成功"),g()}catch(a){"cancel"!==a&&h.error("删除失败")}},T=async(e,a)=>{const l="disabled"===a?"禁用":"启用";try{await A.confirm(`确定要批量${l}选中的用户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});e.map(e=>e.id);h.success(`批量${l}成功`),g()}catch(t){"cancel"!==t&&h.error("批量操作失败")}},P=async e=>{try{await A.confirm("确定要批量删除选中的用户吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"});e.map(e=>e.id);h.success("批量删除成功"),g()}catch(a){"cancel"!==a&&h.error("批量删除失败")}},I=async()=>{try{h.success("导出成功")}catch(e){h.error("导出失败")}},$=()=>{g(),y()},F=()=>{g()};return L(()=>{g(),y()}),(e,c)=>{const g=r;return M(),Z(l,{title:"用户管理",subtitle:"管理平台所有用户信息",icon:"el-icon-user",stats:m.value,loading:a.value},{actions:Q(()=>[E(g,{class:"modern-btn success",onClick:z},{default:Q(()=>c[3]||(c[3]=[H("i",{class:"el-icon-plus"},null,-1),J(" 添加用户 ",-1)])),_:1,__:[3]}),E(g,{class:"modern-btn secondary",onClick:I},{default:Q(()=>c[4]||(c[4]=[H("i",{class:"el-icon-download"},null,-1),J(" 导出数据 ",-1)])),_:1,__:[4]})]),default:Q(()=>[E(Ce,{data:t.value,columns:b.value,loading:s.value,pagination:p.value,"search-config":v.value,"batch-actions":f.value,onSearch:_,onPageChange:w,onSizeChange:k,onBatchAction:x,onRowAction:V,class:"user-table"},null,8,["data","columns","loading","pagination","search-config","batch-actions"]),E(De,{modelValue:o.value,"onUpdate:modelValue":c[0]||(c[0]=e=>o.value=e),"user-data":i.value,onSuccess:$},null,8,["modelValue","user-data"]),E(Ee,{modelValue:u.value,"onUpdate:modelValue":c[1]||(c[1]=e=>u.value=e),"user-data":i.value,onSuccess:F},null,8,["modelValue","user-data"]),E(ca,{modelValue:n.value,"onUpdate:modelValue":c[2]||(c[2]=e=>n.value=e),"user-id":d.value},null,8,["modelValue","user-id"])]),_:1},8,["stats","loading"])}}},[["__scopeId","data-v-199018c9"]]);export{pa as default};
