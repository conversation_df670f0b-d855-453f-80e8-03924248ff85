import{C as e,a as t,L as s,P as a,b as o,c as i,p as n,d as p,e as r,i as d}from"./chart-Bup65vvO.js";import{_ as l}from"./index-D2bI4m-v.js";import{r as c,e as u,H as m,d as g,k as y,l as h,t as f}from"./vue-vendor-DGsK9sC4.js";import{p as v}from"./element-plus-DcSKpKA8.js";const b=l({__name:"LineChart",props:{data:{type:Object,required:!0},options:{type:Object,default:()=>({})},height:{type:String,default:"400px"}},setup(l){e.register(t,s,a,o,i,n,p,r,d);const b=l,j=c();let x=null;const _={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{x:{display:!0,grid:{display:!1}},y:{display:!0,beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"}}},elements:{line:{tension:.4},point:{radius:4,hoverRadius:6}}},C=()=>{x&&(x.data=b.data,x.options={..._,...b.options},x.update())};return u(()=>{(()=>{x&&x.destroy();const t=j.value.getContext("2d");x=new e(t,{type:"line",data:b.data,options:{..._,...b.options}})})()}),m(()=>{x&&x.destroy()}),g(()=>b.data,C,{deep:!0}),g(()=>b.options,C,{deep:!0}),(e,t)=>(h(),y("div",{class:"line-chart",style:v({height:l.height})},[f("canvas",{ref_key:"chartRef",ref:j},null,512)],4))}},[["__scopeId","data-v-66bbcf93"]]);export{b as L};
