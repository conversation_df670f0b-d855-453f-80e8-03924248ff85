<template>
  <el-dialog
    :model-value="visible"
    title="权限配置"
    width="800px"
    @update:model-value="$emit('update:visible', $event)"
    @close="handleClose"
  >
    <div class="permission-config">
      <div class="role-info">
        <h4>角色：{{ roleData.name }}</h4>
        <p>{{ roleData.description }}</p>
      </div>
      
      <el-divider />
      
      <div class="permission-tree">
        <el-tree
          ref="treeRef"
          :data="permissionTree"
          :props="treeProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="checkedKeys"
          @check="handleCheck"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <el-icon v-if="data.icon" class="node-icon">
                <component :is="data.icon" />
              </el-icon>
              <span>{{ data.name }}</span>
              <el-tag v-if="data.type" size="small" :type="getTagType(data.type)">
                {{ data.type }}
              </el-tag>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const treeRef = ref()
const loading = ref(false)
const checkedKeys = ref([])

const treeProps = {
  children: 'children',
  label: 'name'
}

// 模拟权限树数据
const permissionTree = ref([
  {
    id: 1,
    name: '系统管理',
    icon: 'Setting',
    type: 'menu',
    children: [
      { id: 11, name: '用户管理', type: 'menu' },
      { id: 12, name: '角色管理', type: 'menu' },
      { id: 13, name: '权限管理', type: 'menu' }
    ]
  },
  {
    id: 2,
    name: '社群管理',
    icon: 'Comment',
    type: 'menu',
    children: [
      { id: 21, name: '社群列表', type: 'menu' },
      { id: 22, name: '模板管理', type: 'menu' }
    ]
  },
  {
    id: 3,
    name: '数据统计',
    icon: 'DataAnalysis',
    type: 'menu',
    children: [
      { id: 31, name: '用户统计', type: 'menu' },
      { id: 32, name: '收入统计', type: 'menu' }
    ]
  }
])

watch(() => props.visible, (val) => {
  if (val) {
    // 模拟获取角色已有权限
    checkedKeys.value = [11, 21, 31]
  }
})

const getTagType = (type) => {
  const types = {
    menu: 'primary',
    button: 'success',
    api: 'warning'
  }
  return types[type] || 'info'
}

const handleCheck = (data, checked) => {
  console.log('权限选择变化:', data, checked)
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSubmit = async () => {
  try {
    loading.value = true
    const checkedNodes = treeRef.value.getCheckedKeys()
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('保存权限配置:', checkedNodes)
    ElMessage.success('权限配置保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.permission-config {
  .role-info {
    h4 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }
  
  .permission-tree {
    max-height: 400px;
    overflow-y: auto;
    
    .tree-node {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .node-icon {
        color: #409eff;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
