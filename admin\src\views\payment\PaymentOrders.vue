<template>
  <div class="payment-orders">
    <PageLayout title="支付订单" subtitle="管理和监控所有支付订单">
      <!-- 订单统计 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card pending">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ orderStats.pending }}</div>
                <div class="stat-label">待支付</div>
                <div class="stat-change">+{{ orderStats.pendingChange }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card success">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ orderStats.paid }}</div>
                <div class="stat-label">已支付</div>
                <div class="stat-change">+{{ orderStats.paidChange }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card failed">
              <div class="stat-icon">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ orderStats.failed }}</div>
                <div class="stat-label">支付失败</div>
                <div class="stat-change">+{{ orderStats.failedChange }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card refunded">
              <div class="stat-icon">
                <el-icon><RefreshLeft /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ orderStats.refunded }}</div>
                <div class="stat-label">已退款</div>
                <div class="stat-change">+{{ orderStats.refundedChange }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filter-section">
        <el-card class="filter-card">
          <div class="filter-content">
            <div class="filter-left">
              <el-input
                v-model="searchQuery"
                placeholder="搜索订单号、用户、商品..."
                style="width: 300px"
                @keyup.enter="handleSearch"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              
              <el-select v-model="filters.status" placeholder="订单状态" style="width: 120px">
                <el-option label="全部" value="" />
                <el-option label="待支付" value="pending" />
                <el-option label="已支付" value="paid" />
                <el-option label="支付失败" value="failed" />
                <el-option label="已退款" value="refunded" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
              
              <el-select v-model="filters.payment_method" placeholder="支付方式" style="width: 120px">
                <el-option label="全部" value="" />
                <el-option label="支付宝" value="alipay" />
                <el-option label="微信支付" value="wechat" />
                <el-option label="易支付" value="easypay" />
                <el-option label="银行卡" value="bank" />
              </el-select>
              
              <el-date-picker
                v-model="filters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </div>
            
            <div class="filter-right">
              <el-button @click="handleSearch" type="primary">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetFilters">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button @click="exportOrders" :loading="exporting">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedOrders.length > 0">
        <el-card class="batch-card">
          <div class="batch-content">
            <div class="batch-info">
              <span>已选择 {{ selectedOrders.length }} 个订单</span>
            </div>
            <div class="batch-buttons">
              <el-button-group>
                <el-button @click="batchRefund" :loading="batchLoading">
                  <el-icon><RefreshLeft /></el-icon>
                  批量退款
                </el-button>
                <el-button @click="batchCancel" :loading="batchLoading">
                  <el-icon><Close /></el-icon>
                  批量取消
                </el-button>
                <el-button @click="batchExport" :loading="batchLoading">
                  <el-icon><Download /></el-icon>
                  批量导出
                </el-button>
              </el-button-group>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 订单列表 -->
      <div class="orders-section">
        <el-card class="orders-card">
          <template #header>
            <div class="card-header">
              <h3>订单列表</h3>
              <div class="header-actions">
                <el-button @click="refreshOrders" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            v-loading="loading"
            :data="orderList"
            @selection-change="handleSelectionChange"
            row-key="id"
            stripe
          >
            <el-table-column type="selection" width="55" />
            
            <el-table-column label="订单信息" min-width="200">
              <template #default="{ row }">
                <div class="order-info">
                  <div class="order-number">{{ row.order_no }}</div>
                  <div class="order-meta">
                    <span class="meta-item">
                      <el-icon><User /></el-icon>
                      {{ row.user_name }}
                    </span>
                    <span class="meta-item">
                      <el-icon><Clock /></el-icon>
                      {{ formatDate(row.created_at) }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="商品信息" min-width="180">
              <template #default="{ row }">
                <div class="product-info">
                  <div class="product-name">{{ row.product_name }}</div>
                  <div class="product-desc">{{ row.product_desc }}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="金额" width="120" sortable="custom" prop="amount">
              <template #default="{ row }">
                <div class="amount-info">
                  <div class="amount">¥{{ row.amount.toFixed(2) }}</div>
                  <div v-if="row.discount_amount > 0" class="discount">
                    优惠: ¥{{ row.discount_amount.toFixed(2) }}
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="支付方式" width="120">
              <template #default="{ row }">
                <div class="payment-method">
                  <el-icon v-if="row.payment_method === 'alipay'" class="method-icon alipay">
                    <CreditCard />
                  </el-icon>
                  <el-icon v-else-if="row.payment_method === 'wechat'" class="method-icon wechat">
                    <ChatDotRound />
                  </el-icon>
                  <el-icon v-else-if="row.payment_method === 'easypay'" class="method-icon easypay">
                    <Wallet />
                  </el-icon>
                  <el-icon v-else class="method-icon bank">
                    <CreditCard />
                  </el-icon>
                  <span>{{ getPaymentMethodText(row.payment_method) }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="支付时间" width="160" sortable="custom" prop="paid_at">
              <template #default="{ row }">
                <span v-if="row.paid_at">{{ formatDate(row.paid_at) }}</span>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button size="small" @click="viewOrderDetail(row)">
                    详情
                  </el-button>
                  
                  <el-dropdown @command="handleOrderAction" trigger="click">
                    <el-button size="small">
                      更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item 
                          v-if="row.status === 'paid'" 
                          :command="`refund-${row.id}`"
                        >
                          申请退款
                        </el-dropdown-item>
                        <el-dropdown-item 
                          v-if="row.status === 'pending'" 
                          :command="`cancel-${row.id}`"
                        >
                          取消订单
                        </el-dropdown-item>
                        <el-dropdown-item :command="`resend-${row.id}`">
                          重发通知
                        </el-dropdown-item>
                        <el-dropdown-item :command="`logs-${row.id}`" divided>
                          查看日志
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handlePageSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </el-card>
      </div>
    </PageLayout>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="showOrderDetail"
      :order="currentOrder"
      @action="handleDetailAction"
    />

    <!-- 退款对话框 -->
    <RefundDialog
      v-model="showRefundDialog"
      :order="currentOrder"
      @confirm="handleRefundConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock, Check, Close, RefreshLeft, Search, Refresh, Download,
  User, CreditCard, ChatDotRound, Wallet, ArrowDown
} from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import RefundDialog from './components/RefundDialog.vue'
import {
  getPaymentOrders,
  getPaymentStats,
  refundOrder,
  cancelOrder,
  batchProcessOrders,
  exportPaymentData
} from '@/api/payment'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const batchLoading = ref(false)
const showOrderDetail = ref(false)
const showRefundDialog = ref(false)

const searchQuery = ref('')
const selectedOrders = ref([])
const orderList = ref([])
const currentOrder = ref({})
const total = ref(0)

const pagination = reactive({
  page: 1,
  size: 20
})

const filters = reactive({
  status: '',
  payment_method: '',
  dateRange: null
})

const orderStats = reactive({
  pending: 0,
  paid: 0,
  failed: 0,
  refunded: 0,
  pendingChange: 0,
  paidChange: 0,
  failedChange: 0,
  refundedChange: 0
})

// 模拟订单数据
const mockOrders = [
  {
    id: 1,
    order_no: 'ORD202412010001',
    user_name: '张三',
    product_name: '技术交流群',
    product_desc: '高级技术交流群组',
    amount: 99.00,
    discount_amount: 0,
    payment_method: 'alipay',
    status: 'paid',
    created_at: new Date().toISOString(),
    paid_at: new Date().toISOString()
  },
  {
    id: 2,
    order_no: 'ORD202412010002',
    user_name: '李四',
    product_name: '投资理财群',
    product_desc: '专业投资理财交流',
    amount: 199.00,
    discount_amount: 20.00,
    payment_method: 'wechat',
    status: 'pending',
    created_at: new Date(Date.now() - 3600000).toISOString(),
    paid_at: null
  },
  {
    id: 3,
    order_no: 'ORD202412010003',
    user_name: '王五',
    product_name: '创业交流群',
    product_desc: '创业者经验分享',
    amount: 299.00,
    discount_amount: 0,
    payment_method: 'easypay',
    status: 'failed',
    created_at: new Date(Date.now() - 7200000).toISOString(),
    paid_at: null
  }
]

// 方法
const loadOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchQuery.value,
      ...filters
    }
    
    // 使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    orderList.value = mockOrders
    total.value = mockOrders.length
  } catch (error) {
    ElMessage.error('加载订单失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    // 使用模拟数据
    Object.assign(orderStats, {
      pending: 25,
      paid: 156,
      failed: 8,
      refunded: 12,
      pendingChange: 5,
      paidChange: 23,
      failedChange: 2,
      refundedChange: 3
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadOrders()
}

const resetFilters = () => {
  searchQuery.value = ''
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'dateRange' ? null : ''
  })
  pagination.page = 1
  loadOrders()
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection.map(item => item.id)
}

const refreshOrders = () => {
  loadOrders()
  loadStats()
}

const exportOrders = async () => {
  exporting.value = true
  try {
    const params = {
      keyword: searchQuery.value,
      ...filters
    }
    
    // 模拟导出
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 创建CSV内容
    const csvContent = [
      '订单号,用户名,商品名称,金额,支付方式,状态,创建时间',
      ...orderList.value.map(order => [
        order.order_no,
        order.user_name,
        order.product_name,
        order.amount,
        getPaymentMethodText(order.payment_method),
        getStatusText(order.status),
        formatDate(order.created_at)
      ].join(','))
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `订单数据_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const batchRefund = async () => {
  if (selectedOrders.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要对选中的 ${selectedOrders.value.length} 个订单申请退款吗？`,
      '批量退款确认'
    )
    
    batchLoading.value = true
    await batchProcessOrders(selectedOrders.value, 'refund')
    ElMessage.success('批量退款申请已提交')
    selectedOrders.value = []
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  } finally {
    batchLoading.value = false
  }
}

const batchCancel = async () => {
  if (selectedOrders.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要取消选中的 ${selectedOrders.value.length} 个订单吗？`,
      '批量取消确认'
    )
    
    batchLoading.value = true
    await batchProcessOrders(selectedOrders.value, 'cancel')
    ElMessage.success('批量取消成功')
    selectedOrders.value = []
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  } finally {
    batchLoading.value = false
  }
}

const batchExport = async () => {
  if (selectedOrders.value.length === 0) return
  
  batchLoading.value = true
  try {
    const selectedOrdersData = orderList.value.filter(order => 
      selectedOrders.value.includes(order.id)
    )
    
    const csvContent = [
      '订单号,用户名,商品名称,金额,支付方式,状态,创建时间',
      ...selectedOrdersData.map(order => [
        order.order_no,
        order.user_name,
        order.product_name,
        order.amount,
        getPaymentMethodText(order.payment_method),
        getStatusText(order.status),
        formatDate(order.created_at)
      ].join(','))
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `选中订单_${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    
    ElMessage.success('批量导出成功')
  } catch (error) {
    ElMessage.error('批量导出失败')
  } finally {
    batchLoading.value = false
  }
}

const viewOrderDetail = (order) => {
  currentOrder.value = order
  showOrderDetail.value = true
}

const handleOrderAction = async (command) => {
  const [action, orderId] = command.split('-')
  const order = orderList.value.find(o => o.id === parseInt(orderId))
  
  if (!order) return
  
  switch (action) {
    case 'refund':
      currentOrder.value = order
      showRefundDialog.value = true
      break
    case 'cancel':
      try {
        await ElMessageBox.confirm('确定要取消这个订单吗？', '取消订单')
        await cancelOrder(orderId, '管理员取消')
        ElMessage.success('订单已取消')
        loadOrders()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('取消订单失败')
        }
      }
      break
    case 'resend':
      ElMessage.success('通知已重发')
      break
    case 'logs':
      ElMessage.info('查看订单日志功能开发中...')
      break
  }
}

const handleDetailAction = (action, order) => {
  switch (action) {
    case 'refund':
      currentOrder.value = order
      showRefundDialog.value = true
      break
    case 'cancel':
      handleOrderAction(`cancel-${order.id}`)
      break
  }
}

const handleRefundConfirm = async (refundData) => {
  try {
    await refundOrder(currentOrder.value.id, refundData)
    ElMessage.success('退款申请已提交')
    showRefundDialog.value = false
    loadOrders()
  } catch (error) {
    ElMessage.error('退款申请失败')
  }
}

const handlePageSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadOrders()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadOrders()
}

// 工具函数
const getPaymentMethodText = (method) => {
  const texts = {
    alipay: '支付宝',
    wechat: '微信支付',
    easypay: '易支付',
    bank: '银行卡'
  }
  return texts[method] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    paid: 'success',
    failed: 'danger',
    refunded: 'info',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待支付',
    paid: '已支付',
    failed: '支付失败',
    refunded: '已退款',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadOrders()
  loadStats()
})
</script>

<style lang="scss" scoped>
.payment-orders {
  .stats-section {
    margin-bottom: 24px;

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
      }

      &.pending .stat-icon {
        background: linear-gradient(135deg, #fa8c16, #ffc53d);
      }

      &.success .stat-icon {
        background: linear-gradient(135deg, #52c41a, #95de64);
      }

      &.failed .stat-icon {
        background: linear-gradient(135deg, #ff4d4f, #ff7875);
      }

      &.refunded .stat-icon {
        background: linear-gradient(135deg, #722ed1, #b37feb);
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 4px;
        }

        .stat-change {
          font-size: 12px;
          color: #52c41a;
          font-weight: 500;
        }
      }
    }
  }

  .filter-section {
    margin-bottom: 24px;

    .filter-card {
      .filter-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;

        .filter-left {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;
        }

        .filter-right {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .batch-actions {
    margin-bottom: 24px;

    .batch-card {
      background: #f0f9ff;
      border: 1px solid #3b82f6;

      .batch-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .batch-info {
          font-weight: 500;
          color: #1e40af;
        }

        .batch-buttons {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .orders-section {
    .orders-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }

        .header-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .order-info {
      .order-number {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .order-meta {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #909399;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .product-info {
      .product-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }

      .product-desc {
        font-size: 12px;
        color: #909399;
      }
    }

    .amount-info {
      .amount {
        font-weight: 600;
        color: #303133;
        margin-bottom: 2px;
      }

      .discount {
        font-size: 12px;
        color: #52c41a;
      }
    }

    .payment-method {
      display: flex;
      align-items: center;
      gap: 8px;

      .method-icon {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;

        &.alipay {
          background: #1677ff;
        }

        &.wechat {
          background: #52c41a;
        }

        &.easypay {
          background: #722ed1;
        }

        &.bank {
          background: #fa8c16;
        }
      }

      span {
        font-size: 14px;
        color: #303133;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }

    .text-muted {
      color: #c0c4cc;
    }

    .pagination-wrapper {
      margin-top: 24px;
      display: flex;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .payment-orders {
    .filter-section {
      .filter-card {
        .filter-content {
          flex-direction: column;
          gap: 16px;

          .filter-left {
            width: 100%;
            flex-wrap: wrap;
          }

          .filter-right {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .payment-orders {
    .stats-section {
      :deep(.el-col) {
        margin-bottom: 16px;
      }
    }

    .batch-actions {
      .batch-card {
        .batch-content {
          flex-direction: column;
          gap: 12px;
          text-align: center;
        }
      }
    }

    .orders-section {
      .orders-card {
        :deep(.el-table) {
          .el-table__body-wrapper {
            overflow-x: auto;
          }
        }
      }
    }
  }
}
</style>
