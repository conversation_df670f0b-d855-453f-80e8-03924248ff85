import{_ as e}from"./index-D2bI4m-v.js";/* empty css                  *//* empty css                       *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css                     *//* empty css                          *//* empty css                        *//* empty css                       *//* empty css                       *//* empty css                 *//* empty css                *//* empty css               */import{p as a}from"./export-1WkTdiMF.js";import{b4 as l,b2 as t,U as s,b3 as d,o,as as i,bc as n,bd as r,aW as u,aV as c,bo as p,bp as m,bl as _,be as f,bg as b,bh as v,aH as y,b6 as g,b7 as k,b8 as h,aZ as V,bk as w,cf as x,bf as j,au as z,Q as C,R as U}from"./element-plus-DcSKpKA8.js";import{L as D,r as Y,e as B,k as I,l as M,E as H,z as S,t as $,F as q,Y as T,y as E,D as O}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const P={class:"app-container"},G={class:"stat-item"},J={class:"stat-content"},N={class:"stat-number"},A={class:"stat-item"},F={class:"stat-content"},L={class:"stat-number"},Q={class:"stat-item"},R={class:"stat-content"},W={class:"stat-number"},Z={class:"stat-item"},K={class:"stat-content"},X={class:"stat-number"},ee={class:"export-template"},ae={class:"template-header"},le={class:"template-icon"},te={class:"template-info"},se={class:"template-name"},de={class:"template-desc"},oe={class:"template-stats"},ie={class:"stat-row"},ne={class:"stat-value"},re={class:"stat-row"},ue={class:"stat-value"},ce={class:"stat-row"},pe={class:"stat-value"},me={class:"template-actions"},_e={class:"card-header"},fe={class:"table-pagination"},be={class:"dialog-footer"},ve={key:0},ye={key:1,style:{"text-align":"center",padding:"40px"}},ge={class:"dialog-footer"},ke=e({__name:"DataExport",setup(e){const ke=D({total_exports:1245,today_exports:23,total_size:"2.5GB",pending_exports:3}),he=Y([{key:"users",name:"用户数据",description:"导出所有用户的基本信息",icon:"el-icon-user",count:"12,345",size:"2.5MB",last_updated:"2024-01-01 10:00",loading:!1},{key:"orders",name:"订单数据",description:"导出所有订单记录",icon:"el-icon-goods",count:"8,765",size:"5.2MB",last_updated:"2024-01-01 09:30",loading:!1},{key:"finance",name:"财务数据",description:"导出收支明细记录",icon:"el-icon-money",count:"3,456",size:"1.8MB",last_updated:"2024-01-01 08:45",loading:!1}]),Ve=D({data_type:"",format:"excel",date_range:[],limit:1e4,fields:[],filters:"",sort_by:"created_at",sort_order:"desc",name:""}),we=Y([]),xe=Y([{id:1,name:"用户数据导出",data_type:"users",format:"excel",status:"completed",progress:100,file_size:"2.5MB",created_at:"2024-01-01 10:00:00"},{id:2,name:"订单数据导出",data_type:"orders",format:"csv",status:"processing",progress:65,file_size:"-",created_at:"2024-01-01 10:30:00"},{id:3,name:"财务数据导出",data_type:"finance",format:"json",status:"failed",progress:0,file_size:"-",created_at:"2024-01-01 09:15:00"}]),je=D({visible:!1,template:null}),ze=D({visible:!1,data:[],columns:[]}),Ce=D({frequency:"daily",time:null,email:"",enabled:!0}),Ue=Y(!1),De=Y(1),Ye=Y(10),Be=Y(0),Ie=e=>{we.value={users:[{key:"id",label:"ID"},{key:"username",label:"用户名"},{key:"email",label:"邮箱"},{key:"phone",label:"手机号"},{key:"created_at",label:"创建时间"}],orders:[{key:"id",label:"订单ID"},{key:"user_id",label:"用户ID"},{key:"amount",label:"金额"},{key:"status",label:"状态"},{key:"created_at",label:"创建时间"}],finance:[{key:"id",label:"ID"},{key:"type",label:"类型"},{key:"amount",label:"金额"},{key:"balance",label:"余额"},{key:"created_at",label:"创建时间"}]}[e]||[],Ve.fields=we.value.map(e=>e.key)},Me=async()=>{if(Ve.data_type){Ue.value=!0;try{await new Promise(e=>setTimeout(e,3e3)),C.success("导出任务已创建"),qe()}catch(e){C.error("导出任务创建失败")}finally{Ue.value=!1}}else C.warning("请选择数据类型")},He=()=>{Object.assign(Ve,{data_type:"",format:"excel",date_range:[],limit:1e4,fields:[],filters:"",sort_by:"created_at",sort_order:"desc",name:""}),we.value=[]},Se=async()=>{if(Ve.data_type)try{C.info("正在获取预览数据...");const e={data_type:Ve.data_type,fields:Ve.fields,filters:Ve.filters,date_range:Ve.date_range,limit:10},l=await a(e);l.data.success?(ze.visible=!0,ze.data=l.data.data,ze.columns=l.data.columns,C.success("预览数据获取成功")):C.error("预览数据获取失败")}catch(e){C.error("预览数据获取失败："+e.message)}else C.warning("请选择数据类型")},$e=e=>({pending:"待处理",processing:"处理中",completed:"已完成",failed:"失败"}[e]||e),qe=()=>{C.success("任务列表已刷新")},Te=e=>{Ye.value=e,qe()},Ee=e=>{De.value=e,qe()},Oe=()=>{C.success("定时导出设置已保存"),je.visible=!1};return B(()=>{Be.value=xe.value.length}),(e,a)=>{const D=t,Y=l,B=d,Pe=i,Ge=c,Je=u,Ne=r,Ae=m,Fe=p,Le=_,Qe=f,Re=v,We=b,Ze=y,Ke=n,Xe=k,ea=h,aa=V,la=g,ta=w,sa=x,da=j,oa=z;return M(),I("div",P,[H(B,{class:"overview-card"},{header:S(()=>a[19]||(a[19]=[$("div",{class:"card-header"},[$("span",null,"📊 数据导出概览")],-1)])),default:S(()=>[H(Y,{gutter:20},{default:S(()=>[H(D,{span:6},{default:S(()=>[$("div",G,[a[21]||(a[21]=$("div",{class:"stat-icon export-icon"},[$("i",{class:"el-icon-download"})],-1)),$("div",J,[$("div",N,s(ke.total_exports),1),a[20]||(a[20]=$("div",{class:"stat-label"},"总导出次数",-1))])])]),_:1}),H(D,{span:6},{default:S(()=>[$("div",A,[a[23]||(a[23]=$("div",{class:"stat-icon today-icon"},[$("i",{class:"el-icon-calendar-today"})],-1)),$("div",F,[$("div",L,s(ke.today_exports),1),a[22]||(a[22]=$("div",{class:"stat-label"},"今日导出",-1))])])]),_:1}),H(D,{span:6},{default:S(()=>[$("div",Q,[a[25]||(a[25]=$("div",{class:"stat-icon size-icon"},[$("i",{class:"el-icon-files"})],-1)),$("div",R,[$("div",W,s(ke.total_size),1),a[24]||(a[24]=$("div",{class:"stat-label"},"总文件大小",-1))])])]),_:1}),H(D,{span:6},{default:S(()=>[$("div",Z,[a[27]||(a[27]=$("div",{class:"stat-icon pending-icon"},[$("i",{class:"el-icon-loading"})],-1)),$("div",K,[$("div",X,s(ke.pending_exports),1),a[26]||(a[26]=$("div",{class:"stat-label"},"待处理任务",-1))])])]),_:1})]),_:1})]),_:1}),H(B,{style:{"margin-top":"20px"}},{header:S(()=>a[28]||(a[28]=[$("div",{class:"card-header"},[$("span",null,"⚡ 快速导出")],-1)])),default:S(()=>[H(Y,{gutter:20},{default:S(()=>[(M(!0),I(q,null,T(he.value,e=>(M(),E(D,{span:8,key:e.key},{default:S(()=>[$("div",ee,[$("div",ae,[$("div",le,[$("i",{class:o(e.icon)},null,2)]),$("div",te,[$("div",se,s(e.name),1),$("div",de,s(e.description),1)])]),$("div",oe,[$("div",ie,[a[29]||(a[29]=$("span",{class:"stat-label"},"数据量:",-1)),$("span",ne,s(e.count),1)]),$("div",re,[a[30]||(a[30]=$("span",{class:"stat-label"},"预计大小:",-1)),$("span",ue,s(e.size),1)]),$("div",ce,[a[31]||(a[31]=$("span",{class:"stat-label"},"最后更新:",-1)),$("span",pe,s(e.last_updated),1)])]),$("div",me,[H(Pe,{type:"primary",onClick:a=>(async e=>{e.loading=!0;try{await new Promise(e=>setTimeout(e,2e3)),C.success(`${e.name} 导出成功`)}catch(a){C.error(`${e.name} 导出失败`)}finally{e.loading=!1}})(e),loading:e.loading},{default:S(()=>a[32]||(a[32]=[O(" 立即导出 ",-1)])),_:2,__:[32]},1032,["onClick","loading"]),H(Pe,{type:"info",onClick:a=>(e=>{je.template=e,je.visible=!0})(e)},{default:S(()=>a[33]||(a[33]=[O(" 定时导出 ",-1)])),_:2,__:[33]},1032,["onClick"])])])]),_:2},1024))),128))]),_:1})]),_:1}),H(B,{style:{"margin-top":"20px"}},{header:S(()=>a[34]||(a[34]=[$("div",{class:"card-header"},[$("span",null,"🎯 自定义导出")],-1)])),default:S(()=>[H(Ke,{model:Ve,"label-width":"120px"},{default:S(()=>[H(Y,{gutter:20},{default:S(()=>[H(D,{span:12},{default:S(()=>[H(Ne,{label:"导出数据类型"},{default:S(()=>[H(Je,{modelValue:Ve.data_type,"onUpdate:modelValue":a[0]||(a[0]=e=>Ve.data_type=e),placeholder:"请选择数据类型",onChange:Ie},{default:S(()=>[H(Ge,{label:"用户数据",value:"users"}),H(Ge,{label:"订单数据",value:"orders"}),H(Ge,{label:"财务数据",value:"finance"}),H(Ge,{label:"分销数据",value:"distribution"}),H(Ge,{label:"社群数据",value:"community"}),H(Ge,{label:"防红数据",value:"anti-block"}),H(Ge,{label:"系统日志",value:"logs"})]),_:1},8,["modelValue"])]),_:1}),H(Ne,{label:"导出格式"},{default:S(()=>[H(Fe,{modelValue:Ve.format,"onUpdate:modelValue":a[1]||(a[1]=e=>Ve.format=e)},{default:S(()=>[H(Ae,{label:"excel"},{default:S(()=>a[35]||(a[35]=[O("Excel (.xlsx)",-1)])),_:1,__:[35]}),H(Ae,{label:"csv"},{default:S(()=>a[36]||(a[36]=[O("CSV (.csv)",-1)])),_:1,__:[36]}),H(Ae,{label:"json"},{default:S(()=>a[37]||(a[37]=[O("JSON (.json)",-1)])),_:1,__:[37]})]),_:1},8,["modelValue"])]),_:1}),H(Ne,{label:"时间范围"},{default:S(()=>[H(Le,{modelValue:Ve.date_range,"onUpdate:modelValue":a[2]||(a[2]=e=>Ve.date_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),H(Ne,{label:"数据量限制"},{default:S(()=>[H(Qe,{modelValue:Ve.limit,"onUpdate:modelValue":a[3]||(a[3]=e=>Ve.limit=e),min:1,max:1e5},null,8,["modelValue"]),a[38]||(a[38]=$("span",{class:"form-tip"},"建议单次导出不超过50000条数据",-1))]),_:1,__:[38]})]),_:1}),H(D,{span:12},{default:S(()=>[H(Ne,{label:"导出字段"},{default:S(()=>[H(We,{modelValue:Ve.fields,"onUpdate:modelValue":a[4]||(a[4]=e=>Ve.fields=e)},{default:S(()=>[(M(!0),I(q,null,T(we.value,e=>(M(),E(Re,{key:e.key,label:e.key},{default:S(()=>[O(s(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),H(Ne,{label:"筛选条件"},{default:S(()=>[H(Ze,{type:"textarea",modelValue:Ve.filters,"onUpdate:modelValue":a[5]||(a[5]=e=>Ve.filters=e),placeholder:"请输入筛选条件（JSON格式）"},null,8,["modelValue"])]),_:1}),H(Ne,{label:"排序方式"},{default:S(()=>[H(Je,{modelValue:Ve.sort_by,"onUpdate:modelValue":a[6]||(a[6]=e=>Ve.sort_by=e),placeholder:"选择排序字段"},{default:S(()=>[H(Ge,{label:"创建时间",value:"created_at"}),H(Ge,{label:"更新时间",value:"updated_at"}),H(Ge,{label:"ID",value:"id"})]),_:1},8,["modelValue"]),H(Je,{modelValue:Ve.sort_order,"onUpdate:modelValue":a[7]||(a[7]=e=>Ve.sort_order=e),placeholder:"排序方式",style:{"margin-left":"10px"}},{default:S(()=>[H(Ge,{label:"升序",value:"asc"}),H(Ge,{label:"降序",value:"desc"})]),_:1},8,["modelValue"])]),_:1}),H(Ne,{label:"导出名称"},{default:S(()=>[H(Ze,{modelValue:Ve.name,"onUpdate:modelValue":a[8]||(a[8]=e=>Ve.name=e),placeholder:"请输入导出文件名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),H(Ne,null,{default:S(()=>[H(Pe,{type:"primary",onClick:Me,loading:Ue.value},{default:S(()=>a[39]||(a[39]=[O(" 开始导出 ",-1)])),_:1,__:[39]},8,["loading"]),H(Pe,{onClick:He},{default:S(()=>a[40]||(a[40]=[O("重置",-1)])),_:1,__:[40]}),H(Pe,{type:"info",onClick:Se},{default:S(()=>a[41]||(a[41]=[O("预览数据",-1)])),_:1,__:[41]})]),_:1})]),_:1},8,["model"])]),_:1}),H(B,{style:{"margin-top":"20px"}},{header:S(()=>[$("div",_e,[a[43]||(a[43]=$("span",null,"📋 导出任务列表",-1)),H(Pe,{type:"primary",onClick:qe},{default:S(()=>a[42]||(a[42]=[O("刷新",-1)])),_:1,__:[42]})])]),default:S(()=>[H(la,{data:xe.value,style:{width:"100%"}},{default:S(()=>[H(Xe,{prop:"id",label:"任务ID",width:"80"}),H(Xe,{prop:"name",label:"任务名称"}),H(Xe,{prop:"data_type",label:"数据类型",width:"100"}),H(Xe,{prop:"format",label:"格式",width:"80"}),H(Xe,{prop:"status",label:"状态",width:"100"},{default:S(e=>{return[H(ea,{type:(a=e.row.status,{pending:"info",processing:"warning",completed:"success",failed:"danger"}[a]||"info")},{default:S(()=>[O(s($e(e.row.status)),1)]),_:2},1032,["type"])];var a}),_:1}),H(Xe,{prop:"progress",label:"进度",width:"120"},{default:S(e=>[H(aa,{percentage:e.row.progress,status:"failed"===e.row.status?"exception":""},null,8,["percentage","status"])]),_:1}),H(Xe,{prop:"file_size",label:"文件大小",width:"100"}),H(Xe,{prop:"created_at",label:"创建时间",width:"160"}),H(Xe,{label:"操作",width:"200"},{default:S(e=>[H(Pe,{type:"primary",size:"small",onClick:a=>{return l=e.row,void C.success(`开始下载 ${l.name}`);var l},disabled:"completed"!==e.row.status},{default:S(()=>a[44]||(a[44]=[O(" 下载 ",-1)])),_:2,__:[44]},1032,["onClick","disabled"]),H(Pe,{type:"warning",size:"small",onClick:a=>{return l=e.row,C.info(`重试任务 ${l.name}`),l.status="pending",void(l.progress=0);var l},disabled:"failed"!==e.row.status},{default:S(()=>a[45]||(a[45]=[O(" 重试 ",-1)])),_:2,__:[45]},1032,["onClick","disabled"]),H(Pe,{type:"danger",size:"small",onClick:a=>{return l=e.row,void U.confirm(`确定要删除任务 ${l.name} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{C.success("删除成功"),qe()});var l}},{default:S(()=>a[46]||(a[46]=[O(" 删除 ",-1)])),_:2,__:[46]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),$("div",fe,[H(ta,{"current-page":De.value,"onUpdate:currentPage":a[9]||(a[9]=e=>De.value=e),"page-size":Ye.value,"onUpdate:pageSize":a[10]||(a[10]=e=>Ye.value=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:Be.value,onSizeChange:Te,onCurrentChange:Ee},null,8,["current-page","page-size","total"])])]),_:1}),H(oa,{title:"定时导出设置",modelValue:je.visible,"onUpdate:modelValue":a[16]||(a[16]=e=>je.visible=e),width:"600px"},{footer:S(()=>[$("div",be,[H(Pe,{onClick:a[15]||(a[15]=e=>je.visible=!1)},{default:S(()=>a[47]||(a[47]=[O("取消",-1)])),_:1,__:[47]}),H(Pe,{type:"primary",onClick:Oe},{default:S(()=>a[48]||(a[48]=[O("保存",-1)])),_:1,__:[48]})])]),default:S(()=>[H(Ke,{model:Ce,"label-width":"100px"},{default:S(()=>[H(Ne,{label:"导出频率"},{default:S(()=>[H(Je,{modelValue:Ce.frequency,"onUpdate:modelValue":a[11]||(a[11]=e=>Ce.frequency=e),placeholder:"请选择导出频率"},{default:S(()=>[H(Ge,{label:"每日",value:"daily"}),H(Ge,{label:"每周",value:"weekly"}),H(Ge,{label:"每月",value:"monthly"})]),_:1},8,["modelValue"])]),_:1}),H(Ne,{label:"执行时间"},{default:S(()=>[H(sa,{modelValue:Ce.time,"onUpdate:modelValue":a[12]||(a[12]=e=>Ce.time=e),placeholder:"选择时间"},null,8,["modelValue"])]),_:1}),H(Ne,{label:"邮件通知"},{default:S(()=>[H(Ze,{modelValue:Ce.email,"onUpdate:modelValue":a[13]||(a[13]=e=>Ce.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),H(Ne,{label:"是否启用"},{default:S(()=>[H(da,{modelValue:Ce.enabled,"onUpdate:modelValue":a[14]||(a[14]=e=>Ce.enabled=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),H(oa,{title:"数据预览",modelValue:ze.visible,"onUpdate:modelValue":a[18]||(a[18]=e=>ze.visible=e),width:"80%",top:"5vh"},{footer:S(()=>[$("div",ge,[H(Pe,{onClick:a[17]||(a[17]=e=>ze.visible=!1)},{default:S(()=>a[51]||(a[51]=[O("关闭",-1)])),_:1,__:[51]})])]),default:S(()=>[ze.data.length>0?(M(),I("div",ve,[H(la,{data:ze.data,stripe:""},{default:S(()=>[(M(!0),I(q,null,T(ze.columns,e=>(M(),E(Xe,{key:e.key,prop:e.key,label:e.label,"show-overflow-tooltip":""},null,8,["prop","label"]))),128))]),_:1},8,["data"]),a[49]||(a[49]=$("div",{style:{"margin-top":"10px",color:"#666","font-size":"14px"}},[$("i",{class:"el-icon-info"}),O(" 预览数据仅显示前10条记录 ")],-1))])):(M(),I("div",ye,a[50]||(a[50]=[$("i",{class:"el-icon-document",style:{"font-size":"48px",color:"#ddd"}},null,-1),$("p",{style:{color:"#999","margin-top":"10px"}},"暂无数据",-1)])))]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-c8cc2d32"]]);export{ke as default};
