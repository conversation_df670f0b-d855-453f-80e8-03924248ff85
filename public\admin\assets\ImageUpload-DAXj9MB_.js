import{_ as e,u as a}from"./index-D2bI4m-v.js";/* empty css                  *//* empty css                    */import{T as l,aU as t,b_ as s,bt as u,bn as i,au as r,Q as o}from"./element-plus-DcSKpKA8.js";import{r as m,c as d,d as n,k as p,l as c,E as v,z as f,u as _,t as g}from"./vue-vendor-DGsK9sC4.js";const y={class:"image-upload"},b=["src"],V={class:"el-upload-list__item-actions"},k=["onClick"],x=["onClick"],z=["src"],h=e({__name:"ImageUpload",props:{modelValue:{type:[String,Array],default:""},limit:{type:Number,default:1},accept:{type:String,default:"image/*"},maxSize:{type:Number,default:5}},emits:["update:modelValue"],setup(e,{emit:h}){const j=e,I=h,S=a(),A=m([]),C=m(!1),U=m(""),w=d(()=>"/api/v1/admin/upload/image"),B=d(()=>({Authorization:`Bearer ${S.token}`}));n(()=>j.modelValue,e=>{e?Array.isArray(e)?A.value=e.map((e,a)=>({name:`image-${a}`,url:e,uid:a})):A.value=[{name:"image",url:e,uid:1}]:A.value=[]},{immediate:!0});const $=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<j.maxSize;return a?!!l||(o.error(`图片大小不能超过 ${j.maxSize}MB!`),!1):(o.error("只能上传图片文件!"),!1)},N=(e,a)=>{200===e.code?(a.url=e.data.url,L(),o.success("上传成功")):o.error(e.message||"上传失败")},E=e=>{console.error("上传失败:",e),o.error("上传失败")},J=e=>{const a=A.value.findIndex(a=>a.uid===e.uid);a>-1&&(A.value.splice(a,1),L())},L=()=>{const e=A.value.map(e=>e.url).filter(Boolean);1===j.limit?I("update:modelValue",e[0]||""):I("update:modelValue",e)};return(a,o)=>{const m=l,d=i,n=r;return c(),p("div",y,[v(d,{"file-list":A.value,"onUpdate:fileList":o[0]||(o[0]=e=>A.value=e),action:w.value,headers:B.value,"before-upload":$,"on-success":N,"on-error":E,"on-remove":J,limit:e.limit,accept:e.accept,"list-type":"picture-card","auto-upload":!0},{file:f(({file:e})=>[g("div",null,[g("img",{class:"el-upload-list__item-thumbnail",src:e.url,alt:""},null,8,b),g("span",V,[g("span",{class:"el-upload-list__item-preview",onClick:a=>(e=>{U.value=e.url,C.value=!0})(e)},[v(m,null,{default:f(()=>[v(_(s))]),_:1})],8,k),g("span",{class:"el-upload-list__item-delete",onClick:a=>J(e)},[v(m,null,{default:f(()=>[v(_(u))]),_:1})],8,x)])])]),default:f(()=>[v(m,null,{default:f(()=>[v(_(t))]),_:1})]),_:1},8,["file-list","action","headers","limit","accept"]),v(n,{modelValue:C.value,"onUpdate:modelValue":o[1]||(o[1]=e=>C.value=e),title:"图片预览"},{default:f(()=>[g("img",{"w-full":"",src:U.value,alt:"Preview Image"},null,8,z)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b5106737"]]);export{h as I};
