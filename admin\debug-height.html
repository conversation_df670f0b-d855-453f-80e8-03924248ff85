<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面高度调试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .test-frame {
            width: 100%;
            height: 768px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #f5f5f5;
        }
        
        .btn.active {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }
        
        .height-info {
            background: #fff3e0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ff9800;
        }
        
        .height-value {
            font-weight: bold;
            color: #e65100;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.pass {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        
        .status.fail {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 登录页面高度调试工具</h1>
        
        <div class="debug-info">
            <h3>📊 当前测试信息</h3>
            <p><strong>测试目标:</strong> 验证1366×768分辨率下登录页面是否需要滚动</p>
            <p><strong>期望结果:</strong> 页面内容完全显示在768px高度内，无需滚动</p>
            <p><strong>当前窗口:</strong> <span id="windowSize">检测中...</span></p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="setFrameHeight(1080)">1920×1080</button>
            <button class="btn active" onclick="setFrameHeight(768)">1366×768</button>
            <button class="btn" onclick="setFrameHeight(720)">1280×720</button>
            <button class="btn" onclick="setFrameHeight(600)">1024×600</button>
            <button class="btn" onclick="refreshFrame()">🔄 刷新</button>
            <button class="btn" onclick="openInNewTab()">🔗 新窗口打开</button>
        </div>
        
        <div class="height-info">
            <h3>📏 高度分析</h3>
            <p>测试框架高度: <span class="height-value" id="frameHeight">768px</span></p>
            <p>页面内容高度: <span class="height-value" id="contentHeight">检测中...</span></p>
            <p>是否需要滚动: <span class="height-value" id="scrollNeeded">检测中...</span></p>
            
            <div id="testStatus" class="status">等待检测...</div>
        </div>
        
        <iframe id="testFrame" class="test-frame" src="http://localhost:3002/admin/" title="登录页面"></iframe>
        
        <div class="debug-info">
            <h3>🎯 修复措施验证</h3>
            <ul>
                <li>✅ Logo区域在≤900px高度时隐藏</li>
                <li>✅ 功能特色展示在≤900px高度时隐藏</li>
                <li>✅ 欢迎文字在≤900px高度时隐藏</li>
                <li>✅ 状态指示器在≤900px高度时隐藏</li>
                <li>✅ 登录按钮高度减少到48px</li>
                <li>✅ 所有间距进一步优化</li>
            </ul>
        </div>
    </div>
    
    <script>
        function updateWindowSize() {
            document.getElementById('windowSize').textContent = `${window.innerWidth}×${window.innerHeight}`;
        }
        
        function setFrameHeight(height) {
            const frame = document.getElementById('testFrame');
            frame.style.height = height + 'px';
            document.getElementById('frameHeight').textContent = height + 'px';
            
            // 更新按钮状态
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 延迟检测内容高度
            setTimeout(checkContentHeight, 1000);
        }
        
        function checkContentHeight() {
            const frame = document.getElementById('testFrame');
            const frameHeight = parseInt(frame.style.height) || 768;
            
            try {
                const iframeDoc = frame.contentDocument || frame.contentWindow.document;
                const contentHeight = Math.max(
                    iframeDoc.body.scrollHeight,
                    iframeDoc.body.offsetHeight,
                    iframeDoc.documentElement.clientHeight,
                    iframeDoc.documentElement.scrollHeight,
                    iframeDoc.documentElement.offsetHeight
                );
                
                document.getElementById('contentHeight').textContent = contentHeight + 'px';
                
                const needsScroll = contentHeight > frameHeight;
                const scrollStatus = needsScroll ? '❌ 需要滚动' : '✅ 无需滚动';
                document.getElementById('scrollNeeded').textContent = scrollStatus;
                
                const statusDiv = document.getElementById('testStatus');
                if (needsScroll) {
                    statusDiv.className = 'status fail';
                    statusDiv.textContent = `❌ 测试失败: 内容高度(${contentHeight}px) > 框架高度(${frameHeight}px)，需要滚动 ${contentHeight - frameHeight}px`;
                } else {
                    statusDiv.className = 'status pass';
                    statusDiv.textContent = `✅ 测试通过: 内容完全显示在${frameHeight}px高度内，无需滚动`;
                }
                
            } catch (e) {
                document.getElementById('contentHeight').textContent = '无法检测 (跨域限制)';
                document.getElementById('scrollNeeded').textContent = '无法检测';
                document.getElementById('testStatus').textContent = '⚠️ 无法检测内容高度，请在新窗口中手动测试';
                document.getElementById('testStatus').className = 'status';
            }
        }
        
        function refreshFrame() {
            const frame = document.getElementById('testFrame');
            frame.src = frame.src;
            setTimeout(checkContentHeight, 2000);
        }
        
        function openInNewTab() {
            window.open('http://localhost:3002/admin/', '_blank');
        }
        
        // 初始化
        window.addEventListener('load', () => {
            updateWindowSize();
            setTimeout(checkContentHeight, 2000);
        });
        
        window.addEventListener('resize', updateWindowSize);
        
        // 定期检查
        setInterval(checkContentHeight, 5000);
    </script>
</body>
</html>
