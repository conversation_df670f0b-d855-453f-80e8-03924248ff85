import{d as t}from"./index-D2bI4m-v.js";const e={getMarketingConfig:e=>t.get(`/groups/${e}/marketing-config`),updateMarketingConfig:(e,a)=>t.put(`/groups/${e}/marketing-config`,a),generateVirtualMembers:(e,a=13)=>t.post(`/groups/${e}/virtual-members`,{count:a}),previewGroup:(e,a="北京")=>t.get(`/groups/${e}/preview`,{params:{city:a}}),testCityLocation:(e,a)=>t.post(`/groups/${e}/test-city`,{test_city:a}),applyMarketingTemplate:(e,a)=>t.post(`/groups/${e}/apply-template`,{template_id:a}),getMarketingTemplates:()=>t.get("/marketing-templates"),batchUpdateMarketing:(e,a)=>t.post("/groups/batch-marketing",{group_ids:e,marketing_config:a}),batchApplyTemplate:(e,a)=>t.post("/groups/batch-marketing",{group_ids:e,template_id:a})},a={getDomainHealth:()=>t.get("/anti-block/domain-health"),checkDomainHealth:e=>t.post("/anti-block/check-domain",{domain:e}),getBrowserStats:(e=7)=>t.get("/anti-block/browser-stats",{params:{days:e}}),validateGroupAccess:e=>t.post(`/anti-block/validate-access/${e}`),getAccessReport:(e,a="7days")=>t.get(`/anti-block/access-report/${e}`,{params:{period:a}})};export{a,e as m};
