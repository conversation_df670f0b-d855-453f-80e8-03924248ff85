import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'normalize.css/normalize.css'
import '@/styles/index.scss'
import apiPlugin from '@/plugins/api'
import { setupPermissionDirectives } from '@/directives/permission'
import { sessionManager, logSession } from '@/utils/security'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { setupMockApi } from '@/utils/mock-api'

// 在开发环境中启用模拟API
if (import.meta.env.DEV) {
  setupMockApi()
  console.log('🔧 Mock API已启用，用于开发环境测试')
}

// 检查预览模式并初始化
const urlParams = new URLSearchParams(window.location.search)
const isPreviewMode = urlParams.get('preview') === 'true' || localStorage.getItem('preview-mode') === 'true'

if (isPreviewMode) {
  console.log('🎭 预览模式已启用')
  localStorage.setItem('preview-mode', 'true')
}

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(apiPlugin)

// 注册权限指令
setupPermissionDirectives(app)

// 设置全局会话过期监听器
window.addEventListener('sessionExpired', () => {
  const userStore = useUserStore()
  ElMessage.warning({
    message: '会话已过期，请重新登录',
    duration: 5000
  })

  // 记录会话过期日志
  logSession(
    userStore.userInfo?.username || 'unknown',
    'session_expired',
    sessionManager.getCurrentSession()?.id,
    '自动会话过期检测'
  )

  // 清理用户数据并跳转到登录页
  userStore.logout().then(() => {
    router.push('/login')
  })
})

// 页面可见性变化时更新会话活动
document.addEventListener('visibilitychange', () => {
  if (!document.hidden && sessionManager.getCurrentSession()) {
    sessionManager.updateActivity()
  }
})

// 用户活动监听（鼠标移动、键盘输入等）
let activityTimer = null
const updateActivityThrottled = () => {
  if (activityTimer) return

  activityTimer = setTimeout(() => {
    if (sessionManager.getCurrentSession()) {
      sessionManager.updateActivity()
    }
    activityTimer = null
  }, 30000) // 30秒内最多更新一次
}

// 监听用户活动
['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(event => {
  document.addEventListener(event, updateActivityThrottled, { passive: true })
})

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err, info)
  ElMessage.error('应用出现错误，请刷新页面重试')
}

// 全局未捕获的Promise错误处理
window.addEventListener('unhandledrejection', event => {
  console.error('未处理的Promise错误:', event.reason)
  event.preventDefault()
})

// 挂载应用
try {
  app.mount('#app')
  console.log('✅ 应用挂载成功')
} catch (error) {
  console.error('❌ 应用挂载失败:', error)
}