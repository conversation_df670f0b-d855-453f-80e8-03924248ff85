import{_ as s}from"./index-D2bI4m-v.js";import{ag as a,k as t,l as e,t as o,E as r,z as l,D as n}from"./vue-vendor-DGsK9sC4.js";import{as as p}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";const m={class:"test-page"},u=s({__name:"TestPage",setup(s){const u=a(),i=()=>{u.go(-1)};return(s,a)=>{const u=p;return e(),t("div",m,[a[1]||(a[1]=o("h1",null,"测试页面",-1)),a[2]||(a[2]=o("p",null,"如果您能看到这个页面，说明路由系统工作正常。",-1)),r(u,{type:"primary",onClick:i},{default:l(()=>a[0]||(a[0]=[n("返回",-1)])),_:1,__:[0]})])}}},[["__scopeId","data-v-c06e9cfd"]]);export{u as default};
