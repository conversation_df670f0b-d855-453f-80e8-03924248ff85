<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Traits\SubstationScope;

/**
 * 用户模型 - 优化版
 * 支持多角色用户管理：超级管理员、分站管理员、分销商等
 * 
 * 优化内容：
 * 1. 添加查询作用域优化数据库查询
 * 2. 增加缓存机制提升性能
 * 3. 优化关联关系预加载
 * 4. 添加数据验证和访问器
 * 5. 增加索引建议
 */
class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, HasRoles, SubstationScope;

    /**
     * 用户角色常量
     */
    const ROLE_ADMIN = 'admin';           // 超级管理员
    const ROLE_SUBSTATION = 'substation'; // 分站管理员
    const ROLE_DISTRIBUTOR = 'distributor'; // 分销商
    const ROLE_USER = 'user';             // 普通用户

    /**
     * 用户状态常量
     */
    const STATUS_ACTIVE = 1;    // 正常
    const STATUS_DISABLED = 2;  // 禁用
    const STATUS_EXPIRED = 3;   // 到期

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'username',
        'nickname',
        'email',
        'phone',
        'password',
        'avatar',
        'role',
        'status',
        'balance',
        'frozen_balance',
        'total_commission',
        'parent_id',
        'distributor_level',
        'substation_id',
        'invite_code',
        'invited_by',
        'phone_verified_at',
        'email_verified_at',
        'last_login_at',
        'last_login_ip',
        'login_count',
        'remark',
        'extra_data',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'balance' => 'decimal:2',
        'frozen_balance' => 'decimal:2',
        'total_commission' => 'decimal:2',
        'extra_data' => 'array',
        'login_count' => 'integer',
        'status' => 'integer',
        'distributor_level' => 'integer',
    ];

    /**
     * JWT 相关方法
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    /**
     * 关联提现记录
     */
    public function withdrawals(): HasMany
    {
        return $this->hasMany(Withdrawal::class);
    }

    /**
     * 关联佣金记录
     */
    public function commissionLogs(): HasMany
    {
        return $this->hasMany(CommissionLog::class);
    }

    /**
     * 关联余额变动记录
     */
    public function balanceLogs(): HasMany
    {
        return $this->hasMany(BalanceLog::class);
    }

    /**
     * 模型启动时的操作
     */
    protected static function boot()
    {
        parent::boot();
        
        // 创建用户时自动生成邀请码
        static::creating(function ($user) {
            if (empty($user->invite_code)) {
                $user->invite_code = static::generateInviteCode();
            }
        });
        
        // 更新用户时清除相关缓存
        static::updated(function ($user) {
            static::clearUserCache($user->id);
        });
    }

    // ========== 查询作用域 ==========
    
    /**
     * 活跃用户查询作用域
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 按角色查询作用域
     */
    public function scopeRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * 分销商查询作用域
     */
    public function scopeDistributors($query)
    {
        return $query->where('role', self::ROLE_DISTRIBUTOR)
                    ->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 有余额的用户查询作用域
     */
    public function scopeWithBalance($query)
    {
        return $query->where('balance', '>', 0);
    }

    /**
     * 最近登录用户查询作用域
     */
    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_login_at', '>=', now()->subDays($days));
    }

    // ========== 关联关系优化 ==========

    /**
     * 用户所属分销组 - 添加缓存
     */
    public function distributionGroup(): BelongsTo
    {
        return $this->belongsTo(DistributionGroup::class, 'distribution_group_id')
                    ->select(['id', 'name', 'status', 'commission_rate']);
    }

    /**
     * 用户所属分站 - 添加缓存
     */
    public function substation(): BelongsTo
    {
        return $this->belongsTo(Substation::class, 'substation_id')
                    ->select(['id', 'name', 'status', 'domain']);
    }

    /**
     * 用户的直接下级 - 优化查询
     */
    public function children(): HasMany
    {
        return $this->hasMany(User::class, 'parent_id')
                    ->select(['id', 'username', 'nickname', 'role', 'status', 'parent_id', 'created_at']);
    }

    /**
     * 用户的上级
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'parent_id')
                    ->select(['id', 'username', 'nickname', 'role']);
    }

    /**
     * 用户的订单 - 添加索引优化
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'user_id')
                    ->latest('created_at');
    }



    // ========== 访问器优化 ==========

    /**
     * 获取用户显示名称
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->nickname ?: $this->username;
    }

    /**
     * 获取用户角色名称
     */
    public function getRoleNameAttribute(): string
    {
        return match ($this->role) {
            self::ROLE_ADMIN => '超级管理员',
            self::ROLE_SUBSTATION => '分站管理员',
            self::ROLE_DISTRIBUTOR => '分销商',
            self::ROLE_USER => '普通用户',
            default => '未知',
        };
    }

    /**
     * 获取用户状态名称
     */
    public function getStatusNameAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_ACTIVE => '正常',
            self::STATUS_DISABLED => '禁用',
            self::STATUS_EXPIRED => '到期',
            default => '未知',
        };
    }

    /**
     * 获取可用余额
     */
    public function getAvailableBalanceAttribute(): string
    {
        return bcadd($this->balance, '0', 2);
    }

    /**
     * 获取头像URL
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }
        
        // 默认头像
        return asset('images/default-avatar.png');
    }

    // ========== 缓存优化方法 ==========

    /**
     * 获取用户缓存数据
     */
    public static function getCachedUser($id)
    {
        $cacheKey = "user:profile:{$id}";
        
        return Cache::remember($cacheKey, 3600, function () use ($id) {
            return static::with(['distributionGroup', 'substation'])
                        ->find($id);
        });
    }

    /**
     * 清除用户缓存
     */
    public static function clearUserCache($id)
    {
        $cacheKeys = [
            "user:profile:{$id}",
            "user:permissions:{$id}",
            "user:stats:{$id}",
        ];
        
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * 获取用户统计信息（带缓存）
     */
    public function getCachedStats()
    {
        $cacheKey = "user:stats:{$this->id}";
        
        return Cache::remember($cacheKey, 1800, function () {
            return [
                'total_orders' => $this->orders()->count(),
                'total_commission' => $this->commissionLogs()->sum('amount'),
                'children_count' => $this->children()->count(),
                'this_month_orders' => $this->orders()
                    ->whereMonth('created_at', now()->month)
                    ->count(),
            ];
        });
    }

    // ========== 业务逻辑优化 ==========

    /**
     * 生成邀请码
     */
    public static function generateInviteCode(): string
    {
        do {
            $code = 'U' . strtoupper(Str::random(7));
        } while (static::where('invite_code', $code)->exists());
        
        return $code;
    }

    /**
     * 调整用户余额（原子操作）
     */
    public function adjustBalance($amount, $type = 'manual', $description = '')
    {
        // 使用数据库事务和行级锁
        return \DB::transaction(function () use ($amount, $type, $description) {
            // 锁定当前用户记录
            $user = static::where('id', $this->id)->lockForUpdate()->first();
            
            if (!$user) {
                throw new \Exception('用户不存在');
            }
            
            $oldBalance = $user->balance;
            $newBalance = bcadd($oldBalance, $amount, 2);
            
            if (bccomp($newBalance, '0', 2) < 0) {
                throw new \Exception('余额不足');
            }
            
            // 更新余额
            $user->update(['balance' => $newBalance]);
            
            // 记录余额变动日志
            BalanceLog::create([
                'user_id' => $this->id,
                'type' => $type,
                'amount' => $amount,
                'balance_before' => $oldBalance,
                'balance_after' => $newBalance,
                'description' => $description,
            ]);
            
            // 清除缓存
            static::clearUserCache($this->id);
            
            return $newBalance;
        });
    }

    /**
     * 检查用户权限（带缓存）
     */
    public function hasPermissionCached($permission)
    {
        $cacheKey = "user:permissions:{$this->id}";
        
        $permissions = Cache::remember($cacheKey, 3600, function () {
            return $this->getAllPermissions()->pluck('name')->toArray();
        });
        
        return in_array($permission, $permissions);
    }

    /**
     * 获取下级用户列表（优化查询）
     */
    public function getDescendants($maxDepth = 3)
    {
        $descendants = collect();
        $currentLevel = collect([$this]);
        
        for ($depth = 0; $depth < $maxDepth; $depth++) {
            $nextLevel = collect();
            
            foreach ($currentLevel as $user) {
                $children = $user->children()->with(['distributionGroup'])->get();
                $descendants = $descendants->merge($children);
                $nextLevel = $nextLevel->merge($children);
            }
            
            if ($nextLevel->isEmpty()) {
                break;
            }
            
            $currentLevel = $nextLevel;
        }
        
        return $descendants;
    }



    /**
     * 检查用户是否为活跃状态
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * 检查用户是否为管理员
     */
    public function isAdmin(): bool
    {
        return $this->role === self::ROLE_ADMIN;
    }

    /**
     * 检查用户是否为分站管理员
     */
    public function isSubstation(): bool
    {
        return $this->role === self::ROLE_SUBSTATION;
    }

    /**
     * 检查用户是否为分销商
     */
    public function isDistributor(): bool
    {
        return $this->role === self::ROLE_DISTRIBUTOR;
    }

    /**
     * 更新最后登录信息
     */
    public function updateLastLogin(string $ip): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip,
        ]);
    }

    /**
     * 增加余额
     */
    public function addBalance(float $amount): void
    {
        $this->adjustBalance($amount, 'commission', '佣金收入');
    }

    /**
     * 获取微信群关联
     */
    public function wechatGroups(): HasMany
    {
        return $this->hasMany(WechatGroup::class, 'user_id');
    }

    // ========== 数据库索引建议 ==========
    
    /**
     * 建议的数据库索引（请在迁移文件中添加）
     * 
     * $table->index(['role', 'status']);
     * $table->index(['parent_id']);
     * $table->index(['invite_code']);
     * $table->index(['last_login_at']);
     * $table->index(['created_at']);
     * $table->index(['distribution_group_id']);
     * $table->index(['substation_id']);
     * 
     * 复合索引：
     * $table->index(['role', 'status', 'created_at']);
     * $table->index(['parent_id', 'status']);
     */
}