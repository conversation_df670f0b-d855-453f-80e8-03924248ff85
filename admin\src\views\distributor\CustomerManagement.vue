<template>
  <div class="customer-management">
    <div class="page-header">
      <div class="header-left">
        <h2>客户管理</h2>
        <p class="page-description">管理您的客户信息，跟踪客户状态，提升客户转化率</p>
      </div>
      <div class="header-actions">
        <el-button type="info" @click="showHelpDialog = true">
          <el-icon><QuestionFilled /></el-icon>
          功能说明
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增客户
        </el-button>
        <el-button @click="exportCustomers">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatCard
          title="总客户数"
          :value="stats.total_customers || 0"
          icon="User"
          color="#409EFF"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="活跃客户"
          :value="stats.active_customers || 0"
          icon="UserFilled"
          color="#67C23A"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="潜在客户"
          :value="stats.potential_customers || 0"
          icon="View"
          color="#E6A23C"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="需要跟进"
          :value="stats.need_follow_up || 0"
          icon="Bell"
          color="#F56C6C"
        />
      </el-col>
    </el-row>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索客户姓名、手机号、微信号"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filters.status" placeholder="客户状态" clearable @change="loadCustomers">
            <el-option label="活跃" value="active" />
            <el-option label="不活跃" value="inactive" />
            <el-option label="潜在" value="potential" />
            <el-option label="流失" value="lost" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filters.level" placeholder="客户等级" clearable @change="loadCustomers">
            <el-option label="A级客户" value="A" />
            <el-option label="B级客户" value="B" />
            <el-option label="C级客户" value="C" />
            <el-option label="D级客户" value="D" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filters.source" placeholder="客户来源" clearable @change="loadCustomers">
            <el-option label="推荐" value="referral" />
            <el-option label="广告" value="advertisement" />
            <el-option label="社交媒体" value="social_media" />
            <el-option label="直接访问" value="direct" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="filters.tag" placeholder="客户标签" clearable @change="loadCustomers">
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-checkbox v-model="filters.need_follow_up" @change="loadCustomers">
            需要跟进
          </el-checkbox>
        </el-col>
      </el-row>
    </el-card>

    <!-- 客户列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>客户列表</span>
          <div class="header-actions">
            <el-button size="small" @click="batchUpdateStatus" :disabled="selectedCustomers.length === 0">
              批量操作
            </el-button>
            <el-button size="small" @click="loadCustomers">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="customers"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="姓名" width="120">
          <template #default="{ row }">
            <div class="customer-name">
              <span>{{ row.name }}</span>
              <div class="customer-tags" v-if="row.tags && row.tags.length">
                <el-tag
                  v-for="tag in row.tags.slice(0, 2)"
                  :key="tag"
                  size="small"
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="wechat" label="微信号" width="120" />
        <el-table-column label="等级" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelColor(row.level)">{{ row.level_text }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">{{ row.status_text }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="company" label="公司" width="150" show-overflow-tooltip />
        <el-table-column label="消费金额" width="100">
          <template #default="{ row }">
            <span class="amount">¥{{ row.total_spent }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_count" label="订单数" width="80" />
        <el-table-column label="最后联系" width="120">
          <template #default="{ row }">
            <span v-if="row.last_contact_at">
              {{ formatDate(row.last_contact_at) }}
            </span>
            <span v-else class="no-contact">未联系</span>
          </template>
        </el-table-column>
        <el-table-column label="跟进状态" width="120">
          <template #default="{ row }">
            <div v-if="row.next_follow_up">
              <el-tag
                :type="getFollowUpColor(row.follow_up_days)"
                size="small"
              >
                {{ getFollowUpText(row.follow_up_days) }}
              </el-tag>
            </div>
            <span v-else class="no-follow-up">无计划</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewCustomer(row)">
              详情
            </el-button>
            <el-button size="small" @click="addFollowUp(row)">
              跟进
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.per_page"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadCustomers"
          @current-change="loadCustomers"
        />
      </div>
    </el-card>

    <!-- 创建/编辑客户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingCustomer ? '编辑客户' : '新增客户'"
      width="800px"
    >
      <div class="customer-form">
        <el-form :model="customerForm" :rules="customerRules" ref="customerFormRef" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户姓名" prop="name">
                <el-input v-model="customerForm.name" placeholder="请输入客户姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="customerForm.phone" placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="微信号" prop="wechat">
                <el-input v-model="customerForm.wechat" placeholder="请输入微信号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户等级" prop="level">
                <el-select v-model="customerForm.level" placeholder="选择客户等级">
                  <el-option label="A级客户" value="A" />
                  <el-option label="B级客户" value="B" />
                  <el-option label="C级客户" value="C" />
                  <el-option label="D级客户" value="D" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="公司名称">
                <el-input v-model="customerForm.company" placeholder="请输入公司名称" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input 
                  v-model="customerForm.remark" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入备注信息"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCustomer" :loading="saveLoading">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 客户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="客户详情"
      width="1000px"
    >
      <div v-if="selectedCustomer" class="customer-detail">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="客户姓名">{{ selectedCustomer.name }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedCustomer.phone }}</el-descriptions-item>
          <el-descriptions-item label="微信号">{{ selectedCustomer.wechat || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="客户等级">
            <el-tag :type="getLevelColor(selectedCustomer.level)">{{ selectedCustomer.level_text }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="客户状态">
            <el-tag :type="getStatusColor(selectedCustomer.status)">{{ selectedCustomer.status_text }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="公司名称">{{ selectedCustomer.company || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="消费金额">¥{{ selectedCustomer.total_spent }}</el-descriptions-item>
          <el-descriptions-item label="订单数量">{{ selectedCustomer.order_count }}</el-descriptions-item>
          <el-descriptions-item label="最后联系">{{ selectedCustomer.last_contact_at ? formatDate(selectedCustomer.last_contact_at) : '未联系' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" span="2">{{ formatDate(selectedCustomer.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备注" span="3">{{ selectedCustomer.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="editCustomer(selectedCustomer)">
          编辑
        </el-button>
        <el-button @click="addFollowUp(selectedCustomer)">
          添加跟进
        </el-button>
      </template>
    </el-dialog>

    <!-- 跟进记录对话框 -->
    <el-dialog
      v-model="showFollowUpDialog"
      title="添加跟进记录"
      width="600px"
    >
      <div class="follow-up-form">
        <el-form :model="followUpForm" :rules="followUpRules" ref="followUpFormRef" label-width="100px">
          <el-form-item label="跟进内容" prop="content">
            <el-input 
              v-model="followUpForm.content" 
              type="textarea" 
              :rows="4"
              placeholder="请输入跟进内容"
            />
          </el-form-item>
          <el-form-item label="跟进方式" prop="method">
            <el-select v-model="followUpForm.method" placeholder="选择跟进方式">
              <el-option label="电话" value="phone" />
              <el-option label="微信" value="wechat" />
              <el-option label="邮件" value="email" />
              <el-option label="面谈" value="meeting" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="下次跟进" prop="next_follow_up">
            <el-date-picker
              v-model="followUpForm.next_follow_up"
              type="datetime"
              placeholder="选择下次跟进时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input 
              v-model="followUpForm.remark" 
              type="textarea" 
              :rows="2"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showFollowUpDialog = false">取消</el-button>
        <el-button type="primary" @click="saveFollowUp" :loading="followUpLoading">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="showBatchDialog"
      title="批量操作"
      width="400px"
    >
      <div class="batch-operation">
        <p>已选择 {{ selectedCustomers.length }} 个客户</p>
        <el-form :model="batchForm" label-width="80px">
          <el-form-item label="操作类型">
            <el-select v-model="batchForm.action" placeholder="请选择操作">
              <el-option label="更新状态" value="status" />
              <el-option label="更新等级" value="level" />
              <el-option label="添加标签" value="tag" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="batchForm.action === 'status'" label="新状态">
            <el-select v-model="batchForm.status" placeholder="选择状态">
              <el-option label="活跃" value="active" />
              <el-option label="不活跃" value="inactive" />
              <el-option label="潜在" value="potential" />
              <el-option label="流失" value="lost" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="batchForm.action === 'level'" label="新等级">
            <el-select v-model="batchForm.level" placeholder="选择等级">
              <el-option label="A级客户" value="A" />
              <el-option label="B级客户" value="B" />
              <el-option label="C级客户" value="C" />
              <el-option label="D级客户" value="D" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="batchForm.action === 'tag'" label="标签">
            <el-input v-model="batchForm.tag" placeholder="输入标签名称" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button type="primary" @click="executeBatchOperation" :loading="batchLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="客户管理功能说明"
      width="900px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>📋 功能概述</h3>
          <p>客户管理系统帮助您有效管理和跟踪客户信息，提升客户转化率和维护效率。通过客户分级、状态跟踪、跟进提醒等功能，让您的客户管理更加专业和高效。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>客户信息管理</h4>
                  <p>完整记录客户基本信息、联系方式、公司信息等</p>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Bell /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>跟进提醒</h4>
                  <p>设置跟进计划，系统自动提醒，不错过任何商机</p>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>客户分级</h4>
                  <p>A/B/C/D四级分类，精准定位客户价值</p>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>数据统计</h4>
                  <p>实时统计客户数量、状态分布、转化情况</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 客户等级说明 -->
        <div class="help-section">
          <h3>🏆 客户等级说明</h3>
          <el-table :data="levelExplanation" style="width: 100%">
            <el-table-column prop="level" label="等级" width="80">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="等级名称" width="120" />
            <el-table-column prop="criteria" label="评定标准" />
            <el-table-column prop="strategy" label="维护策略" />
          </el-table>
        </div>

        <!-- 客户状态说明 -->
        <div class="help-section">
          <h3>📊 客户状态说明</h3>
          <el-table :data="statusExplanation" style="width: 100%">
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.color" size="small">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="状态描述" />
            <el-table-column prop="action" label="建议操作" />
          </el-table>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何新增客户？" name="add-customer">
              <div class="guide-content">
                <ol>
                  <li>点击页面右上角的"新增客户"按钮</li>
                  <li>填写客户基本信息（姓名、手机号为必填项）</li>
                  <li>选择客户等级和来源</li>
                  <li>添加客户标签（可选）</li>
                  <li>点击"保存"完成客户创建</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 建议：新增客户时尽量填写完整信息，有助于后续的客户管理和跟进
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何设置跟进提醒？" name="follow-up">
              <div class="guide-content">
                <ol>
                  <li>在客户列表中找到需要跟进的客户</li>
                  <li>点击"跟进"按钮</li>
                  <li>填写跟进内容和下次跟进时间</li>
                  <li>选择跟进方式（电话、微信、邮件等）</li>
                  <li>保存后系统会在指定时间提醒您</li>
                </ol>
                <el-alert type="warning" :closable="false">
                  ⚠️ 注意：跟进提醒会在系统消息中显示，请及时查看处理
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何进行批量操作？" name="batch-operation">
              <div class="guide-content">
                <ol>
                  <li>在客户列表中勾选需要操作的客户</li>
                  <li>点击"批量操作"按钮</li>
                  <li>选择操作类型（更新状态、等级或标签）</li>
                  <li>设置新的值</li>
                  <li>确认执行批量操作</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 提示：批量操作可以大大提高工作效率，适用于客户状态统一更新的场景
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何使用筛选功能？" name="filter">
              <div class="guide-content">
                <p><strong>可用筛选条件：</strong></p>
                <ul>
                  <li><strong>关键词搜索</strong>：支持按姓名、手机号、微信号搜索</li>
                  <li><strong>客户状态</strong>：活跃、不活跃、潜在、流失</li>
                  <li><strong>客户等级</strong>：A/B/C/D级客户</li>
                  <li><strong>客户来源</strong>：推荐、广告、社交媒体等</li>
                  <li><strong>客户标签</strong>：自定义标签筛选</li>
                  <li><strong>跟进状态</strong>：需要跟进的客户</li>
                </ul>
                <el-alert type="info" :closable="false">
                  💡 技巧：多个筛选条件可以组合使用，帮助您快速找到目标客户
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 使用技巧 -->
        <div class="help-section">
          <h3>💡 使用技巧</h3>
          <div class="tips-grid">
            <div class="tip-item">
              <div class="tip-icon">🎯</div>
              <div class="tip-content">
                <h4>精准分级</h4>
                <p>根据客户消费能力和购买意向进行分级，A级客户重点维护，D级客户适度跟进</p>
              </div>
            </div>
            <div class="tip-item">
              <div class="tip-icon">⏰</div>
              <div class="tip-content">
                <h4>定期跟进</h4>
                <p>建议A级客户每周跟进，B级客户每两周跟进，C/D级客户每月跟进</p>
              </div>
            </div>
            <div class="tip-item">
              <div class="tip-icon">🏷️</div>
              <div class="tip-content">
                <h4>标签管理</h4>
                <p>使用标签标记客户特征，如"高价值"、"决策者"、"价格敏感"等</p>
              </div>
            </div>
            <div class="tip-item">
              <div class="tip-icon">📊</div>
              <div class="tip-content">
                <h4>数据分析</h4>
                <p>定期查看客户统计数据，分析客户结构和转化趋势</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="help-section">
          <h3>❓ 常见问题</h3>
          <el-collapse v-model="activeFAQ">
            <el-collapse-item title="为什么我的客户状态没有自动更新？" name="faq1">
              <p>客户状态需要手动更新或通过系统规则自动判断。建议定期检查客户最后联系时间和订单情况，及时更新状态。</p>
            </el-collapse-item>
            <el-collapse-item title="如何批量导入客户信息？" name="faq2">
              <p>点击"批量导入"按钮，下载模板文件，按照模板格式填写客户信息后上传。支持Excel格式文件。</p>
            </el-collapse-item>
            <el-collapse-item title="客户等级可以自动调整吗？" name="faq3">
              <p>目前客户等级需要手动调整。系统会根据客户的消费金额和订单数量提供等级建议，您可以参考后手动调整。</p>
            </el-collapse-item>
            <el-collapse-item title="跟进提醒在哪里查看？" name="faq4">
              <p>跟进提醒会在系统消息中显示，同时在客户列表的"跟进状态"列中也会显示提醒信息。</p>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Download, Search, Refresh, Bell, User, UserFilled, View,
  ArrowDown, QuestionFilled, TrendCharts
} from '@element-plus/icons-vue'
import StatCard from '@/components/StatCard.vue'
// 暂时注释掉有问题的组件导入，使用内联组件替代
// import CustomerDialog from './components/CustomerDialog.vue'
// import CustomerDetail from './components/CustomerDetail.vue'
// import FollowUpDialog from './components/FollowUpDialog.vue'
import { customerApi } from '@/api/customer'
import { mockCustomerApi } from '@/api/mock-customer'

// 根据环境选择API
const api = process.env.NODE_ENV === 'development' ? mockCustomerApi : customerApi

// 响应式数据
const loading = ref(false)
const batchLoading = ref(false)
const saveLoading = ref(false)
const followUpLoading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showFollowUpDialog = ref(false)
const showBatchDialog = ref(false)
const showHelpDialog = ref(false)

// 帮助对话框相关数据
const activeGuides = ref(['add-customer'])
const activeFAQ = ref([])

// 客户等级说明数据
const levelExplanation = ref([
  {
    level: 'A级',
    color: 'danger',
    name: '核心客户',
    criteria: '月消费≥5000元，订单频次高，忠诚度高',
    strategy: '专人对接，每周跟进，提供VIP服务'
  },
  {
    level: 'B级',
    color: 'warning',
    name: '重要客户',
    criteria: '月消费1000-5000元，有稳定需求',
    strategy: '定期维护，每两周跟进，重点培养'
  },
  {
    level: 'C级',
    color: 'primary',
    name: '潜力客户',
    criteria: '月消费500-1000元，有增长潜力',
    strategy: '适度关注，每月跟进，挖掘需求'
  },
  {
    level: 'D级',
    color: 'info',
    name: '普通客户',
    criteria: '月消费<500元，偶尔购买',
    strategy: '基础维护，季度跟进，群发营销'
  }
])

// 客户状态说明数据
const statusExplanation = ref([
  {
    status: '活跃',
    color: 'success',
    description: '近30天内有购买行为或主动联系',
    action: '保持现有服务水平，及时响应需求'
  },
  {
    status: '不活跃',
    color: 'warning',
    description: '30-90天内无购买行为，但有互动',
    action: '主动联系了解情况，提供优惠激活'
  },
  {
    status: '潜在',
    color: 'primary',
    description: '有意向但未成交，处于考虑阶段',
    action: '持续跟进，提供专业建议和方案'
  },
  {
    status: '流失',
    color: 'danger',
    description: '超过90天无任何互动或明确拒绝',
    action: '尝试挽回，了解流失原因，改进服务'
  }
])

const customers = ref([])
const stats = ref({})
const availableTags = ref([])
const selectedCustomers = ref([])
const editingCustomer = ref(null)
const selectedCustomer = ref(null)

const filters = reactive({
  keyword: '',
  status: '',
  level: '',
  source: '',
  tag: '',
  need_follow_up: false
})

const pagination = reactive({
  current_page: 1,
  per_page: 20,
  total: 0
})

const batchForm = reactive({
  action: '',
  status: '',
  level: '',
  tag: ''
})

const customerForm = reactive({
  name: '',
  phone: '',
  wechat: '',
  level: '',
  company: '',
  remark: ''
})

const customerRules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择客户等级', trigger: 'change' }
  ]
}

const followUpForm = reactive({
  content: '',
  method: '',
  next_follow_up: '',
  remark: ''
})

const followUpRules = {
  content: [
    { required: true, message: '请输入跟进内容', trigger: 'blur' }
  ],
  method: [
    { required: true, message: '请选择跟进方式', trigger: 'change' }
  ]
}

const customerFormRef = ref()
const followUpFormRef = ref()

// 方法
const loadCustomers = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current_page,
      limit: pagination.per_page,
      ...filters
    }
    
    const response = await api.getList(params)
    customers.value = response.data.data
    pagination.total = response.data.total
    pagination.current_page = response.data.current_page
    pagination.per_page = response.data.per_page
  } catch (error) {
    console.error('加载客户列表失败:', error)
    ElMessage.error('加载客户列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await api.getStats()
    stats.value = response.data
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const loadTags = async () => {
  try {
    const response = await api.getTags()
    availableTags.value = response.data
  } catch (error) {
    console.error('加载标签失败:', error)
  }
}

const handleSearch = debounce(() => {
  pagination.current_page = 1
  loadCustomers()
}, 500)

const handleSelectionChange = (selection) => {
  selectedCustomers.value = selection
}

const viewCustomer = (customer) => {
  selectedCustomer.value = customer
  showDetailDialog.value = true
}

const editCustomer = (customer) => {
  editingCustomer.value = customer
  // 填充表单数据
  Object.keys(customerForm).forEach(key => {
    if (customer[key] !== undefined) {
      customerForm[key] = customer[key]
    }
  })
  showCreateDialog.value = true
}

const addFollowUp = (customer) => {
  selectedCustomer.value = customer
  showFollowUpDialog.value = true
}

const handleCommand = (command, customer) => {
  switch (command) {
    case 'edit':
      editCustomer(customer)
      break
    case 'delete':
      deleteCustomer(customer)
      break
  }
}

const deleteCustomer = async (customer) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户"${customer.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await api.delete(customer.id)
    ElMessage.success('客户删除成功')
    loadCustomers()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const saveCustomer = async () => {
  try {
    await customerFormRef.value.validate()
    saveLoading.value = true
    
    if (editingCustomer.value) {
      // 编辑客户
      await api.update(editingCustomer.value.id, customerForm)
      ElMessage.success('客户信息更新成功')
    } else {
      // 新增客户
      await api.create(customerForm)
      ElMessage.success('客户创建成功')
    }
    
    handleCustomerSuccess()
  } catch (error) {
    console.error('保存客户失败:', error)
    ElMessage.error('保存客户失败')
  } finally {
    saveLoading.value = false
  }
}

const saveFollowUp = async () => {
  try {
    await followUpFormRef.value.validate()
    followUpLoading.value = true
    
    const data = {
      customer_id: selectedCustomer.value.id,
      ...followUpForm
    }
    
    await api.addFollowUp(data)
    ElMessage.success('跟进记录添加成功')
    handleFollowUpSuccess()
  } catch (error) {
    console.error('保存跟进记录失败:', error)
    ElMessage.error('保存跟进记录失败')
  } finally {
    followUpLoading.value = false
  }
}

const handleCustomerSuccess = () => {
  showCreateDialog.value = false
  editingCustomer.value = null
  // 重置表单
  Object.keys(customerForm).forEach(key => {
    customerForm[key] = ''
  })
  loadCustomers()
  loadStats()
  loadTags()
}

const handleFollowUpSuccess = () => {
  showFollowUpDialog.value = false
  // 重置表单
  Object.keys(followUpForm).forEach(key => {
    followUpForm[key] = ''
  })
  loadCustomers()
}

const batchUpdateStatus = () => {
  if (selectedCustomers.value.length === 0) {
    ElMessage.warning('请先选择客户')
    return
  }
  showBatchDialog.value = true
}

const executeBatchOperation = async () => {
  if (!batchForm.action) {
    ElMessage.warning('请选择操作类型')
    return
  }
  
  try {
    batchLoading.value = true
    const customerIds = selectedCustomers.value.map(c => c.id)
    
    if (batchForm.action === 'status') {
      await api.batchUpdateStatus({
        customer_ids: customerIds,
        status: batchForm.status
      })
    }
    // 其他批量操作...
    
    ElMessage.success('批量操作成功')
    showBatchDialog.value = false
    loadCustomers()
    loadStats()
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  } finally {
    batchLoading.value = false
  }
}

const exportCustomers = async () => {
  try {
    const response = await api.export(filters)
    // 处理导出逻辑
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 工具方法
const getLevelColor = (level) => {
  const colors = {
    'A': 'danger',
    'B': 'warning', 
    'C': 'primary',
    'D': 'info'
  }
  return colors[level] || 'info'
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'success',
    'inactive': 'warning',
    'potential': 'primary',
    'lost': 'danger'
  }
  return colors[status] || 'info'
}

const getFollowUpColor = (days) => {
  if (days < 0) return 'danger'  // 逾期
  if (days <= 1) return 'warning' // 即将到期
  return 'success' // 正常
}

const getFollowUpText = (days) => {
  if (days < 0) return `逾期${Math.abs(days)}天`
  if (days === 0) return '今日跟进'
  if (days === 1) return '明日跟进'
  return `${days}天后`
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 防抖函数
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 生命周期
onMounted(() => {
  loadCustomers()
  loadStats()
  loadTags()
})
</script>

<style scoped>
.customer-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customer-tags {
  display: flex;
  gap: 4px;
}

.amount {
  color: #67C23A;
  font-weight: bold;
}

.no-contact,
.no-follow-up {
  color: #909399;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-operation {
  padding: 20px 0;
}

.batch-operation p {
  margin-bottom: 20px;
  color: #606266;
}

/* 帮助对话框样式 */
.help-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.help-content {
  .help-section {
    margin-bottom: 30px;
    
    h3 {
      color: #303133;
      margin-bottom: 15px;
      font-size: 18px;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 8px;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
  }
  
  .feature-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
    
    .feature-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #409EFF;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      flex-shrink: 0;
      
      .el-icon {
        color: white;
        font-size: 18px;
      }
    }
    
    .feature-content {
      flex: 1;
      
      h4 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 16px;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
  
  .guide-content {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    
    ol, ul {
      margin: 0 0 15px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.5;
      }
    }
    
    .el-alert {
      margin-top: 15px;
    }
  }
  
  .tips-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    
    .tip-item {
      display: flex;
      align-items: flex-start;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #409EFF;
      
      .tip-icon {
        font-size: 24px;
        margin-right: 15px;
        flex-shrink: 0;
      }
      
      .tip-content {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          color: #303133;
          font-size: 16px;
        }
        
        p {
          margin: 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
}

.page-description {
  color: #909399;
  font-size: 14px;
  margin: 5px 0 0 0;
}

.header-left {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .customer-management {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .filter-card .el-row {
    flex-direction: column;
  }
  
  .filter-card .el-col {
    margin-bottom: 10px;
  }
  
  .help-content .tips-grid {
    grid-template-columns: 1fr;
  }
  
  .help-content .feature-item {
    flex-direction: column;
    text-align: center;
    
    .feature-icon {
      margin: 0 0 15px 0;
    }
  }
}
</style>