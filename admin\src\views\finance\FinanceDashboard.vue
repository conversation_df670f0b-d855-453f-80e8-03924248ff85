<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>财务管理工作台</h2>
        <p class="page-description">全面掌握平台财务状况，实时监控收支情况，高效管理资金流转</p>
      </div>
      <div class="header-actions">
        <el-button type="info" @click="showHelpDialog = true">
          <el-icon><QuestionFilled /></el-icon>
          功能说明
        </el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 财务概览卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon primary">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stats-value">¥{{ formatMoney(stats.total_revenue) }}</div>
          <div class="stats-label">总收入</div>
          <div class="stats-change positive">
            <el-icon><CaretTop /></el-icon>
            +{{ stats.revenue_growth }}%
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon success">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="stats-value">¥{{ formatMoney(stats.total_commission) }}</div>
          <div class="stats-label">总佣金</div>
          <div class="stats-change positive">
            <el-icon><CaretTop /></el-icon>
            +{{ stats.commission_growth }}%
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon warning">
            <el-icon><CreditCard /></el-icon>
          </div>
          <div class="stats-value">¥{{ formatMoney(stats.pending_withdrawal) }}</div>
          <div class="stats-label">待提现</div>
          <div class="stats-change">
            {{ stats.pending_count }}笔申请
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stats-card">
          <div class="stats-icon danger">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stats-value">¥{{ formatMoney(stats.today_revenue) }}</div>
          <div class="stats-label">今日收入</div>
          <div class="stats-change positive">
            <el-icon><CaretTop /></el-icon>
            +{{ stats.today_growth }}%
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>收入趋势分析</h3>
              <div class="chart-controls">
                <el-button-group>
                  <el-button 
                    v-for="period in timePeriods" 
                    :key="period.value"
                    :type="selectedPeriod === period.value ? 'primary' : 'default'"
                    size="small"
                    @click="selectedPeriod = period.value; fetchChartData()"
                  >
                    {{ period.label }}
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <LineChart 
              :data="revenueChartData" 
              :options="chartOptions"
              :height="300"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>收入来源分布</h3>
            </div>
          </template>
          <div class="chart-content">
            <PieChart 
              :data="sourceChartData" 
              :options="pieChartOptions"
              :height="300"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作和最新交易 -->
    <el-row :gutter="20" class="action-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>快捷操作</h3>
            </div>
          </template>
          <div class="quick-actions">
            <div class="action-grid">
              <div class="action-item" @click="navigateToTransactions">
                <el-icon><Tickets /></el-icon>
                <span>交易记录</span>
              </div>
              <div class="action-item" @click="navigateToCommissions">
                <el-icon><Medal /></el-icon>
                <span>佣金明细</span>
              </div>
              <div class="action-item" @click="navigateToWithdrawals">
                <el-icon><Upload /></el-icon>
                <span>提现管理</span>
              </div>
              <div class="action-item" @click="handleExportReport">
                <el-icon><Download /></el-icon>
                <span>导出报表</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>最新交易</h3>
              <el-button type="text" @click="navigateToTransactions">查看全部</el-button>
            </div>
          </template>
          <div class="recent-transactions">
            <div 
              v-for="transaction in recentTransactions" 
              :key="transaction.id"
              class="transaction-item"
            >
              <div class="transaction-info">
                <div class="transaction-title">{{ transaction.title }}</div>
                <div class="transaction-time">{{ formatDate(transaction.created_at) }}</div>
              </div>
              <div class="transaction-amount" :class="transaction.type === 'income' ? 'positive' : 'negative'">
                {{ transaction.type === 'income' ? '+' : '-' }}¥{{ transaction.amount.toFixed(2) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 财务统计表格 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>财务统计详情</h3>
          <div>
            <el-button type="primary" size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="financeStats" border>
        <el-table-column label="统计项目" prop="name" width="150" />
        <el-table-column label="今日" width="120">
          <template #default="{ row }">
            <span class="money-text">¥{{ row.today.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本周" width="120">
          <template #default="{ row }">
            <span class="money-text">¥{{ row.week.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本月" width="120">
          <template #default="{ row }">
            <span class="money-text">¥{{ row.month.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本年" width="120">
          <template #default="{ row }">
            <span class="money-text">¥{{ row.year.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总计">
          <template #default="{ row }">
            <span class="money-text total">¥{{ row.total.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="增长率" width="100">
          <template #default="{ row }">
            <span :class="row.growth >= 0 ? 'positive-growth' : 'negative-growth'">
              {{ row.growth >= 0 ? '+' : '' }}{{ row.growth.toFixed(1) }}%
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="财务管理工作台功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>💰 功能概述</h3>
          <p>财务管理工作台是平台资金管理的核心系统，提供全面的财务数据统计、收支分析、提现管理等功能，帮助您实时掌握平台财务状况，做出明智的财务决策。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>财务概览</h4>
                  <p>实时显示总收入、总佣金、待提现等关键财务指标</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>数据分析</h4>
                  <p>收入趋势图表、来源分布分析、增长率统计</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Tickets /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>交易管理</h4>
                  <p>查看所有交易记录，支持筛选和导出功能</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Medal /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>佣金管理</h4>
                  <p>佣金明细查询、结算记录、分成比例管理</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Upload /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>提现管理</h4>
                  <p>处理用户提现申请、审核提现资格、资金划转</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Download /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>报表导出</h4>
                  <p>生成财务报表、数据导出、定期统计分析</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 财务指标说明 -->
        <div class="help-section">
          <h3>📊 财务指标说明</h3>
          <el-table :data="financeMetrics" style="width: 100%">
            <el-table-column prop="metric" label="指标名称" width="120">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.metric }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="指标说明" />
            <el-table-column prop="calculation" label="计算方式" />
            <el-table-column prop="significance" label="业务意义" />
          </el-table>
        </div>

        <!-- 收入来源分析 -->
        <div class="help-section">
          <h3>💸 收入来源分析</h3>
          <div class="revenue-sources">
            <div class="source-item">
              <div class="source-icon" style="background: #1890ff;">
                <el-icon><Comment /></el-icon>
              </div>
              <div class="source-content">
                <h4>群组收入</h4>
                <p>用户加入付费群组产生的收入，是平台主要收入来源</p>
                <div class="source-details">
                  <span>占比：约60-70%</span>
                  <span>特点：稳定性高，增长潜力大</span>
                </div>
              </div>
            </div>
            <div class="source-item">
              <div class="source-icon" style="background: #52c41a;">
                <el-icon><Share /></el-icon>
              </div>
              <div class="source-content">
                <h4>佣金收入</h4>
                <p>分销商推广产生的佣金收入，激励推广积极性</p>
                <div class="source-details">
                  <span>占比：约20-25%</span>
                  <span>特点：增长快速，波动较大</span>
                </div>
              </div>
            </div>
            <div class="source-item">
              <div class="source-icon" style="background: #faad14;">
                <el-icon><Shop /></el-icon>
              </div>
              <div class="source-content">
                <h4>分站收入</h4>
                <p>分站管理费用和服务费收入</p>
                <div class="source-details">
                  <span>占比：约10-15%</span>
                  <span>特点：稳定增长，利润率高</span>
                </div>
              </div>
            </div>
            <div class="source-item">
              <div class="source-icon" style="background: #f5222d;">
                <el-icon><Star /></el-icon>
              </div>
              <div class="source-content">
                <h4>其他收入</h4>
                <p>广告收入、增值服务等其他收入来源</p>
                <div class="source-details">
                  <span>占比：约5-10%</span>
                  <span>特点：多样化，补充性收入</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 提现管理流程 -->
        <div class="help-section">
          <h3>🏦 提现管理流程</h3>
          <div class="withdrawal-process">
            <div class="process-step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>用户申请</h4>
                <p>用户在前台提交提现申请，填写提现金额和收款信息</p>
              </div>
            </div>
            <div class="process-arrow">→</div>
            <div class="process-step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>系统审核</h4>
                <p>系统自动检查用户余额、提现限额、实名认证等条件</p>
              </div>
            </div>
            <div class="process-arrow">→</div>
            <div class="process-step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>人工审核</h4>
                <p>管理员审核提现申请，核实用户身份和提现合规性</p>
              </div>
            </div>
            <div class="process-arrow">→</div>
            <div class="process-step">
              <div class="step-number">4</div>
              <div class="step-content">
                <h4>资金处理</h4>
                <p>审核通过后，系统自动或手动处理资金转账</p>
              </div>
            </div>
            <div class="process-arrow">→</div>
            <div class="process-step">
              <div class="step-number">5</div>
              <div class="step-content">
                <h4>完成提现</h4>
                <p>资金到账，更新提现状态，发送通知给用户</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何查看财务数据？" name="view-data">
              <div class="guide-content">
                <ol>
                  <li>在工作台首页查看核心财务指标卡片</li>
                  <li>查看"收入趋势分析"图表了解收入变化</li>
                  <li>在"收入来源分布"图表中查看收入结构</li>
                  <li>在财务统计表格中查看详细的分时段数据</li>
                  <li>可以切换时间段查看不同期间的数据</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：数据每小时更新一次，增长率基于同期对比计算
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何处理提现申请？" name="withdrawal-process">
              <div class="guide-content">
                <ol>
                  <li>点击"提现管理"进入提现管理页面</li>
                  <li>查看待审核的提现申请列表</li>
                  <li>点击"详情"查看申请人信息和提现详情</li>
                  <li>核实用户身份、余额、提现合规性</li>
                  <li>点击"通过"或"拒绝"处理申请</li>
                  <li>填写审核意见并确认操作</li>
                </ol>
                <el-alert type="warning" :closable="false">
                  ⚠️ 注意：提现审核要严格核实用户身份，防范资金风险
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何导出财务报表？" name="export-report">
              <div class="guide-content">
                <ol>
                  <li>点击"导出报表"按钮</li>
                  <li>选择报表类型（收入报表、佣金报表、提现报表等）</li>
                  <li>设置导出时间范围</li>
                  <li>选择导出格式（Excel、PDF等）</li>
                  <li>点击"确认导出"生成报表</li>
                  <li>下载生成的报表文件</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 说明：报表支持多种格式，可用于财务分析和对账
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何分析收入趋势？" name="revenue-analysis">
              <div class="guide-content">
                <ol>
                  <li>查看收入趋势图表，观察收入变化规律</li>
                  <li>切换不同时间段（7天、30天、90天、1年）</li>
                  <li>对比不同时期的收入水平和增长率</li>
                  <li>分析收入来源分布，识别主要收入驱动因素</li>
                  <li>结合业务活动分析收入波动原因</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 建议：定期分析收入趋势，制定相应的运营策略
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 风险提示 -->
        <div class="help-section">
          <h3>⚠️ 风险提示与注意事项</h3>
          <div class="risk-alerts">
            <el-alert type="error" :closable="false" style="margin-bottom: 15px;">
              <template #title>
                <strong>资金安全风险</strong>
              </template>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>严格审核提现申请，防范虚假提现和洗钱风险</li>
                <li>定期核对账务，确保资金流水准确无误</li>
                <li>建立资金监控机制，及时发现异常交易</li>
              </ul>
            </el-alert>
            
            <el-alert type="warning" :closable="false" style="margin-bottom: 15px;">
              <template #title>
                <strong>合规风险</strong>
              </template>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>确保所有财务操作符合相关法律法规</li>
                <li>完善财务记录，保留必要的凭证和文档</li>
                <li>定期进行财务审计，确保合规经营</li>
              </ul>
            </el-alert>
            
            <el-alert type="info" :closable="false">
              <template #title>
                <strong>操作建议</strong>
              </template>
              <ul style="margin: 10px 0; padding-left: 20px;">
                <li>建议每日查看财务数据，及时发现异常情况</li>
                <li>定期备份财务数据，防止数据丢失</li>
                <li>建立多人审核机制，降低操作风险</li>
              </ul>
            </el-alert>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="help-section">
          <h3>❓ 常见问题</h3>
          <el-collapse v-model="activeFAQ">
            <el-collapse-item title="财务数据多久更新一次？" name="faq1">
              <p>财务数据每小时自动更新一次，重要指标如收入、佣金等实时更新。您也可以点击"刷新数据"按钮手动更新。</p>
            </el-collapse-item>
            <el-collapse-item title="提现申请的处理时间是多久？" name="faq2">
              <p>一般情况下，提现申请在1-3个工作日内处理完成。具体时间取决于申请金额、用户等级和审核复杂度。</p>
            </el-collapse-item>
            <el-collapse-item title="如何设置提现限额？" name="faq3">
              <p>可以在系统设置中配置不同用户等级的提现限额，包括单次提现限额、日提现限额和月提现限额。</p>
            </el-collapse-item>
            <el-collapse-item title="财务报表可以定期自动生成吗？" name="faq4">
              <p>系统支持定期自动生成财务报表功能，可以设置每日、每周或每月自动生成并发送到指定邮箱。</p>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Money, Wallet, CreditCard, TrendCharts, CaretTop, Tickets, Medal, Upload, Download, Refresh,
  QuestionFilled, Comment, Share, Shop, Star
} from '@element-plus/icons-vue'
import LineChart from '@/components/Charts/LineChart.vue'
import PieChart from '@/components/Charts/PieChart.vue'
import { getFinanceStats, getFinanceCharts, getRecentTransactions, exportFinanceReport } from '@/api/finance'
import { formatDate } from '@/utils/format'

const router = useRouter()

// 响应式数据
const stats = ref({
  total_revenue: 0,
  total_commission: 0,
  pending_withdrawal: 0,
  today_revenue: 0,
  revenue_growth: 0,
  commission_growth: 0,
  today_growth: 0,
  pending_count: 0
})

const selectedPeriod = ref('7d')
const recentTransactions = ref([])
const financeStats = ref([])
const showHelpDialog = ref(false)

// 帮助对话框相关数据
const activeGuides = ref(['view-data'])
const activeFAQ = ref([])

// 财务指标说明数据
const financeMetrics = ref([
  {
    metric: '总收入',
    color: 'primary',
    description: '平台所有收入来源的总和',
    calculation: '群组收入 + 佣金收入 + 分站收入 + 其他收入',
    significance: '反映平台整体盈利能力和业务规模'
  },
  {
    metric: '总佣金',
    color: 'success',
    description: '支付给分销商的佣金总额',
    calculation: '所有分销商佣金的累计金额',
    significance: '体现分销体系的活跃度和激励效果'
  },
  {
    metric: '待提现',
    color: 'warning',
    description: '用户申请但尚未处理的提现金额',
    calculation: '所有待审核提现申请的金额总和',
    significance: '反映资金流动性需求和风险控制情况'
  },
  {
    metric: '今日收入',
    color: 'info',
    description: '当日产生的收入总额',
    calculation: '当日所有收入来源的实时统计',
    significance: '监控日常运营状况和收入波动'
  }
])

const timePeriods = [
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' },
  { label: '1年', value: '1y' }
]

// 图表数据
const revenueChartData = ref({
  labels: [],
  datasets: [{
    label: '收入',
    data: [],
    borderColor: '#1890ff',
    backgroundColor: 'rgba(24, 144, 255, 0.1)',
    fill: true,
    tension: 0.4
  }]
})

const sourceChartData = ref({
  labels: ['群组收入', '佣金收入', '分站收入', '其他收入'],
  datasets: [{
    data: [0, 0, 0, 0],
    backgroundColor: [
      '#1890ff',
      '#52c41a',
      '#faad14',
      '#f5222d'
    ]
  }]
})

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: function(value) {
          return '¥' + (value / 1000).toFixed(1) + 'k'
        }
      }
    }
  }
})

const pieChartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
})

// 获取统计数据
const fetchStats = async () => {
  try {
    const { data } = await getFinanceStats()
    stats.value = data.overview
    financeStats.value = data.details
  } catch (error) {
    console.error('获取财务统计失败:', error)
    ElMessage.error('获取财务统计失败')
  }
}

// 获取图表数据
const fetchChartData = async () => {
  try {
    const { data } = await getFinanceCharts({ period: selectedPeriod.value })
    revenueChartData.value = data.revenue
    sourceChartData.value = data.source
  } catch (error) {
    console.error('获取图表数据失败:', error)
  }
}

// 获取最新交易
const fetchRecentTransactions = async () => {
  try {
    const { data } = await getRecentTransactions({ limit: 10 })
    recentTransactions.value = data
  } catch (error) {
    console.error('获取最新交易失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    fetchStats(),
    fetchChartData(),
    fetchRecentTransactions()
  ])
  ElMessage.success('数据刷新成功')
}

// 导航方法
const navigateToTransactions = () => {
  router.push('/finance/transactions')
}

const navigateToCommissions = () => {
  router.push('/finance/commission-logs')
}

const navigateToWithdrawals = () => {
  router.push('/finance/withdraw')
}

// 导出报表
const handleExportReport = async () => {
  try {
    await exportFinanceReport()
    ElMessage.success('报表导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 格式化金额
const formatMoney = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + 'W'
  } else if (amount >= 1000) {
    return (amount / 1000).toFixed(1) + 'K'
  }
  return amount.toFixed(2)
}

// 初始化
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 24px;
}

.chart-section {
  margin-bottom: 24px;
}

.action-section {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }
}

.chart-content {
  padding: 16px 0;
}

.quick-actions {
  padding: 16px 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: #e2e8f0;
    transform: translateY(-2px);
  }
  
  .el-icon {
    font-size: 24px;
    color: #3b82f6;
  }
  
  span {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
  }
}

.recent-transactions {
  padding: 16px 0;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
  
  &:last-child {
    border-bottom: none;
  }
}

.transaction-info {
  .transaction-title {
    font-size: 14px;
    color: #1e293b;
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .transaction-time {
    font-size: 12px;
    color: #64748b;
  }
}

.transaction-amount {
  font-size: 14px;
  font-weight: 600;
  
  &.positive {
    color: #10b981;
  }
  
  &.negative {
    color: #ef4444;
  }
}

.money-text {
  color: #f56c6c;
  font-weight: 600;
  
  &.total {
    font-size: 16px;
    font-weight: 700;
  }
}

.positive-growth {
  color: #10b981;
  font-weight: 600;
}

.negative-growth {
  color: #ef4444;
  font-weight: 600;
}

.chart-controls {
  .el-button-group {
    .el-button {
      padding: 4px 12px;
      font-size: 12px;
    }
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.header-left {
  flex: 1;
  
  h2 {
    margin: 0 0 5px 0;
    color: #303133;
    font-size: 24px;
  }
}

.page-description {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 帮助对话框样式 */
.help-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.help-content {
  .help-section {
    margin-bottom: 30px;
    
    h3 {
      color: #303133;
      margin-bottom: 15px;
      font-size: 18px;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 8px;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
  }
  
  .feature-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }
    
    .feature-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #409EFF;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      flex-shrink: 0;
      
      .el-icon {
        color: white;
        font-size: 18px;
      }
    }
    
    .feature-content {
      flex: 1;
      
      h4 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 16px;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
  
  .revenue-sources {
    .source-item {
      display: flex;
      align-items: flex-start;
      padding: 20px;
      margin-bottom: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #409EFF;
      
      .source-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
        
        .el-icon {
          color: white;
          font-size: 20px;
        }
      }
      
      .source-content {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          color: #303133;
          font-size: 16px;
        }
        
        p {
          margin: 0 0 10px 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
        
        .source-details {
          display: flex;
          gap: 15px;
          
          span {
            font-size: 12px;
            color: #909399;
            background: rgba(64, 158, 255, 0.1);
            padding: 2px 8px;
            border-radius: 4px;
          }
        }
      }
    }
  }
  
  .withdrawal-process {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    
    .process-step {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      flex: 1;
      min-width: 120px;
      
      .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #409EFF;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 10px;
      }
      
      .step-content {
        h4 {
          margin: 0 0 5px 0;
          color: #303133;
          font-size: 14px;
        }
        
        p {
          margin: 0;
          color: #606266;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }
    
    .process-arrow {
      color: #409EFF;
      font-size: 18px;
      font-weight: bold;
      margin: 0 5px;
    }
  }
  
  .guide-content {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    
    ol, ul {
      margin: 0 0 15px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.5;
      }
    }
    
    .el-alert {
      margin-top: 15px;
    }
  }
  
  .risk-alerts {
    .el-alert {
      ul {
        margin: 10px 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: inherit;
          line-height: 1.5;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .help-content {
    .feature-item {
      flex-direction: column;
      text-align: center;
      
      .feature-icon {
        margin: 0 0 15px 0;
      }
    }
    
    .revenue-sources .source-item {
      flex-direction: column;
      text-align: center;
      
      .source-icon {
        margin: 0 0 15px 0;
      }
    }
    
    .withdrawal-process {
      flex-direction: column;
      
      .process-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
      }
    }
  }
}
</style>