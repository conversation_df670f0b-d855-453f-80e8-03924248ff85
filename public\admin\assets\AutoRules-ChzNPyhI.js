import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css               */import{r as l,c as a,L as t,d as o,y as d,l as r,z as u,A as s,E as n,t as i,k as c,F as m,Y as p,B as g,D as _,e as f,a2 as y,u as b}from"./vue-vendor-DGsK9sC4.js";import{bc as v,bd as V,aH as w,aW as k,aV as h,bf as j,bj as x,as as U,au as C,Q as O,b3 as N,b6 as S,b7 as T,b8 as q,U as J,T as D,a0 as R,R as B}from"./element-plus-DcSKpKA8.js";import{P as $}from"./PageLayout-OFR6SHfu.js";/* empty css                     *//* empty css                  */import{f as L}from"./format-3eU4VJ9V.js";import"./utils-4VKArNEK.js";const Z={class:"dynamic-config"},z={class:"dialog-footer"},E=e({__name:"RuleEditDialog",props:{modelValue:Boolean,ruleData:{type:Object,default:null}},emits:["update:modelValue","success"],setup(e,{emit:f}){const y=e,b=f,N=l(null),S=l(!1),T=l(!1),q=l([]),J=a(()=>!!y.ruleData),D={name:"",type:"",target_group_ids:[],enabled:!0,config:{}},R=t({...D}),B={name:[{required:!0,message:"请输入规则名称",trigger:"blur"}],type:[{required:!0,message:"请选择规则类型",trigger:"change"}],"config.keyword":[{required:!0,message:"请输入关键词",trigger:"blur"}],"config.reply_content":[{required:!0,message:"请输入回复内容",trigger:"blur"}],"config.message":[{required:!0,message:"请输入内容",trigger:"blur"}],"config.trigger":[{required:!0,message:"请选择触发条件",trigger:"change"}],"config.kick_reason":[{required:!0,message:"请输入踢出提示",trigger:"blur"}],"config.cron":[{required:!0,message:"请输入CRON表达式",trigger:"blur"}]};o(()=>y.ruleData,e=>{e?Object.assign(R,JSON.parse(JSON.stringify(e))):Object.assign(R,JSON.parse(JSON.stringify(D)))}),o(()=>R.type,()=>{R.config={}});const $=()=>{N.value.resetFields(),Object.assign(R,JSON.parse(JSON.stringify(D)))},L=()=>{N.value.validate(e=>{e&&(T.value=!0,setTimeout(()=>{T.value=!1,O.success(J.value?"规则更新成功":"规则创建成功"),b("success",R),b("update:modelValue",!1)},500))})};return setTimeout(()=>{q.value=[{id:101,name:"创业精英交流群"},{id:102,name:"技术爱好者社区"},{id:103,name:"产品经理学习圈"}]},300),(l,a)=>{const t=w,o=V,f=h,y=k,b=j,O=v,D=U,E=C,I=x;return r(),d(E,{"model-value":e.modelValue,title:J.value?"编辑规则":"创建新规则",width:"600px","onUpdate:modelValue":a[13]||(a[13]=e=>l.$emit("update:modelValue",e)),onClose:$},{footer:u(()=>[i("span",z,[n(D,{onClick:a[12]||(a[12]=e=>l.$emit("update:modelValue",!1))},{default:u(()=>a[14]||(a[14]=[_("取消",-1)])),_:1,__:[14]}),n(D,{type:"primary",onClick:L,loading:T.value},{default:u(()=>a[15]||(a[15]=[_(" 确定 ",-1)])),_:1,__:[15]},8,["loading"])])]),default:u(()=>[s((r(),d(O,{ref_key:"formRef",ref:N,model:R,rules:B,"label-width":"120px"},{default:u(()=>[n(o,{label:"规则名称",prop:"name"},{default:u(()=>[n(t,{modelValue:R.name,"onUpdate:modelValue":a[0]||(a[0]=e=>R.name=e),placeholder:"例如：回复“入群”关键词"},null,8,["modelValue"])]),_:1}),n(o,{label:"规则类型",prop:"type"},{default:u(()=>[n(y,{modelValue:R.type,"onUpdate:modelValue":a[1]||(a[1]=e=>R.type=e),placeholder:"请选择规则类型",style:{width:"100%"}},{default:u(()=>[n(f,{label:"关键词自动回复",value:"keyword_reply"}),n(f,{label:"新成员欢迎语",value:"new_member_welcome"}),n(f,{label:"违规自动踢人",value:"auto_kick"}),n(f,{label:"定时发送消息",value:"scheduled_message"})]),_:1},8,["modelValue"])]),_:1}),n(o,{label:"目标群组",prop:"target_group_ids"},{default:u(()=>[n(y,{modelValue:R.target_group_ids,"onUpdate:modelValue":a[2]||(a[2]=e=>R.target_group_ids=e),multiple:"",filterable:"",placeholder:"选择目标群组（可留空表示所有）",style:{width:"100%"}},{default:u(()=>[n(f,{label:"所有群组",value:"all"}),(r(!0),c(m,null,p(q.value,e=>(r(),d(f,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i("div",Z,["keyword_reply"===R.type?(r(),c(m,{key:0},[n(o,{label:"关键词",prop:"config.keyword"},{default:u(()=>[n(t,{modelValue:R.config.keyword,"onUpdate:modelValue":a[3]||(a[3]=e=>R.config.keyword=e),placeholder:"多个关键词用逗号隔开"},null,8,["modelValue"])]),_:1}),n(o,{label:"回复内容",prop:"config.reply_content"},{default:u(()=>[n(t,{type:"textarea",modelValue:R.config.reply_content,"onUpdate:modelValue":a[4]||(a[4]=e=>R.config.reply_content=e),rows:3,placeholder:"请输入回复内容"},null,8,["modelValue"])]),_:1})],64)):g("",!0),"new_member_welcome"===R.type?(r(),d(o,{key:1,label:"欢迎语",prop:"config.message"},{default:u(()=>[n(t,{type:"textarea",modelValue:R.config.message,"onUpdate:modelValue":a[5]||(a[5]=e=>R.config.message=e),rows:3,placeholder:"可用 {memberName} 指代新成员昵称"},null,8,["modelValue"])]),_:1})):g("",!0),"auto_kick"===R.type?(r(),c(m,{key:2},[n(o,{label:"触发条件",prop:"config.trigger"},{default:u(()=>[n(y,{modelValue:R.config.trigger,"onUpdate:modelValue":a[6]||(a[6]=e=>R.config.trigger=e),placeholder:"请选择触发条件",style:{width:"100%"}},{default:u(()=>[n(f,{label:"消息包含链接",value:"contains_url"}),n(f,{label:"消息包含敏感词",value:"contains_sensitive_word"}),n(f,{label:"发送文件",value:"sends_file"})]),_:1},8,["modelValue"])]),_:1}),"contains_sensitive_word"===R.config.trigger?(r(),d(o,{key:0,label:"敏感词列表",prop:"config.sensitive_words"},{default:u(()=>[n(t,{modelValue:R.config.sensitive_words,"onUpdate:modelValue":a[7]||(a[7]=e=>R.config.sensitive_words=e),placeholder:"多个敏感词用逗号隔开"},null,8,["modelValue"])]),_:1})):g("",!0),n(o,{label:"踢出提示",prop:"config.kick_reason"},{default:u(()=>[n(t,{modelValue:R.config.kick_reason,"onUpdate:modelValue":a[8]||(a[8]=e=>R.config.kick_reason=e),placeholder:"例如：您因发送广告被移出群聊"},null,8,["modelValue"])]),_:1})],64)):g("",!0),"scheduled_message"===R.type?(r(),c(m,{key:3},[n(o,{label:"CRON表达式",prop:"config.cron"},{default:u(()=>[n(t,{modelValue:R.config.cron,"onUpdate:modelValue":a[9]||(a[9]=e=>R.config.cron=e),placeholder:"例如：0 20 * * * (每天晚上8点)"},null,8,["modelValue"])]),_:1}),n(o,{label:"发送内容",prop:"config.message"},{default:u(()=>[n(t,{type:"textarea",modelValue:R.config.message,"onUpdate:modelValue":a[10]||(a[10]=e=>R.config.message=e),rows:3,placeholder:"请输入要定时发送的内容"},null,8,["modelValue"])]),_:1})],64)):g("",!0)]),n(o,{label:"是否启用"},{default:u(()=>[n(b,{modelValue:R.enabled,"onUpdate:modelValue":a[11]||(a[11]=e=>R.enabled=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[I,S.value]])]),_:1},8,["model-value","title"])}}},[["__scopeId","data-v-19d6a805"]]),I={class:"app-container"},P={class:"page-header"},A={class:"toolbar-container"},F={key:0},H={key:1},K=e({__name:"AutoRules",setup(e){const t=l(!1),o=l(!1),m=l(""),p=l([]),g=l(null),v=a(()=>m.value?p.value.filter(e=>e.name.toLowerCase().includes(m.value.toLowerCase())):p.value),V=e=>({keyword_reply:{text:"关键词自动回复",type:"primary"},new_member_welcome:{text:"新成员欢迎语",type:"success"},auto_kick:{text:"违规自动踢人",type:"danger"},scheduled_message:{text:"定时发送消息",type:"warning"}}[e]||{text:"未知类型",type:"info"}),k=()=>{t.value=!0,setTimeout(()=>{p.value=[{id:1,name:'回复"入群"关键词',type:"keyword_reply",target_group_ids:"all",enabled:!0,created_at:"2024-05-20T10:00:00Z",config:{keyword:"入群",reply_content:"欢迎加入，请先阅读群规！"}},{id:2,name:"新成员进群欢迎",type:"new_member_welcome",target_group_ids:[101,102],enabled:!0,created_at:"2024-05-21T11:30:00Z",config:{message:"欢迎新朋友 @{memberName}！"}},{id:3,name:"禁止发送广告链接",type:"auto_kick",target_group_ids:"all",enabled:!1,created_at:"2024-05-22T14:00:00Z",config:{kick_reason:"发送广告链接",trigger:"contains_url"}},{id:4,name:"每日晚报",type:"scheduled_message",target_group_ids:[101],enabled:!0,created_at:"2024-05-23T18:00:00Z",config:{cron:"0 20 * * *",message:"【每日晚报】..."}}],t.value=!1},500)},h=()=>{},C=()=>{g.value=null,o.value=!0},Z=e=>{if(g.value){const l=p.value.findIndex(l=>l.id===e.id);-1!==l&&(p.value[l]=e)}else p.value.unshift({...e,id:Date.now()});k()};return f(()=>{k()}),(e,l)=>{const a=D,f=U,k=w,z=T,K=q,Q=j,W=S,Y=N,G=x;return r(),c("div",I,[n($,null,{header:u(()=>[i("div",P,[i("h1",null,[n(a,null,{default:u(()=>[n(b(R))]),_:1}),l[2]||(l[2]=_(" 自动化规则管理 ",-1))]),l[3]||(l[3]=i("p",null,"创建和管理社群的自动化规则，提高管理效率。",-1))])]),default:u(()=>[i("div",A,[n(f,{type:"primary",icon:"Plus",onClick:C},{default:u(()=>l[4]||(l[4]=[_(" 创建新规则 ",-1)])),_:1,__:[4]}),n(k,{modelValue:m.value,"onUpdate:modelValue":l[0]||(l[0]=e=>m.value=e),placeholder:"搜索规则名称",style:{width:"250px"},class:"search-input",clearable:"",onKeyup:y(h,["enter"])},{append:u(()=>[n(f,{icon:"Search",onClick:h})]),_:1},8,["modelValue"])]),n(Y,{class:"rules-list-card"},{header:u(()=>l[5]||(l[5]=[i("div",{class:"card-header"},[i("span",null,"规则列表")],-1)])),default:u(()=>[s((r(),d(W,{data:v.value,style:{width:"100%"}},{default:u(()=>[n(z,{prop:"name",label:"规则名称",width:"200"}),n(z,{label:"规则类型",width:"180"},{default:u(({row:e})=>[n(K,{type:V(e.type).type},{default:u(()=>[_(J(V(e.type).text),1)]),_:2},1032,["type"])]),_:1}),n(z,{label:"目标群组"},{default:u(({row:e})=>["all"===e.target_group_ids?(r(),c("span",F,"所有群组")):(r(),c("span",H,J(e.target_group_ids.length)+" 个群组",1))]),_:1}),n(z,{label:"状态",width:"100"},{default:u(({row:e})=>[n(Q,{modelValue:e.enabled,"onUpdate:modelValue":l=>e.enabled=l,onChange:l=>{return a=e,void O.success(`规则 "${a.name}" 已${a.enabled?"启用":"禁用"}`);var a}},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),n(z,{label:"创建时间",width:"180"},{default:u(({row:e})=>[_(J(b(L)(e.created_at)),1)]),_:1}),n(z,{label:"操作",width:"150",fixed:"right"},{default:u(({row:e})=>[n(f,{link:"",type:"primary",size:"small",onClick:l=>{return a=e,g.value=JSON.parse(JSON.stringify(a)),void(o.value=!0);var a}},{default:u(()=>l[6]||(l[6]=[_("编辑",-1)])),_:2,__:[6]},1032,["onClick"]),n(f,{link:"",type:"danger",size:"small",onClick:l=>{return a=e,void B.confirm(`确定要删除规则 "${a.name}" 吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{p.value=p.value.filter(e=>e.id!==a.id),O.success("规则删除成功")}).catch(()=>{});var a}},{default:u(()=>l[7]||(l[7]=[_("删除",-1)])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[G,t.value]])]),_:1})]),_:1}),n(E,{modelValue:o.value,"onUpdate:modelValue":l[1]||(l[1]=e=>o.value=e),"rule-data":g.value,onSuccess:Z},null,8,["modelValue","rule-data"])])}}},[["__scopeId","data-v-c2edf9cf"]]);export{K as default};
