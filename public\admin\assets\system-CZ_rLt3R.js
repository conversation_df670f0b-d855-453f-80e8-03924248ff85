import{c as t}from"./index-D2bI4m-v.js";const e={getSystemInfo:()=>t({url:"/admin/system/info",method:"get"}),getHealthStatus:()=>t({url:"/admin/system/health",method:"get"}),runHealthCheck:()=>t({url:"/admin/system/health-check",method:"post"}),runSystemDetection:()=>t({url:"/admin/system/detection",method:"post"}),getDetectionHistory:()=>t({url:"/admin/system/detection-history",method:"get"}),getSystemSettings:()=>t({url:"/admin/system/settings",method:"get"}),updateSystemSettings:e=>t({url:"/admin/system/settings",method:"put",data:e}),getSystemConfig:()=>t({url:"/admin/system/config",method:"get"}),updateSystemConfig:e=>t({url:"/admin/system/config",method:"put",data:e}),getSystemLogs:e=>t({url:"/admin/system/logs",method:"get",params:e}),clearCache:()=>t({url:"/admin/system/cache/clear",method:"post"}),createBackup:()=>t({url:"/admin/system/backup",method:"post"}),getBackupList:()=>t({url:"/admin/system/backup/list",method:"get"}),restoreBackup:e=>t({url:`/admin/system/backup/${e}/restore`,method:"post"}),testPaymentConfig:(e,s)=>t({url:"/admin/system/payment/test",method:"post",data:{provider:e,config:s}}),getPaymentConfig:()=>t({url:"/admin/system/payment/config",method:"get"}),updatePaymentConfig:e=>t({url:"/admin/system/payment/config",method:"put",data:e}),getEmailConfig:()=>t({url:"/admin/system/email/config",method:"get"}),updateEmailConfig:e=>t({url:"/admin/system/email/config",method:"put",data:e}),testEmailConfig:e=>t({url:"/admin/system/email/test",method:"post",data:e}),getSmsConfig:()=>t({url:"/admin/system/sms/config",method:"get"}),updateSmsConfig:e=>t({url:"/admin/system/sms/config",method:"put",data:e}),testSmsConfig:e=>t({url:"/admin/system/sms/test",method:"post",data:e})},s=e.getSystemSettings,m=e.updateSystemSettings,a=e.testPaymentConfig;export{s as g,e as s,a as t,m as u};
