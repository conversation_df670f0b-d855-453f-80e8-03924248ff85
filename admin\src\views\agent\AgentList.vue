<template>
  <div class="agent-list">
    <div class="page-header">
      <h2>代理商管理</h2>
      <p>管理平台代理商，包括代理商信息、状态管理和绩效分析</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatCard
          title="总代理商"
          :value="agentStats.total_agents || 0"
          icon="Avatar"
          color="#409EFF"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="活跃代理商"
          :value="agentStats.active_agents || 0"
          icon="Check"
          color="#67C23A"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="平台代理商"
          :value="agentStats.platform_agents || 0"
          icon="Star"
          color="#E6A23C"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="分站代理商"
          :value="agentStats.substation_agents || 0"
          icon="OfficeBuilding"
          color="#F56C6C"
        />
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索代理商名称或编码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="代理商等级">
          <el-select v-model="searchForm.agent_level" placeholder="选择等级" clearable>
            <el-option label="平台代理商" value="platform" />
            <el-option label="分站代理商" value="substation" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商类型">
          <el-select v-model="searchForm.agent_type" placeholder="选择类型" clearable>
            <el-option label="个人代理" value="individual" />
            <el-option label="企业代理" value="enterprise" />
            <el-option label="渠道代理" value="channel" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="正常" value="active" />
            <el-option label="未激活" value="inactive" />
            <el-option label="暂停" value="suspended" />
            <el-option label="已过期" value="expired" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增代理商
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 代理商列表 -->
    <el-card class="agents-table">
      <template #header>
        <span>代理商列表</span>
      </template>
      
      <el-table :data="agents.data" stripe v-loading="loading">
        <el-table-column prop="agent_code" label="代理商编码" />
        
        <el-table-column prop="agent_name" label="代理商名称" />
        
        <el-table-column prop="user.username" label="关联用户" />
        
        <el-table-column prop="agent_level_label" label="代理商等级">
          <template #default="{ row }">
            <el-tag :type="getAgentLevelColor(row.agent_level)">
              {{ row.agent_level === 'platform' ? '平台代理商' : '分站代理商' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="agent_type_label" label="代理商类型">
          <template #default="{ row }">
            <el-tag :type="getAgentTypeColor(row.agent_type)">
              {{ getAgentTypeText(row.agent_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="commission_display" label="佣金设置">
          <template #default="{ row }">
            <span v-if="row.no_commission" class="no-commission">不抽佣</span>
            <span v-else class="commission-rate">{{ row.commission_rate }}%</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_commission" label="总佣金">
          <template #default="{ row }">
            <span class="amount">¥{{ row.total_commission || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status_label" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="validity_display" label="有效期">
          <template #default="{ row }">
            <span v-if="row.is_permanent" class="permanent">永久有效</span>
            <span v-else-if="row.end_date" :class="getValidityClass(row.end_date)">
              {{ formatDate(row.end_date) }}
            </span>
            <span v-else class="no-expiry">未设置</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewAgent(row)">
              查看详情
            </el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="small" type="primary">
                更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'edit', row}">编辑</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'renew', row}" v-if="!row.is_permanent">续费</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'status', row}">状态管理</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', row}" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="agents.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建代理商对话框 -->
    <el-dialog v-model="createDialogVisible" title="新增代理商" width="600px">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="120px">
        <el-form-item label="关联用户" prop="user_id">
          <el-select v-model="createForm.user_id" placeholder="选择用户" filterable>
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :label="`${user.name} (${user.username})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商名称" prop="agent_name">
          <el-input v-model="createForm.agent_name" placeholder="请输入代理商名称" />
        </el-form-item>
        <el-form-item label="代理商等级" prop="agent_level">
          <el-radio-group v-model="createForm.agent_level">
            <el-radio label="platform">平台代理商</el-radio>
            <el-radio label="substation">分站代理商</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="代理商类型" prop="agent_type">
          <el-select v-model="createForm.agent_type" placeholder="选择类型">
            <el-option label="个人代理" value="individual" />
            <el-option label="企业代理" value="enterprise" />
            <el-option label="渠道代理" value="channel" />
          </el-select>
        </el-form-item>
        <el-form-item label="佣金比例" prop="commission_rate">
          <el-input-number
            v-model="createForm.commission_rate"
            :min="0"
            :max="100"
            :precision="2"
            controls-position="right"
          />
          <span style="margin-left: 10px;">%</span>
        </el-form-item>
        <el-form-item label="有效期" prop="validity_period">
          <el-select v-model="createForm.validity_period" placeholder="选择有效期">
            <el-option label="1周" value="week" />
            <el-option label="1个月" value="month" />
            <el-option label="3个月" value="quarter" />
            <el-option label="6个月" value="half_year" />
            <el-option label="1年" value="year" />
            <el-option label="永久" value="permanent" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="createForm.validity_period === 'custom'" label="自定义结束日期">
          <el-date-picker
            v-model="createForm.custom_end_date"
            type="date"
            placeholder="选择结束日期"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="createForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCreate" :loading="createLoading">
          确认创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, ArrowDown, Avatar, Check, Star, OfficeBuilding } from '@element-plus/icons-vue'
import StatCard from '@/components/StatCard.vue'
import { agentApi } from '@/api/agent'

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const createDialogVisible = ref(false)

const agentStats = ref({})
const agents = ref({ data: [], total: 0 })
const availableUsers = ref([])

const searchForm = reactive({
  keyword: '',
  agent_level: '',
  agent_type: '',
  status: ''
})

const createForm = reactive({
  user_id: '',
  agent_name: '',
  agent_level: 'platform',
  agent_type: 'individual',
  commission_rate: 10,
  validity_period: 'year',
  custom_end_date: '',
  remark: ''
})

const createRules = {
  user_id: [{ required: true, message: '请选择关联用户', trigger: 'change' }],
  agent_name: [{ required: true, message: '请输入代理商名称', trigger: 'blur' }],
  agent_level: [{ required: true, message: '请选择代理商等级', trigger: 'change' }],
  agent_type: [{ required: true, message: '请选择代理商类型', trigger: 'change' }],
  commission_rate: [{ required: true, message: '请输入佣金比例', trigger: 'blur' }],
  validity_period: [{ required: true, message: '请选择有效期', trigger: 'change' }]
}

const createFormRef = ref()

// 方法
const loadAgentStats = async () => {
  try {
    const response = await agentApi.getStats()
    agentStats.value = response.data
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  }
}

const loadAgents = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm
    }
    const response = await agentApi.getList(params)
    agents.value = response.data
  } catch (error) {
    ElMessage.error('加载代理商列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadAgents()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadAgents()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadAgents()
}

const showCreateDialog = () => {
  createDialogVisible.value = true
  // 这里应该加载可用用户列表
  loadAvailableUsers()
}

const loadAvailableUsers = async () => {
  // 模拟加载用户数据
  availableUsers.value = [
    { id: 1, name: '张三', username: 'zhangsan' },
    { id: 2, name: '李四', username: 'lisi' }
  ]
}

const confirmCreate = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true
    
    await agentApi.create(createForm)
    ElMessage.success('代理商创建成功')
    createDialogVisible.value = false
    loadAgents()
    loadAgentStats()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('创建代理商失败')
    }
  } finally {
    createLoading.value = false
  }
}

const viewAgent = (agent) => {
  ElMessage.info(`查看代理商 ${agent.agent_name} 详情`)
}

const handleCommand = ({ action, row }) => {
  switch (action) {
    case 'edit':
      ElMessage.info(`编辑代理商 ${row.agent_name}`)
      break
    case 'renew':
      ElMessage.info(`续费代理商 ${row.agent_name}`)
      break
    case 'status':
      ElMessage.info(`管理代理商 ${row.agent_name} 状态`)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

const handleDelete = async (agent) => {
  try {
    await ElMessageBox.confirm(`确定要删除代理商 ${agent.agent_name} 吗？`, '确认删除', {
      type: 'warning'
    })
    
    await agentApi.delete(agent.id)
    ElMessage.success('删除成功')
    loadAgents()
    loadAgentStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'success',
    'inactive': 'info',
    'suspended': 'warning',
    'expired': 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'active': '正常',
    'inactive': '未激活',
    'suspended': '暂停',
    'expired': '已过期'
  }
  return texts[status] || '未知'
}

const getAgentLevelColor = (level) => {
  return level === 'platform' ? 'primary' : 'success'
}

const getAgentTypeColor = (type) => {
  const colors = {
    'individual': 'primary',
    'enterprise': 'success',
    'channel': 'warning'
  }
  return colors[type] || 'info'
}

const getAgentTypeText = (type) => {
  const texts = {
    'individual': '个人代理',
    'enterprise': '企业代理',
    'channel': '渠道代理'
  }
  return texts[type] || '未知'
}

const getValidityClass = (endDate) => {
  const now = new Date()
  const end = new Date(endDate)
  const diffDays = Math.ceil((end - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'expired'
  if (diffDays <= 7) return 'expiring-soon'
  return 'valid'
}

// 生命周期
onMounted(() => {
  loadAgentStats()
  loadAgents()
})
</script>

<style scoped>
.agent-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.agents-table {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.amount {
  color: #67C23A;
  font-weight: bold;
}

.no-commission {
  color: #67C23A;
}

.commission-rate {
  color: #409EFF;
}

.permanent {
  color: #67C23A;
}

.valid {
  color: #409EFF;
}

.expiring-soon {
  color: #E6A23C;
}

.expired {
  color: #F56C6C;
}

.no-expiry {
  color: #909399;
}
</style>