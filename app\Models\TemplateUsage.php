<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 模板使用记录模型
 * 记录模板的使用情况和效果数据
 */
class TemplateUsage extends Model
{
    use HasFactory;

    protected $fillable = [
        'template_id',
        'user_id',
        'instance_type',
        'instance_id',
        'customization_data',
        'performance_metrics',
        'status',
        'created_at_timestamp',
        'completed_at',
        'rating',
        'feedback',
    ];

    protected $casts = [
        'customization_data' => 'array',
        'performance_metrics' => 'array',
        'created_at_timestamp' => 'integer',
        'completed_at' => 'datetime',
        'rating' => 'decimal:1',
    ];

    // 状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_ACTIVE = 'active';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * 模板关联
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }

    /**
     * 用户关联
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取实例对象（多态关联）
     */
    public function instance()
    {
        return $this->morphTo('instance', 'instance_type', 'instance_id');
    }

    /**
     * 作用域：成功的使用记录
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：按模板筛选
     */
    public function scopeForTemplate($query, int $templateId)
    {
        return $query->where('template_id', $templateId);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按时间范围筛选
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}