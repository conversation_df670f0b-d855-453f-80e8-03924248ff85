import{_ as e}from"./index-D2bI4m-v.js";/* empty css                      *//* empty css               *//* empty css                  */import{r as a,c as l,d as t,y as s,l as u,z as o,E as i,k as d,F as n,Y as c,t as r,D as m,B as p,u as v,L as f,e as b,a2 as g}from"./vue-vendor-DGsK9sC4.js";import{bc as _,bd as y,aH as h,aW as V,aV as w,bf as k,as as x,U as D,au as T,Q as U,b8 as C,T as j,ao as z,bL as P,Y as B,ab as q,bg as I,bh as R,bo as S,bp as $,bl as O,aU as E,bk as F,aw as L,ax as Q,ay as A,av as H,R as N}from"./element-plus-DcSKpKA8.js";/* empty css                     *//* empty css                  *//* empty css                       *//* empty css                       *//* empty css                 *//* empty css                          *//* empty css                    */import{b as Y,c as G}from"./content-BaH0vSdJ.js";import"./utils-4VKArNEK.js";const K={class:"dialog-footer"},W=e({__name:"TemplateDialog",props:{modelValue:{type:Boolean,default:!1},templateData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:p}){const v=e,f=p,b=a(),g=a(!1),C=a({name:"",category:"",tags:[],content:"",description:"",isPublic:!1}),j=a(["营销","推广","活动","通知","祝福","产品","客服","回复","热门","精选","新品","优惠"]),z={name:[{required:!0,message:"请输入模板名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],category:[{required:!0,message:"请选择模板分类",trigger:"change"}],content:[{required:!0,message:"请输入模板内容",trigger:"blur"},{min:10,max:500,message:"长度在 10 到 500 个字符",trigger:"blur"}]},P=l({get:()=>v.modelValue,set:e=>f("update:modelValue",e)}),B=l(()=>v.templateData&&v.templateData.id);t(()=>v.templateData,e=>{e&&e.id?Object.assign(C.value,{name:e.name||"",category:e.category||"",tags:e.tags||[],content:e.content||"",description:e.description||"",isPublic:e.isPublic||!1}):q()},{immediate:!0});const q=()=>{C.value={name:"",category:"",tags:[],content:"",description:"",isPublic:!1},b.value&&b.value.clearValidate()},I=()=>{P.value=!1,q()},R=async()=>{try{await b.value.validate(),g.value=!0,await new Promise(e=>setTimeout(e,1e3));const e=B.value?"更新":"创建";U.success(`模板${e}成功`),f("success",{...C.value,id:B.value?v.templateData.id:Date.now()}),I()}catch(e){console.error("表单验证失败:",e)}finally{g.value=!1}};return(e,a)=>{const l=h,t=y,p=w,v=V,f=k,U=_,q=x,S=T;return u(),s(S,{modelValue:P.value,"onUpdate:modelValue":a[6]||(a[6]=e=>P.value=e),title:B.value?"编辑模板":"创建模板",width:"600px",onClose:I},{footer:o(()=>[r("div",K,[i(q,{onClick:I},{default:o(()=>a[8]||(a[8]=[m("取消",-1)])),_:1,__:[8]}),i(q,{type:"primary",loading:g.value,onClick:R},{default:o(()=>[m(D(B.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:o(()=>[i(U,{ref_key:"formRef",ref:b,model:C.value,rules:z,"label-width":"80px"},{default:o(()=>[i(t,{label:"模板名称",prop:"name"},{default:o(()=>[i(l,{modelValue:C.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>C.value.name=e),placeholder:"请输入模板名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),i(t,{label:"模板分类",prop:"category"},{default:o(()=>[i(v,{modelValue:C.value.category,"onUpdate:modelValue":a[1]||(a[1]=e=>C.value.category=e),placeholder:"请选择模板分类",style:{width:"100%"}},{default:o(()=>[i(p,{label:"营销推广",value:"marketing"}),i(p,{label:"活动通知",value:"activity"}),i(p,{label:"节日祝福",value:"festival"}),i(p,{label:"产品介绍",value:"product"}),i(p,{label:"客服回复",value:"service"}),i(p,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),i(t,{label:"模板标签",prop:"tags"},{default:o(()=>[i(v,{modelValue:C.value.tags,"onUpdate:modelValue":a[2]||(a[2]=e=>C.value.tags=e),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或输入标签",style:{width:"100%"}},{default:o(()=>[(u(!0),d(n,null,c(j.value,e=>(u(),s(p,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),i(t,{label:"模板内容",prop:"content"},{default:o(()=>[i(l,{modelValue:C.value.content,"onUpdate:modelValue":a[3]||(a[3]=e=>C.value.content=e),type:"textarea",rows:6,placeholder:"请输入模板内容",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),i(t,{label:"模板描述",prop:"description"},{default:o(()=>[i(l,{modelValue:C.value.description,"onUpdate:modelValue":a[4]||(a[4]=e=>C.value.description=e),type:"textarea",rows:3,placeholder:"请输入模板描述（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),i(t,{label:"是否公开"},{default:o(()=>[i(f,{modelValue:C.value.isPublic,"onUpdate:modelValue":a[5]||(a[5]=e=>C.value.isPublic=e),"active-text":"公开","inactive-text":"私有"},null,8,["modelValue"]),a[7]||(a[7]=r("div",{class:"form-tip"}," 公开模板可被其他用户使用 ",-1))]),_:1,__:[7]})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-a21e9da8"]]),J={class:"template-preview"},M={class:"template-info"},X={class:"info-header"},Z={class:"info-tags"},ee={class:"info-meta"},ae={class:"meta-item"},le={class:"meta-item"},te={class:"meta-item"},se={key:0,class:"info-description"},ue={class:"template-content"},oe={class:"content-header"},ie={class:"content-actions"},de={class:"content-body"},ne={class:"content-preview"},ce={key:0,class:"template-variables"},re={class:"variables-list"},me={class:"variable-desc"},pe={class:"template-stats"},ve={class:"stats-grid"},fe={class:"stat-item"},be={class:"stat-value"},ge={class:"stat-item"},_e={class:"stat-value"},ye={class:"stat-item"},he={class:"stat-value"},Ve={class:"dialog-footer"},we=e({__name:"TemplatePreviewDialog",props:{modelValue:{type:Boolean,default:!1},templateData:{type:Object,default:()=>({})}},emits:["update:modelValue","use-template"],setup(e,{emit:t}){const f=e,b=t,g=l({get:()=>f.modelValue,set:e=>b("update:modelValue",e)}),_=a([{name:"{用户名}",description:"接收者的用户名"},{name:"{产品名}",description:"推广的产品名称"},{name:"{价格}",description:"产品价格"},{name:"{链接}",description:"产品链接"},{name:"{时间}",description:"当前时间"}]),y=async()=>{try{await navigator.clipboard.writeText(f.templateData.content||""),U.success("内容已复制到剪贴板")}catch(e){U.error("复制失败，请手动复制")}},h=()=>{g.value=!1},V=()=>{b("use-template",f.templateData),h()};return(a,l)=>{const t=C,f=j,b=x,w=T;return u(),s(w,{modelValue:g.value,"onUpdate:modelValue":l[0]||(l[0]=e=>g.value=e),title:"模板预览",width:"800px",onClose:h},{footer:o(()=>[r("div",Ve,[i(b,{onClick:h},{default:o(()=>l[8]||(l[8]=[m("关闭",-1)])),_:1,__:[8]}),i(b,{type:"primary",onClick:V},{default:o(()=>l[9]||(l[9]=[m(" 使用此模板 ",-1)])),_:1,__:[9]})])]),default:o(()=>{return[r("div",J,[r("div",M,[r("div",X,[r("h3",null,D(e.templateData.name),1),r("div",Z,[(u(!0),d(n,null,c(e.templateData.tags,e=>(u(),s(t,{key:e,size:"small",type:"info",effect:"plain"},{default:o(()=>[m(D(e),1)]),_:2},1024))),128))])]),r("div",ee,[r("span",ae,[i(f,null,{default:o(()=>[i(v(z))]),_:1}),m(" "+D(e.templateData.creator||"系统"),1)]),r("span",le,[i(f,null,{default:o(()=>[i(v(P))]),_:1}),m(" "+D((a=e.templateData.createdAt,a?new Date(a).toLocaleDateString("zh-CN"):"未知")),1)]),r("span",te,[i(f,null,{default:o(()=>[i(v(B))]),_:1}),m(" 使用 "+D(e.templateData.useCount||0)+" 次 ",1)])]),e.templateData.description?(u(),d("div",se,[r("p",null,D(e.templateData.description),1)])):p("",!0)]),r("div",ue,[r("div",oe,[l[2]||(l[2]=r("h4",null,"模板内容",-1)),r("div",ie,[i(b,{size:"small",onClick:y},{default:o(()=>[i(f,null,{default:o(()=>[i(v(q))]),_:1}),l[1]||(l[1]=m(" 复制内容 ",-1))]),_:1,__:[1]})])]),r("div",de,[r("div",ne,D(e.templateData.content),1)])]),_.value.length>0?(u(),d("div",ce,[l[3]||(l[3]=r("h4",null,"可用变量",-1)),r("div",re,[(u(!0),d(n,null,c(_.value,e=>(u(),d("div",{key:e.name,class:"variable-item"},[r("code",null,D(e.name),1),r("span",me,D(e.description),1)]))),128))])])):p("",!0),r("div",pe,[l[7]||(l[7]=r("h4",null,"使用统计",-1)),r("div",ve,[r("div",fe,[r("div",be,D(e.templateData.useCount||0),1),l[4]||(l[4]=r("div",{class:"stat-label"},"总使用次数",-1))]),r("div",ge,[r("div",_e,D(e.templateData.successRate||"0%"),1),l[5]||(l[5]=r("div",{class:"stat-label"},"成功率",-1))]),r("div",ye,[r("div",he,D(e.templateData.avgRating||"0.0"),1),l[6]||(l[6]=r("div",{class:"stat-label"},"平均评分",-1))])])])])];var a}),_:1},8,["modelValue"])}}},[["__scopeId","data-v-5b9cb297"]]),ke={class:"use-template"},xe={class:"template-info"},De={class:"template-desc"},Te={key:0,class:"variable-section"},Ue={key:0,class:"variable-tip"},Ce={class:"content-preview"},je={class:"preview-box"},ze={class:"publish-settings"},Pe={class:"dialog-footer"},Be=e({__name:"UseTemplateDialog",props:{modelValue:{type:Boolean,default:!1},templateData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:v}){const f=e,b=v,g=a(),k=a(),C=a(!1),j=a({}),z=a({title:"",channels:[],publishType:"now",publishTime:null,tags:[]}),P=a(["营销","推广","活动","通知","产品","优惠","新品","热门","精选","限时","特价","福利"]),B=a([{name:"{用户名}",key:"username",label:"用户名",placeholder:"请输入用户名",tip:"接收者的用户名"},{name:"{产品名}",key:"productName",label:"产品名",placeholder:"请输入产品名称",tip:"推广的产品名称"},{name:"{价格}",key:"price",label:"价格",placeholder:"请输入价格",tip:"产品价格，如：99.9"},{name:"{链接}",key:"link",label:"链接",placeholder:"请输入链接地址",tip:"产品或活动链接"}]),q={title:[{required:!0,message:"请输入内容标题",trigger:"blur"}],channels:[{required:!0,message:"请选择发布渠道",trigger:"change"}]},E=l({get:()=>f.modelValue,set:e=>b("update:modelValue",e)}),F=l(()=>{let e=f.templateData.content||"";return B.value.forEach(a=>{const l=j.value[a.key]||a.name;e=e.replace(new RegExp(a.name.replace(/[{}]/g,"\\$&"),"g"),l)}),e});t(()=>f.templateData,e=>{e&&e.name&&(j.value={},z.value={title:e.name,channels:[],publishType:"now",publishTime:null,tags:e.tags||[]})},{immediate:!0});const L=()=>{E.value=!1},Q=async()=>{try{await k.value.validate(),C.value=!0,await new Promise(e=>setTimeout(e,1500));const e={title:z.value.title,content:F.value,channels:z.value.channels,publishType:z.value.publishType,publishTime:z.value.publishTime,tags:z.value.tags,templateId:f.templateData.id,variables:j.value};U.success("内容创建成功"),b("success",e),L()}catch(e){console.error("表单验证失败:",e)}finally{C.value=!1}};return(a,l)=>{const t=h,v=y,f=_,b=R,U=I,A=$,H=S,N=O,Y=w,G=V,K=x,W=T;return u(),s(W,{modelValue:E.value,"onUpdate:modelValue":l[5]||(l[5]=e=>E.value=e),title:"使用模板创建内容",width:"700px",onClose:L},{footer:o(()=>[r("div",Pe,[i(K,{onClick:L},{default:o(()=>l[15]||(l[15]=[m("取消",-1)])),_:1,__:[15]}),i(K,{type:"primary",loading:C.value,onClick:Q},{default:o(()=>l[16]||(l[16]=[m(" 创建内容 ",-1)])),_:1,__:[16]},8,["loading"])])]),default:o(()=>[r("div",ke,[r("div",xe,[r("h4",null,D(e.templateData.name),1),r("p",De,D(e.templateData.description),1)]),B.value.length>0?(u(),d("div",Te,[l[6]||(l[6]=r("h4",null,"填充变量",-1)),i(f,{ref_key:"variableFormRef",ref:g,model:j.value,"label-width":"100px"},{default:o(()=>[(u(!0),d(n,null,c(B.value,e=>(u(),s(v,{key:e.name,label:e.label,prop:e.key},{default:o(()=>[i(t,{modelValue:j.value[e.key],"onUpdate:modelValue":a=>j.value[e.key]=a,placeholder:e.placeholder,type:e.type||"text"},null,8,["modelValue","onUpdate:modelValue","placeholder","type"]),e.tip?(u(),d("div",Ue,D(e.tip),1)):p("",!0)]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model"])])):p("",!0),r("div",Ce,[l[7]||(l[7]=r("h4",null,"内容预览",-1)),r("div",je,D(F.value),1)]),r("div",ze,[l[14]||(l[14]=r("h4",null,"发布设置",-1)),i(f,{ref_key:"publishFormRef",ref:k,model:z.value,rules:q,"label-width":"100px"},{default:o(()=>[i(v,{label:"内容标题",prop:"title"},{default:o(()=>[i(t,{modelValue:z.value.title,"onUpdate:modelValue":l[0]||(l[0]=e=>z.value.title=e),placeholder:"请输入内容标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),i(v,{label:"发布渠道",prop:"channels"},{default:o(()=>[i(U,{modelValue:z.value.channels,"onUpdate:modelValue":l[1]||(l[1]=e=>z.value.channels=e)},{default:o(()=>[i(b,{label:"wechat"},{default:o(()=>l[8]||(l[8]=[m("微信群",-1)])),_:1,__:[8]}),i(b,{label:"qq"},{default:o(()=>l[9]||(l[9]=[m("QQ群",-1)])),_:1,__:[9]}),i(b,{label:"telegram"},{default:o(()=>l[10]||(l[10]=[m("Telegram",-1)])),_:1,__:[10]}),i(b,{label:"website"},{default:o(()=>l[11]||(l[11]=[m("官网",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),i(v,{label:"发布时间",prop:"publishTime"},{default:o(()=>[i(H,{modelValue:z.value.publishType,"onUpdate:modelValue":l[2]||(l[2]=e=>z.value.publishType=e)},{default:o(()=>[i(A,{label:"now"},{default:o(()=>l[12]||(l[12]=[m("立即发布",-1)])),_:1,__:[12]}),i(A,{label:"scheduled"},{default:o(()=>l[13]||(l[13]=[m("定时发布",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"]),"scheduled"===z.value.publishType?(u(),s(N,{key:0,modelValue:z.value.publishTime,"onUpdate:modelValue":l[3]||(l[3]=e=>z.value.publishTime=e),type:"datetime",placeholder:"选择发布时间",style:{"margin-left":"12px"}},null,8,["modelValue"])):p("",!0)]),_:1}),i(v,{label:"内容标签",prop:"tags"},{default:o(()=>[i(G,{modelValue:z.value.tags,"onUpdate:modelValue":l[4]||(l[4]=e=>z.value.tags=e),multiple:"",filterable:"","allow-create":"",placeholder:"请选择或输入标签",style:{width:"100%"}},{default:o(()=>[(u(!0),d(n,null,c(P.value,e=>(u(),s(Y,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-ef78808f"]]),qe={class:"app-container"},Ie={class:"filter-container"},Re={class:"template-grid"},Se=["onClick"],$e={class:"template-header"},Oe={class:"template-title"},Ee={class:"template-actions"},Fe={class:"template-preview"},Le={class:"template-footer"},Qe={class:"template-meta"},Ae={class:"usage-count"},He={class:"template-status"},Ne={class:"create-content"},Ye={class:"pagination-container"},Ge=e({__name:"ContentTemplates",setup(e){const l=a([]),t=a(0),s=a(!0),p=a(!1),_=a(!1),y=a(!1),k=a({}),T=f({page:1,limit:12,keyword:"",category:"",status:""}),z=async()=>{s.value=!0;try{const{data:e}=await Y(T);l.value=e.list,t.value=e.total}catch(e){console.error("获取模板列表失败:",e),U.error("获取模板列表失败")}finally{s.value=!1}},P=()=>{T.page=1,z()},B=()=>{k.value={},p.value=!0},q=e=>{const[a,t]=e.split("-"),s=parseInt(t),u=l.value.find(e=>e.id===s);switch(a){case"edit":(e=>{k.value={...e},p.value=!0})(u);break;case"copy":(async()=>{try{await N.confirm("确定要复制这个模板吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),U.success("模板复制成功"),z()}catch(e){"cancel"!==e&&U.error("复制失败")}})();break;case"use":(e=>{k.value={...e},y.value=!0})(u);break;case"delete":(async e=>{try{await N.confirm("确定要删除这个模板吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await G(e),U.success("删除成功"),z()}catch(a){"cancel"!==a&&U.error("删除失败")}})(s)}},I=e=>{T.limit=e,z()},R=e=>{T.page=e,z()},S=()=>{z()},$=()=>{U.success("模板使用成功，内容已创建")},O=e=>({announcement:"群组公告",welcome:"欢迎语",rules:"群规则",promotion:"推广文案"}[e]||"其他"),K=e=>({active:"启用",inactive:"禁用"}[e]||"未知"),J=e=>{if(!e)return"";const a=e.replace(/<[^>]*>/g,"");return a.length>100?a.substring(0,100)+"...":a};return b(()=>{z()}),(e,a)=>{const s=h,f=w,b=V,U=x,z=j,N=A,Y=Q,G=H,M=C,X=F;return u(),d("div",qe,[r("div",Ie,[i(s,{modelValue:T.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>T.keyword=e),placeholder:"搜索模板名称、标签",style:{width:"200px"},class:"filter-item",onKeyup:g(P,["enter"]),clearable:""},null,8,["modelValue"]),i(b,{modelValue:T.category,"onUpdate:modelValue":a[1]||(a[1]=e=>T.category=e),placeholder:"模板分类",clearable:"",style:{width:"120px"},class:"filter-item"},{default:o(()=>[i(f,{label:"全部",value:""}),i(f,{label:"群组公告",value:"announcement"}),i(f,{label:"欢迎语",value:"welcome"}),i(f,{label:"群规则",value:"rules"}),i(f,{label:"推广文案",value:"promotion"})]),_:1},8,["modelValue"]),i(b,{modelValue:T.status,"onUpdate:modelValue":a[2]||(a[2]=e=>T.status=e),placeholder:"状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:o(()=>[i(f,{label:"全部",value:""}),i(f,{label:"启用",value:"active"}),i(f,{label:"禁用",value:"inactive"})]),_:1},8,["modelValue"]),i(U,{class:"filter-item",type:"primary",icon:"Search",onClick:P},{default:o(()=>a[8]||(a[8]=[m(" 搜索 ",-1)])),_:1,__:[8]}),i(U,{class:"filter-item",type:"success",icon:"Plus",onClick:B},{default:o(()=>a[9]||(a[9]=[m(" 创建模板 ",-1)])),_:1,__:[9]})]),r("div",Re,[(u(!0),d(n,null,c(l.value,e=>{return u(),d("div",{key:e.id,class:"template-card",onClick:a=>(e=>{k.value={...e},_.value=!0})(e)},[r("div",$e,[r("div",Oe,D(e.name),1),r("div",Ee,[i(G,{onCommand:q},{dropdown:o(()=>[i(Y,null,{default:o(()=>[i(N,{command:`edit-${e.id}`},{default:o(()=>a[10]||(a[10]=[m("编辑",-1)])),_:2,__:[10]},1032,["command"]),i(N,{command:`copy-${e.id}`},{default:o(()=>a[11]||(a[11]=[m("复制",-1)])),_:2,__:[11]},1032,["command"]),i(N,{command:`use-${e.id}`},{default:o(()=>a[12]||(a[12]=[m("使用模板",-1)])),_:2,__:[12]},1032,["command"]),i(N,{command:`delete-${e.id}`,divided:""},{default:o(()=>a[13]||(a[13]=[m("删除",-1)])),_:2,__:[13]},1032,["command"])]),_:2},1024)]),default:o(()=>[i(U,{type:"text",size:"small"},{default:o(()=>[i(z,null,{default:o(()=>[i(v(L))]),_:1})]),_:1})]),_:2},1024)])]),r("div",Fe,D(J(e.content)),1),r("div",Le,[r("div",Qe,[i(M,{type:(t=e.category,{announcement:"primary",welcome:"success",rules:"warning",promotion:"danger"}[t]||"info"),size:"small"},{default:o(()=>[m(D(O(e.category)),1)]),_:2},1032,["type"]),r("span",Ae,"使用 "+D(e.usage_count||0)+" 次",1)]),r("div",He,[i(M,{type:(l=e.status,{active:"success",inactive:"danger"}[l]||"info"),size:"small"},{default:o(()=>[m(D(K(e.status)),1)]),_:2},1032,["type"])])])],8,Se);var l,t}),128)),r("div",{class:"template-card create-card",onClick:B},[r("div",Ne,[i(z,{class:"create-icon"},{default:o(()=>[i(v(E))]),_:1}),a[14]||(a[14]=r("div",{class:"create-text"},"创建新模板",-1))])])]),r("div",Ye,[i(X,{"current-page":T.page,"onUpdate:currentPage":a[3]||(a[3]=e=>T.page=e),"page-size":T.limit,"onUpdate:pageSize":a[4]||(a[4]=e=>T.limit=e),"page-sizes":[12,24,36,48],total:t.value,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:I,onCurrentChange:R},null,8,["current-page","page-size","total"])]),i(W,{modelValue:p.value,"onUpdate:modelValue":a[5]||(a[5]=e=>p.value=e),"template-data":k.value,onSuccess:S},null,8,["modelValue","template-data"]),i(we,{modelValue:_.value,"onUpdate:modelValue":a[6]||(a[6]=e=>_.value=e),"template-data":k.value},null,8,["modelValue","template-data"]),i(Be,{modelValue:y.value,"onUpdate:modelValue":a[7]||(a[7]=e=>y.value=e),"template-data":k.value,onSuccess:$},null,8,["modelValue","template-data"])])}}},[["__scopeId","data-v-fef64d84"]]);export{Ge as default};
