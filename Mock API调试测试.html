<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock API调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Mock API调试测试</h1>
        
        <h3>测试按钮</h3>
        <button class="test-button" onclick="testWechatGroups()">测试群组列表API</button>
        <button class="test-button" onclick="testMarketingTemplates()">测试营销模板API</button>
        <button class="test-button" onclick="testMockConfig()">检查Mock配置</button>
        <button class="test-button" onclick="clearResults()">清除结果</button>
        
        <h3>测试结果</h3>
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testWechatGroups() {
            addResult('开始测试群组列表API...');
            
            try {
                const response = await fetch('/api/v1/wechat-groups?page=1&size=20');
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ 群组列表API测试成功: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`❌ 群组列表API测试失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 群组列表API测试异常: ${error.message}`, 'error');
            }
        }

        async function testMarketingTemplates() {
            addResult('开始测试营销模板API...');
            
            try {
                const response = await fetch('/api/v1/marketing-templates');
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ 营销模板API测试成功: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`❌ 营销模板API测试失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 营销模板API测试异常: ${error.message}`, 'error');
            }
        }

        function testMockConfig() {
            addResult('检查Mock配置...');
            
            // 检查是否有Mock API相关的全局变量或配置
            if (window.mockApiResponses) {
                addResult(`✅ 找到Mock配置: ${Object.keys(window.mockApiResponses).length} 个路径`, 'success');
                addResult(`Mock路径列表: ${Object.keys(window.mockApiResponses).join(', ')}`);
            } else {
                addResult('⚠️ 未找到Mock配置', 'error');
            }
            
            // 检查axios拦截器
            if (window.axios && window.axios.interceptors) {
                addResult(`✅ Axios拦截器已配置`, 'success');
            } else {
                addResult('⚠️ Axios拦截器未找到', 'error');
            }
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addResult('Mock API调试测试页面已加载');
            addResult('请点击测试按钮开始测试');
            
            // 检查当前环境
            addResult(`当前URL: ${window.location.href}`);
            addResult(`开发模式: ${window.location.hostname === 'localhost'}`);
        });
    </script>
</body>
</html>
