<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨鑫流量变现 - 后台管理系统预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f0f2f5;
            color: #333;
            line-height: 1.6;
            height: 100vh;
            display: flex;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 220px;
            background: #001529;
            color: #fff;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h2 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .sidebar-header p {
            font-size: 12px;
            opacity: 0.7;
        }
        
        .sidebar-menu {
            padding: 15px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
            color: rgba(255, 255, 255, 0.65);
        }
        
        .menu-item:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .menu-item.active {
            color: #fff;
            background: #1890ff;
        }
        
        .menu-item svg {
            margin-right: 10px;
            width: 16px;
            height: 16px;
        }
        
        .menu-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 10px 0;
        }
        
        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            margin-left: 220px;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* 顶部导航栏样式 */
        .top-nav {
            background: #fff;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb span {
            margin: 0 5px;
            color: #999;
        }
        
        .user-actions {
            display: flex;
            align-items: center;
        }
        
        .user-actions .action-item {
            margin-left: 20px;
            cursor: pointer;
            color: #666;
            transition: all 0.3s;
        }
        
        .user-actions .action-item:hover {
            color: #1890ff;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 20px;
        }
        
        /* 卡片样式 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: all 0.3s;
        }
        
        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        .stat-card {
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #fff;
        }
        
        .stat-icon.blue {
            background: linear-gradient(135deg, #1890ff, #096dd9);
        }
        
        .stat-icon.green {
            background: linear-gradient(135deg, #52c41a, #389e0d);
        }
        
        .stat-icon.orange {
            background: linear-gradient(135deg, #fa8c16, #d46b08);
        }
        
        .stat-icon.purple {
            background: linear-gradient(135deg, #722ed1, #531dab);
        }
        
        .stat-info h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stat-info .value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .stat-info .trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            margin-top: 5px;
        }
        
        .trend.up {
            color: #52c41a;
        }
        
        .trend.down {
            color: #f5222d;
        }
        
        .trend svg {
            width: 12px;
            height: 12px;
            margin-right: 4px;
        }
        
        /* 图表卡片 */
        .chart-card {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .chart-actions {
            display: flex;
        }
        
        .chart-action {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            margin-left: 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .chart-action:hover {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .chart-action.active {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
            border-radius: 4px;
        }
        
        /* 表格样式 */
        .table-card {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .table-actions {
            display: flex;
        }
        
        .table-action {
            padding: 6px 12px;
            background: #1890ff;
            color: #fff;
            border-radius: 4px;
            margin-left: 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }
        
        .table-action:hover {
            background: #096dd9;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-table th {
            background: #fafafa;
            font-weight: 500;
            color: #666;
        }
        
        .data-table tr:hover {
            background: #fafafa;
        }
        
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .status-tag.success {
            background: rgba(82, 196, 26, 0.1);
            color: #52c41a;
        }
        
        .status-tag.warning {
            background: rgba(250, 140, 22, 0.1);
            color: #fa8c16;
        }
        
        .status-tag.error {
            background: rgba(245, 34, 45, 0.1);
            color: #f5222d;
        }
        
        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            font-size: 12px;
            color: #666;
        }
        
        .pagination {
            display: flex;
            align-items: center;
        }
        
        .page-item {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #d9d9d9;
            margin: 0 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-item:hover {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .page-item.active {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 80px;
                transform: translateX(-80px);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .sidebar-header h2,
            .sidebar-header p,
            .menu-item span {
                display: none;
            }
            
            .menu-item {
                justify-content: center;
                padding: 15px 0;
            }
            
            .menu-item svg {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>晨鑫流量变现</h2>
            <p>管理后台</p>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-item active">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
                <span>仪表盘</span>
            </div>
            
            <div class="menu-item">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                <span>用户管理</span>
            </div>
            
            <div class="menu-item">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                </svg>
                <span>分销员管理</span>
            </div>
            
            <div class="menu-divider"></div>
            
            <div class="menu-item">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                </svg>
                <span>内容管理</span>
            </div>
            
            <div class="menu-item">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                </svg>
                <span>订单管理</span>
            </div>
            
            <div class="menu-item">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                </svg>
                <span>财务管理</span>
            </div>
            
            <div class="menu-divider"></div>
            
            <div class="menu-item">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"/>
                </svg>
                <span>系统设置</span>
            </div>
            
            <div class="menu-item">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                </svg>
                <span>帮助中心</span>
            </div>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="breadcrumb">
                <a href="#">首页</a>
                <span>/</span>
                <a href="#">仪表盘</a>
            </div>
            
            <div class="user-actions">
                <div class="action-item">
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                        <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                    </svg>
                </div>
                <div class="action-item">
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-14h2v6h-2zm0 8h2v2h-2z"/>
                    </svg>
                </div>
                <div class="user-avatar">
                    管
                </div>
            </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="card-grid">
            <div class="card stat-card">
                <div class="stat-icon blue">
                    <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                    </svg>
                </div>
                <div class="stat-info">
                    <h3>总用户数</h3>
                    <div class="value">12,846</div>
                    <div class="trend up">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 14l5-5 5 5z"/>
                        </svg>
                        12.5%
                    </div>
                </div>
            </div>
            
            <div class="card stat-card">
                <div class="stat-icon green">
                    <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
                <div class="stat-info">
                    <h3>活跃用户</h3>
                    <div class="value">5,273</div>
                    <div class="trend up">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 14l5-5 5 5z"/>
                        </svg>
                        8.2%
                    </div>
                </div>
            </div>
            
            <div class="card stat-card">
                <div class="stat-icon orange">
                    <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                        <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                    </svg>
                </div>
                <div class="stat-info">
                    <h3>总收入</h3>
                    <div class="value">¥128,546.00</div>
                    <div class="trend up">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 14l5-5 5 5z"/>
                        </svg>
                        15.3%
                    </div>
                </div>
            </div>
            
            <div class="card stat-card">
                <div class="stat-icon purple">
                    <svg viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
                        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                    </svg>
                </div>
                <div class="stat-info">
                    <h3>分销员数量</h3>
                    <div class="value">1,254</div>
                    <div class="trend up">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 14l5-5 5 5z"/>
                        </svg>
                        5.7%
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 图表卡片 -->
        <div class="chart-card">
            <div class="chart-header">
                <div class="chart-title">用户增长趋势</div>
                <div class="chart-actions">
                    <div class="chart-action">今日</div>
                    <div class="chart-action active">本周</div>
                    <div class="chart-action">本月</div>
                    <div class="chart-action">全年</div>
                </div>
            </div>
            <div class="chart-container">
                <div style="text-align: center; color: #999;">
                    <svg viewBox="0 0 24 24" width="48" height="48" fill="currentColor" style="opacity: 0.5;">
                        <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"/>
                    </svg>
                    <p style="margin-top: 10px;">用户增长趋势图表</p>
                    <p style="font-size: 12px; margin-top: 5px;">预览模式下显示模拟数据</p>
                </div>
            </div>
        </div>
        
        <!-- 表格卡片 -->
        <div class="table-card">
            <div class="table-header">
                <div class="table-title">最近订单</div>
                <div class="table-actions">
                    <button class="table-action">导出数据</button>
                    <button class="table-action">刷新</button>
                </div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>用户</th>
                        <th>金额</th>
                        <th>状态</th>
                        <th>时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>ORD-20240801-12345</td>
                        <td>张三</td>
                        <td>¥1,299.00</td>
                        <td><span class="status-tag success">已完成</span></td>
                        <td>2024-08-01 14:23:45</td>
                        <td><a href="#" style="color: #1890ff;">查看</a></td>
                    </tr>
                    <tr>
                        <td>ORD-20240801-12344</td>
                        <td>李四</td>
                        <td>¥899.00</td>
                        <td><span class="status-tag warning">处理中</span></td>
                        <td>2024-08-01 13:45:22</td>
                        <td><a href="#" style="color: #1890ff;">查看</a></td>
                    </tr>
                    <tr>
                        <td>ORD-20240801-12343</td>
                        <td>王五</td>
                        <td>¥2,499.00</td>
                        <td><span class="status-tag success">已完成</span></td>
                        <td>2024-08-01 11:32:18</td>
                        <td><a href="#" style="color: #1890ff;">查看</a></td>
                    </tr>
                    <tr>
                        <td>ORD-20240801-12342</td>
                        <td>赵六</td>
                        <td>¥599.00</td>
                        <td><span class="status-tag success">已完成</span></td>
                        <td>2024-08-01 10:15:36</td>
                        <td><a href="#" style="color: #1890ff;">查看</a></td>
                    </tr>
                    <tr>
                        <td>ORD-20240801-12341</td>
                        <td>钱七</td>
                        <td>¥1,799.00</td>
                        <td><span class="status-tag error">已取消</span></td>
                        <td>2024-08-01 09:05:12</td>
                        <td><a href="#" style="color: #1890ff;">查看</a></td>
                    </tr>
                </tbody>
            </table>
            
            <div class="table-footer">
                <div>显示 1-5 条，共 42 条</div>
                <div class="pagination">
                    <div class="page-item">«</div>
                    <div class="page-item active">1</div>
                    <div class="page-item">2</div>
                    <div class="page-item">3</div>
                    <div class="page-item">4</div>
                    <div class="page-item">»</div>
                </div>
            </div>
        </div>
        
        <!-- 两列布局 -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px;">
            <!-- 左侧卡片 -->
            <div class="card">
                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">分销员业绩排行</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>分销员</th>
                            <th>本月业绩</th>
                            <th>团队人数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>王强</td>
                            <td>¥25,689.00</td>
                            <td>128</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>李明</td>
                            <td>¥18,452.00</td>
                            <td>95</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>张华</td>
                            <td>¥15,784.00</td>
                            <td>87</td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>刘芳</td>
                            <td>¥12,356.00</td>
                            <td>76</td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>陈晓</td>
                            <td>¥10,892.00</td>
                            <td>64</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 右侧卡片 -->
            <div class="card">
                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">系统公告</h3>
                <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;">
                    <div style="font-weight: 500; margin-bottom: 5px;">平台功能更新通知</div>
                    <div style="font-size: 12px; color: #999; margin-bottom: 5px;">2024-08-01 10:30</div>
                    <div style="font-size: 14px; color: #666;">晨鑫流量变现平台已完成最新版本更新，新增多级分销功能和数据分析工具...</div>
                </div>
                <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;">
                    <div style="font-weight: 500; margin-bottom: 5px;">系统维护通知</div>
                    <div style="font-size: 12px; color: #999; margin-bottom: 5px;">2024-07-28 15:45</div>
                    <div style="font-size: 14px; color: #666;">系统将于本周日凌晨2:00-4:00进行例行维护，期间服务可能短暂不可用...</div>
                </div>
                <div style="margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;">
                    <div style="font-weight: 500; margin-bottom: 5px;">新增支付渠道通知</div>
                    <div style="font-size: 12px; color: #999; margin-bottom: 5px;">2024-07-25 09:15</div>
                    <div style="font-size: 14px; color: #666;">平台已新增微信支付和支付宝企业支付渠道，提供更便捷的支付体验...</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
