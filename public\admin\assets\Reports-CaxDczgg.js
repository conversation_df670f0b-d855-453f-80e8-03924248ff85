import{_ as e}from"./index-D2bI4m-v.js";/* empty css                        *//* empty css                    *//* empty css               *//* empty css                 *//* empty css                *//* empty css               *//* empty css                       *//* empty css                  */import{b3 as a,b4 as l,b2 as t,aW as s,aV as r,bl as o,aY as u,as as d,U as i,bm as c,b6 as n,b7 as v,T as p,a4 as m,aM as _,Q as f}from"./element-plus-DcSKpKA8.js";import{P as h}from"./PageLayout-OFR6SHfu.js";import{r as b,L as y,e as w,y as g,l as k,z as j,t as G,E as U,k as V,B as C,D as R,u as O}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const P={class:"reports-container"},x={key:0,class:"report-section"},I={class:"metric-content"},T={class:"metric-value"},z={class:"metric-change positive"},E={class:"metric-content"},F={class:"metric-value"},L={class:"metric-change positive"},N={class:"metric-content"},q={class:"metric-value"},B={class:"metric-change positive"},D={class:"metric-content"},M={class:"metric-value"},Q={class:"metric-change positive"},W={class:"chart-placeholder"},Y={key:1,class:"report-section"},A={key:2,class:"report-section"},H={key:3,class:"report-section"},J=e({__name:"Reports",setup(e){const J=b(!1),K=b("week"),S=b([]),X=b("overview"),Z=y({totalRevenue:125680.5,revenueGrowth:15.2,totalOrders:1256,ordersGrowth:8.7,totalUsers:8945,usersGrowth:12.3,totalGroups:156,groupsGrowth:6.8}),$=b([{date:"2024-08-01",newUsers:45,activeUsers:1250,retentionRate:85.2},{date:"2024-08-02",newUsers:52,activeUsers:1298,retentionRate:86.1}]),ee=b([{date:"2024-08-01",revenue:8520.5,orders:85,avgOrderValue:100.24},{date:"2024-08-02",revenue:9240.8,orders:92,avgOrderValue:100.44}]),ae=b([{name:"VIP群组",members:98,messages:1250,revenue:9800},{name:"普通群组",members:156,messages:2340,revenue:1560}]),le=e=>new Intl.NumberFormat("zh-CN").format(e),te=e=>{"custom"!==e&&(S.value=[]),re()},se=()=>{re()},re=async()=>{J.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),f.success("数据已刷新")}catch(e){f.error("刷新失败")}finally{J.value=!1}},oe=()=>{f.success("报表导出功能开发中")};return w(()=>{re()}),(e,f)=>{const b=p,y=d,w=r,ue=s,de=t,ie=o,ce=u,ne=l,ve=a,pe=c,me=v,_e=n;return k(),g(h,{title:"数据报表",subtitle:"查看详细的数据分析报表",icon:"TrendCharts",loading:J.value},{actions:j(()=>[U(y,{onClick:oe},{default:j(()=>[U(b,null,{default:j(()=>[U(O(m))]),_:1}),f[6]||(f[6]=R(" 导出报表 ",-1))]),_:1,__:[6]}),U(y,{type:"primary",onClick:re},{default:j(()=>[U(b,null,{default:j(()=>[U(O(_))]),_:1}),f[7]||(f[7]=R(" 刷新数据 ",-1))]),_:1,__:[7]})]),default:j(()=>[G("div",P,[U(ve,{class:"filter-card",shadow:"never"},{default:j(()=>[U(ne,{gutter:16,align:"middle"},{default:j(()=>[U(de,{span:6},{default:j(()=>[U(ue,{modelValue:K.value,"onUpdate:modelValue":f[0]||(f[0]=e=>K.value=e),placeholder:"选择时间范围",onChange:te},{default:j(()=>[U(w,{label:"今日",value:"today"}),U(w,{label:"昨日",value:"yesterday"}),U(w,{label:"最近7天",value:"week"}),U(w,{label:"最近30天",value:"month"}),U(w,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),"custom"===K.value?(k(),g(de,{key:0,span:10},{default:j(()=>[U(ie,{modelValue:S.value,"onUpdate:modelValue":f[1]||(f[1]=e=>S.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:se},null,8,["modelValue"])]),_:1})):C("",!0),U(de,{span:8},{default:j(()=>[U(ce,null,{default:j(()=>[U(y,{type:"overview"===X.value?"primary":"",onClick:f[2]||(f[2]=e=>X.value="overview")},{default:j(()=>f[8]||(f[8]=[R(" 概览 ",-1)])),_:1,__:[8]},8,["type"]),U(y,{type:"user"===X.value?"primary":"",onClick:f[3]||(f[3]=e=>X.value="user")},{default:j(()=>f[9]||(f[9]=[R(" 用户 ",-1)])),_:1,__:[9]},8,["type"]),U(y,{type:"finance"===X.value?"primary":"",onClick:f[4]||(f[4]=e=>X.value="finance")},{default:j(()=>f[10]||(f[10]=[R(" 财务 ",-1)])),_:1,__:[10]},8,["type"]),U(y,{type:"group"===X.value?"primary":"",onClick:f[5]||(f[5]=e=>X.value="group")},{default:j(()=>f[11]||(f[11]=[R(" 群组 ",-1)])),_:1,__:[11]},8,["type"])]),_:1})]),_:1})]),_:1})]),_:1}),"overview"===X.value?(k(),V("div",x,[U(ne,{gutter:24},{default:j(()=>[U(de,{span:6},{default:j(()=>[U(ve,{class:"metric-card"},{default:j(()=>[G("div",I,[G("div",T,i(le(Z.totalRevenue)),1),f[12]||(f[12]=G("div",{class:"metric-label"},"总收入（元）",-1)),G("div",z,"+"+i(Z.revenueGrowth)+"%",1)])]),_:1})]),_:1}),U(de,{span:6},{default:j(()=>[U(ve,{class:"metric-card"},{default:j(()=>[G("div",E,[G("div",F,i(le(Z.totalOrders)),1),f[13]||(f[13]=G("div",{class:"metric-label"},"总订单数",-1)),G("div",L,"+"+i(Z.ordersGrowth)+"%",1)])]),_:1})]),_:1}),U(de,{span:6},{default:j(()=>[U(ve,{class:"metric-card"},{default:j(()=>[G("div",N,[G("div",q,i(le(Z.totalUsers)),1),f[14]||(f[14]=G("div",{class:"metric-label"},"总用户数",-1)),G("div",B,"+"+i(Z.usersGrowth)+"%",1)])]),_:1})]),_:1}),U(de,{span:6},{default:j(()=>[U(ve,{class:"metric-card"},{default:j(()=>[G("div",D,[G("div",M,i(le(Z.totalGroups)),1),f[15]||(f[15]=G("div",{class:"metric-label"},"总群组数",-1)),G("div",Q,"+"+i(Z.groupsGrowth)+"%",1)])]),_:1})]),_:1})]),_:1}),U(ve,{class:"chart-card",shadow:"never"},{header:j(()=>f[16]||(f[16]=[G("h3",null,"收入趋势",-1)])),default:j(()=>[G("div",W,[U(pe,{description:"图表组件待集成"})])]),_:1})])):C("",!0),"user"===X.value?(k(),V("div",Y,[U(ve,{shadow:"never"},{header:j(()=>f[17]||(f[17]=[G("h3",null,"用户分析报表",-1)])),default:j(()=>[U(_e,{data:$.value,style:{width:"100%"}},{default:j(()=>[U(me,{prop:"date",label:"日期"}),U(me,{prop:"newUsers",label:"新增用户"}),U(me,{prop:"activeUsers",label:"活跃用户"}),U(me,{prop:"retentionRate",label:"留存率"},{default:j(({row:e})=>[R(i(e.retentionRate)+"% ",1)]),_:1})]),_:1},8,["data"])]),_:1})])):C("",!0),"finance"===X.value?(k(),V("div",A,[U(ve,{shadow:"never"},{header:j(()=>f[18]||(f[18]=[G("h3",null,"财务分析报表",-1)])),default:j(()=>[U(_e,{data:ee.value,style:{width:"100%"}},{default:j(()=>[U(me,{prop:"date",label:"日期"}),U(me,{prop:"revenue",label:"收入（元）"}),U(me,{prop:"orders",label:"订单数"}),U(me,{prop:"avgOrderValue",label:"客单价（元）"})]),_:1},8,["data"])]),_:1})])):C("",!0),"group"===X.value?(k(),V("div",H,[U(ve,{shadow:"never"},{header:j(()=>f[19]||(f[19]=[G("h3",null,"群组分析报表",-1)])),default:j(()=>[U(_e,{data:ae.value,style:{width:"100%"}},{default:j(()=>[U(me,{prop:"name",label:"群组名称"}),U(me,{prop:"members",label:"成员数"}),U(me,{prop:"messages",label:"消息数"}),U(me,{prop:"revenue",label:"收入（元）"})]),_:1},8,["data"])]),_:1})])):C("",!0)])]),_:1},8,["loading"])}}},[["__scopeId","data-v-9bf53583"]]);export{J as default};
