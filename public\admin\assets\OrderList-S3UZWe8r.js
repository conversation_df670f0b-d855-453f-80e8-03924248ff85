import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                    *//* empty css                         *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                             *//* empty css                       *//* empty css               */import{T as a,a4 as t,as as l,aM as s,b2 as o,a7 as d,U as n,a_ as u,ai as i,bH as r,bX as c,a6 as _,b4 as p,b6 as m,b7 as v,bM as f,bN as b,bY as g,b8 as w,av as y,aO as h,ax as k,ay as D,bj as C,bk as z,aH as V,b1 as j,aW as x,aV as F,bl as Y,b3 as U,b9 as q,b5 as E,bb as M,ba as A,au as O,ad as T,Q as $}from"./element-plus-DcSKpKA8.js";import{r as I,L,e as N,k as S,l as P,t as R,E as H,z as Q,D as B,u as G,A as W,y as X,B as J,F as K,Y as Z}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const ee={class:"order-list"},ae={class:"page-header"},te={class:"header-actions"},le={class:"stat-card"},se={class:"stat-icon",style:{"background-color":"#409EFF20",color:"#409EFF"}},oe={class:"stat-content"},de={class:"stat-value"},ne={class:"stat-trend positive"},ue={class:"stat-card"},ie={class:"stat-icon",style:{"background-color":"#67C23A20",color:"#67C23A"}},re={class:"stat-content"},ce={class:"stat-value"},_e={class:"stat-trend positive"},pe={class:"stat-card"},me={class:"stat-icon",style:{"background-color":"#E6A23C20",color:"#E6A23C"}},ve={class:"stat-content"},fe={class:"stat-value"},be={class:"stat-trend neutral"},ge={class:"stat-card"},we={class:"stat-icon",style:{"background-color":"#F56C6C20",color:"#F56C6C"}},ye={class:"stat-content"},he={class:"stat-value"},ke={class:"stat-trend positive"},De={class:"card-header"},Ce={class:"header-actions"},ze={class:"order-detail"},Ve={class:"customer-info"},je={class:"customer-name"},xe={class:"customer-phone"},Fe={class:"product-info"},Ye={class:"product-name"},Ue={class:"product-spec"},qe={class:"amount-info"},Ee={class:"total-amount"},Me={key:0,class:"unit-price"},Ae={key:0},Oe={key:1,class:"no-payment"},Te={key:0},$e={key:1,class:"unpaid-text"},Ie={class:"pagination-wrapper"},Le={key:0,class:"order-detail-dialog"},Ne={class:"log-content"},Se={class:"log-action"},Pe={class:"log-description"},Re={class:"log-operator"},He={class:"dev-notice"},Qe=e({__name:"OrderList",setup(e){const Qe=I(!1),Be=I(!1),Ge=I(!1),We=I(""),Xe=I(""),Je=I([]),Ke=I(null),Ze=I("basic"),ea=L({total_orders:156,total_amount:458650.5,pending_orders:12,success_rate:94.2}),aa=I([]),ta=L({current_page:1,per_page:20,total:0}),la=[{id:1,order_no:"ORD202401001",customer_name:"张三",customer_phone:"13800138001",product_name:"VIP群组套餐",product_spec:"年费版",quantity:1,unit_price:2999,total_amount:2999,discount_amount:0,payment_method:"微信支付",status:"completed",commission_rate:15,commission_amount:449.85,commission_status:"settled",commission_settled_at:new Date(Date.now()-1728e5),created_at:new Date(Date.now()-864e6),paid_at:new Date(Date.now()-7776e5),remark:"客户要求加急处理",logs:[{id:1,action:"订单创建",description:"客户通过推广链接下单",operator:"系统",created_at:new Date(Date.now()-864e6)},{id:2,action:"支付完成",description:"客户完成微信支付",operator:"系统",created_at:new Date(Date.now()-7776e5)},{id:3,action:"订单完成",description:"服务交付完成",operator:"客服",created_at:new Date(Date.now()-6048e5)}]},{id:2,order_no:"ORD202401002",customer_name:"李四",customer_phone:"13800138002",product_name:"标准群组套餐",product_spec:"月费版",quantity:2,unit_price:999,total_amount:1998,discount_amount:100,payment_method:"",status:"pending",commission_rate:10,commission_amount:189.8,commission_status:"pending",commission_settled_at:null,created_at:new Date(Date.now()-2592e5),paid_at:null,remark:"",logs:[{id:1,action:"订单创建",description:"客户通过推广链接下单",operator:"系统",created_at:new Date(Date.now()-2592e5)}]},{id:3,order_no:"ORD202401003",customer_name:"王五",customer_phone:"13800138003",product_name:"企业群组套餐",product_spec:"定制版",quantity:1,unit_price:4999,total_amount:4999,discount_amount:500,payment_method:"支付宝",status:"paid",commission_rate:15,commission_amount:674.85,commission_status:"pending",commission_settled_at:null,created_at:new Date(Date.now()-432e6),paid_at:new Date(Date.now()-3456e5),remark:"企业客户，需要开发票",logs:[{id:1,action:"订单创建",description:"客户通过推广链接下单",operator:"系统",created_at:new Date(Date.now()-432e6)},{id:2,action:"支付完成",description:"客户完成支付宝支付",operator:"系统",created_at:new Date(Date.now()-3456e5)}]}],sa=async()=>{try{Qe.value=!0,await new Promise(e=>setTimeout(e,500));let e=[...la];if(We.value&&(e=e.filter(e=>e.order_no.includes(We.value)||e.customer_name.includes(We.value)||e.customer_phone.includes(We.value)||e.product_name.includes(We.value))),Xe.value&&(e=e.filter(e=>e.status===Xe.value)),Je.value&&2===Je.value.length){const a=new Date(Je.value[0]),t=new Date(Je.value[1]);e=e.filter(e=>{const l=new Date(e.created_at);return l>=a&&l<=t})}aa.value=e,ta.total=e.length}catch(e){$.error("加载订单数据失败")}finally{Qe.value=!1}},oa=()=>{sa(),$.success("数据已刷新")},da=function(e,a){let t;return function(...l){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e(...l)},a)}}(()=>{ta.current_page=1,sa()},500),na=e=>{Ke.value=e,Ze.value="basic",Be.value=!0},ua=(e=Ke.value)=>{$.info(`联系客户 ${e.customer_name}`),Ge.value=!0},ia=e=>{$.info(`向客户 ${e.customer_name} 发送支付提醒`),Ge.value=!0},ra=e=>{$.info(`查看订单 ${e.order_no} 的物流信息`),Ge.value=!0},ca=e=>{$.info(`为订单 ${e.order_no} 提供售后服务`),Ge.value=!0},_a=()=>{$.info("导出订单功能开发中..."),Ge.value=!0},pa=e=>({pending:"warning",paid:"primary",completed:"success",cancelled:"info",refunded:"danger"}[e]||"info"),ma=e=>({pending:"待支付",paid:"已支付",completed:"已完成",cancelled:"已取消",refunded:"已退款"}[e]||"未知"),va=e=>({pending:"待结算",settled:"已结算",frozen:"已冻结"}[e]||"未知"),fa=e=>Number(e).toFixed(2),ba=e=>new Date(e).toLocaleDateString("zh-CN"),ga=e=>new Date(e).toLocaleString("zh-CN");return N(()=>{sa()}),(e,$)=>{const I=a,L=l,N=o,la=p,wa=V,ya=F,ha=x,ka=Y,Da=b,Ca=f,za=v,Va=g,ja=w,xa=D,Fa=k,Ya=y,Ua=m,qa=z,Ea=U,Ma=E,Aa=A,Oa=M,Ta=q,$a=O,Ia=C;return P(),S("div",ee,[R("div",ae,[$[12]||($[12]=R("div",{class:"header-left"},[R("h2",null,"订单查看"),R("p",{class:"page-description"},"查看通过您推广产生的所有订单信息")],-1)),R("div",te,[H(L,{type:"primary",onClick:_a},{default:Q(()=>[H(I,null,{default:Q(()=>[H(G(t))]),_:1}),$[10]||($[10]=B(" 导出订单 ",-1))]),_:1,__:[10]}),H(L,{onClick:oa},{default:Q(()=>[H(I,null,{default:Q(()=>[H(G(s))]),_:1}),$[11]||($[11]=B(" 刷新数据 ",-1))]),_:1,__:[11]})])]),H(la,{gutter:20,class:"stats-row"},{default:Q(()=>[H(N,{span:6},{default:Q(()=>[R("div",le,[R("div",se,[H(I,{size:24},{default:Q(()=>[H(G(d))]),_:1})]),R("div",oe,[R("div",de,n(ea.total_orders),1),$[14]||($[14]=R("div",{class:"stat-title"},"总订单数",-1)),R("div",ne,[H(I,null,{default:Q(()=>[H(G(u))]),_:1}),$[13]||($[13]=B(" 15.2% ",-1))])])])]),_:1}),H(N,{span:6},{default:Q(()=>[R("div",ue,[R("div",ie,[H(I,{size:24},{default:Q(()=>[H(G(i))]),_:1})]),R("div",re,[R("div",ce,"¥"+n(fa(ea.total_amount)),1),$[16]||($[16]=R("div",{class:"stat-title"},"总订单金额",-1)),R("div",_e,[H(I,null,{default:Q(()=>[H(G(u))]),_:1}),$[15]||($[15]=B(" 23.8% ",-1))])])])]),_:1}),H(N,{span:6},{default:Q(()=>[R("div",pe,[R("div",me,[H(I,{size:24},{default:Q(()=>[H(G(r))]),_:1})]),R("div",ve,[R("div",fe,n(ea.pending_orders),1),$[18]||($[18]=R("div",{class:"stat-title"},"待处理订单",-1)),R("div",be,[H(I,null,{default:Q(()=>[H(G(c))]),_:1}),$[17]||($[17]=B(" 0% ",-1))])])])]),_:1}),H(N,{span:6},{default:Q(()=>[R("div",ge,[R("div",we,[H(I,{size:24},{default:Q(()=>[H(G(_))]),_:1})]),R("div",ye,[R("div",he,n(ea.success_rate)+"%",1),$[20]||($[20]=R("div",{class:"stat-title"},"成功率",-1)),R("div",ke,[H(I,null,{default:Q(()=>[H(G(u))]),_:1}),$[19]||($[19]=B(" 2.1% ",-1))])])])]),_:1})]),_:1}),H(Ea,null,{header:Q(()=>[R("div",De,[$[21]||($[21]=R("span",null,"订单列表",-1)),R("div",Ce,[H(wa,{modelValue:We.value,"onUpdate:modelValue":$[0]||($[0]=e=>We.value=e),placeholder:"搜索订单号或客户",style:{width:"200px","margin-right":"10px"},clearable:"",onInput:G(da)},{prefix:Q(()=>[H(I,null,{default:Q(()=>[H(G(j))]),_:1})]),_:1},8,["modelValue","onInput"]),H(ha,{modelValue:Xe.value,"onUpdate:modelValue":$[1]||($[1]=e=>Xe.value=e),placeholder:"订单状态",style:{width:"120px","margin-right":"10px"},onChange:sa},{default:Q(()=>[H(ya,{label:"全部",value:""}),H(ya,{label:"待支付",value:"pending"}),H(ya,{label:"已支付",value:"paid"}),H(ya,{label:"已完成",value:"completed"}),H(ya,{label:"已取消",value:"cancelled"}),H(ya,{label:"已退款",value:"refunded"})]),_:1},8,["modelValue"]),H(ka,{modelValue:Je.value,"onUpdate:modelValue":$[2]||($[2]=e=>Je.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:sa},null,8,["modelValue"])])])]),default:Q(()=>[W((P(),X(Ua,{data:aa.value,stripe:""},{default:Q(()=>[H(za,{type:"expand"},{default:Q(({row:e})=>[R("div",ze,[H(Ca,{column:3,border:""},{default:Q(()=>[H(Da,{label:"订单号"},{default:Q(()=>[B(n(e.order_no),1)]),_:2},1024),H(Da,{label:"客户姓名"},{default:Q(()=>[B(n(e.customer_name),1)]),_:2},1024),H(Da,{label:"客户电话"},{default:Q(()=>[B(n(e.customer_phone),1)]),_:2},1024),H(Da,{label:"产品名称"},{default:Q(()=>[B(n(e.product_name),1)]),_:2},1024),H(Da,{label:"产品规格"},{default:Q(()=>[B(n(e.product_spec),1)]),_:2},1024),H(Da,{label:"购买数量"},{default:Q(()=>[B(n(e.quantity),1)]),_:2},1024),H(Da,{label:"单价"},{default:Q(()=>[B("¥"+n(fa(e.unit_price)),1)]),_:2},1024),H(Da,{label:"总金额"},{default:Q(()=>[B("¥"+n(fa(e.total_amount)),1)]),_:2},1024),H(Da,{label:"优惠金额"},{default:Q(()=>[B("¥"+n(fa(e.discount_amount)),1)]),_:2},1024),H(Da,{label:"支付方式"},{default:Q(()=>[B(n(e.payment_method),1)]),_:2},1024),H(Da,{label:"支付时间"},{default:Q(()=>[B(n(e.paid_at?ga(e.paid_at):"未支付"),1)]),_:2},1024),H(Da,{label:"备注"},{default:Q(()=>[B(n(e.remark||"无"),1)]),_:2},1024)]),_:2},1024)])]),_:1}),H(za,{prop:"order_no",label:"订单号",width:"180"},{default:Q(({row:e})=>[H(Va,{type:"primary",onClick:a=>na(e)},{default:Q(()=>[B(n(e.order_no),1)]),_:2},1032,["onClick"])]),_:1}),H(za,{label:"客户信息",width:"150"},{default:Q(({row:e})=>[R("div",Ve,[R("div",je,n(e.customer_name),1),R("div",xe,n(e.customer_phone),1)])]),_:1}),H(za,{label:"产品信息",width:"200"},{default:Q(({row:e})=>[R("div",Fe,[R("div",Ye,n(e.product_name),1),R("div",Ue,n(e.product_spec),1)])]),_:1}),H(za,{prop:"quantity",label:"数量",width:"80"}),H(za,{label:"订单金额",width:"120"},{default:Q(({row:e})=>[R("div",qe,[R("div",Ee,"¥"+n(fa(e.total_amount)),1),e.quantity>1?(P(),S("div",Me,"单价: ¥"+n(fa(e.unit_price)),1)):J("",!0)])]),_:1}),H(za,{label:"状态",width:"100"},{default:Q(({row:e})=>[H(ja,{type:pa(e.status),size:"small"},{default:Q(()=>[B(n(ma(e.status)),1)]),_:2},1032,["type"])]),_:1}),H(za,{label:"支付方式",width:"100"},{default:Q(({row:e})=>[e.payment_method?(P(),S("span",Ae,n(e.payment_method),1)):(P(),S("span",Oe,"未选择"))]),_:1}),H(za,{label:"创建时间",width:"120"},{default:Q(({row:e})=>[B(n(ba(e.created_at)),1)]),_:1}),H(za,{label:"支付时间",width:"120"},{default:Q(({row:e})=>[e.paid_at?(P(),S("span",Te,n(ba(e.paid_at)),1)):(P(),S("span",$e,"未支付"))]),_:1}),H(za,{label:"操作",width:"150",fixed:"right"},{default:Q(({row:e})=>[H(L,{size:"small",onClick:a=>na(e)},{default:Q(()=>$[22]||($[22]=[B(" 详情 ",-1)])),_:2,__:[22]},1032,["onClick"]),H(Ya,{onCommand:a=>((e,a)=>{switch(e){case"contact":ua(a);break;case"remind":ia(a);break;case"track":ra(a);break;case"service":ca(a)}})(a,e)},{dropdown:Q(()=>[H(Fa,null,{default:Q(()=>["pending"===e.status?(P(),X(xa,{key:0,command:"contact"},{default:Q(()=>$[24]||($[24]=[B("联系客户",-1)])),_:1,__:[24]})):J("",!0),"pending"===e.status?(P(),X(xa,{key:1,command:"remind"},{default:Q(()=>$[25]||($[25]=[B("支付提醒",-1)])),_:1,__:[25]})):J("",!0),"paid"===e.status?(P(),X(xa,{key:2,command:"track"},{default:Q(()=>$[26]||($[26]=[B("物流跟踪",-1)])),_:1,__:[26]})):J("",!0),H(xa,{command:"service",divided:""},{default:Q(()=>$[27]||($[27]=[B("售后服务",-1)])),_:1,__:[27]})]),_:2},1024)]),default:Q(()=>[H(L,{size:"small"},{default:Q(()=>[$[23]||($[23]=B(" 更多",-1)),H(I,{class:"el-icon--right"},{default:Q(()=>[H(G(h))]),_:1})]),_:1,__:[23]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[Ia,Qe.value]]),R("div",Ie,[H(qa,{"current-page":ta.current_page,"onUpdate:currentPage":$[3]||($[3]=e=>ta.current_page=e),"page-size":ta.per_page,"onUpdate:pageSize":$[4]||($[4]=e=>ta.per_page=e),total:ta.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:sa,onCurrentChange:sa},null,8,["current-page","page-size","total"])])]),_:1}),H($a,{modelValue:Be.value,"onUpdate:modelValue":$[7]||($[7]=e=>Be.value=e),title:"订单详情",width:"800px"},{footer:Q(()=>[H(L,{onClick:$[6]||($[6]=e=>Be.value=!1)},{default:Q(()=>$[28]||($[28]=[B("关闭",-1)])),_:1,__:[28]}),Ke.value&&"pending"===Ke.value.status?(P(),X(L,{key:0,type:"primary",onClick:ua},{default:Q(()=>$[29]||($[29]=[B(" 联系客户 ",-1)])),_:1,__:[29]})):J("",!0)]),default:Q(()=>[Ke.value?(P(),S("div",Le,[H(Ta,{modelValue:Ze.value,"onUpdate:modelValue":$[5]||($[5]=e=>Ze.value=e)},{default:Q(()=>[H(Ma,{label:"基本信息",name:"basic"},{default:Q(()=>[H(Ca,{column:2,border:""},{default:Q(()=>[H(Da,{label:"订单号"},{default:Q(()=>[B(n(Ke.value.order_no),1)]),_:1}),H(Da,{label:"订单状态"},{default:Q(()=>[H(ja,{type:pa(Ke.value.status)},{default:Q(()=>[B(n(ma(Ke.value.status)),1)]),_:1},8,["type"])]),_:1}),H(Da,{label:"客户姓名"},{default:Q(()=>[B(n(Ke.value.customer_name),1)]),_:1}),H(Da,{label:"客户电话"},{default:Q(()=>[B(n(Ke.value.customer_phone),1)]),_:1}),H(Da,{label:"产品名称"},{default:Q(()=>[B(n(Ke.value.product_name),1)]),_:1}),H(Da,{label:"产品规格"},{default:Q(()=>[B(n(Ke.value.product_spec),1)]),_:1}),H(Da,{label:"购买数量"},{default:Q(()=>[B(n(Ke.value.quantity),1)]),_:1}),H(Da,{label:"单价"},{default:Q(()=>[B("¥"+n(fa(Ke.value.unit_price)),1)]),_:1}),H(Da,{label:"总金额"},{default:Q(()=>[B("¥"+n(fa(Ke.value.total_amount)),1)]),_:1}),H(Da,{label:"优惠金额"},{default:Q(()=>[B("¥"+n(fa(Ke.value.discount_amount)),1)]),_:1}),H(Da,{label:"实付金额"},{default:Q(()=>[B("¥"+n(fa(Ke.value.total_amount-Ke.value.discount_amount)),1)]),_:1}),H(Da,{label:"支付方式"},{default:Q(()=>[B(n(Ke.value.payment_method||"未选择"),1)]),_:1}),H(Da,{label:"创建时间"},{default:Q(()=>[B(n(ga(Ke.value.created_at)),1)]),_:1}),H(Da,{label:"支付时间"},{default:Q(()=>[B(n(Ke.value.paid_at?ga(Ke.value.paid_at):"未支付"),1)]),_:1}),H(Da,{label:"备注",span:"2"},{default:Q(()=>[B(n(Ke.value.remark||"无"),1)]),_:1})]),_:1})]),_:1}),H(Ma,{label:"佣金信息",name:"commission"},{default:Q(()=>[H(Ca,{column:2,border:""},{default:Q(()=>[H(Da,{label:"佣金比例"},{default:Q(()=>[B(n(Ke.value.commission_rate)+"%",1)]),_:1}),H(Da,{label:"佣金金额"},{default:Q(()=>[B("¥"+n(fa(Ke.value.commission_amount)),1)]),_:1}),H(Da,{label:"佣金状态"},{default:Q(()=>{return[H(ja,{type:(e=Ke.value.commission_status,{pending:"warning",settled:"success",frozen:"danger"}[e]||"info")},{default:Q(()=>[B(n(va(Ke.value.commission_status)),1)]),_:1},8,["type"])];var e}),_:1}),H(Da,{label:"结算时间"},{default:Q(()=>[B(n(Ke.value.commission_settled_at?ga(Ke.value.commission_settled_at):"未结算"),1)]),_:1})]),_:1})]),_:1}),H(Ma,{label:"操作记录",name:"logs"},{default:Q(()=>[H(Oa,null,{default:Q(()=>[(P(!0),S(K,null,Z(Ke.value.logs,e=>{return P(),X(Aa,{key:e.id,timestamp:ga(e.created_at),type:(a=e.action,{"订单创建":"primary","支付完成":"success","订单完成":"success","订单取消":"warning","申请退款":"danger"}[a]||"primary")},{default:Q(()=>[R("div",Ne,[R("div",Se,n(e.action),1),R("div",Pe,n(e.description),1),R("div",Re,"操作人: "+n(e.operator),1)])]),_:2},1032,["timestamp","type"]);var a}),128))]),_:1})]),_:1})]),_:1},8,["modelValue"])])):J("",!0)]),_:1},8,["modelValue"]),H($a,{modelValue:Ge.value,"onUpdate:modelValue":$[9]||($[9]=e=>Ge.value=e),title:"功能开发中",width:"400px",center:""},{footer:Q(()=>[H(L,{type:"primary",onClick:$[8]||($[8]=e=>Ge.value=!1)},{default:Q(()=>$[33]||($[33]=[B("知道了",-1)])),_:1,__:[33]})]),default:Q(()=>[R("div",He,[H(I,{size:60,color:"#409EFF"},{default:Q(()=>[H(G(T))]),_:1}),$[30]||($[30]=R("h3",null,"功能开发中",-1)),$[31]||($[31]=R("p",null,"该功能正在紧急开发中，敬请期待！",-1)),$[32]||($[32]=R("p",null,"预计上线时间：2024年1月",-1))])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-219ee3ad"]]);export{Qe as default};
