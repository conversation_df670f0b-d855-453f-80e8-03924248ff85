import"./index-D2bI4m-v.js";import{f as e}from"./chunk-KZPPZA2C-BZQYgWVq.js";const t=Array.from({length:15},(t,n)=>({id:n+1,name:`${e.company.name()}分站`,domain:e.internet.domainName(),owner_name:e.internet.userName(),owner_id:e.number.int({min:100,max:200}),status:e.helpers.arrayElement(["active","inactive","expired"]),created_at:e.date.past().toISOString(),expired_at:e.date.future().toISOString(),package_name:`套餐${e.helpers.arrayElement(["A","B","C"])}`})),n={getSubstationList(e){const{page:n=1,limit:r=10,keyword:a,status:s}=e;let i=t.filter(e=>{let t=!0;return!a||e.name.includes(a)||e.domain.includes(a)||(t=!1),s&&e.status!==s&&(t=!1),t});const o=i.length,u=(n-1)*r,d=n*r,c=i.slice(u,d);return Promise.resolve({code:0,data:{list:c,total:o},message:"成功"})},createSubstation(n){const r={...n,id:t.length+1,created_at:(new Date).toISOString(),expired_at:e.date.future().toISOString(),status:"active"};return t.unshift(r),Promise.resolve({code:0,data:r,message:"创建成功"})},updateSubstation(e,n){const r=t.findIndex(t=>t.id===e);return-1!==r?(t[r]={...t[r],...n},Promise.resolve({code:0,data:t[r],message:"更新成功"})):Promise.reject(new Error("分站未找到"))},deleteSubstation(e){const n=t.findIndex(t=>t.id===e);return-1!==n?(t.splice(n,1),Promise.resolve({code:0,message:"删除成功"})):Promise.reject(new Error("分站未找到"))},renewSubstation(n,r){const a=t.findIndex(e=>e.id===n);return-1!==a?(t[a].expired_at=e.date.future().toISOString(),Promise.resolve({code:0,data:t[a],message:"续费成功"})):Promise.reject(new Error("分站未找到"))}};function r(e){return n.getSubstationList(e)}function a(e){return n.createSubstation(e)}function s(e,t){return n.updateSubstation(e,t)}function i(e){return n.deleteSubstation(e)}function o(e,t){return n.renewSubstation(e,t)}function u(e){return n.getSubstationFinanceStats(e)}function d(e){return n.getSubstationSettlementRecords(e)}function c(e){return n.generateSubstationFinanceReport(e)}export{u as a,d as b,a as c,i as d,c as e,r as g,o as r,s as u};
