# 数据库迁移优化

## 1. 群组表字段扩展迁移

### 创建迁移文件：

```bash
php artisan make:migration add_marketing_fields_to_wechat_groups_table
```

### 迁移内容：

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMarketingFieldsToWechatGroupsTable extends Migration
{
    public function up()
    {
        Schema::table('wechat_groups', function (Blueprint $table) {
            // 营销展示字段
            $table->string('read_count_display', 20)->nullable()->comment('阅读数显示文本');
            $table->integer('like_count')->default(0)->comment('点赞数');
            $table->integer('want_see_count')->default(0)->comment('想看数');
            $table->string('button_title', 100)->nullable()->comment('入群按钮文案');
            
            // 内容设置字段
            $table->string('group_intro_title', 60)->nullable()->comment('群简介标题');
            $table->text('group_intro_content')->nullable()->comment('群简介内容');
            $table->string('faq_title', 60)->nullable()->comment('常见问题标题');
            $table->text('faq_content')->nullable()->comment('常见问题内容');
            $table->text('member_reviews')->nullable()->comment('群友评论');
            
            // 客服广告字段
            $table->string('customer_service_qr')->nullable()->comment('客服二维码');
            $table->string('ad_qr_code')->nullable()->comment('广告二维码');
            $table->tinyInteger('show_customer_service')->default(1)->comment('是否显示客服信息 1=不显示 2=显示');
            $table->string('customer_service_avatar')->nullable()->comment('客服头像');
            $table->string('customer_service_title', 100)->nullable()->comment('客服标题');
            $table->string('customer_service_desc', 500)->nullable()->comment('客服描述');
            
            // 展示控制字段
            $table->string('avatar_library', 20)->default('qq')->comment('头像库选择 qq/za');
            $table->tinyInteger('wx_accessible')->default(1)->comment('微信浏览器访问控制 1=能打开 2=不能打开');
            $table->tinyInteger('display_type')->default(1)->comment('展示类型 1=文字+图片 2=纯图片');
            
            // 城市定位增强字段
            $table->tinyInteger('auto_city_replace')->default(1)->comment('自动城市替换 0=关闭 1=开启');
            $table->string('city_insert_strategy', 20)->default('auto')->comment('城市插入策略');
            
            // 虚拟数据增强字段
            $table->integer('virtual_members')->default(0)->comment('虚拟成员数');
            $table->integer('virtual_orders')->default(0)->comment('虚拟订单数');
            $table->decimal('virtual_income', 10, 2)->default(0)->comment('虚拟收入');
            $table->integer('today_views')->default(0)->comment('今日浏览量');
            $table->integer('total_joins')->default(0)->comment('总加入数');
            
            // 营销标签
            $table->json('marketing_tags')->nullable()->comment('营销标签');
            
            // 扩展内容字段
            $table->string('extra_title1', 60)->nullable()->comment('扩展标题1');
            $table->text('extra_content1')->nullable()->comment('扩展内容1');
            $table->string('extra_title2', 60)->nullable()->comment('扩展标题2');
            $table->text('extra_content2')->nullable()->comment('扩展内容2');
            $table->string('extra_title3', 60)->nullable()->comment('扩展标题3');
            $table->text('extra_content3')->nullable()->comment('扩展内容3');
            
            // 统计字段
            $table->integer('view_count')->default(0)->comment('总浏览次数');
            $table->timestamp('last_activity_at')->nullable()->comment('最后活动时间');
            
            // 索引
            $table->index(['status', 'auto_city_replace']);
            $table->index(['user_id', 'status']);
            $table->index(['substation_id', 'status']);
            $table->index('view_count');
            $table->index('last_activity_at');
        });
    }

    public function down()
    {
        Schema::table('wechat_groups', function (Blueprint $table) {
            $table->dropColumn([
                'read_count_display', 'like_count', 'want_see_count', 'button_title',
                'group_intro_title', 'group_intro_content', 'faq_title', 'faq_content', 'member_reviews',
                'customer_service_qr', 'ad_qr_code', 'show_customer_service', 
                'customer_service_avatar', 'customer_service_title', 'customer_service_desc',
                'avatar_library', 'wx_accessible', 'display_type',
                'auto_city_replace', 'city_insert_strategy',
                'virtual_members', 'virtual_orders', 'virtual_income', 'today_views', 'total_joins',
                'marketing_tags',
                'extra_title1', 'extra_content1', 'extra_title2', 'extra_content2', 'extra_title3', 'extra_content3',
                'view_count', 'last_activity_at'
            ]);
        });
    }
}
```

## 2. 域名池表优化迁移

### 创建迁移文件：

```bash
php artisan make:migration enhance_domain_pools_table
```

### 迁移内容：

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EnhanceDomainPoolsTable extends Migration
{
    public function up()
    {
        Schema::table('domain_pools', function (Blueprint $table) {
            // 健康监控字段
            $table->integer('health_score')->default(100)->comment('健康分数 0-100');
            $table->timestamp('last_check_at')->nullable()->comment('最后检查时间');
            $table->integer('failure_count')->default(0)->comment('失败次数');
            $table->boolean('is_primary')->default(false)->comment('是否为主域名');
            
            // 使用统计字段
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->integer('max_daily_usage')->default(1000)->comment('每日最大使用次数');
            $table->integer('daily_usage_count')->default(0)->comment('今日使用次数');
            $table->date('usage_date')->nullable()->comment('使用统计日期');
            
            // 风险评估字段
            $table->enum('risk_level', ['low', 'medium', 'high'])->default('low')->comment('风险等级');
            $table->json('risk_factors')->nullable()->comment('风险因素');
            
            // 性能监控字段
            $table->integer('avg_response_time')->default(0)->comment('平均响应时间(ms)');
            $table->decimal('uptime_rate', 5, 2)->default(100.00)->comment('可用率');
            
            // 分组和标签
            $table->string('group_tag', 50)->nullable()->comment('分组标签');
            $table->json('tags')->nullable()->comment('标签');
            
            // 索引优化
            $table->index(['status', 'risk_level', 'health_score']);
            $table->index(['substation_id', 'status']);
            $table->index(['usage_count', 'max_daily_usage']);
            $table->index('last_check_at');
            $table->index('is_primary');
        });
    }

    public function down()
    {
        Schema::table('domain_pools', function (Blueprint $table) {
            $table->dropColumn([
                'health_score', 'last_check_at', 'failure_count', 'is_primary',
                'usage_count', 'max_daily_usage', 'daily_usage_count', 'usage_date',
                'risk_level', 'risk_factors',
                'avg_response_time', 'uptime_rate',
                'group_tag', 'tags'
            ]);
        });
    }
}
```

## 3. 群组访问日志表

### 创建新表迁移：

```bash
php artisan make:migration create_group_access_logs_table
```

### 迁移内容：

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGroupAccessLogsTable extends Migration
{
    public function up()
    {
        Schema::create('group_access_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('wechat_group_id')->comment('群组ID');
            $table->string('visitor_ip', 45)->comment('访问者IP');
            $table->string('user_agent')->nullable()->comment('用户代理');
            $table->string('referer')->nullable()->comment('来源页面');
            $table->string('detected_city', 50)->nullable()->comment('检测到的城市');
            $table->string('browser_type', 20)->nullable()->comment('浏览器类型');
            $table->boolean('is_wechat_browser')->default(false)->comment('是否微信浏览器');
            $table->string('access_domain', 100)->nullable()->comment('访问域名');
            $table->enum('access_result', ['success', 'blocked', 'error'])->comment('访问结果');
            $table->string('block_reason')->nullable()->comment('阻止原因');
            $table->json('extra_data')->nullable()->comment('额外数据');
            $table->timestamps();

            // 索引
            $table->index(['wechat_group_id', 'created_at']);
            $table->index(['visitor_ip', 'created_at']);
            $table->index(['detected_city', 'created_at']);
            $table->index(['access_result', 'created_at']);
            $table->index('is_wechat_browser');
            
            // 外键
            $table->foreign('wechat_group_id')->references('id')->on('wechat_groups')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('group_access_logs');
    }
}
```

## 4. 城市定位缓存表

### 创建缓存表迁移：

```bash
php artisan make:migration create_ip_city_cache_table
```

### 迁移内容：

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIpCityCacheTable extends Migration
{
    public function up()
    {
        Schema::create('ip_city_cache', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address', 45)->unique()->comment('IP地址');
            $table->string('city', 50)->comment('城市名称');
            $table->string('province', 50)->nullable()->comment('省份');
            $table->string('country', 50)->default('中国')->comment('国家');
            $table->string('isp', 100)->nullable()->comment('运营商');
            $table->string('data_source', 20)->comment('数据来源');
            $table->json('raw_data')->nullable()->comment('原始数据');
            $table->integer('hit_count')->default(1)->comment('命中次数');
            $table->timestamp('last_hit_at')->useCurrent()->comment('最后命中时间');
            $table->timestamps();

            // 索引
            $table->index(['city', 'created_at']);
            $table->index(['province', 'created_at']);
            $table->index('data_source');
            $table->index('last_hit_at');
        });
    }

    public function down()
    {
        Schema::dropIfExists('ip_city_cache');
    }
}
```

## 5. 数据填充器（Seeder）

### 创建营销数据填充器：

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\WechatGroup;

class MarketingDataSeeder extends Seeder
{
    public function run()
    {
        // 为现有群组添加默认营销数据
        WechatGroup::whereNull('read_count_display')->chunk(100, function ($groups) {
            foreach ($groups as $group) {
                $group->update([
                    'read_count_display' => $this->getRandomReadCount(),
                    'like_count' => rand(100, 999),
                    'want_see_count' => rand(50, 500),
                    'button_title' => '立即加入群聊',
                    'group_intro_title' => '群简介',
                    'faq_title' => '常见问题',
                    'avatar_library' => 'qq',
                    'wx_accessible' => 1,
                    'display_type' => 1,
                    'auto_city_replace' => 1,
                    'virtual_members' => rand(50, 200),
                    'virtual_orders' => rand(20, 100),
                    'virtual_income' => rand(1000, 5000),
                    'today_views' => rand(10, 50),
                    'total_joins' => rand(100, 500),
                ]);
            }
        });
    }

    private function getRandomReadCount(): string
    {
        $options = ['10万+', '5万+', '3万+', '1万+', '8888', '6666', '5555'];
        return $options[array_rand($options)];
    }
}
```

## 6. 运行迁移命令

```bash
# 运行所有迁移
php artisan migrate

# 运行数据填充
php artisan db:seed --class=MarketingDataSeeder

# 如果需要回滚
php artisan migrate:rollback --step=4
```