import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       *//* empty css               *//* empty css                */import{H as a}from"./echarts-DTArWCqr.js";import{b2 as l,b3 as t,U as s,b4 as o,bc as i,bd as d,aW as u,aV as r,bl as n,aH as p,as as c,b6 as _,b7 as m,b8 as b,bj as f,bk as v,bM as g,bN as y,bR as h,au as w,bo as V,bp as k,be as x,Q as j,R as z}from"./element-plus-DcSKpKA8.js";import{L as U,r as C,e as Y,k as D,l as I,E as M,z as W,t as H,u as P,F as T,Y as A,y as N,D as S,A as q,B}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const O={class:"app-container"},J={class:"stat-item"},K={class:"stat-content"},L={class:"stat-number"},Q={class:"stat-item"},R={class:"stat-content"},E={class:"stat-number"},F={class:"stat-item"},G={class:"stat-content"},X={class:"stat-number"},Z={class:"stat-item"},$={class:"stat-content"},ee={class:"stat-number"},ae={class:"card-header"},le={class:"user-info"},te={class:"user-name"},se={class:"user-role"},oe={class:"module-name"},ie={class:"table-pagination"},de={key:0,class:"log-detail"},ue={class:"detail-section"},re={key:0,class:"detail-section"},ne={class:"json-data"},pe={key:1,class:"detail-section"},ce={class:"json-data"},_e={key:2,class:"detail-section"},me={class:"dialog-footer"},be=e({__name:"OperationLogs",setup(e){const be=U({total:25687,today:156,active_users:45,errors:12}),fe=U({page:1,per_page:20,user_id:"",operation_type:"",module:"",status:"",date_range:[],ip:""}),ve=C([{id:1,user:{nickname:"管理员",role_name:"超级管理员"},operation_type:"login",module:"system",description:"用户登录系统",status:"success",ip_address:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",execution_time:120,created_at:"2024-01-01 10:00:00",request_data:{username:"admin"},response_data:{success:!0}},{id:2,user:{nickname:"普通用户",role_name:"用户"},operation_type:"create",module:"user",description:"创建新用户",status:"success",ip_address:"***********",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",execution_time:350,created_at:"2024-01-01 09:30:00"}]),ge=C([{id:1,nickname:"管理员"},{id:2,nickname:"普通用户"}]),ye=U({visible:!1,log:null}),he=U({visible:!1}),we=U({type:"by_date",before_date:null,keep_count:1e4,status:""}),Ve=C({}),ke=C({}),xe=C(!1),je=C(!1),ze=C(0),Ue=e=>({login:"success",create:"primary",update:"warning",delete:"danger",export:"info",setting:"primary"}[e]||"info"),Ce=e=>({login:"登录",create:"创建",update:"更新",delete:"删除",export:"导出",setting:"设置"}[e]||e),Ye=e=>({user:"用户管理",order:"订单管理",finance:"财务管理",distribution:"分销管理",system:"系统设置"}[e]||e),De=e=>({success:"success",failed:"danger",warning:"warning"}[e]||"info"),Ie=e=>({success:"成功",failed:"失败",warning:"警告"}[e]||e),Me=()=>{xe.value=!0,setTimeout(()=>{xe.value=!1,j.success("搜索完成")},1e3)},We=()=>{Object.assign(fe,{page:1,per_page:20,user_id:"",operation_type:"",module:"",status:"",date_range:[],ip:""}),Me()},He=()=>{j.success("导出任务已创建")},Pe=()=>{Me()},Te=()=>{he.visible=!0},Ae=async()=>{je.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),j.success("日志清理完成"),he.visible=!1,Pe()}catch(e){j.error("日志清理失败")}finally{je.value=!1}},Ne=e=>{fe.per_page=e,Me()},Se=e=>{fe.page=e,Me()};return Y(()=>{ze.value=ve.value.length,Ve.value={title:{text:"操作类型分布",left:"center"},tooltip:{trigger:"item"},series:[{name:"操作类型",type:"pie",radius:"50%",data:[{value:1048,name:"登录"},{value:735,name:"创建"},{value:580,name:"更新"},{value:484,name:"删除"},{value:300,name:"导出"}]}]},ke.value={title:{text:"操作趋势",left:"center"},tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{name:"操作次数",type:"line",data:[120,132,101,134,90,230,210]}]}}),(e,U)=>{const C=t,Y=l,qe=o,Be=r,Oe=u,Je=d,Ke=n,Le=p,Qe=c,Re=i,Ee=m,Fe=b,Ge=_,Xe=v,Ze=y,$e=g,ea=h,aa=w,la=k,ta=V,sa=x,oa=f;return I(),D("div",O,[M(qe,{gutter:20},{default:W(()=>[M(Y,{span:6},{default:W(()=>[M(C,{class:"stat-card"},{default:W(()=>[H("div",J,[U[16]||(U[16]=H("div",{class:"stat-icon total-icon"},[H("i",{class:"el-icon-document"})],-1)),H("div",K,[H("div",L,s(be.total),1),U[15]||(U[15]=H("div",{class:"stat-label"},"总日志数",-1))])])]),_:1})]),_:1}),M(Y,{span:6},{default:W(()=>[M(C,{class:"stat-card"},{default:W(()=>[H("div",Q,[U[18]||(U[18]=H("div",{class:"stat-icon today-icon"},[H("i",{class:"el-icon-calendar-today"})],-1)),H("div",R,[H("div",E,s(be.today),1),U[17]||(U[17]=H("div",{class:"stat-label"},"今日操作",-1))])])]),_:1})]),_:1}),M(Y,{span:6},{default:W(()=>[M(C,{class:"stat-card"},{default:W(()=>[H("div",F,[U[20]||(U[20]=H("div",{class:"stat-icon user-icon"},[H("i",{class:"el-icon-user"})],-1)),H("div",G,[H("div",X,s(be.active_users),1),U[19]||(U[19]=H("div",{class:"stat-label"},"活跃用户",-1))])])]),_:1})]),_:1}),M(Y,{span:6},{default:W(()=>[M(C,{class:"stat-card"},{default:W(()=>[H("div",Z,[U[22]||(U[22]=H("div",{class:"stat-icon error-icon"},[H("i",{class:"el-icon-warning"})],-1)),H("div",$,[H("div",ee,s(be.errors),1),U[21]||(U[21]=H("div",{class:"stat-label"},"错误操作",-1))])])]),_:1})]),_:1})]),_:1}),M(qe,{gutter:20,style:{"margin-top":"20px"}},{default:W(()=>[M(Y,{span:12},{default:W(()=>[M(C,null,{header:W(()=>U[23]||(U[23]=[H("div",{class:"card-header"},[H("span",null,"📊 操作类型统计")],-1)])),default:W(()=>[M(P(a),{class:"chart",option:Ve.value,autoresize:""},null,8,["option"])]),_:1})]),_:1}),M(Y,{span:12},{default:W(()=>[M(C,null,{header:W(()=>U[24]||(U[24]=[H("div",{class:"card-header"},[H("span",null,"📈 操作趋势")],-1)])),default:W(()=>[M(P(a),{class:"chart",option:ke.value,autoresize:""},null,8,["option"])]),_:1})]),_:1})]),_:1}),M(C,{style:{"margin-top":"20px"}},{header:W(()=>U[25]||(U[25]=[H("div",{class:"card-header"},[H("span",null,"🔍 日志筛选")],-1)])),default:W(()=>[M(Re,{inline:!0,model:fe,class:"filter-form"},{default:W(()=>[M(Je,{label:"操作用户"},{default:W(()=>[M(Oe,{modelValue:fe.user_id,"onUpdate:modelValue":U[0]||(U[0]=e=>fe.user_id=e),placeholder:"选择用户",clearable:"",filterable:""},{default:W(()=>[(I(!0),D(T,null,A(ge.value,e=>(I(),N(Be,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),M(Je,{label:"操作类型"},{default:W(()=>[M(Oe,{modelValue:fe.operation_type,"onUpdate:modelValue":U[1]||(U[1]=e=>fe.operation_type=e),placeholder:"选择类型",clearable:""},{default:W(()=>[M(Be,{label:"登录",value:"login"}),M(Be,{label:"创建",value:"create"}),M(Be,{label:"更新",value:"update"}),M(Be,{label:"删除",value:"delete"}),M(Be,{label:"导出",value:"export"}),M(Be,{label:"设置",value:"setting"})]),_:1},8,["modelValue"])]),_:1}),M(Je,{label:"模块"},{default:W(()=>[M(Oe,{modelValue:fe.module,"onUpdate:modelValue":U[2]||(U[2]=e=>fe.module=e),placeholder:"选择模块",clearable:""},{default:W(()=>[M(Be,{label:"用户管理",value:"user"}),M(Be,{label:"订单管理",value:"order"}),M(Be,{label:"财务管理",value:"finance"}),M(Be,{label:"分销管理",value:"distribution"}),M(Be,{label:"系统设置",value:"system"})]),_:1},8,["modelValue"])]),_:1}),M(Je,{label:"状态"},{default:W(()=>[M(Oe,{modelValue:fe.status,"onUpdate:modelValue":U[3]||(U[3]=e=>fe.status=e),placeholder:"选择状态",clearable:""},{default:W(()=>[M(Be,{label:"成功",value:"success"}),M(Be,{label:"失败",value:"failed"}),M(Be,{label:"警告",value:"warning"})]),_:1},8,["modelValue"])]),_:1}),M(Je,{label:"时间范围"},{default:W(()=>[M(Ke,{modelValue:fe.date_range,"onUpdate:modelValue":U[4]||(U[4]=e=>fe.date_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),M(Je,{label:"IP地址"},{default:W(()=>[M(Le,{modelValue:fe.ip,"onUpdate:modelValue":U[5]||(U[5]=e=>fe.ip=e),placeholder:"IP地址",clearable:""},null,8,["modelValue"])]),_:1}),M(Je,null,{default:W(()=>[M(Qe,{type:"primary",onClick:Me},{default:W(()=>U[26]||(U[26]=[H("i",{class:"el-icon-search"},null,-1),S(" 搜索 ",-1)])),_:1,__:[26]}),M(Qe,{onClick:We},{default:W(()=>U[27]||(U[27]=[H("i",{class:"el-icon-refresh"},null,-1),S(" 重置 ",-1)])),_:1,__:[27]}),M(Qe,{type:"success",onClick:He},{default:W(()=>U[28]||(U[28]=[H("i",{class:"el-icon-download"},null,-1),S(" 导出 ",-1)])),_:1,__:[28]})]),_:1})]),_:1},8,["model"])]),_:1}),M(C,{style:{"margin-top":"20px"}},{header:W(()=>[H("div",ae,[U[31]||(U[31]=H("span",null,"📋 操作日志",-1)),H("div",null,[M(Qe,{type:"primary",onClick:Pe},{default:W(()=>U[29]||(U[29]=[H("i",{class:"el-icon-refresh"},null,-1),S(" 刷新 ",-1)])),_:1,__:[29]}),M(Qe,{type:"warning",onClick:Te},{default:W(()=>U[30]||(U[30]=[H("i",{class:"el-icon-delete"},null,-1),S(" 清理日志 ",-1)])),_:1,__:[30]})])])]),default:W(()=>[q((I(),N(Ge,{data:ve.value,style:{width:"100%"}},{default:W(()=>[M(Ee,{prop:"id",label:"ID",width:"80"}),M(Ee,{prop:"user",label:"操作用户",width:"120"},{default:W(e=>[H("div",le,[H("div",te,s(e.row.user?.nickname||"系统"),1),H("div",se,s(e.row.user?.role_name||"System"),1)])]),_:1}),M(Ee,{prop:"operation_type",label:"操作类型",width:"100"},{default:W(e=>[M(Fe,{type:Ue(e.row.operation_type)},{default:W(()=>[S(s(Ce(e.row.operation_type)),1)]),_:2},1032,["type"])]),_:1}),M(Ee,{prop:"module",label:"模块",width:"100"},{default:W(e=>[H("span",oe,s(Ye(e.row.module)),1)]),_:1}),M(Ee,{prop:"description",label:"操作描述","min-width":"200","show-overflow-tooltip":""}),M(Ee,{prop:"status",label:"状态",width:"80"},{default:W(e=>[M(Fe,{type:De(e.row.status),size:"small"},{default:W(()=>[S(s(Ie(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),M(Ee,{prop:"ip_address",label:"IP地址",width:"120"}),M(Ee,{prop:"user_agent",label:"用户代理",width:"150","show-overflow-tooltip":""}),M(Ee,{prop:"execution_time",label:"耗时",width:"80"},{default:W(e=>[S(s(e.row.execution_time)+"ms ",1)]),_:1}),M(Ee,{prop:"created_at",label:"操作时间",width:"160"}),M(Ee,{label:"操作",width:"120"},{default:W(e=>[M(Qe,{type:"primary",size:"small",onClick:a=>{return l=e.row,ye.log=l,void(ye.visible=!0);var l}},{default:W(()=>U[32]||(U[32]=[S(" 详情 ",-1)])),_:2,__:[32]},1032,["onClick"]),"failed"===e.row.status?(I(),N(Qe,{key:0,type:"danger",size:"small",onClick:a=>(e.row,void z.confirm("确定要删除这条日志吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{j.success("删除成功"),Pe()}))},{default:W(()=>U[33]||(U[33]=[S(" 删除 ",-1)])),_:2,__:[33]},1032,["onClick"])):B("",!0)]),_:1})]),_:1},8,["data"])),[[oa,xe.value]]),H("div",ie,[M(Xe,{"current-page":fe.page,"onUpdate:currentPage":U[6]||(U[6]=e=>fe.page=e),"page-size":fe.per_page,"onUpdate:pageSize":U[7]||(U[7]=e=>fe.per_page=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:ze.value,onSizeChange:Ne,onCurrentChange:Se},null,8,["current-page","page-size","total"])])]),_:1}),M(aa,{title:"操作详情",modelValue:ye.visible,"onUpdate:modelValue":U[8]||(U[8]=e=>ye.visible=e),width:"800px"},{default:W(()=>[ye.log?(I(),D("div",de,[M($e,{column:2,border:""},{default:W(()=>[M(Ze,{label:"操作ID"},{default:W(()=>[S(s(ye.log.id),1)]),_:1}),M(Ze,{label:"操作用户"},{default:W(()=>[S(s(ye.log.user?.nickname||"系统"),1)]),_:1}),M(Ze,{label:"操作类型"},{default:W(()=>[M(Fe,{type:Ue(ye.log.operation_type)},{default:W(()=>[S(s(Ce(ye.log.operation_type)),1)]),_:1},8,["type"])]),_:1}),M(Ze,{label:"模块"},{default:W(()=>[S(s(Ye(ye.log.module)),1)]),_:1}),M(Ze,{label:"操作描述"},{default:W(()=>[S(s(ye.log.description),1)]),_:1}),M(Ze,{label:"状态"},{default:W(()=>[M(Fe,{type:De(ye.log.status)},{default:W(()=>[S(s(Ie(ye.log.status)),1)]),_:1},8,["type"])]),_:1}),M(Ze,{label:"IP地址"},{default:W(()=>[S(s(ye.log.ip_address),1)]),_:1}),M(Ze,{label:"执行时间"},{default:W(()=>[S(s(ye.log.execution_time)+"ms ",1)]),_:1}),M(Ze,{label:"操作时间"},{default:W(()=>[S(s(ye.log.created_at),1)]),_:1})]),_:1}),H("div",ue,[U[34]||(U[34]=H("h4",null,"用户代理",-1)),M(Le,{type:"textarea",value:ye.log.user_agent,readonly:""},null,8,["value"])]),ye.log.request_data?(I(),D("div",re,[U[35]||(U[35]=H("h4",null,"请求数据",-1)),H("pre",ne,s(JSON.stringify(ye.log.request_data,null,2)),1)])):B("",!0),ye.log.response_data?(I(),D("div",pe,[U[36]||(U[36]=H("h4",null,"响应数据",-1)),H("pre",ce,s(JSON.stringify(ye.log.response_data,null,2)),1)])):B("",!0),ye.log.error_message?(I(),D("div",_e,[U[37]||(U[37]=H("h4",null,"错误信息",-1)),M(ea,{title:ye.log.error_message,type:"error","show-icon":""},null,8,["title"])])):B("",!0)])):B("",!0)]),_:1},8,["modelValue"]),M(aa,{title:"清理日志",modelValue:he.visible,"onUpdate:modelValue":U[14]||(U[14]=e=>he.visible=e),width:"500px"},{footer:W(()=>[H("div",me,[M(Qe,{onClick:U[13]||(U[13]=e=>he.visible=!1)},{default:W(()=>U[41]||(U[41]=[S("取消",-1)])),_:1,__:[41]}),M(Qe,{type:"danger",onClick:Ae,loading:je.value},{default:W(()=>U[42]||(U[42]=[S(" 确认清理 ",-1)])),_:1,__:[42]},8,["loading"])])]),default:W(()=>[M(Re,{model:we,"label-width":"100px"},{default:W(()=>[M(Je,{label:"清理方式"},{default:W(()=>[M(ta,{modelValue:we.type,"onUpdate:modelValue":U[9]||(U[9]=e=>we.type=e)},{default:W(()=>[M(la,{label:"by_date"},{default:W(()=>U[38]||(U[38]=[S("按时间",-1)])),_:1,__:[38]}),M(la,{label:"by_count"},{default:W(()=>U[39]||(U[39]=[S("按数量",-1)])),_:1,__:[39]}),M(la,{label:"by_status"},{default:W(()=>U[40]||(U[40]=[S("按状态",-1)])),_:1,__:[40]})]),_:1},8,["modelValue"])]),_:1}),"by_date"===we.type?(I(),N(Je,{key:0,label:"清理条件"},{default:W(()=>[M(Ke,{modelValue:we.before_date,"onUpdate:modelValue":U[10]||(U[10]=e=>we.before_date=e),type:"date",placeholder:"清理此日期之前的日志"},null,8,["modelValue"])]),_:1})):B("",!0),"by_count"===we.type?(I(),N(Je,{key:1,label:"保留数量"},{default:W(()=>[M(sa,{modelValue:we.keep_count,"onUpdate:modelValue":U[11]||(U[11]=e=>we.keep_count=e),min:1e3,max:1e5},null,8,["modelValue"])]),_:1})):B("",!0),"by_status"===we.type?(I(),N(Je,{key:2,label:"清理状态"},{default:W(()=>[M(Oe,{modelValue:we.status,"onUpdate:modelValue":U[12]||(U[12]=e=>we.status=e),placeholder:"选择要清理的状态"},{default:W(()=>[M(Be,{label:"成功",value:"success"}),M(Be,{label:"失败",value:"failed"}),M(Be,{label:"警告",value:"warning"})]),_:1},8,["modelValue"])]),_:1})):B("",!0),M(Je,null,{default:W(()=>[M(ea,{title:"注意：清理操作不可恢复，请谨慎操作！",type:"warning","show-icon":"",closable:!1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-dd26c41f"]]);export{be as default};
