<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

/**
 * 统一模板基础模型
 * 整合所有类型的模板管理
 */
class Template extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'type',
        'category',
        'description',
        'cover_image',
        'template_data',
        'metadata',
        'tags',
        'source',
        'is_public',
        'is_active',
        'usage_count',
        'success_rate',
        'rating',
        'price',
        'created_by',
        'version',
        'parent_template_id',
    ];

    protected $casts = [
        'template_data' => 'array',
        'metadata' => 'array',
        'tags' => 'array',
        'is_public' => 'boolean',
        'is_active' => 'boolean',
        'usage_count' => 'integer',
        'success_rate' => 'decimal:2',
        'rating' => 'decimal:1',
        'price' => 'decimal:2',
        'version' => 'integer',
    ];

    // 模板类型常量
    const TYPE_GROUP = 'group';
    const TYPE_CONTENT = 'content';
    const TYPE_MARKETING = 'marketing';
    const TYPE_NOTIFICATION = 'notification';
    const TYPE_LANDING_PAGE = 'landing_page';

    // 模板来源常量
    const SOURCE_SYSTEM = 'system';
    const SOURCE_USER = 'user';
    const SOURCE_COMMUNITY = 'community';
    const SOURCE_AI = 'ai';

    // 模板分类常量
    const CATEGORY_BUSINESS = 'business';
    const CATEGORY_EDUCATION = 'education';
    const CATEGORY_SOCIAL = 'social';
    const CATEGORY_ENTERTAINMENT = 'entertainment';
    const CATEGORY_TECHNOLOGY = 'technology';
    const CATEGORY_HEALTH = 'health';
    const CATEGORY_FINANCE = 'finance';

    /**
     * 创建者关联
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 父模板关联
     */
    public function parentTemplate(): BelongsTo
    {
        return $this->belongsTo(Template::class, 'parent_template_id');
    }

    /**
     * 子模板关联
     */
    public function childTemplates(): HasMany
    {
        return $this->hasMany(Template::class, 'parent_template_id');
    }

    /**
     * 模板使用记录
     */
    public function usageRecords(): HasMany
    {
        return $this->hasMany(TemplateUsage::class);
    }

    /**
     * 获取封面图片URL
     */
    public function getCoverImageUrlAttribute(): ?string
    {
        if (!$this->cover_image) {
            return null;
        }

        if (filter_var($this->cover_image, FILTER_VALIDATE_URL)) {
            return $this->cover_image;
        }

        return Storage::url($this->cover_image);
    }

    /**
     * 渲染模板内容
     */
    public function render(array $variables = []): array
    {
        $data = $this->template_data;
        
        if (empty($variables)) {
            return $data;
        }

        return $this->replaceVariablesRecursive($data, $variables);
    }

    /**
     * 递归替换变量
     */
    private function replaceVariablesRecursive($data, array $variables): mixed
    {
        if (is_string($data)) {
            foreach ($variables as $key => $value) {
                $data = str_replace('{{' . $key . '}}', $value, $data);
            }
            return $data;
        }

        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->replaceVariablesRecursive($value, $variables);
            }
            return $data;
        }

        return $data;
    }

    /**
     * 获取模板变量
     */
    public function getVariables(): array
    {
        $variables = [];
        $this->extractVariablesRecursive($this->template_data, $variables);
        return array_unique($variables);
    }

    /**
     * 递归提取变量
     */
    private function extractVariablesRecursive($data, array &$variables): void
    {
        if (is_string($data)) {
            preg_match_all('/\{\{([^}]+)\}\}/', $data, $matches);
            if (!empty($matches[1])) {
                $variables = array_merge($variables, $matches[1]);
            }
        } elseif (is_array($data)) {
            foreach ($data as $value) {
                $this->extractVariablesRecursive($value, $variables);
            }
        }
    }

    /**
     * 验证模板数据
     */
    public function validateData(): bool
    {
        $requiredFields = $this->getRequiredFields();
        
        foreach ($requiredFields as $field) {
            if (!isset($this->template_data[$field]) || empty($this->template_data[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取必填字段
     */
    private function getRequiredFields(): array
    {
        return match($this->type) {
            self::TYPE_GROUP => ['title', 'description', 'price'],
            self::TYPE_CONTENT => ['title', 'content'],
            self::TYPE_MARKETING => ['title', 'content', 'call_to_action'],
            self::TYPE_NOTIFICATION => ['subject', 'content'],
            self::TYPE_LANDING_PAGE => ['title', 'content'],
            default => ['title']
        };
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * 更新成功率
     */
    public function updateSuccessRate(float $rate): void
    {
        $this->update(['success_rate' => $rate]);
    }

    /**
     * 更新评分
     */
    public function updateRating(float $rating): void
    {
        $this->update(['rating' => $rating]);
    }

    /**
     * 作用域：活跃模板
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：公开模板
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：按来源筛选
     */
    public function scopeFromSource($query, string $source)
    {
        return $query->where('source', $source);
    }

    /**
     * 作用域：按标签筛选
     */
    public function scopeWithTag($query, string $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * 作用域：热门模板
     */
    public function scopePopular($query, int $limit = 10)
    {
        return $query->orderBy('usage_count', 'desc')
                    ->orderBy('rating', 'desc')
                    ->limit($limit);
    }

    /**
     * 作用域：高质量模板
     */
    public function scopeHighQuality($query, float $minRating = 4.0, float $minSuccessRate = 0.8)
    {
        return $query->where('rating', '>=', $minRating)
                    ->where('success_rate', '>=', $minSuccessRate);
    }

    /**
     * 获取模板类型列表
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_GROUP => '群组模板',
            self::TYPE_CONTENT => '内容模板',
            self::TYPE_MARKETING => '营销模板',
            self::TYPE_NOTIFICATION => '通知模板',
            self::TYPE_LANDING_PAGE => '落地页模板',
        ];
    }

    /**
     * 获取模板来源列表
     */
    public static function getSources(): array
    {
        return [
            self::SOURCE_SYSTEM => '系统模板',
            self::SOURCE_USER => '用户模板',
            self::SOURCE_COMMUNITY => '社区模板',
            self::SOURCE_AI => 'AI生成',
        ];
    }

    /**
     * 获取模板分类列表
     */
    public static function getCategories(): array
    {
        return [
            self::CATEGORY_BUSINESS => '商务创业',
            self::CATEGORY_EDUCATION => '教育培训',
            self::CATEGORY_SOCIAL => '社交交友',
            self::CATEGORY_ENTERTAINMENT => '娱乐休闲',
            self::CATEGORY_TECHNOLOGY => '技术科技',
            self::CATEGORY_HEALTH => '健康养生',
            self::CATEGORY_FINANCE => '投资理财',
        ];
    }
}