<template>
  <div class="group-create-form">
    <!-- 页面头部 (仅在独立页面模式显示) -->
    <div v-if="mode === 'page'" class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">创建群组</h1>
          <p class="page-subtitle">创建新的微信群组并配置完整的营销功能</p>
        </div>
        <div class="header-actions">
          <el-button @click="handleCancel">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button v-if="showPreview" type="success" @click="handlePreview" :disabled="!form.title">
            <el-icon><View /></el-icon>
            预览效果
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            创建群组
          </el-button>
        </div>
      </div>
    </div>

    <!-- 创建表单 -->
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="default"
      >
        <!-- 基础信息配置 -->
        <el-card v-if="isFieldVisible('basic')" class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>基础信息</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="群组名称" prop="title">
                <el-input 
                  v-model="form.title" 
                  placeholder="请输入群组名称，支持xxx占位符"
                  maxlength="200"
                  show-word-limit
                />
                <div class="form-tip">
                  💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="群组价格" prop="price">
                <el-input-number
                  v-model="form.price"
                  :min="0"
                  :precision="2"
                  placeholder="0.00"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item v-if="isFieldVisible('payment_methods')" label="付款方式" prop="payment_methods">
            <el-checkbox-group v-model="form.payment_methods">
              <el-checkbox value="wechat" border>
                <el-icon><ChatDotRound /></el-icon>
                微信支付
              </el-checkbox>
              <el-checkbox value="alipay" border>
                <el-icon><Money /></el-icon>
                支付宝
              </el-checkbox>
              <el-checkbox value="epay" border>
                <el-icon><CreditCard /></el-icon>
                易支付
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="群组类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择群组类型" style="width: 100%">
                  <el-option label="普通群" value="normal" />
                  <el-option label="VIP群" value="vip" />
                  <el-option label="分销群" value="distribution" />
                  <el-option label="测试群" value="test" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="isFieldVisible('status')" label="发布状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="立即发布" value="active" />
                  <el-option label="保存草稿" value="draft" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="群组描述" prop="description">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="3"
              placeholder="请输入群组描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <!-- 城市定位配置 -->
        <el-card v-if="isFieldVisible('city_location')" class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Location /></el-icon>
              <span>城市定位</span>
              <el-switch 
                v-model="form.auto_city_replace" 
                class="header-switch"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="关闭"
                @change="handleCityToggle"
              />
            </div>
          </template>
          
          <div v-if="form.auto_city_replace">
            <el-form-item label="插入策略">
              <el-select v-model="form.city_insert_strategy" placeholder="选择策略" style="width: 100%">
                <el-option label="自动替换xxx" value="auto" />
                <el-option label="前缀插入" value="prefix" />
                <el-option label="后缀插入" value="suffix" />
                <el-option label="自然插入" value="natural" />
              </el-select>
            </el-form-item>

            <el-form-item label="测试城市替换">
              <el-row :gutter="12">
                <el-col :span="8">
                  <el-input v-model="testCity" placeholder="输入城市名" />
                </el-col>
                <el-col :span="8">
                  <el-button @click="testCityReplacement">测试替换</el-button>
                </el-col>
                <el-col :span="8">
                  <el-input v-model="testResult" placeholder="替换结果" readonly />
                </el-col>
              </el-row>
            </el-form-item>
          </div>
        </el-card>

        <!-- 付费后内容配置 -->
        <el-card v-if="isFieldVisible('paid_content')" class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>付费后内容</span>
            </div>
          </template>

          <el-form-item label="内容类型" prop="paid_content_type">
            <el-radio-group v-model="form.paid_content_type">
              <el-radio value="qr_code">入群二维码</el-radio>
              <el-radio value="image">图片资源</el-radio>
              <el-radio value="link">下载链接</el-radio>
              <el-radio value="document">文档内容</el-radio>
              <el-radio value="video">视频内容</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 二维码配置 -->
          <div v-if="form.paid_content_type === 'qr_code'">
            <el-form-item label="入群二维码" prop="qr_code">
              <el-upload
                class="qr-uploader"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleQRSuccess"
                :before-upload="beforeUpload"
              >
                <img v-if="form.qr_code" :src="form.qr_code" class="qr-image">
                <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：400x400像素，支持jpg/png格式</div>
            </el-form-item>
          </div>

          <!-- 图片资源配置 -->
          <div v-if="form.paid_content_type === 'image'">
            <el-form-item label="付费图片">
              <el-upload
                class="image-uploader"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :file-list="form.paid_images"
                :on-success="handleImageSuccess"
                :before-upload="beforeUpload"
                multiple
              >
                <el-button type="primary">
                  <el-icon><Plus /></el-icon>
                  上传图片
                </el-button>
              </el-upload>
            </el-form-item>
          </div>

          <!-- 链接配置 -->
          <div v-if="form.paid_content_type === 'link'">
            <el-form-item label="下载链接">
              <el-input v-model="form.paid_link" placeholder="请输入下载链接" />
            </el-form-item>
            <el-form-item label="链接描述">
              <el-input v-model="form.paid_link_desc" placeholder="请输入链接描述" />
            </el-form-item>
          </div>

          <!-- 文档内容配置 -->
          <div v-if="form.paid_content_type === 'document'">
            <el-form-item label="文档内容">
              <el-input
                v-model="form.paid_document_content"
                type="textarea"
                :rows="6"
                placeholder="请输入文档内容"
              />
            </el-form-item>
          </div>

          <!-- 视频配置 -->
          <div v-if="form.paid_content_type === 'video'">
            <el-form-item label="视频链接">
              <el-input v-model="form.paid_video_url" placeholder="请输入视频链接" />
            </el-form-item>
            <el-form-item label="视频标题">
              <el-input v-model="form.paid_video_title" placeholder="请输入视频标题" />
            </el-form-item>
            <el-form-item label="视频描述">
              <el-input
                v-model="form.paid_video_desc"
                type="textarea"
                :rows="3"
                placeholder="请输入视频描述"
              />
            </el-form-item>
          </div>
        </el-card>

        <!-- 营销展示配置 -->
        <el-card v-if="isFieldVisible('marketing')" class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Promotion /></el-icon>
              <span>营销展示</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="阅读数显示">
                <el-input v-model="form.read_count_display" placeholder="如：10万+" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="点赞数">
                <el-input-number v-model="form.like_count" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="想看数">
                <el-input-number v-model="form.want_see_count" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="入群按钮文案">
                <el-input v-model="form.button_title" placeholder="如：立即加入群聊" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="头像库选择">
                <el-select v-model="form.avatar_library" style="width: 100%">
                  <el-option label="QQ头像库" value="qq" />
                  <el-option label="微信头像库" value="wechat" />
                  <el-option label="随机头像库" value="random" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="显示类型">
                <el-select v-model="form.display_type" style="width: 100%">
                  <el-option label="标准显示" :value="1" />
                  <el-option label="简洁显示" :value="2" />
                  <el-option label="详细显示" :value="3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="微信访问权限">
                <el-switch
                  v-model="form.wx_accessible"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="允许"
                  inactive-text="限制"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 内容管理配置 (完整富文本编辑器版本) -->
        <el-card v-if="isFieldVisible('content')" class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>内容管理</span>
            </div>
          </template>

          <el-form-item label="群简介标题">
            <el-input v-model="form.group_intro_title" placeholder="群简介" />
          </el-form-item>

          <el-form-item label="群简介内容">
            <div class="rich-editor-container">
              <div class="editor-actions">
                <el-button size="small" type="primary" @click="insertTemplate('intro')">
                  <el-icon><Star /></el-icon>
                  插入模板
                </el-button>
                <el-button size="small" @click="insertImage('intro')">
                  <el-icon><Picture /></el-icon>
                  插入图片
                </el-button>
                <el-button size="small" @click="form.group_intro_content = ''" type="danger" plain>
                  <el-icon><Delete /></el-icon>
                  清空内容
                </el-button>
              </div>

              <div class="rich-text-editor-wrapper">
                <RichTextEditor
                  v-model="form.group_intro_content"
                  :height="200"
                  placeholder="详细介绍群组的价值和特色，支持富文本格式。可以添加粗体、斜体、列表、链接、图片等内容。"
                  :max-length="2000"
                />
              </div>

              <div class="editor-help">
                <el-icon><InfoFilled /></el-icon>
                <span>支持富文本格式，可以添加<strong>粗体</strong>、<em>斜体</em>、列表、链接等内容</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="FAQ标题">
            <el-input v-model="form.faq_title" placeholder="常见问题" />
          </el-form-item>

          <el-form-item label="常见问题">
            <div class="rich-editor-container">
              <div class="editor-actions">
                <el-button size="small" type="primary" @click="insertTemplate('faq')">
                  <el-icon><Star /></el-icon>
                  插入FAQ模板
                </el-button>
                <el-button size="small" @click="form.faq_content = ''" type="danger" plain>
                  <el-icon><Delete /></el-icon>
                  清空内容
                </el-button>
              </div>

              <div class="rich-text-editor-wrapper">
                <RichTextEditor
                  v-model="form.faq_content"
                  :height="180"
                  placeholder="输入常见问题和答案，支持富文本格式。建议格式：Q: 问题内容 A: 答案内容"
                  :max-length="3000"
                />
              </div>

              <div class="editor-help">
                <el-icon><InfoFilled /></el-icon>
                <span>建议格式：<strong>Q: 问题内容</strong> <br> <strong>A: 答案内容</strong>，每个问答占一行</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="群友评论">
            <div class="rich-editor-container">
              <div class="editor-actions">
                <el-button size="small" type="primary" @click="insertTemplate('reviews')">
                  <el-icon><Star /></el-icon>
                  插入评价模板
                </el-button>
                <el-button size="small" @click="form.member_reviews = ''" type="danger" plain>
                  <el-icon><Delete /></el-icon>
                  清空内容
                </el-button>
              </div>

              <div class="rich-text-editor-wrapper">
                <RichTextEditor
                  v-model="form.member_reviews"
                  :height="160"
                  placeholder="输入群友评价内容，支持富文本格式。建议格式：用户名：评价内容 ⭐⭐⭐⭐⭐"
                  :max-length="2000"
                />
              </div>

              <div class="editor-help">
                <el-icon><InfoFilled /></el-icon>
                <span>建议格式：<strong>用户名：评价内容 ⭐⭐⭐⭐⭐</strong>，每个评价占一行</span>
              </div>
            </div>
          </el-form-item>
        </el-card>

        <!-- 虚拟数据配置 -->
        <el-card v-if="isFieldVisible('virtual')" class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>虚拟数据</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="虚拟成员数">
                <el-input-number v-model="form.virtual_members" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="虚拟订单数">
                <el-input-number v-model="form.virtual_orders" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="虚拟收入">
                <el-input-number
                  v-model="form.virtual_income"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="今日浏览量">
                <el-input-number v-model="form.today_views" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示虚拟活动">
                <el-switch
                  v-model="form.show_virtual_activity"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示成员头像">
                <el-switch
                  v-model="form.show_member_avatars"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="显示"
                  inactive-text="隐藏"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="显示成员评价">
            <el-switch
              v-model="form.show_member_reviews"
              :active-value="1"
              :inactive-value="0"
              active-text="显示"
              inactive-text="隐藏"
            />
          </el-form-item>
        </el-card>

        <!-- 客服信息配置 -->
        <el-card v-if="isFieldVisible('customer_service')" class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Service /></el-icon>
              <span>客服信息</span>
              <el-switch
                v-model="form.show_customer_service"
                class="header-switch"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="关闭"
              />
            </div>
          </template>

          <div v-if="form.show_customer_service">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客服标题">
                  <el-input v-model="form.customer_service_title" placeholder="联系客服" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服描述">
                  <el-input v-model="form.customer_service_desc" placeholder="有问题请联系客服" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客服头像">
                  <el-upload
                    class="avatar-uploader"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeUpload"
                  >
                    <img v-if="form.customer_service_avatar" :src="form.customer_service_avatar" class="avatar">
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服二维码">
                  <el-upload
                    class="qr-uploader"
                    :action="uploadUrl"
                    :headers="uploadHeaders"
                    :show-file-list="false"
                    :on-success="handleServiceQRSuccess"
                    :before-upload="beforeUpload"
                  >
                    <img v-if="form.customer_service_qr" :src="form.customer_service_qr" class="qr-image">
                    <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="广告二维码">
              <el-upload
                class="qr-uploader"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleAdQRSuccess"
                :before-upload="beforeUpload"
              >
                <img v-if="form.ad_qr_code" :src="form.ad_qr_code" class="qr-image">
                <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">用于推广的二维码</div>
            </el-form-item>
          </div>
        </el-card>

        <!-- 对话框模式的底部按钮 -->
        <div v-if="mode === 'dialog'" class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button v-if="showPreview" type="success" @click="handlePreview" :disabled="!form.title">
            <el-icon><View /></el-icon>
            预览
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            创建群组
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 完整预览对话框 -->
    <el-dialog v-model="showPreviewDialog" title="群组完整预览" width="800px" class="preview-dialog">
      <div v-if="previewData" class="preview-content">
        <!-- 群组头部信息 -->
        <div class="preview-header">
          <h2 class="group-title">{{ previewData.title }}</h2>
          <div class="group-stats">
            <span class="stat-item">
              <el-icon><View /></el-icon>
              {{ previewData.read_count_display }}
            </span>
            <span class="stat-item">
              <el-icon><Star /></el-icon>
              {{ previewData.like_count }}
            </span>
            <span class="stat-item">
              <el-icon><ChatDotRound /></el-icon>
              {{ previewData.want_see_count }}
            </span>
          </div>
          <div class="group-price">
            <span class="price-label">群组价格:</span>
            <span class="price-value">¥{{ previewData.price }}</span>
          </div>
        </div>

        <!-- 群组描述 -->
        <div v-if="previewData.description" class="preview-section">
          <h4>群组描述</h4>
          <p>{{ previewData.description }}</p>
        </div>

        <!-- 群简介 -->
        <div v-if="previewData.group_intro_content" class="preview-section">
          <h4>{{ previewData.group_intro_title || '群简介' }}</h4>
          <div class="rich-content" v-html="previewData.group_intro_content"></div>
        </div>

        <!-- 常见问题 -->
        <div v-if="previewData.faq_content" class="preview-section">
          <h4>{{ previewData.faq_title || '常见问题' }}</h4>
          <div class="rich-content" v-html="previewData.faq_content"></div>
        </div>

        <!-- 群友评价 -->
        <div v-if="previewData.member_reviews" class="preview-section">
          <h4>群友评价</h4>
          <div class="rich-content" v-html="previewData.member_reviews"></div>
        </div>

        <!-- 虚拟数据展示 -->
        <div class="preview-section">
          <h4>群组数据</h4>
          <div class="virtual-stats">
            <div class="stat-card">
              <div class="stat-number">{{ previewData.virtual_members }}</div>
              <div class="stat-label">群成员</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ previewData.virtual_orders }}</div>
              <div class="stat-label">订单数</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">¥{{ previewData.virtual_income }}</div>
              <div class="stat-label">总收入</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ previewData.today_views }}</div>
              <div class="stat-label">今日浏览</div>
            </div>
          </div>
        </div>

        <!-- 虚拟成员展示 -->
        <div v-if="previewData.generated_members" class="preview-section">
          <h4>群成员展示</h4>
          <div class="member-avatars">
            <div
              v-for="member in previewData.generated_members.slice(0, 10)"
              :key="member.id"
              class="member-avatar"
              :title="member.name"
            >
              <img :src="member.avatar" :alt="member.name">
            </div>
            <div v-if="previewData.generated_members.length > 10" class="more-members">
              +{{ previewData.generated_members.length - 10 }}
            </div>
          </div>
        </div>

        <!-- 客服信息 -->
        <div v-if="previewData.show_customer_service" class="preview-section">
          <h4>{{ previewData.customer_service_title || '联系客服' }}</h4>
          <p>{{ previewData.customer_service_desc || '有问题请联系客服' }}</p>
        </div>

        <!-- 入群按钮 -->
        <div class="preview-footer">
          <el-button type="primary" size="large" class="join-button">
            {{ previewData.button_title }}
          </el-button>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPreviewDialog = false">关闭预览</el-button>
          <el-button type="primary" @click="showPreviewDialog = false">
            确认效果
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  ArrowLeft, Check, Plus, Location, InfoFilled,
  Promotion, Document, DataAnalysis, Service, Setting, View,
  Star, Picture, Delete, ChatDotRound, Money, CreditCard
} from '@element-plus/icons-vue'
import { createGroup } from '@/api/community'
import RichTextEditor from '@/components/RichTextEditor.vue'

// Props定义
const props = defineProps({
  // 显示模式：page(独立页面) | dialog(对话框)
  mode: {
    type: String,
    default: 'dialog'
  },
  // 用户角色
  userRole: {
    type: String,
    default: 'admin',
    validator: (value) => ['admin', 'distributor', 'owner'].includes(value)
  },
  // 默认值配置
  defaultValues: {
    type: Object,
    default: () => ({})
  },
  // 隐藏字段列表
  hiddenFields: {
    type: Array,
    default: () => []
  },
  // 是否显示预览功能
  showPreview: {
    type: Boolean,
    default: true
  },
  // 是否显示模板功能
  showTemplates: {
    type: Boolean,
    default: true
  }
})

// Emits定义
const emit = defineEmits(['success', 'cancel'])

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const showPreviewDialog = ref(false)
const previewData = ref(null)
const testCity = ref('北京')
const testResult = ref('')

// 角色配置（基于完整功能）
const roleConfigs = {
  admin: {
    hiddenFields: [],
    defaultValues: {
      type: 'normal',
      auto_city_replace: 0,
      show_customer_service: 1
    },
    permissions: ['basic', 'paid_content', 'city_location', 'marketing', 'content', 'virtual', 'customer_service']
  },
  distributor: {
    hiddenFields: ['show_customer_service', 'ad_qr_code', 'virtual_income'],
    defaultValues: {
      type: 'distribution',
      auto_city_replace: 1,
      show_customer_service: 0,
      read_count_display: '5万+',
      like_count: 666,
      want_see_count: 888,
      button_title: '立即加入分销群'
    },
    permissions: ['basic', 'paid_content', 'city_location', 'marketing', 'content', 'virtual']
  },
  owner: {
    hiddenFields: ['virtual_income'],
    defaultValues: {
      type: 'community',
      auto_city_replace: 1,
      show_customer_service: 1,
      read_count_display: '8万+',
      like_count: 1500,
      want_see_count: 1000,
      button_title: '加入学习群'
    },
    permissions: ['basic', 'paid_content', 'city_location', 'marketing', 'content', 'virtual', 'customer_service']
  }
}

// 获取当前角色配置
const currentRoleConfig = computed(() => roleConfigs[props.userRole] || roleConfigs.admin)

// 表单数据（基于GroupAdd.vue的完整功能）
const form = reactive({
  // 基础信息
  title: '',
  price: 0,
  payment_methods: ['wechat', 'alipay'], // 默认支持微信和支付宝
  type: 'normal',
  status: 'active',
  description: '',

  // 付费后内容配置
  paid_content_type: 'qr_code', // qr_code, image, link, document, video
  qr_code: '', // 入群二维码
  paid_images: [], // 付费图片资源
  paid_link: '', // 下载链接
  paid_link_desc: '', // 链接描述
  paid_document_content: '', // 文档内容
  paid_video_url: '', // 视频链接
  paid_video_title: '', // 视频标题
  paid_video_desc: '', // 视频描述

  // 城市定位配置
  auto_city_replace: 0,
  city_insert_strategy: 'auto',

  // 营销展示配置
  read_count_display: '10万+',
  like_count: 888,
  want_see_count: 666,
  button_title: '立即加入群聊',
  avatar_library: 'qq',
  display_type: 1,
  wx_accessible: 1,

  // 内容配置
  group_intro_title: '群简介',
  group_intro_content: '',
  faq_title: '常见问题',
  faq_content: '',
  member_reviews: '',

  // 虚拟数据配置
  virtual_members: 100,
  virtual_orders: 50,
  virtual_income: 5000.00,
  today_views: 1200,
  show_virtual_activity: 1,
  show_member_avatars: 1,
  show_member_reviews: 1,

  // 客服配置
  show_customer_service: 1,
  customer_service_title: '',
  customer_service_desc: '',
  customer_service_avatar: '',
  customer_service_qr: '',
  ad_qr_code: '',

  ...props.defaultValues,
  ...currentRoleConfig.value.defaultValues
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入群组价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择群组类型', trigger: 'change' }
  ]
}

// 字段可见性判断
const isFieldVisible = (fieldName) => {
  const allHiddenFields = [...props.hiddenFields, ...currentRoleConfig.value.hiddenFields]
  return !allHiddenFields.includes(fieldName) && currentRoleConfig.value.permissions.includes(fieldName)
}

// 方法
const handleCityToggle = (value) => {
  if (value && !form.title.includes('xxx')) {
    ElMessage.info('建议在群组名称中使用"xxx"作为城市占位符，如："xxx交流群"')
  }
}

const testCityReplacement = () => {
  if (!form.title || !testCity.value) {
    ElMessage.warning('请输入群组名称和测试城市')
    return
  }

  testResult.value = form.title.replace(/xxx/g, testCity.value)
  ElMessage.success('城市替换测试完成')
}

// 上传相关配置
const uploadUrl = computed(() => '/api/upload')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}))

// 上传处理方法
const beforeUpload = (file) => {
  const isImage = file.type.indexOf('image/') === 0
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleQRSuccess = (response) => {
  form.qr_code = response.url
  ElMessage.success('二维码上传成功')
}

const handleImageSuccess = (response) => {
  form.paid_images.push({
    name: response.name,
    url: response.url
  })
  ElMessage.success('图片上传成功')
}

const handleAvatarSuccess = (response) => {
  form.customer_service_avatar = response.url
  ElMessage.success('头像上传成功')
}

const handleServiceQRSuccess = (response) => {
  form.customer_service_qr = response.url
  ElMessage.success('客服二维码上传成功')
}

const handleAdQRSuccess = (response) => {
  form.ad_qr_code = response.url
  ElMessage.success('广告二维码上传成功')
}

// 模板系统 (基于GroupAdd.vue)
const marketingTemplates = ref([])

const fetchMarketingTemplates = async () => {
  try {
    // 模拟获取营销模板数据
    marketingTemplates.value = [
      {
        id: 1,
        name: '商务交流模板',
        config: {
          read_count_display: '10万+',
          like_count: 888,
          want_see_count: 666,
          button_title: '立即加入商务群',
          group_intro_title: '商务交流群',
          group_intro_content: '专业的商务交流平台，拓展人脉，共享资源',
          virtual_members: 150,
          virtual_orders: 75
        }
      },
      {
        id: 2,
        name: '技术分享模板',
        config: {
          read_count_display: '5万+',
          like_count: 666,
          want_see_count: 888,
          button_title: '加入技术群',
          group_intro_title: '技术交流群',
          group_intro_content: '技术大牛聚集地，分享最新技术动态',
          virtual_members: 200,
          virtual_orders: 100
        }
      },
      {
        id: 3,
        name: '学习教育模板',
        config: {
          read_count_display: '8万+',
          like_count: 1500,
          want_see_count: 1000,
          button_title: '加入学习群',
          group_intro_title: '学习交流群',
          group_intro_content: '专业的学习交流平台，共同进步成长',
          virtual_members: 180,
          virtual_orders: 90
        }
      }
    ]
  } catch (error) {
    console.error('获取营销模板失败:', error)
  }
}

// 富文本编辑器模板插入功能
const insertTemplate = (type) => {
  let template = ''

  switch (type) {
    case 'intro':
      template = `<h3>🎯 群组特色</h3>
<p>• <strong>专业交流</strong>：汇聚行业精英，分享最新资讯</p>
<p>• <strong>资源共享</strong>：独家资料、工具、经验分享</p>
<p>• <strong>人脉拓展</strong>：结识志同道合的朋友</p>
<p>• <strong>持续成长</strong>：定期活动、培训、讲座</p>

<h3>💎 群组价值</h3>
<p>• 获得行业内部消息和机会</p>
<p>• 学习最新的专业技能</p>
<p>• 建立有价值的人脉关系</p>
<p>• 参与高质量的讨论和交流</p>`
      form.group_intro_content = template
      break

    case 'faq':
      template = `<div><strong>Q: 这个群主要讨论什么内容？</strong></div>
<div>A: 我们主要分享行业资讯、专业技能、实用工具和经验心得，致力于为成员提供有价值的内容。</div>
<br>
<div><strong>Q: 群内有什么规则吗？</strong></div>
<div>A: 我们倡导友善交流、互相尊重，禁止发布广告、恶意信息等。具体规则入群后会详细说明。</div>
<br>
<div><strong>Q: 如何参与群内活动？</strong></div>
<div>A: 群内会定期组织线上分享、讨论活动，所有成员都可以积极参与，也欢迎主动分享有价值的内容。</div>
<br>
<div><strong>Q: 遇到问题如何联系管理员？</strong></div>
<div>A: 可以在群内@管理员，或者私信联系。我们会及时回复和处理大家的问题。</div>`
      form.faq_content = template
      break

    case 'reviews':
      template = `<div><strong>张先生</strong>：群里的资源真的很棒，学到了很多实用的技巧！⭐⭐⭐⭐⭐</div>
<br>
<div><strong>李女士</strong>：群主很用心，经常分享有价值的内容，强烈推荐！⭐⭐⭐⭐⭐</div>
<br>
<div><strong>王总</strong>：通过这个群认识了很多同行朋友，合作机会很多。⭐⭐⭐⭐⭐</div>
<br>
<div><strong>陈经理</strong>：群内氛围很好，大家都很乐于分享，收获满满。⭐⭐⭐⭐⭐</div>`
      form.member_reviews = template
      break
  }

  ElMessage.success('模板内容已插入')
}

const insertImage = (type) => {
  ElMessage.info('图片上传功能开发中，敬请期待')
}

// 虚拟成员生成功能
const generateVirtualMembers = () => {
  const avatars = [
    'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
  ]

  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']

  return Array.from({ length: Math.min(form.virtual_members, 20) }, (_, index) => ({
    id: index + 1,
    name: names[index % names.length],
    avatar: avatars[index % avatars.length]
  }))
}

const handlePreview = () => {
  // 生成虚拟成员数据
  const virtualMembers = generateVirtualMembers()

  previewData.value = {
    // 基础信息
    title: testResult.value || form.title,
    price: form.price,
    description: form.description,
    type: form.type,

    // 营销展示数据
    read_count_display: form.read_count_display,
    like_count: form.like_count,
    want_see_count: form.want_see_count,
    button_title: form.button_title,

    // 内容数据
    group_intro_title: form.group_intro_title,
    group_intro_content: form.group_intro_content,
    faq_title: form.faq_title,
    faq_content: form.faq_content,
    member_reviews: form.member_reviews,

    // 虚拟数据
    virtual_members: form.virtual_members,
    virtual_orders: form.virtual_orders,
    virtual_income: form.virtual_income,
    today_views: form.today_views,

    // 客服信息
    show_customer_service: form.show_customer_service,
    customer_service_title: form.customer_service_title,
    customer_service_desc: form.customer_service_desc,

    // 付费内容
    paid_content_type: form.paid_content_type,
    qr_code: form.qr_code,

    // 生成的虚拟成员
    generated_members: virtualMembers
  }
  showPreviewDialog.value = true
}

const handleCancel = () => {
  if (props.mode === 'page') {
    router.go(-1)
  } else {
    emit('cancel')
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    ...props.defaultValues,
    ...currentRoleConfig.value.defaultValues
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      ...form,
      user_role: props.userRole
    }
    
    // 调用API创建群组
    const response = await createGroup(submitData)
    
    if (response.code === 200) {
      ElMessage.success('群组创建成功！')
      emit('success', response.data)
      
      if (props.mode === 'page') {
        // 根据角色跳转到不同页面
        const redirectMap = {
          admin: '/community/groups',
          distributor: '/distributor/group-management',
          owner: '/owner/group-dashboard'
        }
        router.push(redirectMap[props.userRole] || '/community/groups')
      }
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建群组失败:', error)
    ElMessage.error('创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  // 应用默认值
  Object.assign(form, {
    ...props.defaultValues,
    ...currentRoleConfig.value.defaultValues
  })

  // 初始化模板系统
  fetchMarketingTemplates()
})
</script>

<style lang="scss" scoped>
.group-create-form {
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
    }
    
    .page-subtitle {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .config-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      
      .header-switch {
        margin-left: auto;
      }
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #ebeef5;
  }
  
  .preview-content {
    h3 {
      margin-top: 0;
      color: #1f2937;
    }

    p {
      margin: 8px 0;
      color: #4b5563;
    }
  }

  /* 上传组件样式 */
  .qr-uploader, .avatar-uploader, .image-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .qr-uploader .el-upload {
    width: 120px;
    height: 120px;
  }

  .avatar-uploader .el-upload {
    width: 80px;
    height: 80px;
  }

  .qr-image, .avatar {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }

  .qr-uploader-icon, .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  /* 富文本编辑器样式 */
  .rich-editor-container {
    .editor-actions {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
    }

    .rich-text-editor-wrapper {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
    }

    .editor-help {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }

  /* 预览对话框样式 */
  .preview-dialog {
    .preview-content {
      max-height: 70vh;
      overflow-y: auto;

      .preview-header {
        text-align: center;
        padding: 20px 0;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 20px;

        .group-title {
          margin: 0 0 12px 0;
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
        }

        .group-stats {
          display: flex;
          justify-content: center;
          gap: 20px;
          margin-bottom: 12px;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #6b7280;
            font-size: 14px;
          }
        }

        .group-price {
          .price-label {
            color: #6b7280;
            margin-right: 8px;
          }

          .price-value {
            font-size: 20px;
            font-weight: 600;
            color: #ef4444;
          }
        }
      }

      .preview-section {
        margin-bottom: 24px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
        }

        .rich-content {
          line-height: 1.6;
          color: #4b5563;

          h3 {
            color: #1f2937;
            margin: 16px 0 8px 0;
          }

          p {
            margin: 8px 0;
          }

          strong {
            color: #1f2937;
          }
        }

        .virtual-stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 16px;

          .stat-card {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;

            .stat-number {
              font-size: 20px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 4px;
            }

            .stat-label {
              font-size: 12px;
              color: #6b7280;
            }
          }
        }

        .member-avatars {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .member-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #e5e7eb;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .more-members {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #6b7280;
            border: 2px solid #e5e7eb;
          }
        }
      }

      .preview-footer {
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid #ebeef5;

        .join-button {
          min-width: 200px;
          height: 48px;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .group-create-form {
    .page-header .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }

    .dialog-footer {
      flex-wrap: wrap;
      gap: 8px;

      .el-button {
        flex: 1;
        min-width: 80px;
      }
    }

    .qr-uploader .el-upload,
    .avatar-uploader .el-upload {
      width: 100px;
      height: 100px;
    }
  }
}
</style>
