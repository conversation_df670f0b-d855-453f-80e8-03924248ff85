<template>
  <div class="app-container">
    <!-- 系统状态概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon server-icon">
              <i class="el-icon-monitor"></i>
            </div>
            <div class="status-content">
              <div class="status-title">服务器状态</div>
              <div class="status-value" :class="systemStatus.server_status ? 'text-success' : 'text-danger'">
                {{ systemStatus.server_status ? '运行正常' : '异常' }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon cpu-icon">
              <i class="el-icon-cpu"></i>
            </div>
            <div class="status-content">
              <div class="status-title">CPU使用率</div>
              <div class="status-value">{{ systemStatus.cpu_usage }}%</div>
              <el-progress :percentage="systemStatus.cpu_usage" :color="getCpuColor(systemStatus.cpu_usage)" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon memory-icon">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="status-content">
              <div class="status-title">内存使用</div>
              <div class="status-value">{{ systemStatus.memory_usage }}%</div>
              <el-progress :percentage="systemStatus.memory_usage" :color="getMemoryColor(systemStatus.memory_usage)" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon disk-icon">
              <i class="el-icon-folder"></i>
            </div>
            <div class="status-content">
              <div class="status-title">磁盘空间</div>
              <div class="status-value">{{ systemStatus.disk_usage }}%</div>
              <el-progress :percentage="systemStatus.disk_usage" :color="getDiskColor(systemStatus.disk_usage)" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时监控图表 -->
    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>📊 CPU & 内存监控</span>
              <el-button type="text" @click="refreshCharts">刷新</el-button>
            </div>
          </template>
          <v-chart class="chart" :option="performanceChartOptions" autoresize />
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>🌐 网络流量监控</span>
            </div>
          </template>
          <v-chart class="chart" :option="networkChartOptions" autoresize />
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务状态监控 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>⚙️ 服务状态监控</span>
          <el-button type="primary" @click="refreshServices">刷新状态</el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8" v-for="service in services" :key="service.name">
          <div class="service-item">
            <div class="service-header">
              <div class="service-name">{{ service.name }}</div>
              <el-tag :type="service.status === 'running' ? 'success' : 'danger'">
                {{ service.status === 'running' ? '运行中' : '已停止' }}
              </el-tag>
            </div>
            <div class="service-info">
              <div class="info-item">
                <span class="info-label">CPU:</span>
                <span class="info-value">{{ service.cpu }}%</span>
              </div>
              <div class="info-item">
                <span class="info-label">内存:</span>
                <span class="info-value">{{ service.memory }}MB</span>
              </div>
              <div class="info-item">
                <span class="info-label">运行时间:</span>
                <span class="info-value">{{ service.uptime }}</span>
              </div>
            </div>
            <div class="service-actions">
              <el-button type="success" size="small" v-if="service.status !== 'running'" @click="startService(service)">
                启动
              </el-button>
              <el-button type="warning" size="small" v-if="service.status === 'running'" @click="restartService(service)">
                重启
              </el-button>
              <el-button type="danger" size="small" v-if="service.status === 'running'" @click="stopService(service)">
                停止
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 系统日志 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>📋 系统日志</span>
          <div>
            <el-select v-model="logLevel" placeholder="选择日志级别" style="width: 120px; margin-right: 10px">
              <el-option label="全部" value="" />
              <el-option label="错误" value="error" />
              <el-option label="警告" value="warning" />
              <el-option label="信息" value="info" />
              <el-option label="调试" value="debug" />
            </el-select>
            <el-button type="primary" @click="refreshLogs">刷新日志</el-button>
            <el-button type="warning" @click="clearLogs">清空日志</el-button>
          </div>
        </div>
      </template>
      
      <div class="log-container">
        <div class="log-item" v-for="log in logs" :key="log.id" :class="'log-' + log.level">
          <div class="log-time">{{ log.created_at }}</div>
          <div class="log-level">
            <el-tag :type="getLogLevelType(log.level)" size="small">{{ log.level.toUpperCase() }}</el-tag>
          </div>
          <div class="log-message">{{ log.message }}</div>
        </div>
      </div>
      
      <div class="log-pagination">
        <el-pagination
          v-model:current-page="logPage"
          v-model:page-size="logPageSize"
          :page-sizes="[20, 50, 100, 200]"
          :small="false"
          :disabled="false"
          :background="false"
          layout="total, sizes, prev, pager, next, jumper"
          :total="logTotal"
          @size-change="handleLogSizeChange"
          @current-change="handleLogCurrentChange"
        />
      </div>
    </el-card>

    <!-- 系统操作工具 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>🔧 系统工具</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="tool-item">
            <h4>🗑️ 清理缓存</h4>
            <p>清理系统缓存文件，释放存储空间</p>
            <el-button type="warning" @click="clearCache" :loading="clearing">清理缓存</el-button>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="tool-item">
            <h4>🔄 重启队列</h4>
            <p>重启后台任务队列处理器</p>
            <el-button type="info" @click="restartQueue" :loading="restarting">重启队列</el-button>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="tool-item">
            <h4>📊 生成报表</h4>
            <p>生成系统运行状况报表</p>
            <el-button type="success" @click="generateReport" :loading="generating">生成报表</el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import VChart from "vue-echarts";
import { getSystemInfo, clearSystemCache, restartSystemQueue } from '@/api/system'

const systemStatus = reactive({
  server_status: true,
  cpu_usage: 35,
  memory_usage: 68,
  disk_usage: 42
})

const services = ref([
  { name: 'Laravel应用', status: 'running', cpu: 15, memory: 256, uptime: '2天3小时' },
  { name: 'MySQL数据库', status: 'running', cpu: 8, memory: 512, uptime: '5天12小时' },
  { name: 'Redis缓存', status: 'running', cpu: 3, memory: 128, uptime: '5天12小时' },
  { name: 'Nginx服务', status: 'running', cpu: 2, memory: 64, uptime: '5天12小时' },
  { name: '队列处理器', status: 'running', cpu: 12, memory: 192, uptime: '1天6小时' },
  { name: '定时任务', status: 'running', cpu: 1, memory: 32, uptime: '5天12小时' }
])

const logs = ref([])
const logLevel = ref('')
const logPage = ref(1)
const logPageSize = ref(20)
const logTotal = ref(0)

const clearing = ref(false)
const restarting = ref(false)
const generating = ref(false)

const performanceChartOptions = ref({})
const networkChartOptions = ref({})

let refreshTimer = null

// 获取颜色函数
const getCpuColor = (value) => {
  if (value < 50) return '#67C23A'
  if (value < 80) return '#E6A23C'
  return '#F56C6C'
}

const getMemoryColor = (value) => {
  if (value < 60) return '#67C23A'
  if (value < 85) return '#E6A23C'
  return '#F56C6C'
}

const getDiskColor = (value) => {
  if (value < 70) return '#67C23A'
  if (value < 90) return '#E6A23C'
  return '#F56C6C'
}

const getLogLevelType = (level) => {
  const types = {
    error: 'danger',
    warning: 'warning',
    info: 'info',
    debug: 'info'
  }
  return types[level] || 'info'
}

// 刷新图表
const refreshCharts = () => {
  // 更新性能图表
  performanceChartOptions.value = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['CPU使用率', '内存使用率']
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      max: 100
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        data: [20, 25, 30, 45, 35, 28, 32]
      },
      {
        name: '内存使用率',
        type: 'line',
        data: [50, 55, 60, 75, 68, 62, 65]
      }
    ]
  }
  
  // 更新网络图表
  networkChartOptions.value = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['入站流量', '出站流量']
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '入站流量',
        type: 'bar',
        data: [120, 200, 150, 800, 700, 550, 400]
      },
      {
        name: '出站流量',
        type: 'bar',
        data: [80, 150, 100, 600, 500, 400, 300]
      }
    ]
  }
}

// 刷新服务状态
const refreshServices = async () => {
  try {
    // 模拟API调用
    ElMessage.success('服务状态已刷新')
  } catch (error) {
    ElMessage.error('刷新服务状态失败')
  }
}

// 服务操作
const startService = (service) => {
  ElMessageBox.confirm(`确定要启动 ${service.name} 吗？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    service.status = 'running'
    ElMessage.success(`${service.name} 已启动`)
  })
}

const restartService = (service) => {
  ElMessageBox.confirm(`确定要重启 ${service.name} 吗？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`${service.name} 已重启`)
  })
}

const stopService = (service) => {
  ElMessageBox.confirm(`确定要停止 ${service.name} 吗？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    service.status = 'stopped'
    ElMessage.success(`${service.name} 已停止`)
  })
}

// 日志相关
const refreshLogs = () => {
  logs.value = [
    { id: 1, level: 'info', message: '系统启动完成', created_at: '2024-01-01 12:00:00' },
    { id: 2, level: 'warning', message: 'CPU使用率较高', created_at: '2024-01-01 12:05:00' },
    { id: 3, level: 'error', message: '数据库连接失败', created_at: '2024-01-01 12:10:00' }
  ]
  logTotal.value = logs.value.length
}

const clearLogs = () => {
  ElMessageBox.confirm('确定要清空所有日志吗？', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    logs.value = []
    logTotal.value = 0
    ElMessage.success('日志已清空')
  })
}

const handleLogSizeChange = (size) => {
  logPageSize.value = size
  refreshLogs()
}

const handleLogCurrentChange = (page) => {
  logPage.value = page
  refreshLogs()
}

// 系统工具
const clearCache = async () => {
  clearing.value = true
  try {
    await clearSystemCache()
    ElMessage.success('缓存清理成功')
  } catch (error) {
    ElMessage.error('缓存清理失败')
  } finally {
    clearing.value = false
  }
}

const restartQueue = async () => {
  restarting.value = true
  try {
    await restartSystemQueue()
    ElMessage.success('队列重启成功')
  } catch (error) {
    ElMessage.error('队列重启失败')
  } finally {
    restarting.value = false
  }
}

const generateReport = async () => {
  generating.value = true
  try {
    // 模拟生成报表
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('报表生成成功')
  } catch (error) {
    ElMessage.error('报表生成失败')
  } finally {
    generating.value = false
  }
}

// 自动刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    // 更新系统状态
    systemStatus.cpu_usage = Math.floor(Math.random() * 100)
    systemStatus.memory_usage = Math.floor(Math.random() * 100)
    systemStatus.disk_usage = Math.floor(Math.random() * 100)
  }, 5000)
}

onMounted(() => {
  refreshCharts()
  refreshLogs()
  startAutoRefresh()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.status-card {
  height: 120px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 10px;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.server-icon { background: #409EFF; }
.cpu-icon { background: #67C23A; }
.memory-icon { background: #E6A23C; }
.disk-icon { background: #F56C6C; }

.status-content {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.status-value {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.text-success { color: #67C23A; }
.text-danger { color: #F56C6C; }

.chart {
  height: 300px;
}

.service-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.service-name {
  font-weight: bold;
  font-size: 16px;
}

.service-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.info-label {
  color: #909399;
}

.info-value {
  font-weight: bold;
}

.service-actions {
  text-align: right;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  width: 150px;
  color: #909399;
  font-size: 12px;
}

.log-level {
  width: 80px;
  margin-right: 10px;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.log-error { border-left: 3px solid #F56C6C; }
.log-warning { border-left: 3px solid #E6A23C; }
.log-info { border-left: 3px solid #409EFF; }
.log-debug { border-left: 3px solid #909399; }

.log-pagination {
  margin-top: 20px;
  text-align: center;
}

.tool-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.tool-item h4 {
  margin-bottom: 10px;
  color: #303133;
}

.tool-item p {
  margin-bottom: 15px;
  color: #909399;
  font-size: 14px;
}
</style> 