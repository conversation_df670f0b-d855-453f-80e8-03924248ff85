<template>
  <el-dialog
    v-model="dialogVisible"
    title="推广链接详情"
    width="1000px"
    :close-on-click-modal="false"
  >
    <div v-if="link" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><InfoFilled /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>链接标题：</label>
              <span>{{ link.title || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>短链接：</label>
              <div class="link-info">
                <el-tag size="small" type="info">{{ link.short_code }}</el-tag>
                <span class="link-url">{{ link.short_url }}</span>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="copyToClipboard(link.short_url)"
                >
                  复制
                </el-button>
              </div>
            </div>
            <div class="info-item">
              <label>关联群组：</label>
              <span>{{ link.group?.title }}</span>
            </div>
            <div class="info-item">
              <label>创建者：</label>
              <span>{{ link.creator?.name || '未知' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>状态：</label>
              <el-tag :type="getStatusType(link.status_color)" size="small">
                {{ link.status_text }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>点击次数：</label>
              <span class="click-count">{{ link.click_count }}</span>
            </div>
            <div class="info-item">
              <label>剩余天数：</label>
              <span :class="getRemainingDaysClass(link.remaining_days)">
                {{ link.remaining_days }}天
              </span>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(link.created_at) }}</span>
            </div>
          </el-col>
        </el-row>

        <div v-if="link.description" class="info-item full-width">
          <label>链接描述：</label>
          <p class="description">{{ link.description }}</p>
        </div>
      </el-card>

      <!-- 统计数据 -->
      <el-card class="stats-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><TrendCharts /></el-icon>
            <span>统计数据</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.total_clicks }}</div>
              <div class="stat-label">总点击量</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.click_rate }}%</div>
              <div class="stat-label">点击率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.daily_average_clicks }}</div>
              <div class="stat-label">日均点击</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.created_days_ago }}</div>
              <div class="stat-label">创建天数</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 点击趋势图表 -->
      <el-card class="chart-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><DataLine /></el-icon>
            <span>点击趋势</span>
            <div class="header-actions">
              <el-radio-group v-model="trendDays" size="small" @change="loadClickTrend">
                <el-radio-button :label="7">7天</el-radio-button>
                <el-radio-button :label="15">15天</el-radio-button>
                <el-radio-button :label="30">30天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <div ref="chartRef" class="chart-container"></div>
      </el-card>

      <!-- 追踪参数 -->
      <el-card v-if="link.tracking_params && Object.keys(link.tracking_params).length" class="tracking-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Connection /></el-icon>
            <span>追踪参数</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col v-for="(value, key) in link.tracking_params" :key="key" :span="12">
            <div class="tracking-item">
              <label>{{ getTrackingParamLabel(key) }}：</label>
              <span>{{ value }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="actions-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>操作记录</span>
          </div>
        </template>

        <el-timeline>
          <el-timeline-item
            :timestamp="formatDate(link.created_at)"
            type="primary"
          >
            链接创建
          </el-timeline-item>
          <el-timeline-item
            v-if="link.last_clicked_at"
            :timestamp="formatDate(link.last_clicked_at)"
            type="success"
          >
            最后一次点击
          </el-timeline-item>
          <el-timeline-item
            :timestamp="formatDate(link.updated_at)"
            type="info"
          >
            最后更新
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="generateQRCode">
          <el-icon><QrCode /></el-icon>
          生成二维码
        </el-button>
        <el-button type="success" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  InfoFilled, 
  TrendCharts, 
  DataLine, 
  Connection, 
  Clock, 
  QrCode, 
  Download 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { usePromotionStore } from '@/stores/promotion'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  link: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'generate-qrcode'])

// 状态管理
const promotionStore = usePromotionStore()

// 响应式数据
const chartRef = ref()
const trendDays = ref(7)
const stats = ref({})
const clickTrend = ref([])
let chartInstance = null

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const loadLinkStats = async () => {
  if (!props.link) return
  
  try {
    const response = await promotionStore.getPromotionLink(props.link.id)
    stats.value = response.stats || {}
    clickTrend.value = response.click_trend || []
    
    nextTick(() => {
      initChart()
    })
  } catch (error) {
    console.error('加载链接统计失败:', error)
  }
}

const loadClickTrend = async () => {
  if (!props.link) return
  
  try {
    const response = await promotionStore.getPromotionLink(props.link.id)
    clickTrend.value = response.click_trend || []
    updateChart()
  } catch (error) {
    console.error('加载点击趋势失败:', error)
  }
}

const initChart = () => {
  if (!chartRef.value || !clickTrend.value.length) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    title: {
      text: '点击趋势',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>点击量: {c}'
    },
    xAxis: {
      type: 'category',
      data: clickTrend.value.map(item => item.date),
      axisLabel: {
        formatter: (value) => {
          const date = new Date(value)
          return `${date.getMonth() + 1}/${date.getDate()}`
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '点击量'
    },
    series: [{
      data: clickTrend.value.map(item => item.clicks),
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(64, 158, 255, 0.1)'
          }]
        }
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
  
  chartInstance.setOption(option)
}

const updateChart = () => {
  if (!chartInstance || !clickTrend.value.length) return
  
  chartInstance.setOption({
    xAxis: {
      data: clickTrend.value.map(item => item.date)
    },
    series: [{
      data: clickTrend.value.map(item => item.clicks)
    }]
  })
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const generateQRCode = () => {
  emit('generate-qrcode', props.link)
}

const exportData = () => {
  const data = {
    基本信息: {
      链接标题: props.link.title || '未设置',
      短链接: props.link.short_url,
      关联群组: props.link.group?.title,
      状态: props.link.status_text,
      创建时间: formatDate(props.link.created_at)
    },
    统计数据: stats.value,
    点击趋势: clickTrend.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `promotion_link_${props.link.short_code}_data.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('数据导出成功')
}

// 工具方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusType = (color) => {
  const typeMap = {
    success: 'success',
    warning: 'warning',
    danger: 'danger'
  }
  return typeMap[color] || 'info'
}

const getRemainingDaysClass = (days) => {
  if (days <= 0) return 'expired'
  if (days <= 7) return 'warning'
  return 'normal'
}

const getTrackingParamLabel = (key) => {
  const labelMap = {
    source: '来源标识',
    utm_medium: '媒介类型',
    utm_campaign: '活动名称',
    utm_source: '流量来源',
    utm_content: '内容标识'
  }
  return labelMap[key] || key
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.link) {
      loadLinkStats()
    }
  }
)

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.header-actions {
  margin-left: auto;
}

.info-card,
.stats-card,
.chart-card,
.tracking-card,
.actions-card {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-size: 14px;
}

.info-item.full-width {
  flex-direction: column;
  align-items: flex-start;
}

.info-item label {
  min-width: 80px;
  color: #909399;
  font-weight: 500;
}

.link-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.link-url {
  font-family: monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.description {
  margin: 5px 0 0 0;
  line-height: 1.6;
  color: #606266;
}

.click-count {
  font-weight: 600;
  color: #409EFF;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.tracking-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
}

.tracking-item label {
  min-width: 80px;
  color: #909399;
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}

.expired {
  color: #f56c6c;
  font-weight: 600;
}

.warning {
  color: #e6a23c;
  font-weight: 600;
}

.normal {
  color: #67c23a;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-timeline-item__content) {
  font-size: 14px;
}
</style>