<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>分站管理</h2>
        <p class="page-description">管理平台分站，配置分站权限，监控分站运营状况</p>
      </div>
      <div class="header-actions">
        <el-button type="info" @click="showHelpDialog = true">
          <el-icon><QuestionFilled /></el-icon>
          功能说明
        </el-button>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增分站
        </el-button>
      </div>
    </div>

    <el-card>
      <!-- 筛选区域 -->
      <el-form :inline="true" :model="queryParams" class="filter-container">
        <el-form-item label="分站名称">
          <el-input v-model="queryParams.name" placeholder="请输入分站名称" clearable />
        </el-form-item>
        <el-form-item label="域名">
          <el-input v-model="queryParams.domain" placeholder="请输入域名" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="2" />
            <el-option label="过期" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb-2">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增分站</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="!multiple" @click="handleDelete">删除</el-button>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-table :data="substationList" @selection-change="handleSelectionChange" v-loading="loading">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" prop="id" width="80" />
        <el-table-column label="分站名称" prop="name" show-overflow-tooltip />
        <el-table-column label="域名" prop="domain" show-overflow-tooltip />
        <el-table-column label="管理员" prop="user.nickname" />
        <el-table-column label="抽成比例" align="center">
          <template #default="scope">
            {{ (scope.row.commission_rate * 100).toFixed(1) }}%
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">{{ getStatusName(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
                      <el-table-column label="到期时间" prop="expire_at" />
        <el-table-column label="创建时间" prop="created_at" />
        <el-table-column label="操作" width="280" align="center">
          <template #default="scope">
            <el-button type="primary" link icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button type="warning" link icon="CreditCard" @click="handlePaymentConfig(scope.row)">支付配置</el-button>
            <el-button type="success" link icon="Clock" @click="handleRenew(scope.row)">续费</el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.per_page"
        @pagination="getList"
      />
    </el-card>

    <!-- 新增/编辑分站对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="分站名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分站名称" />
        </el-form-item>
        <el-form-item label="分站域名" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入分站域名">
            <template #append>.linkhub.pro</template>
          </el-input>
        </el-form-item>
        <el-form-item label="管理员" prop="user_id">
          <el-select v-model="form.user_id" placeholder="请选择管理员" filterable>
            <el-option
              v-for="admin in adminList"
              :key="admin.id"
              :label="`${admin.nickname}(${admin.username})`"
              :value="admin.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="抽成比例" prop="commission_rate">
          <el-input-number v-model="form.commission_rate" :precision="3" :step="0.001" :min="0" :max="1" />
          <span class="ml-2">（0-1之间的小数，如0.1表示10%）</span>
        </el-form-item>
        <el-form-item label="有效期" prop="expire_months">
          <el-select v-model="form.expire_months" placeholder="请选择有效期">
            <el-option label="1个月" :value="1" />
            <el-option label="3个月" :value="3" />
            <el-option label="6个月" :value="6" />
            <el-option label="12个月" :value="12" />
            <el-option label="24个月" :value="24" />
            <el-option label="36个月" :value="36" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入分站描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 续费对话框 -->
    <el-dialog title="分站续费" v-model="renewDialog.visible" width="500px" append-to-body>
      <el-form ref="renewFormRef" :model="renewForm" :rules="renewRules" label-width="100px">
        <el-form-item label="分站名称">
          <el-input :value="renewDialog.substationName" readonly />
        </el-form-item>
        <el-form-item label="当前到期时间">
          <el-input :value="renewDialog.currentExpireDate" readonly />
        </el-form-item>
        <el-form-item label="续费时长" prop="months">
          <el-select v-model="renewForm.months" placeholder="请选择续费时长">
            <el-option label="1个月" :value="1" />
            <el-option label="3个月" :value="3" />
            <el-option label="6个月" :value="6" />
            <el-option label="12个月" :value="12" />
            <el-option label="24个月" :value="24" />
          </el-select>
        </el-form-item>
        <el-form-item label="续费后到期时间" v-if="renewForm.months">
          <el-input :value="getNewExpireDate()" readonly />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="renewDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="submitRenewForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 支付配置对话框 -->
    <el-dialog :title="paymentDialog.title" v-model="paymentDialog.visible" width="800px" append-to-body>
      <el-tabs v-model="activePaymentTab" type="card">
        <el-tab-pane 
          v-for="channel in availableChannels" 
          :key="channel.channel_code"
          :label="channel.channel_name" 
          :name="channel.channel_code"
        >
          <el-card shadow="never" style="margin-bottom: 20px;">
            <template #header>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>{{ channel.channel_name }}配置</span>
                <div>
                  <el-tag v-if="channel.has_config" :type="channel.test_status ? 'success' : 'warning'">
                    {{ channel.test_status ? '已测试通过' : '未测试' }}
                  </el-tag>
                  <el-tag v-else type="info">未配置</el-tag>
                </div>
              </div>
            </template>
            
            <el-form 
              :ref="`paymentForm_${channel.channel_code}`"
              :model="paymentConfigs[channel.channel_code]"
              :rules="paymentRules"
              label-width="120px"
            >
              <template v-if="channel.channel_code === 'payoreo'">
                <el-form-item label="配置名称" prop="config_name">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].config_name" 
                    placeholder="请输入配置名称"
                  />
                </el-form-item>
                <el-form-item label="API地址" prop="api_url">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].api_url" 
                    placeholder="https://api.payoreo.com"
                  />
                </el-form-item>
                <el-form-item label="商户ID" prop="pid">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].pid" 
                    placeholder="请输入易支付商户ID"
                  />
                </el-form-item>
                <el-form-item label="商户秘钥" prop="key">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].key" 
                    type="password" 
                    show-password
                    placeholder="请输入易支付商户秘钥"
                  />
                </el-form-item>
                <el-form-item label="异步通知地址">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].notify_url" 
                    placeholder="留空则使用系统默认"
                  />
                  <div class="form-tip">系统默认: {{ getDefaultNotifyUrl() }}</div>
                </el-form-item>
                <el-form-item label="同步返回地址">
                  <el-input 
                    v-model="paymentConfigs[channel.channel_code].return_url" 
                    placeholder="留空则使用系统默认"
                  />
                  <div class="form-tip">用户支付成功后的跳转页面</div>
                </el-form-item>
              </template>
              
              <template v-else>
                <el-alert 
                  :title="`${channel.channel_name}配置功能开发中`" 
                  type="info" 
                  show-icon 
                  :closable="false"
                />
              </template>
              
              <el-form-item>
                <el-button 
                  type="primary" 
                  @click="savePaymentConfig(channel.channel_code)"
                  :loading="saving"
                >
                  保存配置
                </el-button>
                <el-button 
                  type="success" 
                  @click="testPaymentConfig(channel.channel_code)"
                  :loading="testing"
                  :disabled="!channel.has_config"
                >
                  测试配置
                </el-button>
                <el-button 
                  type="info" 
                  @click="loadPaymentConfig(channel.channel_code)"
                  v-if="channel.has_config"
                >
                  重新加载
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="paymentDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="分站管理功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>🏢 功能概述</h3>
          <p>分站管理系统是平台多站点运营的核心功能，支持创建和管理多个独立的分站点，每个分站拥有独立的域名、管理员和配置，实现平台的规模化扩展和区域化运营。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Plus /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>分站创建</h4>
                  <p>创建新的分站点，配置基本信息和管理权限</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>分站配置</h4>
                  <p>设置分站域名、抽成比例、有效期等参数</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><CreditCard /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>支付配置</h4>
                  <p>为分站配置独立的支付通道和参数</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>管理员管理</h4>
                  <p>指定分站管理员，分配管理权限</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>续费管理</h4>
                  <p>管理分站有效期，处理续费申请</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Monitor /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>状态监控</h4>
                  <p>实时监控分站运行状态和业务数据</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 分站状态说明 -->
        <div class="help-section">
          <h3>📊 分站状态说明</h3>
          <el-table :data="statusExplanation" style="width: 100%">
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="状态描述" />
            <el-table-column prop="features" label="功能限制" />
            <el-table-column prop="action" label="处理建议" />
          </el-table>
        </div>

        <!-- 抽成比例设置 -->
        <div class="help-section">
          <h3>💰 抽成比例设置指南</h3>
          <div class="commission-guide">
            <div class="guide-item">
              <h4>🔸 抽成比例说明</h4>
              <p>抽成比例是平台从分站收入中提取的分成比例，用小数表示（如0.1表示10%）</p>
              <div class="commission-examples">
                <div class="example-item">
                  <span class="example-label">示例1：</span>
                  <span>设置0.05，表示平台抽成5%，分站保留95%</span>
                </div>
                <div class="example-item">
                  <span class="example-label">示例2：</span>
                  <span>设置0.15，表示平台抽成15%，分站保留85%</span>
                </div>
              </div>
            </div>
            <div class="guide-item">
              <h4>🔸 推荐设置范围</h4>
              <el-table :data="commissionRanges" size="small">
                <el-table-column prop="type" label="分站类型" width="120" />
                <el-table-column prop="range" label="推荐比例" width="120" />
                <el-table-column prop="reason" label="设置理由" />
              </el-table>
            </div>
          </div>
        </div>

        <!-- 支付配置指南 -->
        <div class="help-section">
          <h3>💳 支付配置指南</h3>
          <div class="payment-guide">
            <div class="guide-step">
              <h4>📋 配置步骤</h4>
              <ol>
                <li>点击分站列表中的"支付配置"按钮</li>
                <li>选择要配置的支付通道（如易支付）</li>
                <li>填写支付通道的配置信息</li>
                <li>测试配置是否正确</li>
                <li>保存配置并启用</li>
              </ol>
            </div>
            <div class="guide-step">
              <h4>⚙️ 易支付配置说明</h4>
              <div class="config-fields">
                <div class="field-item">
                  <strong>API地址：</strong>易支付平台的API接口地址
                </div>
                <div class="field-item">
                  <strong>商户ID：</strong>在易支付平台注册的商户标识
                </div>
                <div class="field-item">
                  <strong>商户秘钥：</strong>用于签名验证的密钥，请妥善保管
                </div>
                <div class="field-item">
                  <strong>通知地址：</strong>支付成功后的异步通知地址
                </div>
                <div class="field-item">
                  <strong>返回地址：</strong>支付成功后用户跳转的页面
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何创建新分站？" name="create-substation">
              <div class="guide-content">
                <ol>
                  <li>点击页面右上角的"新增分站"按钮</li>
                  <li>填写分站基本信息：
                    <ul>
                      <li>分站名称：建议使用有意义的名称</li>
                      <li>分站域名：输入二级域名（系统自动添加后缀）</li>
                      <li>管理员：选择负责该分站的管理员</li>
                      <li>抽成比例：设置平台分成比例</li>
                      <li>有效期：选择分站的使用期限</li>
                    </ul>
                  </li>
                  <li>填写分站描述（可选）</li>
                  <li>点击"确定"完成创建</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：域名创建后需要进行DNS解析配置才能正常访问
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何配置分站支付？" name="payment-config">
              <div class="guide-content">
                <ol>
                  <li>在分站列表中找到目标分站</li>
                  <li>点击"支付配置"按钮</li>
                  <li>选择支付通道标签页</li>
                  <li>填写支付配置信息：
                    <ul>
                      <li>配置名称：便于识别的名称</li>
                      <li>API地址：支付平台的接口地址</li>
                      <li>商户信息：商户ID和密钥</li>
                      <li>回调地址：通知和返回地址</li>
                    </ul>
                  </li>
                  <li>点击"测试配置"验证设置</li>
                  <li>测试通过后点击"保存配置"</li>
                </ol>
                <el-alert type="warning" :closable="false">
                  ⚠️ 注意：商户密钥等敏感信息请妥善保管，不要泄露
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何为分站续费？" name="renew-substation">
              <div class="guide-content">
                <ol>
                  <li>在分站列表中找到需要续费的分站</li>
                  <li>点击"续费"按钮</li>
                  <li>查看当前到期时间</li>
                  <li>选择续费时长（1-24个月）</li>
                  <li>确认续费后的到期时间</li>
                  <li>点击"确定"完成续费</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 说明：续费后分站状态会自动更新，有效期延长
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何管理分站状态？" name="manage-status">
              <div class="guide-content">
                <ol>
                  <li>在分站列表中查看各分站的状态</li>
                  <li>正常状态：分站正常运行，所有功能可用</li>
                  <li>禁用状态：分站被暂停，用户无法访问</li>
                  <li>过期状态：分站已过期，需要续费才能恢复</li>
                  <li>可以通过编辑功能修改分站状态</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 建议：定期检查分站状态，及时处理过期和异常情况
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 最佳实践 -->
        <div class="help-section">
          <h3>💡 最佳实践建议</h3>
          <div class="best-practices">
            <div class="practice-item">
              <div class="practice-icon">🎯</div>
              <div class="practice-content">
                <h4>合理规划分站</h4>
                <p>根据业务需求和地域特点合理规划分站数量和分布，避免过度分散或集中</p>
              </div>
            </div>
            <div class="practice-item">
              <div class="practice-icon">👥</div>
              <div class="practice-content">
                <h4>选择合适管理员</h4>
                <p>为每个分站选择有经验、负责任的管理员，确保分站正常运营</p>
              </div>
            </div>
            <div class="practice-item">
              <div class="practice-icon">💰</div>
              <div class="practice-content">
                <h4>合理设置抽成</h4>
                <p>根据分站规模、运营成本和市场情况合理设置抽成比例，平衡各方利益</p>
              </div>
            </div>
            <div class="practice-item">
              <div class="practice-icon">🔒</div>
              <div class="practice-content">
                <h4>加强安全管理</h4>
                <p>定期检查分站安全状况，及时更新配置，防范安全风险</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见问题 -->
        <div class="help-section">
          <h3>❓ 常见问题</h3>
          <el-collapse v-model="activeFAQ">
            <el-collapse-item title="分站域名如何解析？" name="faq1">
              <p>分站创建后，需要在DNS服务商处添加CNAME记录，将分站域名指向主站域名。具体操作请联系技术支持。</p>
            </el-collapse-item>
            <el-collapse-item title="抽成比例可以随时修改吗？" name="faq2">
              <p>可以的。管理员可以随时编辑分站信息修改抽成比例，修改后立即生效，影响后续的收入分成计算。</p>
            </el-collapse-item>
            <el-collapse-item title="分站过期后数据会丢失吗？" name="faq3">
              <p>分站过期后数据不会立即删除，但用户无法访问。建议在过期前及时续费，或联系管理员备份重要数据。</p>
            </el-collapse-item>
            <el-collapse-item title="如何批量管理多个分站？" name="faq4">
              <p>可以使用表格上方的批量操作功能，选中多个分站后进行批量删除等操作。更多批量功能正在开发中。</p>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getSubstationList, createSubstation, updateSubstation, deleteSubstation, renewSubstation } from '@/api/substation'
import { getUserList } from '@/api/user'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  Plus, QuestionFilled, Setting, CreditCard, User, Clock, Monitor
} from '@element-plus/icons-vue'
import Pagination from '@/components/Pagination/index.vue'
import dayjs from 'dayjs'

const loading = ref(true)
const substationList = ref([])
const total = ref(0)
const multiple = ref(false)
const ids = ref([])
const adminList = ref([])
const showHelpDialog = ref(false)

// 帮助对话框相关数据
const activeGuides = ref(['create-substation'])
const activeFAQ = ref([])

// 分站状态说明数据
const statusExplanation = ref([
  {
    status: '正常',
    color: 'success',
    description: '分站正常运行，所有功能可用',
    features: '无限制',
    action: '保持现状，定期监控'
  },
  {
    status: '禁用',
    color: 'info',
    description: '分站被管理员暂停使用',
    features: '用户无法访问，管理员可登录',
    action: '检查禁用原因，解决后重新启用'
  },
  {
    status: '过期',
    color: 'danger',
    description: '分站使用期限已到期',
    features: '所有功能停用，数据保留',
    action: '联系客户续费或备份数据'
  }
])

// 抽成比例范围数据
const commissionRanges = ref([
  {
    type: '新建分站',
    range: '5%-10%',
    reason: '吸引新客户，建立合作关系'
  },
  {
    type: '标准分站',
    range: '10%-15%',
    reason: '平衡收益与成本，维持正常运营'
  },
  {
    type: '高级分站',
    range: '15%-20%',
    reason: '提供更多服务，获得更高收益'
  },
  {
    type: '企业分站',
    range: '8%-12%',
    reason: '大客户优惠，长期合作考虑'
  }
])

const queryParams = reactive({
  page: 1,
  per_page: 10,
  name: undefined,
  domain: undefined,
  status: undefined
})

const dialog = reactive({
  visible: false,
  title: ''
})

const renewDialog = reactive({
  visible: false,
  substationName: '',
  currentExpireDate: ''
})

const paymentDialog = reactive({
  visible: false,
  title: '',
  substationId: null
})

const activePaymentTab = ref('payoreo')
const availableChannels = ref([])
const paymentConfigs = reactive({})
const saving = ref(false)
const testing = ref(false)

const formRef = ref()
const renewFormRef = ref()

const form = ref({})
const renewForm = ref({
  months: null
})

const rules = reactive({
  name: [{ required: true, message: '分站名称不能为空', trigger: 'blur' }],
  domain: [{ required: true, message: '分站域名不能为空', trigger: 'blur' }],
  user_id: [{ required: true, message: '管理员不能为空', trigger: 'change' }],
  commission_rate: [{ required: true, message: '抽成比例不能为空', trigger: 'blur' }],
  expire_months: [{ required: true, message: '有效期不能为空', trigger: 'change' }]
})

const renewRules = reactive({
  months: [{ required: true, message: '续费时长不能为空', trigger: 'change' }]
})

const paymentRules = reactive({
  config_name: [{ required: true, message: '配置名称不能为空', trigger: 'blur' }],
  api_url: [
    { required: true, message: 'API地址不能为空', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  pid: [{ required: true, message: '商户ID不能为空', trigger: 'blur' }],
  key: [{ required: true, message: '商户秘钥不能为空', trigger: 'blur' }]
})

const getList = async () => {
  loading.value = true
  const { data } = await getSubstationList(queryParams)
  substationList.value = data.data
  total.value = data.total
  loading.value = false
}

const handleQuery = () => {
  queryParams.page = 1
  getList()
}

const resetQuery = () => {
  queryParams.page = 1
  queryParams.name = undefined
  queryParams.domain = undefined
  queryParams.status = undefined
  handleQuery()
}

const handleAdd = async () => {
  await getAdminList()
  form.value = {
    commission_rate: 0.1,
    expire_months: 12
  }
  dialog.title = '新增分站'
  dialog.visible = true
}

const handleUpdate = async (row) => {
  await getAdminList()
  form.value = { ...row }
  dialog.title = '编辑分站'
  dialog.visible = true
}

const handleDelete = (row) => {
  const substationIds = row.id ? [row.id] : ids.value
  ElMessageBox.confirm(`是否确认删除ID为"${substationIds.join(',')}"的分站？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    for (const id of substationIds) {
      await deleteSubstation(id)
    }
    getList()
    ElMessage.success('删除成功')
  }).catch(() => {})
}

const handleRenew = (row) => {
  renewDialog.substationName = row.name
  renewDialog.currentExpireDate = row.expire_at
  renewForm.value = {
    substation_id: row.id,
    current_expire_date: row.expire_at,
    months: null
  }
  renewDialog.visible = true
}

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
  multiple.value = selection.length > 0
}

const cancel = () => {
  dialog.visible = false
  formRef.value.resetFields()
}

const submitForm = async () => {
  await formRef.value.validate()
  if (form.value.id) {
    await updateSubstation(form.value.id, form.value)
    ElMessage.success('修改成功')
  } else {
    await createSubstation(form.value)
    ElMessage.success('新增成功')
  }
  dialog.visible = false
  getList()
}

const submitRenewForm = async () => {
  await renewFormRef.value.validate()
  await renewSubstation(renewForm.value.substation_id, renewForm.value.months)
  ElMessage.success('续费成功')
  renewDialog.visible = false
  getList()
}

const getAdminList = async () => {
  const { data } = await getUserList({ role: 'substation', per_page: 1000 })
  adminList.value = data.data
}

const getNewExpireDate = () => {
  if (!renewForm.value.months || !renewForm.value.current_expire_date) {
    return ''
  }
  return dayjs(renewForm.value.current_expire_date).add(renewForm.value.months, 'month').format('YYYY-MM-DD HH:mm:ss')
}

const getStatusTagType = (status) => {
  const statusMap = {
    1: 'success',
    2: 'info',
    3: 'danger'
  }
  return statusMap[status]
}

const getStatusName = (status) => {
  const statusMap = {
    1: '正常',
    2: '禁用',
    3: '过期'
  }
  return statusMap[status]
}

// 支付配置相关方法
const handlePaymentConfig = async (row) => {
  paymentDialog.title = `${row.name} - 支付配置`
  paymentDialog.substationId = row.id
  paymentDialog.visible = true
  
  // 获取可配置的支付通道
  await getAvailablePaymentChannels(row.id)
  
  // 初始化支付配置表单
  initPaymentConfigs()
}

const getAvailablePaymentChannels = async (substationId) => {
  try {
    // 调用API获取分站可配置的支付通道
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/user-channels`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        availableChannels.value = result.data
        
        // 如果有可用通道，默认选择第一个
        if (result.data.length > 0) {
          activePaymentTab.value = result.data[0].channel_code
        }
      }
    }
  } catch (error) {
    console.error('获取支付通道失败:', error)
    ElMessage.error('获取支付通道失败')
  }
}

const initPaymentConfigs = () => {
  availableChannels.value.forEach(channel => {
    if (!paymentConfigs[channel.channel_code]) {
      paymentConfigs[channel.channel_code] = {
        config_name: `${channel.channel_name}配置`,
        api_url: channel.channel_code === 'payoreo' ? 'https://api.payoreo.com' : '',
        pid: '',
        key: '',
        notify_url: '',
        return_url: ''
      }
    }
    
    // 如果已有配置，加载现有配置
    if (channel.has_config) {
      loadPaymentConfig(channel.channel_code)
    }
  })
}

const loadPaymentConfig = async (channelCode) => {
  try {
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/user-config/${channelCode}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        paymentConfigs[channelCode] = { ...paymentConfigs[channelCode], ...result.data.config_data }
      }
    }
  } catch (error) {
    console.error('加载支付配置失败:', error)
  }
}

const savePaymentConfig = async (channelCode) => {
  try {
    saving.value = true
    
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/user-config`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        channel_code: channelCode,
        config_name: paymentConfigs[channelCode].config_name,
        config_data: paymentConfigs[channelCode]
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        ElMessage.success('支付配置保存成功')
        // 重新获取支付通道状态
        await getAvailablePaymentChannels(paymentDialog.substationId)
      } else {
        ElMessage.error(result.message || '保存失败')
      }
    } else {
      ElMessage.error('保存失败')
    }
  } catch (error) {
    console.error('保存支付配置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const testPaymentConfig = async (channelCode) => {
  try {
    testing.value = true
    
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/payment-channels/test-config`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        channel_code: channelCode
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        ElMessage.success('配置测试通过')
        // 重新获取支付通道状态
        await getAvailablePaymentChannels(paymentDialog.substationId)
      } else {
        ElMessage.error(result.message || '测试失败')
      }
    } else {
      ElMessage.error('测试失败')
    }
  } catch (error) {
    console.error('测试支付配置失败:', error)
    ElMessage.error('测试失败')
  } finally {
    testing.value = false
  }
}

const getDefaultNotifyUrl = () => {
  return `${window.location.origin}/api/v1/payment/notify/payoreo`
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    margin-bottom: 20px;
  }
  
  .mb-2 {
    margin-bottom: 20px;
  }
  
  .ml-2 {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

:deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 