# FFJQ管理后台预览启动脚本
# 无需部署后端，直接预览管理界面

Write-Host "🚀 启动FFJQ管理后台预览服务..." -ForegroundColor Green

# 检查Node.js环境
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    Write-Host "📥 下载地址: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# 检查npm环境
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 错误: 未找到npm" -ForegroundColor Red
    exit 1
}

# 进入admin目录
if (-not (Test-Path "admin")) {
    Write-Host "❌ 错误: 未找到admin目录，请在项目根目录下运行此脚本" -ForegroundColor Red
    exit 1
}

Set-Location admin

Write-Host "📦 检查依赖包..." -ForegroundColor Yellow

# 检查node_modules是否存在
if (-not (Test-Path "node_modules")) {
    Write-Host "📥 安装依赖包..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖包安装失败" -ForegroundColor Red
        exit 1
    }
}

Write-Host "🎯 启动预览服务器..." -ForegroundColor Green

# 启动预览服务器
node preview-server.js

Write-Host "👋 预览服务器已关闭" -ForegroundColor Yellow