<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserSegmentStat extends Model
{
    protected $fillable = [
        'segment_id',
        'date',
        'member_count',
        'avg_score',
        'new_members',
        'removed_members',
        'retention_rate',
        'stats_data',
    ];

    protected $casts = [
        'date' => 'date',
        'member_count' => 'integer',
        'avg_score' => 'decimal:2',
        'new_members' => 'integer',
        'removed_members' => 'integer',
        'retention_rate' => 'decimal:2',
        'stats_data' => 'array',
    ];

    /**
     * 所属分群
     */
    public function segment()
    {
        return $this->belongsTo(UserSegment::class, 'segment_id');
    }
} 