<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 支付使用日志模型
 * 用于记录和统计支付通道的使用情况
 */
class PaymentUsageLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_type',
        'user_id',
        'channel_code',
        'order_id',
        'order_no',
        'amount',
        'fee',
        'action_type',
        'status',
        'payment_no',
        'request_data',
        'response_data',
        'error_code',
        'error_message',
        'client_ip',
        'user_agent',
        'response_time',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'fee' => 'decimal:2',
        'request_data' => 'array',
        'response_data' => 'array',
        'response_time' => 'integer',
    ];

    /**
     * 关联支付渠道
     */
    public function paymentChannel(): BelongsTo
    {
        return $this->belongsTo(PaymentChannel::class, 'channel_code', 'channel_code');
    }

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联用户（多态关联）
     */
    public function user()
    {
        return $this->morphTo('user', 'user_type', 'user_id');
    }

    /**
     * 获取操作类型名称
     */
    public function getActionTypeNameAttribute(): string
    {
        $types = [
            'create' => '创建支付',
            'callback' => '支付回调',
            'refund' => '退款处理',
            'query' => '状态查询',
        ];

        return $types[$this->action_type] ?? $this->action_type;
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            'pending' => '处理中',
            'success' => '成功',
            'failed' => '失败',
            'cancelled' => '已取消',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute(): string
    {
        $colors = [
            'pending' => 'warning',
            'success' => 'success',
            'failed' => 'danger',
            'cancelled' => 'info',
        ];

        return $colors[$this->status] ?? 'default';
    }

    /**
     * 检查是否成功
     */
    public function isSuccess(): bool
    {
        return $this->status === 'success';
    }

    /**
     * 检查是否失败
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * 获取响应时间描述
     */
    public function getResponseTimeDescAttribute(): string
    {
        if (!$this->response_time) {
            return '未知';
        }

        if ($this->response_time < 1000) {
            return $this->response_time . 'ms';
        }

        return round($this->response_time / 1000, 2) . 's';
    }

    /**
     * 查询作用域：按用户筛选
     */
    public function scopeForUser($query, string $userType, int $userId)
    {
        return $query->where('user_type', $userType)->where('user_id', $userId);
    }

    /**
     * 查询作用域：按支付渠道筛选
     */
    public function scopeByChannel($query, string $channelCode)
    {
        return $query->where('channel_code', $channelCode);
    }

    /**
     * 查询作用域：按操作类型筛选
     */
    public function scopeByActionType($query, string $actionType)
    {
        return $query->where('action_type', $actionType);
    }

    /**
     * 查询作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 查询作用域：成功的记录
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', 'success');
    }

    /**
     * 查询作用域：失败的记录
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * 查询作用域：今日记录
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * 查询作用域：本月记录
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * 查询作用域：按时间范围筛选
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 静态方法：记录支付使用日志
     */
    public static function logUsage(array $data): self
    {
        return static::create([
            'user_type' => $data['user_type'],
            'user_id' => $data['user_id'],
            'channel_code' => $data['channel_code'],
            'order_id' => $data['order_id'] ?? null,
            'order_no' => $data['order_no'] ?? null,
            'amount' => $data['amount'],
            'fee' => $data['fee'] ?? 0,
            'action_type' => $data['action_type'],
            'status' => $data['status'],
            'payment_no' => $data['payment_no'] ?? null,
            'request_data' => $data['request_data'] ?? null,
            'response_data' => $data['response_data'] ?? null,
            'error_code' => $data['error_code'] ?? null,
            'error_message' => $data['error_message'] ?? null,
            'client_ip' => $data['client_ip'] ?? request()->ip(),
            'user_agent' => $data['user_agent'] ?? request()->userAgent(),
            'response_time' => $data['response_time'] ?? null,
        ]);
    }

    /**
     * 静态方法：获取支付统计数据
     */
    public static function getPaymentStats(string $userType = null, int $userId = null, string $period = 'month'): array
    {
        $query = static::query();

        if ($userType && $userId) {
            $query->forUser($userType, $userId);
        }

        // 根据时间段筛选
        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->where('created_at', '>=', now()->startOfWeek());
                break;
            case 'month':
                $query->thisMonth();
                break;
            case 'year':
                $query->whereYear('created_at', now()->year);
                break;
        }

        $totalCount = $query->count();
        $successCount = $query->success()->count();
        $failedCount = $query->failed()->count();
        $totalAmount = $query->success()->sum('amount');
        $totalFee = $query->success()->sum('fee');

        return [
            'total_count' => $totalCount,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'success_rate' => $totalCount > 0 ? round(($successCount / $totalCount) * 100, 2) : 0,
            'total_amount' => $totalAmount,
            'total_fee' => $totalFee,
            'avg_amount' => $successCount > 0 ? round($totalAmount / $successCount, 2) : 0,
            'avg_response_time' => $query->whereNotNull('response_time')->avg('response_time'),
        ];
    }

    /**
     * 静态方法：获取渠道使用统计
     */
    public static function getChannelStats(string $period = 'month'): array
    {
        $query = static::query();

        // 根据时间段筛选
        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->where('created_at', '>=', now()->startOfWeek());
                break;
            case 'month':
                $query->thisMonth();
                break;
            case 'year':
                $query->whereYear('created_at', now()->year);
                break;
        }

        return $query->selectRaw('
                channel_code,
                COUNT(*) as total_count,
                SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_count,
                SUM(CASE WHEN status = "success" THEN amount ELSE 0 END) as total_amount,
                SUM(CASE WHEN status = "success" THEN fee ELSE 0 END) as total_fee,
                AVG(CASE WHEN status = "success" THEN response_time END) as avg_response_time
            ')
            ->groupBy('channel_code')
            ->with('paymentChannel:channel_code,channel_name')
            ->get()
            ->map(function ($item) {
                $item->success_rate = $item->total_count > 0 ? round(($item->success_count / $item->total_count) * 100, 2) : 0;
                return $item;
            });
    }
}