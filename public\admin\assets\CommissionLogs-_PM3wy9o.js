import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                       *//* empty css                *//* empty css                       *//* empty css                        *//* empty css               */import{T as a,a4 as t,as as l,aM as s,b2 as o,ai as r,U as n,a_ as d,bW as i,_ as u,bX as c,a6 as m,b4 as _,bj as p,bo as v,bv as f,b3 as b,b6 as g,b7 as h,bY as w,b8 as y,bk as k,aH as C,b1 as D,aW as z,aV as j,bl as x,bM as V,bN as F,au as A,ad as E,Q as U,R as Y}from"./element-plus-DcSKpKA8.js";import{L as M}from"./LineChart-Ba008-uu.js";import{r as T,L as B,e as L,k as R,l as $,t as I,E as N,z as P,D as S,u as H,A as O,y as W,B as Q}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";import"./chart-Bup65vvO.js";const q={class:"commission-logs"},G={class:"page-header"},X={class:"header-actions"},Z={class:"stat-card"},J={class:"stat-icon",style:{"background-color":"#409EFF20",color:"#409EFF"}},K={class:"stat-content"},ee={class:"stat-value"},ae={class:"stat-trend positive"},te={class:"stat-card"},le={class:"stat-icon",style:{"background-color":"#67C23A20",color:"#67C23A"}},se={class:"stat-content"},oe={class:"stat-value"},re={class:"stat-trend positive"},ne={class:"stat-card"},de={class:"stat-icon",style:{"background-color":"#E6A23C20",color:"#E6A23C"}},ie={class:"stat-content"},ue={class:"stat-value"},ce={class:"stat-trend neutral"},me={class:"stat-card"},_e={class:"stat-icon",style:{"background-color":"#F56C6C20",color:"#F56C6C"}},pe={class:"stat-content"},ve={class:"stat-value"},fe={class:"stat-trend neutral"},be={class:"card-header"},ge={class:"chart-controls"},he={class:"chart-container"},we={class:"card-header"},ye={class:"header-actions"},ke={class:"customer-info"},Ce={class:"customer-name"},De={class:"customer-level"},ze={class:"order-amount"},je={class:"commission-amount"},xe={class:"commission-rate"},Ve={key:0},Fe={key:1,class:"pending-text"},Ae={class:"pagination-wrapper"},Ee={key:0,class:"commission-detail"},Ue={class:"dev-notice"},Ye=e({__name:"CommissionLogs",setup(e){const Ye=T(!1),Me=T(!1),Te=T(!1),Be=T(!1),Le=T(""),Re=T(""),$e=T([]),Ie=T("30d"),Ne=T(null),Pe=B({total_commission:28650.5,month_commission:8650.5,pending_commission:1250,commission_rate:15}),Se=T([]),He=B({current_page:1,per_page:20,total:0}),Oe=T({labels:[],datasets:[{label:"佣金收入",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0}]}),We={responsive:!0,maintainAspectRatio:!1,interaction:{intersect:!1,mode:"index"},plugins:{legend:{display:!0,position:"top"},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",callbacks:{label:function(e){return`佣金收入: ¥${e.parsed.y.toFixed(2)}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{callback:function(e){return"¥"+e}}},x:{grid:{display:!1}}}},Qe=[{id:1,order_no:"ORD202401001",customer_name:"张三",customer_level:"A级客户",product_name:"VIP群组套餐",order_amount:2999,commission_amount:449.85,commission_rate:15,status:"settled",created_at:new Date(Date.now()-432e6),settled_at:new Date(Date.now()-1728e5),remark:"正常结算"},{id:2,order_no:"ORD202401002",customer_name:"李四",customer_level:"B级客户",product_name:"标准群组套餐",order_amount:1999,commission_amount:199.9,commission_rate:10,status:"pending",created_at:new Date(Date.now()-2592e5),settled_at:null,remark:""},{id:3,order_no:"ORD202401003",customer_name:"王五",customer_level:"A级客户",product_name:"企业群组套餐",order_amount:4999,commission_amount:749.85,commission_rate:15,status:"settled",created_at:new Date(Date.now()-6912e5),settled_at:new Date(Date.now()-432e6),remark:"正常结算"}],qe=async()=>{try{Ye.value=!0,await new Promise(e=>setTimeout(e,500));let e=[...Qe];if(Le.value&&(e=e.filter(e=>e.order_no.includes(Le.value)||e.customer_name.includes(Le.value)||e.product_name.includes(Le.value))),Re.value&&(e=e.filter(e=>e.status===Re.value)),$e.value&&2===$e.value.length){const a=new Date($e.value[0]),t=new Date($e.value[1]);e=e.filter(e=>{const l=new Date(e.created_at);return l>=a&&l<=t})}Se.value=e,He.total=e.length}catch(e){U.error("加载佣金数据失败")}finally{Ye.value=!1}},Ge=async()=>{Me.value=!0;try{await new Promise(e=>setTimeout(e,500));const e={"7d":{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[120.5,190.8,300.2,500.6,200.3,300.9,450.7]},"30d":{labels:Array.from({length:30},(e,a)=>`${a+1}日`),data:Array.from({length:30},()=>Math.floor(500*Math.random())+100)},"90d":{labels:["第1月","第2月","第3月"],data:[8000.5,12000.8,15000.2]}}[Ie.value];Oe.value={labels:e.labels,datasets:[{label:"佣金收入",data:e.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"#409EFF",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]}}catch(e){U.error("加载图表数据失败")}finally{Me.value=!1}},Xe=()=>{qe(),Ge(),U.success("数据已刷新")},Ze=function(e,a){let t;return function(...l){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e(...l)},a)}}(()=>{He.current_page=1,qe()},500),Je=()=>{U.info("导出佣金明细功能开发中..."),Be.value=!0},Ke=e=>({settled:"success",pending:"warning",frozen:"danger"}[e]||"info"),ea=e=>({settled:"已结算",pending:"待结算",frozen:"已冻结"}[e]||"未知"),aa=e=>Number(e).toFixed(2),ta=e=>new Date(e).toLocaleDateString("zh-CN"),la=e=>new Date(e).toLocaleString("zh-CN");return L(()=>{qe(),Ge()}),(e,T)=>{const B=a,L=l,Qe=o,sa=_,oa=f,ra=v,na=b,da=C,ia=j,ua=z,ca=x,ma=w,_a=h,pa=y,va=g,fa=k,ba=F,ga=V,ha=A,wa=p;return $(),R("div",q,[I("div",G,[T[12]||(T[12]=I("div",{class:"header-left"},[I("h2",null,"佣金查看"),I("p",{class:"page-description"},"查看您的佣金收入明细，跟踪收益情况")],-1)),I("div",X,[N(L,{type:"primary",onClick:Je},{default:P(()=>[N(B,null,{default:P(()=>[N(H(t))]),_:1}),T[10]||(T[10]=S(" 导出明细 ",-1))]),_:1,__:[10]}),N(L,{onClick:Xe},{default:P(()=>[N(B,null,{default:P(()=>[N(H(s))]),_:1}),T[11]||(T[11]=S(" 刷新数据 ",-1))]),_:1,__:[11]})])]),N(sa,{gutter:20,class:"stats-row"},{default:P(()=>[N(Qe,{span:6},{default:P(()=>[I("div",Z,[I("div",J,[N(B,{size:24},{default:P(()=>[N(H(r))]),_:1})]),I("div",K,[I("div",ee,"¥"+n(aa(Pe.total_commission)),1),T[14]||(T[14]=I("div",{class:"stat-title"},"累计佣金",-1)),I("div",ae,[N(B,null,{default:P(()=>[N(H(d))]),_:1}),T[13]||(T[13]=S(" 12.5% ",-1))])])])]),_:1}),N(Qe,{span:6},{default:P(()=>[I("div",te,[I("div",le,[N(B,{size:24},{default:P(()=>[N(H(i))]),_:1})]),I("div",se,[I("div",oe,"¥"+n(aa(Pe.month_commission)),1),T[16]||(T[16]=I("div",{class:"stat-title"},"本月佣金",-1)),I("div",re,[N(B,null,{default:P(()=>[N(H(d))]),_:1}),T[15]||(T[15]=S(" 8.3% ",-1))])])])]),_:1}),N(Qe,{span:6},{default:P(()=>[I("div",ne,[I("div",de,[N(B,{size:24},{default:P(()=>[N(H(u))]),_:1})]),I("div",ie,[I("div",ue,"¥"+n(aa(Pe.pending_commission)),1),T[18]||(T[18]=I("div",{class:"stat-title"},"待结算",-1)),I("div",ce,[N(B,null,{default:P(()=>[N(H(c))]),_:1}),T[17]||(T[17]=S(" 0% ",-1))])])])]),_:1}),N(Qe,{span:6},{default:P(()=>[I("div",me,[I("div",_e,[N(B,{size:24},{default:P(()=>[N(H(m))]),_:1})]),I("div",pe,[I("div",ve,n(Pe.commission_rate)+"%",1),T[20]||(T[20]=I("div",{class:"stat-title"},"佣金比例",-1)),I("div",fe,[N(B,null,{default:P(()=>[N(H(c))]),_:1}),T[19]||(T[19]=S(" 0% ",-1))])])])]),_:1})]),_:1}),N(na,{class:"chart-card"},{header:P(()=>[I("div",be,[T[24]||(T[24]=I("span",null,"佣金趋势",-1)),I("div",ge,[N(ra,{modelValue:Ie.value,"onUpdate:modelValue":T[0]||(T[0]=e=>Ie.value=e),size:"small",onChange:Ge},{default:P(()=>[N(oa,{value:"7d"},{default:P(()=>T[21]||(T[21]=[S("近7天",-1)])),_:1,__:[21]}),N(oa,{value:"30d"},{default:P(()=>T[22]||(T[22]=[S("近30天",-1)])),_:1,__:[22]}),N(oa,{value:"90d"},{default:P(()=>T[23]||(T[23]=[S("近3个月",-1)])),_:1,__:[23]})]),_:1},8,["modelValue"])])])]),default:P(()=>[O(($(),R("div",he,[Me.value?Q("",!0):($(),W(M,{key:0,data:Oe.value,options:We,height:"300px"},null,8,["data"]))])),[[wa,Me.value]])]),_:1}),N(na,null,{header:P(()=>[I("div",we,[T[25]||(T[25]=I("span",null,"佣金明细",-1)),I("div",ye,[N(da,{modelValue:Le.value,"onUpdate:modelValue":T[1]||(T[1]=e=>Le.value=e),placeholder:"搜索订单号或客户",style:{width:"200px","margin-right":"10px"},clearable:"",onInput:H(Ze)},{prefix:P(()=>[N(B,null,{default:P(()=>[N(H(D))]),_:1})]),_:1},8,["modelValue","onInput"]),N(ua,{modelValue:Re.value,"onUpdate:modelValue":T[2]||(T[2]=e=>Re.value=e),placeholder:"状态",style:{width:"120px","margin-right":"10px"},onChange:qe},{default:P(()=>[N(ia,{label:"全部",value:""}),N(ia,{label:"已结算",value:"settled"}),N(ia,{label:"待结算",value:"pending"}),N(ia,{label:"已冻结",value:"frozen"})]),_:1},8,["modelValue"]),N(ca,{modelValue:$e.value,"onUpdate:modelValue":T[3]||(T[3]=e=>$e.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:qe},null,8,["modelValue"])])])]),default:P(()=>[O(($(),W(va,{data:Se.value,stripe:""},{default:P(()=>[N(_a,{prop:"order_no",label:"订单号",width:"180"},{default:P(({row:e})=>[N(ma,{type:"primary",onClick:a=>{return t=e.order_no,U.info(`查看订单 ${t} 详情`),void(Be.value=!0);var t}},{default:P(()=>[S(n(e.order_no),1)]),_:2},1032,["onClick"])]),_:1}),N(_a,{label:"客户信息",width:"150"},{default:P(({row:e})=>[I("div",ke,[I("div",Ce,n(e.customer_name),1),I("div",De,n(e.customer_level),1)])]),_:1}),N(_a,{prop:"product_name",label:"产品名称",width:"200"}),N(_a,{label:"订单金额",width:"120"},{default:P(({row:e})=>[I("span",ze,"¥"+n(aa(e.order_amount)),1)]),_:1}),N(_a,{label:"佣金金额",width:"120"},{default:P(({row:e})=>[I("span",je,"¥"+n(aa(e.commission_amount)),1)]),_:1}),N(_a,{label:"佣金比例",width:"100"},{default:P(({row:e})=>[I("span",xe,n(e.commission_rate)+"%",1)]),_:1}),N(_a,{label:"状态",width:"100"},{default:P(({row:e})=>[N(pa,{type:Ke(e.status),size:"small"},{default:P(()=>[S(n(ea(e.status)),1)]),_:2},1032,["type"])]),_:1}),N(_a,{label:"结算时间",width:"120"},{default:P(({row:e})=>[e.settled_at?($(),R("span",Ve,n(ta(e.settled_at)),1)):($(),R("span",Fe,"待结算"))]),_:1}),N(_a,{label:"创建时间",width:"120"},{default:P(({row:e})=>[S(n(ta(e.created_at)),1)]),_:1}),N(_a,{label:"操作",width:"120",fixed:"right"},{default:P(({row:e})=>[N(L,{size:"small",onClick:a=>{return t=e,Ne.value=t,void(Te.value=!0);var t}},{default:P(()=>T[26]||(T[26]=[S(" 详情 ",-1)])),_:2,__:[26]},1032,["onClick"]),"pending"===e.status?($(),W(L,{key:0,size:"small",type:"primary",onClick:a=>(async e=>{try{await Y.confirm(`确定要申请结算订单 ${e.order_no} 的佣金吗？`,"确认申请",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),U.success("结算申请已提交，请等待审核"),Be.value=!0}catch(a){"cancel"!==a&&U.error("申请失败")}})(e)},{default:P(()=>T[27]||(T[27]=[S(" 申请结算 ",-1)])),_:2,__:[27]},1032,["onClick"])):Q("",!0)]),_:1})]),_:1},8,["data"])),[[wa,Ye.value]]),I("div",Ae,[N(fa,{"current-page":He.current_page,"onUpdate:currentPage":T[4]||(T[4]=e=>He.current_page=e),"page-size":He.per_page,"onUpdate:pageSize":T[5]||(T[5]=e=>He.per_page=e),total:He.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:qe,onCurrentChange:qe},null,8,["current-page","page-size","total"])])]),_:1}),N(ha,{modelValue:Te.value,"onUpdate:modelValue":T[7]||(T[7]=e=>Te.value=e),title:"佣金详情",width:"600px"},{footer:P(()=>[N(L,{onClick:T[6]||(T[6]=e=>Te.value=!1)},{default:P(()=>T[28]||(T[28]=[S("关闭",-1)])),_:1,__:[28]})]),default:P(()=>[Ne.value?($(),R("div",Ee,[N(ga,{column:2,border:""},{default:P(()=>[N(ba,{label:"订单号"},{default:P(()=>[S(n(Ne.value.order_no),1)]),_:1}),N(ba,{label:"客户姓名"},{default:P(()=>[S(n(Ne.value.customer_name),1)]),_:1}),N(ba,{label:"产品名称"},{default:P(()=>[S(n(Ne.value.product_name),1)]),_:1}),N(ba,{label:"订单金额"},{default:P(()=>[S("¥"+n(aa(Ne.value.order_amount)),1)]),_:1}),N(ba,{label:"佣金金额"},{default:P(()=>[S("¥"+n(aa(Ne.value.commission_amount)),1)]),_:1}),N(ba,{label:"佣金比例"},{default:P(()=>[S(n(Ne.value.commission_rate)+"%",1)]),_:1}),N(ba,{label:"状态"},{default:P(()=>[N(pa,{type:Ke(Ne.value.status)},{default:P(()=>[S(n(ea(Ne.value.status)),1)]),_:1},8,["type"])]),_:1}),N(ba,{label:"创建时间"},{default:P(()=>[S(n(la(Ne.value.created_at)),1)]),_:1}),Ne.value.settled_at?($(),W(ba,{key:0,label:"结算时间"},{default:P(()=>[S(n(la(Ne.value.settled_at)),1)]),_:1})):Q("",!0),Ne.value.remark?($(),W(ba,{key:1,label:"备注"},{default:P(()=>[S(n(Ne.value.remark),1)]),_:1})):Q("",!0)]),_:1})])):Q("",!0)]),_:1},8,["modelValue"]),N(ha,{modelValue:Be.value,"onUpdate:modelValue":T[9]||(T[9]=e=>Be.value=e),title:"功能开发中",width:"400px",center:""},{footer:P(()=>[N(L,{type:"primary",onClick:T[8]||(T[8]=e=>Be.value=!1)},{default:P(()=>T[32]||(T[32]=[S("知道了",-1)])),_:1,__:[32]})]),default:P(()=>[I("div",Ue,[N(B,{size:60,color:"#409EFF"},{default:P(()=>[N(H(E))]),_:1}),T[29]||(T[29]=I("h3",null,"功能开发中",-1)),T[30]||(T[30]=I("p",null,"该功能正在紧急开发中，敬请期待！",-1)),T[31]||(T[31]=I("p",null,"预计上线时间：2024年1月",-1))])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-9c3e1d95"]]);export{Ye as default};
