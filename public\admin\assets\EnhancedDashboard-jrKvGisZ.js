import{_ as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                             *//* empty css                     *//* empty css                        *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css               */import{T as e,aM as l,as as s,aU as t,b2 as c,ae as u,U as r,c3 as d,bG as o,a$ as n,c4 as i,b4 as _,b3 as p,b6 as m,b7 as v,b8 as f,aZ as h,V as b,bE as y,c5 as k,af as g,av as w,aO as V,ax as j,ay as x,bj as z,bk as C,aW as S,aV as U,bc as q,bd as D,aH as Q,be as A,au as E,bM as H,bN as L,c6 as B,Q as O,R as I}from"./element-plus-DcSKpKA8.js";import{i as M}from"./echarts-DTArWCqr.js";import{a as N}from"./marketing-D5ylsfMy.js";import{r as P,L as $,c as F,e as G,k as R,l as T,t as W,E as Y,z as Z,D as J,u as K,A as X,y as aa,F as ea,Y as la,B as sa,n as ta}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const ca={class:"anti-block-dashboard"},ua={class:"page-header"},ra={class:"header-content"},da={class:"header-actions"},oa={class:"stats-section"},na={class:"stats-card primary"},ia={class:"stats-content"},_a={class:"stats-icon"},pa={class:"stats-info"},ma={class:"stats-value"},va={class:"stats-trend"},fa={class:"stats-card success"},ha={class:"stats-content"},ba={class:"stats-icon"},ya={class:"stats-info"},ka={class:"stats-value"},ga={class:"stats-trend positive"},wa={class:"stats-card warning"},Va={class:"stats-content"},ja={class:"stats-icon"},xa={class:"stats-info"},za={class:"stats-value"},Ca={class:"stats-card danger"},Sa={class:"stats-content"},Ua={class:"stats-icon"},qa={class:"stats-info"},Da={class:"stats-value"},Qa={class:"card-header"},Aa={class:"header-actions"},Ea={class:"health-score"},Ha={key:0},La={key:1,class:"text-muted"},Ba={class:"access-status"},Oa={class:"pagination-wrapper"},Ia={class:"browser-stats"},Ma={class:"browser-info"},Na={class:"browser-name"},Pa={class:"browser-count"},$a={key:0,class:"domain-details"},Fa={key:0,class:"mt-4"},Ga={class:"check-item"},Ra={class:"check-item"},Ta={class:"check-item"},Wa={class:"check-item"},Ya={class:"check-item"},Za={class:"check-item"},Ja=a({__name:"EnhancedDashboard",setup(a){const Ja=P(!1),Ka=P([]),Xa=P({total:0,normal:0,abnormal:0,blocked:0,avg_health_score:0}),ae=P([]),ee=P(""),le=P(!1),se=P(!1),te=P(null),ce=P(null),ue=$({page:1,size:20,total:0}),re=$({domain:"",domain_type:"redirect",priority:5,remarks:""}),de=F(()=>ee.value?Ka.value.filter(a=>{switch(ee.value){case"normal":return 1===a.status;case"abnormal":return 2===a.status;case"blocked":return 3===a.status;default:return!0}}):Ka.value),oe=async()=>{Ja.value=!0;try{const a=await N.getDomainHealth();Ka.value=a.data.domains||[],Xa.value=a.data.stats||{},ue.total=a.data.total||0}catch(a){O.error("获取域名列表失败")}finally{Ja.value=!1}},ne=async()=>{try{const a=await N.checkDomainHealth();O.success(`批量检查完成，检查了 ${a.data.checked} 个域名`),oe()}catch(a){O.error("批量检查失败")}},ie=async()=>{if(re.domain)try{O.success("域名添加成功"),le.value=!1,oe(),Object.keys(re).forEach(a=>{re[a]="domain_type"===a?"redirect":"priority"===a?5:""})}catch(a){O.error("域名添加失败")}else O.warning("请输入域名")},_e=async({action:a,domain:e})=>{switch(a){case"restore":try{O.success("域名已恢复"),oe()}catch(l){O.error("域名恢复失败")}break;case"block":try{O.success("域名已封禁"),oe()}catch(l){O.error("域名封禁失败")}break;case"delete":try{await I.confirm("确定要删除这个域名吗？","确认删除",{type:"warning"}),O.success("域名已删除"),oe()}catch(l){"cancel"!==l&&O.error("域名删除失败")}}},pe=a=>{switch(a){case 1:return"success";case 2:return"warning";case 3:return"danger";default:return"info"}},me=a=>a?new Date(a).toLocaleString():"-";return G(()=>{oe(),(async()=>{try{const a=await N.getBrowserStats(7);ae.value=a.data||[]}catch(a){console.error("获取浏览器统计失败:",a)}})(),ta(()=>{ce.value&&M(ce.value).setOption({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{data:[120,200,150,80,70,110,130],type:"line",smooth:!0,areaStyle:{opacity:.3}}]})})}),(a,I)=>{const M=e,P=s,$=c,F=_,G=U,ta=S,Ka=v,ve=f,fe=h,he=b,be=x,ye=j,ke=w,ge=m,we=C,Ve=p,je=Q,xe=D,ze=A,Ce=q,Se=E,Ue=L,qe=H,De=z;return T(),R("div",ca,[W("div",ua,[W("div",ra,[I[13]||(I[13]=W("div",{class:"header-left"},[W("h1",{class:"page-title"},"防封系统管理"),W("p",{class:"page-subtitle"},"监控和管理域名健康状态、访问统计和防封策略")],-1)),W("div",da,[Y(P,{type:"primary",onClick:ne},{default:Z(()=>[Y(M,null,{default:Z(()=>[Y(K(l))]),_:1}),I[11]||(I[11]=J(" 检查所有域名 ",-1))]),_:1,__:[11]}),Y(P,{onClick:I[0]||(I[0]=a=>le.value=!0)},{default:Z(()=>[Y(M,null,{default:Z(()=>[Y(K(t))]),_:1}),I[12]||(I[12]=J(" 添加域名 ",-1))]),_:1,__:[12]})])])]),W("div",oa,[Y(F,{gutter:24},{default:Z(()=>[Y($,{span:6},{default:Z(()=>[W("div",na,[W("div",ia,[W("div",_a,[Y(M,null,{default:Z(()=>[Y(K(u))]),_:1})]),W("div",pa,[W("div",ma,r(Xa.value.total),1),I[14]||(I[14]=W("div",{class:"stats-label"},"总域名数",-1))])]),W("div",va,[W("span",null,"健康域名: "+r(Xa.value.normal),1)])])]),_:1}),Y($,{span:6},{default:Z(()=>[W("div",fa,[W("div",ha,[W("div",ba,[Y(M,null,{default:Z(()=>[Y(K(d))]),_:1})]),W("div",ya,[W("div",ka,r(Xa.value.avg_health_score)+"%",1),I[15]||(I[15]=W("div",{class:"stats-label"},"平均健康分数",-1))])]),W("div",ga,[Y(M,null,{default:Z(()=>[Y(K(o))]),_:1}),I[16]||(I[16]=W("span",null,"+2.3%",-1))])])]),_:1}),Y($,{span:6},{default:Z(()=>[W("div",wa,[W("div",Va,[W("div",ja,[Y(M,null,{default:Z(()=>[Y(K(n))]),_:1})]),W("div",xa,[W("div",za,r(Xa.value.abnormal),1),I[17]||(I[17]=W("div",{class:"stats-label"},"异常域名",-1))])]),I[18]||(I[18]=W("div",{class:"stats-trend"},[W("span",null,"需要关注")],-1))])]),_:1}),Y($,{span:6},{default:Z(()=>[W("div",Ca,[W("div",Sa,[W("div",Ua,[Y(M,null,{default:Z(()=>[Y(K(i))]),_:1})]),W("div",qa,[W("div",Da,r(Xa.value.blocked),1),I[19]||(I[19]=W("div",{class:"stats-label"},"封禁域名",-1))])]),I[20]||(I[20]=W("div",{class:"stats-trend negative"},[W("span",null,"需要处理")],-1))])]),_:1})]),_:1})]),Y(F,{gutter:24},{default:Z(()=>[Y($,{span:16},{default:Z(()=>[Y(Ve,{title:"域名管理"},{header:Z(()=>[W("div",Qa,[I[21]||(I[21]=W("span",null,"域名管理",-1)),W("div",Aa,[Y(ta,{modelValue:ee.value,"onUpdate:modelValue":I[1]||(I[1]=a=>ee.value=a),placeholder:"筛选状态",style:{width:"120px"}},{default:Z(()=>[Y(G,{label:"全部",value:""}),Y(G,{label:"正常",value:"normal"}),Y(G,{label:"异常",value:"abnormal"}),Y(G,{label:"封禁",value:"blocked"})]),_:1},8,["modelValue"])])])]),default:Z(()=>[X((T(),aa(ge,{data:de.value},{default:Z(()=>[Y(Ka,{prop:"domain",label:"域名","min-width":"200"}),Y(Ka,{prop:"status_name",label:"状态",width:"100"},{default:Z(({row:a})=>[Y(ve,{type:pe(a.status)},{default:Z(()=>[J(r(a.status_name),1)]),_:2},1032,["type"])]),_:1}),Y(Ka,{prop:"health_score",label:"健康分数",width:"120"},{default:Z(({row:a})=>{return[Y(fe,{percentage:a.health_score,color:(e=a.health_score,e>=80?"#67c23a":e>=60?"#e6a23c":"#f56c6c"),"show-text":!1},null,8,["percentage","color"]),W("span",Ea,r(a.health_score)+"%",1)];var e}),_:1}),Y(Ka,{prop:"use_count",label:"使用次数",width:"100"}),Y(Ka,{prop:"last_check_time",label:"最后检查",width:"150"},{default:Z(({row:a})=>[a.last_check_time?(T(),R("span",Ha,r(me(a.last_check_time)),1)):(T(),R("span",La,"未检查"))]),_:1}),Y(Ka,{label:"访问检测",width:"120"},{default:Z(({row:a})=>[W("div",Ba,[Y(he,{content:"微信访问",placement:"top"},{default:Z(()=>[Y(M,{color:a.check_results?.wechat_accessible?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(y))]),_:2},1032,["color"])]),_:2},1024),Y(he,{content:"QQ访问",placement:"top"},{default:Z(()=>[Y(M,{color:a.check_results?.qq_accessible?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(k))]),_:2},1032,["color"])]),_:2},1024),Y(he,{content:"SSL证书",placement:"top"},{default:Z(()=>[Y(M,{color:a.check_results?.ssl_valid?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(g))]),_:2},1032,["color"])]),_:2},1024)])]),_:1}),Y(Ka,{label:"操作",width:"200",fixed:"right"},{default:Z(({row:a})=>[Y(P,{size:"small",onClick:e=>(async a=>{try{await N.checkDomainHealth(a.domain),O.success("域名检查完成"),oe()}catch(e){O.error("域名检查失败")}})(a)},{default:Z(()=>I[22]||(I[22]=[J("检查",-1)])),_:2,__:[22]},1032,["onClick"]),Y(P,{size:"small",type:"success",onClick:e=>{return l=a,te.value=l,void(se.value=!0);var l}},{default:Z(()=>I[23]||(I[23]=[J("详情",-1)])),_:2,__:[23]},1032,["onClick"]),Y(ke,{onCommand:_e},{dropdown:Z(()=>[Y(ye,null,{default:Z(()=>[Y(be,{command:{action:"restore",domain:a}},{default:Z(()=>I[25]||(I[25]=[J("恢复",-1)])),_:2,__:[25]},1032,["command"]),Y(be,{command:{action:"block",domain:a}},{default:Z(()=>I[26]||(I[26]=[J("封禁",-1)])),_:2,__:[26]},1032,["command"]),Y(be,{command:{action:"delete",domain:a},divided:""},{default:Z(()=>I[27]||(I[27]=[J("删除",-1)])),_:2,__:[27]},1032,["command"])]),_:2},1024)]),default:Z(()=>[Y(P,{size:"small"},{default:Z(()=>[I[24]||(I[24]=J(" 更多",-1)),Y(M,null,{default:Z(()=>[Y(K(V))]),_:1})]),_:1,__:[24]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[De,Ja.value]]),W("div",Oa,[Y(we,{"current-page":ue.page,"onUpdate:currentPage":I[2]||(I[2]=a=>ue.page=a),"page-size":ue.size,"onUpdate:pageSize":I[3]||(I[3]=a=>ue.size=a),total:ue.total,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next",onSizeChange:oe,onCurrentChange:oe},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),Y($,{span:8},{default:Z(()=>[Y(Ve,{title:"浏览器统计",class:"mb-4"},{default:Z(()=>[W("div",Ia,[(T(!0),R(ea,null,la(ae.value,a=>(T(),R("div",{key:a.browser_type,class:"browser-item"},[W("div",Ma,[W("span",Na,r(a.browser_name),1),W("span",Pa,r(a.count),1)]),Y(fe,{percentage:a.percentage,"show-text":!1,"stroke-width":6},null,8,["percentage"])]))),128))])]),_:1}),Y(Ve,{title:"访问趋势"},{default:Z(()=>[W("div",{class:"trend-chart",ref_key:"trendChart",ref:ce,style:{height:"200px"}},null,512)]),_:1})]),_:1})]),_:1}),Y(Se,{modelValue:le.value,"onUpdate:modelValue":I[9]||(I[9]=a=>le.value=a),title:"添加域名",width:"500px"},{footer:Z(()=>[Y(P,{onClick:I[8]||(I[8]=a=>le.value=!1)},{default:Z(()=>I[28]||(I[28]=[J("取消",-1)])),_:1,__:[28]}),Y(P,{type:"primary",onClick:ie},{default:Z(()=>I[29]||(I[29]=[J("添加",-1)])),_:1,__:[29]})]),default:Z(()=>[Y(Ce,{model:re,"label-width":"100px"},{default:Z(()=>[Y(xe,{label:"域名",required:""},{default:Z(()=>[Y(je,{modelValue:re.domain,"onUpdate:modelValue":I[4]||(I[4]=a=>re.domain=a),placeholder:"请输入域名，如：example.com"},null,8,["modelValue"])]),_:1}),Y(xe,{label:"域名类型"},{default:Z(()=>[Y(ta,{modelValue:re.domain_type,"onUpdate:modelValue":I[5]||(I[5]=a=>re.domain_type=a)},{default:Z(()=>[Y(G,{label:"重定向域名",value:"redirect"}),Y(G,{label:"落地页域名",value:"landing"}),Y(G,{label:"API域名",value:"api"})]),_:1},8,["modelValue"])]),_:1}),Y(xe,{label:"优先级"},{default:Z(()=>[Y(ze,{modelValue:re.priority,"onUpdate:modelValue":I[6]||(I[6]=a=>re.priority=a),min:1,max:10},null,8,["modelValue"])]),_:1}),Y(xe,{label:"备注"},{default:Z(()=>[Y(je,{modelValue:re.remarks,"onUpdate:modelValue":I[7]||(I[7]=a=>re.remarks=a),type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),Y(Se,{modelValue:se.value,"onUpdate:modelValue":I[10]||(I[10]=a=>se.value=a),title:"域名详情",width:"70%"},{default:Z(()=>[te.value?(T(),R("div",$a,[Y(qe,{column:2,border:""},{default:Z(()=>[Y(Ue,{label:"域名"},{default:Z(()=>[J(r(te.value.domain),1)]),_:1}),Y(Ue,{label:"状态"},{default:Z(()=>[Y(ve,{type:pe(te.value.status)},{default:Z(()=>[J(r(te.value.status_name),1)]),_:1},8,["type"])]),_:1}),Y(Ue,{label:"健康分数"},{default:Z(()=>[J(r(te.value.health_score)+"%",1)]),_:1}),Y(Ue,{label:"使用次数"},{default:Z(()=>[J(r(te.value.use_count),1)]),_:1}),Y(Ue,{label:"最后检查"},{default:Z(()=>[J(r(me(te.value.last_check_time)),1)]),_:1}),Y(Ue,{label:"最后使用"},{default:Z(()=>[J(r(me(te.value.last_use_time)),1)]),_:1})]),_:1}),te.value.check_results?(T(),R("div",Fa,[I[36]||(I[36]=W("h4",null,"检查结果详情",-1)),Y(F,{gutter:16},{default:Z(()=>[Y($,{span:8},{default:Z(()=>[W("div",Ga,[Y(M,{color:te.value.check_results.accessible?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(u))]),_:1},8,["color"]),I[30]||(I[30]=W("span",null,"可访问性",-1)),Y(ve,{type:te.value.check_results.accessible?"success":"danger",size:"small"},{default:Z(()=>[J(r(te.value.check_results.accessible?"正常":"异常"),1)]),_:1},8,["type"])])]),_:1}),Y($,{span:8},{default:Z(()=>[W("div",Ra,[Y(M,{color:te.value.check_results.dns_resolved?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(u))]),_:1},8,["color"]),I[31]||(I[31]=W("span",null,"DNS解析",-1)),Y(ve,{type:te.value.check_results.dns_resolved?"success":"danger",size:"small"},{default:Z(()=>[J(r(te.value.check_results.dns_resolved?"正常":"异常"),1)]),_:1},8,["type"])])]),_:1}),Y($,{span:8},{default:Z(()=>[W("div",Ta,[Y(M,{color:te.value.check_results.ssl_valid?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(g))]),_:1},8,["color"]),I[32]||(I[32]=W("span",null,"SSL证书",-1)),Y(ve,{type:te.value.check_results.ssl_valid?"success":"danger",size:"small"},{default:Z(()=>[J(r(te.value.check_results.ssl_valid?"有效":"无效"),1)]),_:1},8,["type"])])]),_:1})]),_:1}),Y(F,{gutter:16,class:"mt-3"},{default:Z(()=>[Y($,{span:8},{default:Z(()=>[W("div",Wa,[Y(M,{color:te.value.check_results.wechat_accessible?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(y))]),_:1},8,["color"]),I[33]||(I[33]=W("span",null,"微信访问",-1)),Y(ve,{type:te.value.check_results.wechat_accessible?"success":"danger",size:"small"},{default:Z(()=>[J(r(te.value.check_results.wechat_accessible?"正常":"受限"),1)]),_:1},8,["type"])])]),_:1}),Y($,{span:8},{default:Z(()=>[W("div",Ya,[Y(M,{color:te.value.check_results.qq_accessible?"#67c23a":"#f56c6c"},{default:Z(()=>[Y(K(k))]),_:1},8,["color"]),I[34]||(I[34]=W("span",null,"QQ访问",-1)),Y(ve,{type:te.value.check_results.qq_accessible?"success":"danger",size:"small"},{default:Z(()=>[J(r(te.value.check_results.qq_accessible?"正常":"受限"),1)]),_:1},8,["type"])])]),_:1}),Y($,{span:8},{default:Z(()=>[W("div",Za,[Y(M,null,{default:Z(()=>[Y(K(B))]),_:1}),I[35]||(I[35]=W("span",null,"响应时间",-1)),Y(ve,{size:"small"},{default:Z(()=>[J(r(te.value.check_results.response_time)+"ms",1)]),_:1})])]),_:1})]),_:1})])):sa("",!0)])):sa("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-7035d1c4"]]);export{Ja as default};
