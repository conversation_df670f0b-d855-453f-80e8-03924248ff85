<template>
  <div class="app-container">
    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索链接名称、目标URL"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
        clearable
      />
      <el-select
        v-model="listQuery.type"
        placeholder="链接类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="全部" value="" />
        <el-option label="群组推广" value="group" />
        <el-option label="分销推广" value="distribution" />
        <el-option label="活动推广" value="activity" />
        <el-option label="其他" value="other" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="全部" value="" />
        <el-option label="活跃" value="active" />
        <el-option label="暂停" value="paused" />
        <el-option label="过期" value="expired" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="Search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="success" icon="Plus" @click="handleCreate">
        创建链接
      </el-button>
      <el-button class="filter-item" type="warning" icon="Download" @click="handleExport">
        导出数据
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <stat-card
          type="primary"
          :icon="Link"
          :value="stats.total_links"
          label="总链接数"
          :trend="{ type: 'up', value: '+15', desc: '较上月' }"
          clickable
          @click="navigateToList"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="success"
          :icon="View"
          :value="stats.total_clicks"
          label="总点击量"
          :trend="{ type: 'up', value: '+28.5%', desc: '较上月' }"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="warning"
          :icon="User"
          :value="stats.unique_visitors"
          label="独立访客"
          :trend="{ type: 'up', value: '+18.2%', desc: '较上月' }"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="danger"
          :icon="TrendCharts"
          :value="stats.conversion_rate"
          label="转化率"
          suffix="%"
          :decimals="1"
          :trend="{ type: 'up', value: '+2.1%', desc: '较上月' }"
        />
      </el-col>
    </el-row>

    <!-- 推广链接列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>推广链接列表</h3>
          <div>
            <el-button type="primary" size="small" @click="handleBatchOperation">批量操作</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="链接ID" prop="id" width="80" />
        <el-table-column label="链接信息" width="250">
          <template #default="{ row }">
            <div class="link-info">
              <div class="link-name">{{ row.name }}</div>
              <div class="link-url">
                <el-link :href="row.short_url" target="_blank" type="primary">
                  {{ row.short_url }}
                </el-link>
                <el-button 
                  type="text" 
                  size="small" 
                  @click="copyToClipboard(row.short_url)"
                  class="copy-btn"
                >
                  <el-icon><DocumentCopy /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="目标URL" width="200">
          <template #default="{ row }">
            <div class="target-url" :title="row.target_url">
              {{ truncateUrl(row.target_url) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="点击统计" width="120">
          <template #default="{ row }">
            <div class="click-stats">
              <div class="total-clicks">{{ row.click_count || 0 }} 次</div>
              <div class="today-clicks">今日: {{ row.today_clicks || 0 }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="转化数据" width="120">
          <template #default="{ row }">
            <div class="conversion-stats">
              <div class="conversions">{{ row.conversions || 0 }} 转化</div>
              <div class="conversion-rate">{{ ((row.conversions || 0) / Math.max(row.click_count || 1, 1) * 100).toFixed(1) }}%</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handleAnalytics(row)">
              统计
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`qrcode-${row.id}`">生成二维码</el-dropdown-item>
                  <el-dropdown-item :command="`copy-${row.id}`">复制链接</el-dropdown-item>
                  <el-dropdown-item :command="`pause-${row.id}`" v-if="row.status === 'active'">暂停链接</el-dropdown-item>
                  <el-dropdown-item :command="`resume-${row.id}`" v-if="row.status === 'paused'">恢复链接</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${row.id}`" divided>删除链接</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 链接编辑对话框 -->
    <LinkDialog
      v-model="dialogVisible"
      :link-data="currentLink"
      @success="handleDialogSuccess"
    />

    <!-- 链接统计抽屉 -->
    <LinkAnalyticsDrawer
      v-model="analyticsDrawerVisible"
      :link-id="currentLinkId"
    />

    <!-- 二维码对话框 -->
    <QRCodeDialog
      v-model="qrcodeDialogVisible"
      :link-data="currentLink"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Link, 
  View, 
  User, 
  TrendCharts, 
  Search, 
  Plus, 
  Download, 
  ArrowDown,
  DocumentCopy
} from '@element-plus/icons-vue'
import StatCard from '@/components/StatCard.vue'
import LinkDialog from './components/LinkDialog.vue'
import LinkAnalyticsDrawer from './components/LinkAnalyticsDrawer.vue'
import QRCodeDialog from './components/QRCodeDialog.vue'
import { promotionApi, promotionAnalyticsApi } from '@/api/promotion'
import { formatDate } from '@/utils/format'

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const dialogVisible = ref(false)
const analyticsDrawerVisible = ref(false)
const qrcodeDialogVisible = ref(false)
const currentLink = ref({})
const currentLinkId = ref(null)
const multipleSelection = ref([])

// 统计数据
const stats = ref({
  total_links: 0,
  total_clicks: 0,
  unique_visitors: 0,
  conversion_rate: 0
})

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  type: '',
  status: ''
})

// 获取推广链接列表
const getList = async () => {
  listLoading.value = true
  try {
    const { data } = await promotionApi.getList(listQuery)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取推广链接列表失败:', error)
    ElMessage.error('获取推广链接列表失败')
  } finally {
    listLoading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const { data } = await promotionApi.getStats()
    stats.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 筛选
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 创建链接
const handleCreate = () => {
  currentLink.value = {}
  dialogVisible.value = true
}

// 编辑链接
const handleEdit = (row) => {
  currentLink.value = { ...row }
  dialogVisible.value = true
}

// 查看统计
const handleAnalytics = (row) => {
  currentLinkId.value = row.id
  analyticsDrawerVisible.value = true
}

// 生成二维码
const handleQRCode = (row) => {
  currentLink.value = { ...row }
  qrcodeDialogVisible.value = true
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 更新链接状态
const handleUpdateStatus = async (linkId, status) => {
  try {
    const action = status === 'paused' ? '暂停' : '恢复'
    await ElMessageBox.confirm(`确定要${action}这个链接吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await promotionApi.update(linkId, { status })
    ElMessage.success(`${action}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 删除链接
const handleDelete = async (linkId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个推广链接吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await promotionApi.delete(linkId)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  const [action, linkId] = command.split('-')
  const id = parseInt(linkId)
  const link = list.value.find(l => l.id === id)
  
  switch (action) {
    case 'qrcode':
      currentLink.value = { ...link }
      qrcodeDialogVisible.value = true
      break
    case 'copy':
      copyToClipboard(link.short_url)
      break
    case 'pause':
      handleUpdateStatus(id, 'paused')
      break
    case 'resume':
      handleUpdateStatus(id, 'active')
      break
    case 'delete':
      handleDelete(id)
      break
  }
}

// 导出数据
const handleExport = async () => {
  try {
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量操作
const handleBatchOperation = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要操作的链接')
    return
  }
  
  ElMessageBox.confirm('请选择批量操作类型', '批量操作', {
    distinguishCancelAndClose: true,
    confirmButtonText: '批量暂停',
    cancelButtonText: '批量恢复'
  }).then(() => {
    batchUpdateStatus('paused')
  }).catch((action) => {
    if (action === 'cancel') {
      batchUpdateStatus('active')
    }
  })
}

// 批量更新状态
const batchUpdateStatus = async (status) => {
  try {
    const linkIds = multipleSelection.value.map(link => link.id)
    ElMessage.success('批量操作成功')
    getList()
  } catch (error) {
    ElMessage.error('批量操作失败')
  }
}

// 选择变化
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 分页
const handleSizeChange = (val) => {
  listQuery.limit = val
  getList()
}

const handleCurrentChange = (val) => {
  listQuery.page = val
  getList()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  getList()
  getStats()
}

// 导航到列表
const navigateToList = () => {
  getList()
}

// 工具函数
const getTypeTagType = (type) => {
  const types = {
    group: 'primary',
    distribution: 'success',
    activity: 'warning',
    other: 'info'
  }
  return types[type] || 'info'
}

const getTypeText = (type) => {
  const texts = {
    group: '群组推广',
    distribution: '分销推广',
    activity: '活动推广',
    other: '其他'
  }
  return texts[type] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    paused: 'warning',
    expired: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    paused: '暂停',
    expired: '过期'
  }
  return texts[status] || '未知'
}

const truncateUrl = (url) => {
  if (!url) return ''
  return url.length > 30 ? url.substring(0, 30) + '...' : url
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 24px;
}

.link-info {
  .link-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
  }
  
  .link-url {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .copy-btn {
      padding: 0;
      min-height: auto;
      
      &:hover {
        color: #409eff;
      }
    }
  }
}

.target-url {
  color: #666;
  font-size: 12px;
  word-break: break-all;
}

.click-stats,
.conversion-stats {
  .total-clicks,
  .conversions {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
  }
  
  .today-clicks,
  .conversion-rate {
    font-size: 12px;
    color: #666;
  }
}

.pagination-container {
  padding: 32px 16px;
  text-align: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }
}
</style>