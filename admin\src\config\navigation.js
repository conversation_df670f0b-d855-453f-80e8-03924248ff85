/**
 * 角色导航配置
 * 定义不同角色用户可以访问的菜单和路由
 */

// 角色导航权限配置
export const roleNavigationConfig = {
  // 超级管理员 - 拥有所有权限
  admin: {
    allowedRoutes: ['*'], // * 表示所有路由
    defaultRoute: '/dashboard',
    workbench: '/dashboard'
  },
  
  // 分站管理员 - 管理本分站相关功能
  substation: {
    allowedRoutes: [
      '/dashboard',
      '/user',
      '/community', 
      '/orders',
      '/finance',
      '/agent',
      '/substation',
      '/distribution/distributors',
      '/promotion'
    ],
    defaultRoute: '/dashboard',
    workbench: '/dashboard'
  },
  
  // 代理商 - 代理商相关功能
  agent: {
    allowedRoutes: [
      '/agent/dashboard',
      '/agent/list',
      '/agent/applications',
      '/agent/commission',
      '/agent/hierarchy', 
      '/agent/performance',
      '/user/center',
      '/user/profile',
      '/finance/commission-logs',
      '/promotion/links'
    ],
    defaultRoute: '/agent/dashboard',
    workbench: '/agent/dashboard'
  },
  
  // 分销员 - 分销和客户管理功能
  distributor: {
    allowedRoutes: [
      '/distributor/dashboard',
      '/distribution/customers',
      '/community/groups',
      '/orders/list',
      '/finance/commission-logs',
      '/promotion/links',
      '/user/center',
      '/user/profile'
    ],
    defaultRoute: '/distributor/dashboard',
    workbench: '/distributor/dashboard'
  },
  
  // 群主 - 群组管理功能
  group_owner: {
    allowedRoutes: [
      '/owner/dashboard',
      '/community/groups',
      '/content/management',
      '/content/templates',
      '/user/center',
      '/user/profile',
      '/orders/list'
    ],
    defaultRoute: '/owner/dashboard',
    workbench: '/owner/dashboard'
  },
  
  // 普通用户 - 基础功能
  user: {
    allowedRoutes: [
      '/user/center',
      '/user/profile',
      '/orders/my',
      '/community/my-groups'
    ],
    defaultRoute: '/user/center',
    workbench: '/user/center'
  }
}

// 角色专属工作台配置
export const roleWorkbenchConfig = {
  admin: {
    title: '管理员控制台',
    description: '系统全局管理和监控',
    quickActions: [
      { name: '用户管理', path: '/user/list', icon: 'User' },
      { name: '系统监控', path: '/system/monitor', icon: 'Monitor' },
      { name: '财务总览', path: '/finance/dashboard', icon: 'Money' },
      { name: '数据分析', path: '/dashboard', icon: 'DataAnalysis' },
      { name: '支付设置', path: '/payment/settings', icon: 'CreditCard' }
    ]
  },
  
  substation: {
    title: '分站管理中心',
    description: '分站运营和用户管理',
    quickActions: [
      { name: '分站用户', path: '/user/list', icon: 'User' },
      { name: '代理商管理', path: '/agent/list', icon: 'Avatar' },
      { name: '分站财务', path: '/substation/finance', icon: 'Money' },
      { name: '权限配置', path: '/substation/permissions', icon: 'Lock' }
    ]
  },
  
  agent: {
    title: '代理商工作台',
    description: '团队管理和业绩分析',
    quickActions: [
      { name: '我的团队', path: '/agent/hierarchy', icon: 'Connection' },
      { name: '佣金管理', path: '/agent/commission', icon: 'Money' },
      { name: '绩效分析', path: '/agent/performance', icon: 'TrendCharts' },
      { name: '申请管理', path: '/agent/applications', icon: 'Document' }
    ]
  },
  
  distributor: {
    title: '分销员工作台',
    description: '客户管理和群组运营',
    quickActions: [
      { name: '客户管理', path: '/distribution/customers', icon: 'User' },
      { name: '我的群组', path: '/community/groups', icon: 'Comment' },
      { name: '推广链接', path: '/promotion/links', icon: 'Link' },
      { name: '佣金查看', path: '/finance/commission-logs', icon: 'Money' }
    ]
  },
  
  group_owner: {
    title: '群主工作台',
    description: '群组内容和成员管理',
    quickActions: [
      { name: '我的群组', path: '/community/groups', icon: 'Comment' },
      { name: '内容管理', path: '/content/management', icon: 'Edit' },
      { name: '群组统计', path: '/community/analytics', icon: 'DataAnalysis' },
      { name: '模板管理', path: '/content/templates', icon: 'DocumentCopy' }
    ]
  },
  
  user: {
    title: '个人中心',
    description: '个人信息和订单管理',
    quickActions: [
      { name: '我的订单', path: '/orders/my', icon: 'Tickets' },
      { name: '我的群组', path: '/community/my-groups', icon: 'Comment' },
      { name: '个人资料', path: '/user/profile', icon: 'User' },
      { name: '账户设置', path: '/user/settings', icon: 'Setting' }
    ]
  }
}

// 菜单权限检查函数
export function checkMenuPermission(route, userRole) {
  if (!userRole || !route) return false
  
  const config = roleNavigationConfig[userRole]
  if (!config) return false
  
  // 管理员拥有所有权限
  if (config.allowedRoutes.includes('*')) return true
  
  // 检查路由是否在允许列表中
  const routePath = route.path || route
  return config.allowedRoutes.some(allowedRoute => {
    // 精确匹配
    if (allowedRoute === routePath) return true
    
    // 前缀匹配（支持子路由）
    if (routePath.startsWith(allowedRoute + '/')) return true
    
    return false
  })
}

// 获取用户默认路由
export function getUserDefaultRoute(userRole) {
  const config = roleNavigationConfig[userRole]
  return config?.defaultRoute || '/user/center'
}

// 获取用户工作台配置
export function getUserWorkbenchConfig(userRole) {
  return roleWorkbenchConfig[userRole] || roleWorkbenchConfig.user
}

// 过滤路由菜单
export function filterRoutesByRole(routes, userRole) {
  if (!userRole || !routes) return []
  
  return routes.filter(route => {
    // 跳过隐藏的路由
    if (route.meta?.hidden) return false
    
    // 检查路由权限
    if (!checkMenuPermission(route, userRole)) return false
    
    // 递归过滤子路由
    if (route.children && route.children.length > 0) {
      route.children = filterRoutesByRole(route.children, userRole)
      // 如果所有子路由都被过滤掉，则隐藏父路由
      if (route.children.length === 0 && route.redirect) {
        return false
      }
    }
    
    return true
  })
}

// 角色显示名称映射
export const roleDisplayNames = {
  admin: '超级管理员',
  substation: '分站管理员', 
  agent: '代理商',
  distributor: '分销员',
  group_owner: '群主',
  user: '普通用户'
}

// 获取角色显示名称
export function getRoleDisplayName(role) {
  return roleDisplayNames[role] || '未知角色'
}