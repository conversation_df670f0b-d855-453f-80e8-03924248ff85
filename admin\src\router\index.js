import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/layout/ModernLayout.vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { sessionManager, logAccess, logSession } from '@/utils/security'

const routes = [
  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', hidden: true }
  },
  // 根路径重定向到仪表板（预览模式）
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/fullscreen-data-screen',
    name: 'FullscreenDataScreen',
    component: () => import('@/views/dashboard/DataScreenFullscreen.vue'),
    meta: { title: '数据大屏', hidden: true, fullscreen: true }
  },
  {
    path: '/test',
    name: 'TestPage',
    component: () => import('@/views/TestPage.vue'),
    meta: { title: '测试页面', hidden: true }
  },
  {
    path: '/dashboard-direct',
    name: 'DashboardDirect',
    component: () => import('@/views/dashboard/ModernDashboard.vue'),
    meta: { title: '直接仪表板', hidden: true }
  },
  {
    path: '/route-checker',
    name: 'RouteChecker',
    component: () => import('@/views/RouteChecker.vue'),
    meta: { title: '路由检测工具', hidden: true }
  },


  {
    path: '/dashboard',
    component: Layout,
    meta: { title: '数据看板', icon: 'Monitor' },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/ModernDashboard.vue'),
        meta: { title: '数据看板', icon: 'Monitor' }
      },
      {
        path: '/security',
        name: 'SecurityManagement',
        component: () => import('@/views/security/SecurityManagement.vue'),
        meta: { title: '安全管理', requiresAuth: true }
      },
    ]
  },
  {
    path: '/data-screen',
    component: Layout,
    meta: { title: '数据大屏', icon: 'DataLine' },
    children: [
      {
        path: '',
        name: 'DataScreen',
        component: () => import('@/views/dashboard/DataScreen.vue'),
        meta: { title: '数据大屏', icon: 'DataLine' }
      },
      {
        path: 'reports',
        name: 'DashboardReports',
        component: () => import('@/views/dashboard/Reports.vue'),
        meta: { title: '数据报表', icon: 'DataAnalysis' }
      }
    ]
  },
  {
    path: '/community',
    component: Layout,
    redirect: '/community/groups',
    meta: { title: '社群管理', icon: 'Comment' },
    children: [
      {
        path: 'groups',
        name: 'GroupList',
        component: () => import('@/views/community/GroupList.vue'),
        meta: { title: '社群列表', icon: 'UserFilled' }
      },
      {
        path: 'rules',
        name: 'AutoRules',
        component: () => import('@/views/community/AutoRules.vue'),
        meta: { title: '自动化规则', icon: 'MagicStick' }
      },
      {
        path: 'events',
        name: 'EventManagement',
        component: () => import('@/views/community/EventManagement.vue'),
        meta: { title: '活动管理', icon: 'Ticket' }
      },
      {
        path: 'moderation',
        name: 'ContentModeration',
        component: () => import('@/views/community/ContentModeration.vue'),
        meta: { title: '内容审核', icon: 'ShieldCheck' }
      },
      {
        path: 'analytics',
        name: 'CommunityAnalytics',
        component: () => import('@/views/community/AnalyticsDashboard.vue'),
        meta: { title: '数据分析', icon: 'DataLine' }
      },
      {
        path: 'members/:id',
        name: 'UserProfile',
        component: () => import('@/views/community/UserProfile.vue'),
        meta: { title: '用户画像', hidden: true }
      },
      {
        path: 'templates',
        name: 'TemplateManagement',
        component: () => import('@/views/community/TemplateManagement.vue'),
        meta: { title: '模板管理', icon: 'Document' }
      },
      {
        path: 'add',
        name: 'GroupAdd',
        component: () => import('@/views/community/GroupAdd.vue'),
        meta: { title: '创建群组', icon: 'Plus' }
      },
      {
        path: 'detail/:id',
        name: 'GroupDetail',
        component: () => import('@/views/community/GroupDetail.vue'),
        meta: { title: '群组详情', icon: 'View', hidden: true }
      },
      {
        path: 'marketing',
        name: 'GroupMarketing',
        component: () => import('@/views/community/GroupMarketing.vue'),
        meta: { title: '营销配置', icon: 'Promotion' }
      }
    ]
  },
  {
    path: '/distribution',
    component: Layout,
    redirect: '/distribution/groups',
    name: 'Distribution',
    meta: { title: '分销管理', icon: 'Share' },
    children: [

      {
        path: 'distributors',
        name: 'DistributorList',
        component: () => import('@/views/distribution/DistributorList.vue'),
        meta: { title: '分销商管理', icon: 'UserFilled' }
      },
      {
        path: 'detail/:id',
        name: 'DistributorDetail',
        component: () => import('@/views/distribution/DistributorDetail.vue'),
        meta: { title: '分销员详情', icon: 'User', hidden: true }
      },
      {
        path: 'customers',
        name: 'CustomerManagement',
        component: () => import('@/views/distributor/CustomerManagement.vue'),
        meta: { title: '客户管理', icon: 'UserFilled' }
      }
    ]
  },
  {
    path: '/distributor',
    component: Layout,
    redirect: '/distributor/dashboard',
    name: 'Distributor',
    meta: { title: '分销员工作台', icon: 'User', roles: ['distributor'] },
    children: [
      {
        path: 'dashboard',
        name: 'DistributorDashboard',
        component: () => import('@/views/distributor/DistributorDashboard.vue'),
        meta: { title: '工作台', icon: 'Monitor', roles: ['distributor'] }
      },
      {
        path: 'group-management',
        name: 'DistributorGroupManagement',
        component: () => import('@/views/distributor/GroupManagement.vue'),
        meta: { title: '群组管理', icon: 'Comment', roles: ['distributor'] }
      },
      {
        path: 'promotion-links',
        name: 'DistributorPromotionLinks',
        component: () => import('@/views/distributor/PromotionLinks.vue'),
        meta: { title: '推广链接', icon: 'Link', roles: ['distributor'] }
      },
      {
        path: 'commission-logs',
        name: 'DistributorCommissionLogs',
        component: () => import('@/views/distributor/CommissionLogs.vue'),
        meta: { title: '佣金查看', icon: 'Money', roles: ['distributor'] }
      },

    ]
  },
  {
    path: '/owner',
    component: Layout,
    redirect: '/owner/dashboard',
    name: 'Owner',
    meta: { title: '群主工作台', icon: 'Comment', roles: ['group_owner'] },
    children: [
      {
        path: 'dashboard',
        name: 'OwnerDashboard',
        component: () => import('@/views/owner/OwnerDashboard.vue'),
        meta: { title: '群主工作台', icon: 'Monitor', roles: ['group_owner'] }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/dashboard',
    name: 'Finance',
    meta: { title: '财务管理', icon: 'Money' },
    children: [
      {
        path: 'dashboard',
        name: 'FinanceDashboard',
        component: () => import('@/views/finance/FinanceDashboard.vue'),
        meta: { title: '财务总览', icon: 'DataLine' }
      },
      {
        path: 'commission-logs',
        name: 'CommissionLog',
        component: () => import('@/views/finance/CommissionLog.vue'),
        meta: { title: '佣金明细', icon: 'Medal' }
      },
      {
        path: 'transactions',
        name: 'TransactionList',
        component: () => import('@/views/finance/TransactionList.vue'),
        meta: { title: '交易记录', icon: 'Goods' }
      },
      {
        path: 'withdraw',
        name: 'WithdrawManage',
        component: () => import('@/views/finance/WithdrawManage.vue'),
        meta: { title: '提现管理', icon: 'Upload' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/center',
    name: 'User',
    meta: { title: '用户管理', icon: 'User' },
    children: [
      {
        path: 'center',
        name: 'UserCenter',
        component: () => import('@/views/user/UserCenter.vue'),
        meta: { title: '用户中心', icon: 'User' }
      },
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/UserList.vue'),
        meta: { title: '用户列表', icon: 'UserFilled' }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/user/Profile.vue'),
        meta: { title: '个人资料', icon: 'Avatar' }
      },
      {
        path: 'analytics',
        name: 'UserAnalytics',
        component: () => import('@/views/user/UserAnalytics.vue'),
        meta: { title: '用户分析', icon: 'DataAnalysis' }
      },
      {
        path: 'add',
        name: 'UserAdd',
        component: () => import('@/views/user/UserAdd.vue'),
        meta: { title: '添加用户', icon: 'Plus' }
      }
    ]
  },
  {
    path: '/substation',
    component: Layout,
    redirect: '/substation/list',
    name: 'Substation',
    meta: { title: '分站管理', icon: 'OfficeBuilding' },
    children: [
      {
        path: 'list',
        name: 'SubstationList',
        component: () => import('@/views/substation/SubstationList.vue'),
        meta: { title: '分站列表', icon: 'List' }
      },
      {
        path: 'finance',
        name: 'SubstationFinance',
        component: () => import('@/views/substation/SubstationFinance.vue'),
        meta: { title: '分站财务', icon: 'Money' }
      },

      {
        path: 'analytics',
        name: 'SubstationAnalytics',
        component: () => import('@/views/substation/SubstationAnalytics.vue'),
        meta: { title: '分站分析', icon: 'DataAnalysis' }
      }
    ]
  },
  {
    path: '/agent',
    component: Layout,
    redirect: '/agent/dashboard',
    name: 'Agent',
    meta: { title: '代理商管理', icon: 'Avatar' },
    children: [
      {
        path: 'dashboard',
        name: 'AgentDashboard',
        component: () => import('@/views/agent/AgentDashboard.vue'),
        meta: { title: '代理商工作台', icon: 'Monitor' }
      },
      {
        path: 'list',
        name: 'AgentList',
        component: () => import('@/views/agent/AgentList.vue'),
        meta: { title: '代理商列表', icon: 'List' }
      },
      {
        path: 'applications',
        name: 'AgentApplications',
        component: () => import('@/views/agent/AgentApplications.vue'),
        meta: { title: '申请管理', icon: 'Document' }
      },

      {
        path: 'hierarchy',
        name: 'AgentHierarchy',
        component: () => import('@/views/agent/AgentHierarchy.vue'),
        meta: { title: '代理商层级', icon: 'Connection' }
      },
      {
        path: 'performance',
        name: 'AgentPerformance',
        component: () => import('@/views/agent/AgentPerformance.vue'),
        meta: { title: '绩效分析', icon: 'TrendCharts' }
      }
    ]
  },
  {
    path: '/anti-block',
    component: Layout,
    redirect: '/anti-block/dashboard',
    name: 'AntiBlock',
    meta: { title: '防红系统', icon: 'Tools' },
    children: [
      {
        path: 'dashboard',
        name: 'AntiBlockDashboard',
        component: () => import('@/views/anti-block/Dashboard.vue'),
        meta: { title: '系统概览', icon: 'DataLine' }
      },
      {
        path: 'enhanced',
        name: 'AntiBlockEnhanced',
        component: () => import('@/views/anti-block/EnhancedDashboard.vue'),
        meta: { title: '增强管理', icon: 'Monitor' }
      },
      {
        path: 'domains',
        name: 'DomainList',
        component: () => import('@/views/anti-block/DomainList.vue'),
        meta: { title: '域名管理', icon: 'Connection' }
      },
      {
        path: 'short-links',
        name: 'ShortLinkList',
        component: () => import('@/views/anti-block/ShortLinkList.vue'),
        meta: { title: '短链接管理', icon: 'Link' }
      },
      {
        path: 'analytics',
        name: 'AntiBlockAnalytics',
        component: () => import('@/views/anti-block/Analytics.vue'),
        meta: { title: '统计分析', icon: 'DataAnalysis' }
      }
    ]
  },

  {
    path: '/permission',
    component: Layout,
    redirect: '/permission/roles',
    name: 'Permission',
    meta: { title: '权限管理', icon: 'Lock' },
    children: [
      {
        path: 'roles',
        name: 'RoleManagement',
        component: () => import('@/views/permission/RoleManagement.vue'),
        meta: { title: '角色管理', icon: 'UserFilled' }
      },
      {
        path: 'permissions',
        name: 'PermissionManagement',
        component: () => import('@/views/permission/PermissionManagement.vue'),
        meta: { title: '权限配置', icon: 'Key' }
      }
    ]
  },
  {
    path: '/promotion',
    component: Layout,
    redirect: '/promotion/links',
    name: 'Promotion',
    meta: { title: '推广管理', icon: 'Share' },
    children: [
      {
        path: 'links',
        name: 'PromotionLinks',
        component: () => import('@/views/promotion/LinkManagement.vue'),
        meta: { title: '推广链接', icon: 'Link' }
      },
      {
        path: 'landing-pages',
        name: 'LandingPages',
        component: () => import('@/views/promotion/LandingPages.vue'),
        meta: { title: '落地页管理', icon: 'Document' }
      },
      {
        path: 'analytics',
        name: 'PromotionAnalytics',
        component: () => import('@/views/promotion/Analytics.vue'),
        meta: { title: '推广分析', icon: 'DataAnalysis' }
      }
    ]
  },
  {
    path: '/orders',
    component: Layout,
    redirect: '/orders/list',
    name: 'Orders',
    meta: { title: '订单管理', icon: 'Tickets' },
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/orders/OrderList.vue'),
        meta: { title: '订单列表', icon: 'List' }
      },
      {
        path: 'analytics',
        name: 'OrderAnalytics',
        component: () => import('@/views/orders/OrderAnalytics.vue'),
        meta: { title: '订单分析', icon: 'DataAnalysis' }
      },
      {
        path: 'detail/:id',
        name: 'OrderDetail',
        component: () => import('@/views/orders/OrderDetail.vue'),
        meta: { title: '订单详情', icon: 'View', hidden: true }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/settings',
    name: 'System',
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('@/views/system/Settings.vue'),
        meta: { title: '系统设置', icon: 'Tools' }
      },


      {
        path: 'export',
        name: 'DataExport',
        component: () => import('@/views/system/DataExport.vue'),
        meta: { title: '数据导出', icon: 'Download' }
      },
      {
        path: 'notifications',
        name: 'SystemNotifications',
        component: () => import('@/views/system/Notifications.vue'),
        meta: { title: '通知管理', icon: 'Bell' }
      },
      {
        path: 'operation-logs',
        name: 'OperationLogs',
        component: () => import('@/views/system/OperationLogs.vue'),
        meta: { title: '操作日志', icon: 'Document' }
      },
      {
        path: 'function-test',
        name: 'FunctionTest',
        component: () => import('@/views/system/FunctionTest.vue'),
        meta: { title: '功能测试', icon: 'Cpu' }
      },
      {
        path: 'user-guide',
        name: 'UserGuide',
        component: () => import('@/views/system/UserGuide.vue'),
        meta: { title: '使用指南', icon: 'InfoFilled' }
      },

      {
        path: 'file-management',
        name: 'FileManagement',
        component: () => import('@/views/system/FileManagement.vue'),
        meta: { title: '文件管理', icon: 'Folder' }
      },

    ]
  },
  {
    path: '/payment',
    component: Layout,
    redirect: '/payment/settings',
    name: 'Payment',
    meta: { title: '支付管理', icon: 'CreditCard' },
    children: [
      {
        path: 'settings',
        name: 'PaymentSettings',
        component: () => import('@/views/payment/PaymentSettings.vue'),
        meta: { title: '支付设置', icon: 'Setting' }
      },
      {
        path: 'orders',
        name: 'PaymentOrders',
        component: () => import('@/views/payment/PaymentOrders.vue'),
        meta: { title: '支付订单', icon: 'Tickets' }
      },
      {
        path: 'refunds',
        name: 'PaymentRefunds',
        component: () => import('@/views/payment/PaymentRefunds.vue'),
        meta: { title: '退款管理', icon: 'RefreshLeft' }
      }
    ]
  },
  {
    path: '/security',
    component: Layout,
    redirect: '/security/management',
    name: 'Security',
    meta: { title: '安全管理', icon: 'Lock' },
    children: [
      {
        path: 'management',
        name: 'SecurityManagement',
        component: () => import('@/views/security/SecurityManagement.vue'),
        meta: { title: '安全管理', icon: 'Shield' }
      }
    ]
  },
  // 错误页面
  {
    path: '/error/:type?',
    name: 'ErrorPage',
    component: () => import('@/views/ErrorPage.vue'),
    meta: { title: '错误页面', hidden: true }
  },
  {
    path: '/403',
    redirect: '/error/403'
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/error/404'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.DEV ? '/' : '/admin/'),
  routes
})

// 路由守卫 - 预览模式下暂时禁用
// router.beforeEach(async (to, from, next) => {
//   NProgress.start()

//   const userStore = useUserStore()
//   const hasToken = userStore.token

//   if (hasToken) {
//     // 检查会话是否有效
//     if (!sessionManager.isSessionValid()) {
//       // 会话已过期，清理数据并重定向到登录页
//       await userStore.logout()
//       ElMessage.warning('会话已过期，请重新登录')
//       logSession(userStore.userInfo?.username || 'unknown', 'session_expired', sessionManager.getCurrentSession()?.id, '会话超时')
//       next(`/login?redirect=${to.path}`)
//       return
//     }

//     // 更新会话活动时间
//     sessionManager.updateActivity()

//     if (to.path === '/login') {
//       // 已登录用户访问登录页，重定向到默认页面
//       const userRole = userStore.userInfo?.role
//       if (userRole) {
//         const { getUserDefaultRoute } = await import('@/config/navigation')
//         const defaultRoute = getUserDefaultRoute(userRole)
//         next({ path: defaultRoute })
//       } else {
//         next({ path: '/dashboard' })
//       }
//     } else {
//       // 确保用户信息已加载
//       if (!userStore.userInfo) {
//         try {
//           await userStore.getUserInfo()
//         } catch (error) {
//           // 获取用户信息失败，可能token已失效
//           await userStore.logout()
//           logSession(userStore.userInfo?.username || 'unknown', 'token_invalid', sessionManager.getCurrentSession()?.id, error.message)
//           next(`/login?redirect=${to.path}`)
//           return
//         }
//       }

//       // 检查路由权限
//       const { checkRoutePermission } = await import('@/utils/permission')
//       const userRole = userStore.userInfo?.role
//       const username = userStore.userInfo?.username

//       if (!checkRoutePermission(to, userRole)) {
//         // 记录未授权访问尝试
//         logAccess(username, to.path, 'access', false, '权限不足')

//         // 无权限访问，重定向到403页面或默认页面
//         if (to.path !== '/403') {
//           ElMessage.error('您没有权限访问此页面')
//           next('/403')
//         } else {
//           next()
//         }
//         return
//       }

//       // 记录成功访问
//       logAccess(username, to.path, 'access', true, `从 ${from.path} 访问`)

//       next()
//     }
//   } else {
//     // 未登录用户
//     if (to.path !== '/login') {
//       // 记录未授权访问尝试
//       logAccess('anonymous', to.path, 'access', false, '未登录')
//       next(`/login?redirect=${to.path}`)
//     } else {
//       next()
//     }
//   }
// })

// 路由守卫 - 支持预览模式
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const userStore = useUserStore()
  const hasToken = userStore.token

  // 检查是否启用了预览模式（通过URL参数或localStorage）
  const urlParams = new URLSearchParams(window.location.search)
  const previewMode = urlParams.get('preview') === 'true' || localStorage.getItem('preview-mode') === 'true'

  if (previewMode) {
    console.log('🎭 启用预览模式：', to.path)
    // 设置预览模式token和用户信息
    const previewToken = `preview-mode-token-${Date.now()}`
    userStore.setToken(previewToken)

    // 确保用户信息正确设置
    const previewUserInfo = {
      id: 'preview-user',
      username: 'admin',
      nickname: '超级管理员 (预览)',
      name: '预览用户',
      email: '<EMAIL>',
      avatar: '/default-avatar.png',
      role: 'admin',
      roles: ['admin'],
      permissions: ['*']
    }

    userStore.setUserInfo(previewUserInfo)
    localStorage.setItem('preview-mode', 'true')

    console.log('✅ 预览模式用户信息已设置:', previewUserInfo)
    next()
    return
  }

  // 如果是预览模式token，直接放行
  if (hasToken && hasToken.startsWith('preview-mode-token-')) {
    console.log('🎭 预览模式路由守卫：允许访问', to.path)
    next()
    return
  }

  if (hasToken) {
    // 正常登录模式的处理逻辑
    if (to.path === '/login') {
      next({ path: '/dashboard' })
    } else {
      // 确保用户信息已加载
      if (!userStore.userInfo) {
        try {
          await userStore.getUserInfo()
        } catch (error) {
          await userStore.logout()
          next(`/login?redirect=${to.path}`)
          return
        }
      }
      next()
    }
  } else {
    // 未登录用户
    if (to.path !== '/login') {
      next(`/login?redirect=${to.path}`)
    } else {
      next()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})

export default router 