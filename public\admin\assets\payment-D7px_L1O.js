import{c as e}from"./index-D2bI4m-v.js";const t={getPaymentConfig:()=>e({url:"/admin/system/payment/config",method:"get"}),updatePaymentConfig:t=>e({url:"/admin/system/payment/config",method:"put",data:t}),togglePaymentMethod:(t,a)=>e({url:`/admin/system/payment/${t}/toggle`,method:"post",data:{enabled:a}}),testPaymentChannel:(t,a)=>e({url:`/admin/system/payment/${t}/test`,method:"post",data:a}),getPaymentStats:()=>e({url:"/admin/system/payment/stats",method:"get"}),updateSecuritySettings:t=>e({url:"/admin/system/payment/security",method:"put",data:t}),refundOrder:(t,a)=>e({url:`/admin/payment/orders/${t}/refund`,method:"post",data:a}),getRefunds:t=>e({url:"/admin/payment/refunds",method:"get",params:t}),batchProcessOrders:(t,a)=>e({url:"/admin/payment/orders/batch",method:"post",data:{orderIds:t,action:a}}),processRefund:(t,a)=>e({url:`/admin/payment/refunds/${t}/process`,method:"post",data:a}),exportRefundData:t=>e({url:"/admin/payment/refunds/export",method:"post",data:t,responseType:"blob"}),cancelOrder:(t,a)=>e({url:`/admin/payment/orders/${t}/cancel`,method:"post",data:{reason:a}})},a=t.getPaymentConfig,s=t.updatePaymentConfig,n=t.togglePaymentMethod,d=t.testPaymentChannel,r=t.getPaymentStats,m=t.updateSecuritySettings,o=t.refundOrder,p=t.getRefunds,u=t.batchProcessOrders,y=t.processRefund,i=t.exportRefundData,g=t.cancelOrder;export{r as a,d as b,m as c,u as d,g as e,p as f,a as g,i as h,y as p,o as r,n as t,s as u};
