import{_ as a}from"./index-D2bI4m-v.js";/* empty css                *//* empty css               *//* empty css               */import{ag as e,ah as s,r as t,L as l,e as i,y as n,l as u,z as r,k as d,B as o,E as c,t as m,D as v,F as p,Y as f,u as _}from"./vue-vendor-DGsK9sC4.js";import{b3 as b,b4 as g,b2 as y,U as h,b8 as j,as as w,T as A,aS as M,ac as k,Q as x}from"./element-plus-DcSKpKA8.js";import{P as C}from"./PageLayout-OFR6SHfu.js";import"./utils-4VKArNEK.js";const R={key:0,class:"group-detail"},F={class:"info-item"},N={class:"info-item"},P={class:"info-item"},T={class:"info-item"},z={class:"info-item"},D={class:"info-item"},L={class:"info-item"},E={class:"info-item"},I={class:"info-item"},S={key:0,class:"info-item"},V={key:1,class:"info-item"},Z={class:"stat-item"},$={class:"stat-value"},q={class:"stat-item"},B={class:"stat-value"},G={class:"stat-item"},O={class:"stat-value"},Q={class:"stat-item"},U={class:"stat-value"},Y=a({__name:"GroupDetail",setup(a){const Y=e(),H=s(),J=t(!0),K=l({id:"",name:"",type:"",status:"",ownerName:"",memberCount:0,maxMembers:0,joinFee:0,description:"",rules:"",tags:[],createdAt:"",lastActiveAt:"",stats:{totalMessages:0,activeMembers:0,totalRevenue:0,conversionRate:0}}),W=a=>({normal:"普通群",vip:"VIP群",distribution:"分销群",test:"测试群"}[a]||"未知"),X=a=>a?new Date(a).toLocaleString("zh-CN"):"-",aa=()=>{Y.push(`/community/groups/edit/${H.params.id}`)};return i(()=>{(async()=>{try{J.value=!0,await new Promise(a=>setTimeout(a,1e3)),Object.assign(K,{id:H.params.id,name:"测试群组",type:"normal",status:"active",ownerName:"张三",memberCount:85,maxMembers:100,joinFee:9.9,description:"这是一个测试群组，用于演示群组功能。",rules:"1. 禁止发广告\n2. 保持友善交流\n3. 遵守群规",tags:["热门","活跃"],createdAt:"2024-01-15T10:30:00Z",lastActiveAt:"2024-08-02T14:20:00Z",stats:{totalMessages:1250,activeMembers:68,totalRevenue:841.5,conversionRate:12.5}})}catch(a){x.error("加载群组详情失败"),console.error(a)}finally{J.value=!1}})()}),(a,e)=>{const s=A,t=w,l=y,i=j,x=g,Y=b;return u(),n(C,{title:K.name||"群组详情",subtitle:"查看群组详细信息",icon:"View",loading:J.value},{actions:r(()=>[c(t,{onClick:e[0]||(e[0]=e=>a.$router.go(-1))},{default:r(()=>[c(s,null,{default:r(()=>[c(_(M))]),_:1}),e[1]||(e[1]=v(" 返回 ",-1))]),_:1,__:[1]}),c(t,{type:"primary",onClick:aa},{default:r(()=>[c(s,null,{default:r(()=>[c(_(k))]),_:1}),e[2]||(e[2]=v(" 编辑群组 ",-1))]),_:1,__:[2]})]),default:r(()=>[J.value?o("",!0):(u(),d("div",R,[c(Y,{class:"info-card",shadow:"never"},{header:r(()=>e[3]||(e[3]=[m("div",{class:"card-header"},[m("h3",null,"基本信息")],-1)])),default:r(()=>[c(x,{gutter:24},{default:r(()=>[c(l,{span:8},{default:r(()=>[m("div",F,[e[4]||(e[4]=m("label",null,"群组名称：",-1)),m("span",null,h(K.name),1)])]),_:1}),c(l,{span:8},{default:r(()=>{return[m("div",N,[e[5]||(e[5]=m("label",null,"群组类型：",-1)),c(i,{type:(a=K.type,{normal:"",vip:"warning",distribution:"success",test:"info"}[a]||"")},{default:r(()=>[v(h(W(K.type)),1)]),_:1},8,["type"])])];var a}),_:1}),c(l,{span:8},{default:r(()=>[m("div",P,[e[6]||(e[6]=m("label",null,"群组状态：",-1)),c(i,{type:"active"===K.status?"success":"danger"},{default:r(()=>[v(h("active"===K.status?"启用":"禁用"),1)]),_:1},8,["type"])])]),_:1})]),_:1}),c(x,{gutter:24},{default:r(()=>[c(l,{span:8},{default:r(()=>[m("div",T,[e[7]||(e[7]=m("label",null,"群主：",-1)),m("span",null,h(K.ownerName),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",z,[e[8]||(e[8]=m("label",null,"当前人数：",-1)),m("span",null,h(K.memberCount)+" / "+h(K.maxMembers),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",D,[e[9]||(e[9]=m("label",null,"入群费用：",-1)),m("span",null,h(K.joinFee)+"元",1)])]),_:1})]),_:1}),c(x,{gutter:24},{default:r(()=>[c(l,{span:8},{default:r(()=>[m("div",L,[e[10]||(e[10]=m("label",null,"创建时间：",-1)),m("span",null,h(X(K.createdAt)),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",E,[e[11]||(e[11]=m("label",null,"最后活跃：",-1)),m("span",null,h(X(K.lastActiveAt)),1)])]),_:1}),c(l,{span:8},{default:r(()=>[m("div",I,[e[12]||(e[12]=m("label",null,"群组标签：",-1)),(u(!0),d(p,null,f(K.tags,a=>(u(),n(i,{key:a,size:"small",style:{"margin-right":"8px"}},{default:r(()=>[v(h(a),1)]),_:2},1024))),128))])]),_:1})]),_:1}),K.description?(u(),d("div",S,[e[13]||(e[13]=m("label",null,"群组描述：",-1)),m("p",null,h(K.description),1)])):o("",!0),K.rules?(u(),d("div",V,[e[14]||(e[14]=m("label",null,"入群规则：",-1)),m("p",null,h(K.rules),1)])):o("",!0)]),_:1}),c(Y,{class:"stats-card",shadow:"never"},{header:r(()=>e[15]||(e[15]=[m("div",{class:"card-header"},[m("h3",null,"统计信息")],-1)])),default:r(()=>[c(x,{gutter:24},{default:r(()=>[c(l,{span:6},{default:r(()=>[m("div",Z,[m("div",$,h(K.stats.totalMessages),1),e[16]||(e[16]=m("div",{class:"stat-label"},"总消息数",-1))])]),_:1}),c(l,{span:6},{default:r(()=>[m("div",q,[m("div",B,h(K.stats.activeMembers),1),e[17]||(e[17]=m("div",{class:"stat-label"},"活跃成员",-1))])]),_:1}),c(l,{span:6},{default:r(()=>[m("div",G,[m("div",O,h(K.stats.totalRevenue),1),e[18]||(e[18]=m("div",{class:"stat-label"},"总收入（元）",-1))])]),_:1}),c(l,{span:6},{default:r(()=>[m("div",Q,[m("div",U,h(K.stats.conversionRate)+"%",1),e[19]||(e[19]=m("div",{class:"stat-label"},"转化率",-1))])]),_:1})]),_:1})]),_:1})]))]),_:1},8,["title","loading"])}}},[["__scopeId","data-v-69398620"]]);export{Y as default};
