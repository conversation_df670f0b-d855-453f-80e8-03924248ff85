import{_ as a}from"./index-D2bI4m-v.js";/* empty css               *//* empty css               *//* empty css                *//* empty css                    *//* empty css                        *//* empty css                          *//* empty css                    *//* empty css                  *//* empty css                    *//* empty css                     *//* empty css                  *//* empty css                  */import{b3 as e,b9 as l,b5 as t,T as s,_ as d,bf as o,bc as u,bd as n,aH as i,aW as r,aV as p,as as _,ae as c,bE as m,bn as f,a5 as y,bW as b,bg as h,bh as V,be as v,bi as g,b8 as w,af as k,b4 as U,b2 as C,ai as j,U as x,am as q,a6 as I,bH as B,aM as D,Q as z}from"./element-plus-DcSKpKA8.js";import{P}from"./PageLayout-OFR6SHfu.js";import{g as A,a as O,t as $,b as M,u as E,c as W}from"./payment-D7px_L1O.js";import{r as H,L,e as N,k as S,l as T,E as F,z as J,t as Q,B as R,u as G,D as K}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const X={class:"payment-settings"},Y={class:"settings-section"},Z={class:"card-header"},aa={class:"payment-config"},ea={class:"config-header"},la={class:"header-info"},ta={key:0,class:"config-form"},sa={class:"payment-config"},da={class:"config-header"},oa={class:"header-info"},ua={key:0,class:"config-form"},na={class:"payment-config"},ia={class:"config-header"},ra={class:"header-info"},pa={key:0,class:"config-form"},_a={class:"payment-config"},ca={class:"config-header"},ma={class:"header-info"},fa={key:0,class:"config-form"},ya={class:"settings-section"},ba={class:"card-header"},ha={class:"settings-section"},Va={class:"card-header"},va={class:"stat-card success"},ga={class:"stat-icon"},wa={class:"stat-content"},ka={class:"stat-value"},Ua={class:"stat-card primary"},Ca={class:"stat-icon"},ja={class:"stat-content"},xa={class:"stat-value"},qa={class:"stat-card warning"},Ia={class:"stat-icon"},Ba={class:"stat-content"},Da={class:"stat-value"},za={class:"stat-card info"},Pa={class:"stat-icon"},Aa={class:"stat-content"},Oa={class:"stat-value"},$a=a({__name:"PaymentSettings",setup(a){const $a=H("alipay"),Ma=H(!1),Ea=H(!1),Wa=L({alipay:!1,wechat:!1,easypay:!1,bank:!1}),Ha=L({alipay:{enabled:!1,app_id:"",private_key:"",public_key:"",gateway:"https://openapi.alipay.com/gateway.do",notify_url:"",return_url:""},wechat:{enabled:!1,app_id:"",mch_id:"",key:"",cert_path:"",notify_url:""},easypay:{enabled:!0,pid:"",key:"",api_url:"",notify_url:"",return_url:""},bank:{enabled:!1,supported_banks:["ICBC","ABC","BOC","CCB"],fee_rate:.6,min_amount:.01,max_amount:5e4}}),La=L({require_payment_password:!0,require_sms_verification:!0,large_amount_threshold:1e3,ip_whitelist:"",risk_rules:["frequency_limit","amount_limit"]}),Na=L({total_amount:0,total_orders:0,success_rate:0,avg_time:0}),Sa=H("/api/upload/cert"),Ta=H({Authorization:`Bearer ${localStorage.getItem("token")}`}),Fa=async()=>{Ma.value=!0;try{await E(Ha),await W(La),z.success("支付设置保存成功")}catch(a){z.error("保存失败："+a.message)}finally{Ma.value=!1}},Ja=async(a,e)=>{try{await $(a,e),z.success(`${a} 支付方式已${e?"启用":"禁用"}`)}catch(l){z.error("操作失败："+l.message),Ha[a].enabled=!e}},Qa=async a=>{Wa[a]=!0;try{const e={amount:.01,subject:"测试支付",out_trade_no:`test_${Date.now()}`};await M(a,e),z.success(`${a} 支付通道测试成功`)}catch(e){z.error(`${a} 支付通道测试失败：`+e.message)}finally{Wa[a]=!1}},Ra=(a,e)=>{200===a.code?(Ha.wechat.cert_path=a.data.path,z.success("证书上传成功")):z.error("证书上传失败")},Ga=a=>{const e="application/x-pkcs12"===a.type||a.name.endsWith(".pem"),l=a.size/1024/1024<2;return e?!!l||(z.error("证书文件大小不能超过 2MB"),!1):(z.error("证书文件格式不正确"),!1)},Ka=async()=>{Ea.value=!0;try{const a=await O();Object.assign(Na,a.data)}catch(a){Object.assign(Na,{total_amount:1234567.89,total_orders:5678,success_rate:98.5,avg_time:2.3})}finally{Ea.value=!1}};return N(()=>{(async()=>{try{const a=await A();Object.assign(Ha,a.data)}catch(a){console.log("使用模拟支付配置数据")}})(),Ka()}),(a,z)=>{const A=s,O=_,$=o,M=i,E=n,W=p,H=r,L=u,N=t,Xa=f,Ya=V,Za=h,ae=v,ee=l,le=e,te=w,se=C,de=U;return T(),S("div",X,[F(P,{title:"支付设置",subtitle:"管理系统支付配置和支付方式"},{default:J(()=>[Q("div",Y,[F(le,{class:"settings-card"},{header:J(()=>[Q("div",Z,[z[37]||(z[37]=Q("h3",null,"支付方式配置",-1)),F(O,{type:"primary",onClick:Fa,loading:Ma.value},{default:J(()=>[F(A,null,{default:J(()=>[F(G(g))]),_:1}),z[36]||(z[36]=K(" 保存所有设置 ",-1))]),_:1,__:[36]},8,["loading"])])]),default:J(()=>[F(ee,{modelValue:$a.value,"onUpdate:modelValue":z[30]||(z[30]=a=>$a.value=a),class:"payment-tabs"},{default:J(()=>[F(N,{label:"支付宝",name:"alipay"},{default:J(()=>[Q("div",aa,[Q("div",ea,[Q("div",la,[F(A,{class:"payment-icon alipay"},{default:J(()=>[F(G(d))]),_:1}),z[38]||(z[38]=Q("div",{class:"payment-info"},[Q("h4",null,"支付宝支付"),Q("p",null,"接入支付宝官方支付接口，支持扫码支付、手机支付等")],-1))]),F($,{modelValue:Ha.alipay.enabled,"onUpdate:modelValue":z[0]||(z[0]=a=>Ha.alipay.enabled=a),size:"large",onChange:z[1]||(z[1]=a=>Ja("alipay",a))},null,8,["modelValue"])]),Ha.alipay.enabled?(T(),S("div",ta,[F(L,{model:Ha.alipay,"label-width":"120px"},{default:J(()=>[F(E,{label:"应用ID",required:""},{default:J(()=>[F(M,{modelValue:Ha.alipay.app_id,"onUpdate:modelValue":z[2]||(z[2]=a=>Ha.alipay.app_id=a),placeholder:"请输入支付宝应用ID","show-password":""},null,8,["modelValue"])]),_:1}),F(E,{label:"商户私钥",required:""},{default:J(()=>[F(M,{modelValue:Ha.alipay.private_key,"onUpdate:modelValue":z[3]||(z[3]=a=>Ha.alipay.private_key=a),type:"textarea",rows:4,placeholder:"请输入商户私钥","show-password":""},null,8,["modelValue"])]),_:1}),F(E,{label:"支付宝公钥",required:""},{default:J(()=>[F(M,{modelValue:Ha.alipay.public_key,"onUpdate:modelValue":z[4]||(z[4]=a=>Ha.alipay.public_key=a),type:"textarea",rows:4,placeholder:"请输入支付宝公钥"},null,8,["modelValue"])]),_:1}),F(E,{label:"网关地址"},{default:J(()=>[F(H,{modelValue:Ha.alipay.gateway,"onUpdate:modelValue":z[5]||(z[5]=a=>Ha.alipay.gateway=a),style:{width:"100%"}},{default:J(()=>[F(W,{label:"正式环境",value:"https://openapi.alipay.com/gateway.do"}),F(W,{label:"沙箱环境",value:"https://openapi.alipaydev.com/gateway.do"})]),_:1},8,["modelValue"])]),_:1}),F(E,{label:"回调地址"},{default:J(()=>[F(M,{modelValue:Ha.alipay.notify_url,"onUpdate:modelValue":z[6]||(z[6]=a=>Ha.alipay.notify_url=a),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),F(E,{label:"返回地址"},{default:J(()=>[F(M,{modelValue:Ha.alipay.return_url,"onUpdate:modelValue":z[7]||(z[7]=a=>Ha.alipay.return_url=a),placeholder:"支付完成后返回地址"},null,8,["modelValue"])]),_:1}),F(E,null,{default:J(()=>[F(O,{onClick:z[8]||(z[8]=a=>Qa("alipay")),loading:Wa.alipay},{default:J(()=>[F(A,null,{default:J(()=>[F(G(c))]),_:1}),z[39]||(z[39]=K(" 测试连接 ",-1))]),_:1,__:[39]},8,["loading"])]),_:1})]),_:1},8,["model"])])):R("",!0)])]),_:1}),F(N,{label:"微信支付",name:"wechat"},{default:J(()=>[Q("div",sa,[Q("div",da,[Q("div",oa,[F(A,{class:"payment-icon wechat"},{default:J(()=>[F(G(m))]),_:1}),z[40]||(z[40]=Q("div",{class:"payment-info"},[Q("h4",null,"微信支付"),Q("p",null,"接入微信官方支付接口，支持扫码支付、公众号支付等")],-1))]),F($,{modelValue:Ha.wechat.enabled,"onUpdate:modelValue":z[9]||(z[9]=a=>Ha.wechat.enabled=a),size:"large",onChange:z[10]||(z[10]=a=>Ja("wechat",a))},null,8,["modelValue"])]),Ha.wechat.enabled?(T(),S("div",ua,[F(L,{model:Ha.wechat,"label-width":"120px"},{default:J(()=>[F(E,{label:"应用ID",required:""},{default:J(()=>[F(M,{modelValue:Ha.wechat.app_id,"onUpdate:modelValue":z[11]||(z[11]=a=>Ha.wechat.app_id=a),placeholder:"请输入微信应用ID","show-password":""},null,8,["modelValue"])]),_:1}),F(E,{label:"商户号",required:""},{default:J(()=>[F(M,{modelValue:Ha.wechat.mch_id,"onUpdate:modelValue":z[12]||(z[12]=a=>Ha.wechat.mch_id=a),placeholder:"请输入微信商户号","show-password":""},null,8,["modelValue"])]),_:1}),F(E,{label:"商户密钥",required:""},{default:J(()=>[F(M,{modelValue:Ha.wechat.key,"onUpdate:modelValue":z[13]||(z[13]=a=>Ha.wechat.key=a),placeholder:"请输入商户密钥","show-password":""},null,8,["modelValue"])]),_:1}),F(E,{label:"证书文件"},{default:J(()=>[F(Xa,{class:"cert-upload",action:Sa.value,headers:Ta.value,"on-success":Ra,"before-upload":Ga,accept:".pem,.p12"},{tip:J(()=>z[42]||(z[42]=[Q("div",{class:"el-upload__tip"}," 支持 .pem 和 .p12 格式的证书文件 ",-1)])),default:J(()=>[F(O,null,{default:J(()=>[F(A,null,{default:J(()=>[F(G(y))]),_:1}),z[41]||(z[41]=K(" 上传证书 ",-1))]),_:1,__:[41]})]),_:1},8,["action","headers"])]),_:1}),F(E,{label:"回调地址"},{default:J(()=>[F(M,{modelValue:Ha.wechat.notify_url,"onUpdate:modelValue":z[14]||(z[14]=a=>Ha.wechat.notify_url=a),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),F(E,null,{default:J(()=>[F(O,{onClick:z[15]||(z[15]=a=>Qa("wechat")),loading:Wa.wechat},{default:J(()=>[F(A,null,{default:J(()=>[F(G(c))]),_:1}),z[43]||(z[43]=K(" 测试连接 ",-1))]),_:1,__:[43]},8,["loading"])]),_:1})]),_:1},8,["model"])])):R("",!0)])]),_:1}),F(N,{label:"易支付",name:"easypay"},{default:J(()=>[Q("div",na,[Q("div",ia,[Q("div",ra,[F(A,{class:"payment-icon easypay"},{default:J(()=>[F(G(b))]),_:1}),z[44]||(z[44]=Q("div",{class:"payment-info"},[Q("h4",null,"易支付"),Q("p",null,"第三方聚合支付平台，支持多种支付方式")],-1))]),F($,{modelValue:Ha.easypay.enabled,"onUpdate:modelValue":z[16]||(z[16]=a=>Ha.easypay.enabled=a),size:"large",onChange:z[17]||(z[17]=a=>Ja("easypay",a))},null,8,["modelValue"])]),Ha.easypay.enabled?(T(),S("div",pa,[F(L,{model:Ha.easypay,"label-width":"120px"},{default:J(()=>[F(E,{label:"商户ID",required:""},{default:J(()=>[F(M,{modelValue:Ha.easypay.pid,"onUpdate:modelValue":z[18]||(z[18]=a=>Ha.easypay.pid=a),placeholder:"请输入易支付商户ID","show-password":""},null,8,["modelValue"])]),_:1}),F(E,{label:"商户密钥",required:""},{default:J(()=>[F(M,{modelValue:Ha.easypay.key,"onUpdate:modelValue":z[19]||(z[19]=a=>Ha.easypay.key=a),placeholder:"请输入商户密钥","show-password":""},null,8,["modelValue"])]),_:1}),F(E,{label:"API地址",required:""},{default:J(()=>[F(M,{modelValue:Ha.easypay.api_url,"onUpdate:modelValue":z[20]||(z[20]=a=>Ha.easypay.api_url=a),placeholder:"请输入易支付API地址"},null,8,["modelValue"])]),_:1}),F(E,{label:"回调地址"},{default:J(()=>[F(M,{modelValue:Ha.easypay.notify_url,"onUpdate:modelValue":z[21]||(z[21]=a=>Ha.easypay.notify_url=a),placeholder:"支付结果异步通知地址"},null,8,["modelValue"])]),_:1}),F(E,{label:"返回地址"},{default:J(()=>[F(M,{modelValue:Ha.easypay.return_url,"onUpdate:modelValue":z[22]||(z[22]=a=>Ha.easypay.return_url=a),placeholder:"支付完成后返回地址"},null,8,["modelValue"])]),_:1}),F(E,null,{default:J(()=>[F(O,{onClick:z[23]||(z[23]=a=>Qa("easypay")),loading:Wa.easypay},{default:J(()=>[F(A,null,{default:J(()=>[F(G(c))]),_:1}),z[45]||(z[45]=K(" 测试连接 ",-1))]),_:1,__:[45]},8,["loading"])]),_:1})]),_:1},8,["model"])])):R("",!0)])]),_:1}),F(N,{label:"银行卡支付",name:"bank"},{default:J(()=>[Q("div",_a,[Q("div",ca,[Q("div",ma,[F(A,{class:"payment-icon bank"},{default:J(()=>[F(G(d))]),_:1}),z[46]||(z[46]=Q("div",{class:"payment-info"},[Q("h4",null,"银行卡支付"),Q("p",null,"支持各大银行的网银支付和快捷支付")],-1))]),F($,{modelValue:Ha.bank.enabled,"onUpdate:modelValue":z[24]||(z[24]=a=>Ha.bank.enabled=a),size:"large",onChange:z[25]||(z[25]=a=>Ja("bank",a))},null,8,["modelValue"])]),Ha.bank.enabled?(T(),S("div",fa,[F(L,{model:Ha.bank,"label-width":"120px"},{default:J(()=>[F(E,{label:"支持银行"},{default:J(()=>[F(Za,{modelValue:Ha.bank.supported_banks,"onUpdate:modelValue":z[26]||(z[26]=a=>Ha.bank.supported_banks=a)},{default:J(()=>[F(Ya,{label:"ICBC"},{default:J(()=>z[47]||(z[47]=[K("工商银行",-1)])),_:1,__:[47]}),F(Ya,{label:"ABC"},{default:J(()=>z[48]||(z[48]=[K("农业银行",-1)])),_:1,__:[48]}),F(Ya,{label:"BOC"},{default:J(()=>z[49]||(z[49]=[K("中国银行",-1)])),_:1,__:[49]}),F(Ya,{label:"CCB"},{default:J(()=>z[50]||(z[50]=[K("建设银行",-1)])),_:1,__:[50]}),F(Ya,{label:"COMM"},{default:J(()=>z[51]||(z[51]=[K("交通银行",-1)])),_:1,__:[51]}),F(Ya,{label:"CMB"},{default:J(()=>z[52]||(z[52]=[K("招商银行",-1)])),_:1,__:[52]}),F(Ya,{label:"CITIC"},{default:J(()=>z[53]||(z[53]=[K("中信银行",-1)])),_:1,__:[53]}),F(Ya,{label:"CEB"},{default:J(()=>z[54]||(z[54]=[K("光大银行",-1)])),_:1,__:[54]})]),_:1},8,["modelValue"])]),_:1}),F(E,{label:"手续费率"},{default:J(()=>[F(ae,{modelValue:Ha.bank.fee_rate,"onUpdate:modelValue":z[27]||(z[27]=a=>Ha.bank.fee_rate=a),min:0,max:10,precision:2,step:.01},null,8,["modelValue"]),z[55]||(z[55]=Q("span",{class:"input-suffix"},"%",-1))]),_:1,__:[55]}),F(E,{label:"最小金额"},{default:J(()=>[F(ae,{modelValue:Ha.bank.min_amount,"onUpdate:modelValue":z[28]||(z[28]=a=>Ha.bank.min_amount=a),min:.01,precision:2},null,8,["modelValue"]),z[56]||(z[56]=Q("span",{class:"input-suffix"},"元",-1))]),_:1,__:[56]}),F(E,{label:"最大金额"},{default:J(()=>[F(ae,{modelValue:Ha.bank.max_amount,"onUpdate:modelValue":z[29]||(z[29]=a=>Ha.bank.max_amount=a),min:1,precision:2},null,8,["modelValue"]),z[57]||(z[57]=Q("span",{class:"input-suffix"},"元",-1))]),_:1,__:[57]})]),_:1},8,["model"])])):R("",!0)])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),Q("div",ya,[F(le,{class:"settings-card"},{header:J(()=>[Q("div",ba,[z[59]||(z[59]=Q("h3",null,"安全设置",-1)),F(te,{type:"warning"},{default:J(()=>[F(A,null,{default:J(()=>[F(G(k))]),_:1}),z[58]||(z[58]=K(" 安全级别：高 ",-1))]),_:1,__:[58]})])]),default:J(()=>[F(L,{model:La,"label-width":"150px"},{default:J(()=>[F(E,{label:"支付密码验证"},{default:J(()=>[F($,{modelValue:La.require_payment_password,"onUpdate:modelValue":z[31]||(z[31]=a=>La.require_payment_password=a)},null,8,["modelValue"]),z[60]||(z[60]=Q("span",{class:"form-tip"},"开启后用户支付时需要输入支付密码",-1))]),_:1,__:[60]}),F(E,{label:"短信验证"},{default:J(()=>[F($,{modelValue:La.require_sms_verification,"onUpdate:modelValue":z[32]||(z[32]=a=>La.require_sms_verification=a)},null,8,["modelValue"]),z[61]||(z[61]=Q("span",{class:"form-tip"},"大额支付时需要短信验证码确认",-1))]),_:1,__:[61]}),F(E,{label:"大额支付阈值"},{default:J(()=>[F(ae,{modelValue:La.large_amount_threshold,"onUpdate:modelValue":z[33]||(z[33]=a=>La.large_amount_threshold=a),min:100,max:5e4,step:100},null,8,["modelValue"]),z[62]||(z[62]=Q("span",{class:"input-suffix"},"元",-1))]),_:1,__:[62]}),F(E,{label:"IP白名单"},{default:J(()=>[F(M,{modelValue:La.ip_whitelist,"onUpdate:modelValue":z[34]||(z[34]=a=>La.ip_whitelist=a),type:"textarea",rows:3,placeholder:"每行一个IP地址，支持CIDR格式"},null,8,["modelValue"])]),_:1}),F(E,{label:"风控规则"},{default:J(()=>[F(Za,{modelValue:La.risk_rules,"onUpdate:modelValue":z[35]||(z[35]=a=>La.risk_rules=a)},{default:J(()=>[F(Ya,{label:"frequency_limit"},{default:J(()=>z[63]||(z[63]=[K("频率限制",-1)])),_:1,__:[63]}),F(Ya,{label:"amount_limit"},{default:J(()=>z[64]||(z[64]=[K("金额限制",-1)])),_:1,__:[64]}),F(Ya,{label:"device_binding"},{default:J(()=>z[65]||(z[65]=[K("设备绑定",-1)])),_:1,__:[65]}),F(Ya,{label:"geo_restriction"},{default:J(()=>z[66]||(z[66]=[K("地域限制",-1)])),_:1,__:[66]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1})]),Q("div",ha,[F(le,{class:"settings-card"},{header:J(()=>[Q("div",Va,[z[68]||(z[68]=Q("h3",null,"支付统计",-1)),F(O,{onClick:Ka,loading:Ea.value},{default:J(()=>[F(A,null,{default:J(()=>[F(G(D))]),_:1}),z[67]||(z[67]=K(" 刷新 ",-1))]),_:1,__:[67]},8,["loading"])])]),default:J(()=>[F(de,{gutter:20},{default:J(()=>[F(se,{span:6},{default:J(()=>{return[Q("div",va,[Q("div",ga,[F(A,null,{default:J(()=>[F(G(j))]),_:1})]),Q("div",wa,[Q("div",ka,"¥"+x((a=Na.total_amount,new Intl.NumberFormat("zh-CN").format(a))),1),z[69]||(z[69]=Q("div",{class:"stat-label"},"总交易金额",-1))])])];var a}),_:1}),F(se,{span:6},{default:J(()=>[Q("div",Ua,[Q("div",Ca,[F(A,null,{default:J(()=>[F(G(q))]),_:1})]),Q("div",ja,[Q("div",xa,x(Na.total_orders),1),z[70]||(z[70]=Q("div",{class:"stat-label"},"总订单数",-1))])])]),_:1}),F(se,{span:6},{default:J(()=>[Q("div",qa,[Q("div",Ia,[F(A,null,{default:J(()=>[F(G(I))]),_:1})]),Q("div",Ba,[Q("div",Da,x(Na.success_rate)+"%",1),z[71]||(z[71]=Q("div",{class:"stat-label"},"成功率",-1))])])]),_:1}),F(se,{span:6},{default:J(()=>[Q("div",za,[Q("div",Pa,[F(A,null,{default:J(()=>[F(G(B))]),_:1})]),Q("div",Aa,[Q("div",Oa,x(Na.avg_time)+"s",1),z[72]||(z[72]=Q("div",{class:"stat-label"},"平均处理时间",-1))])])]),_:1})]),_:1})]),_:1})])]),_:1})])}}},[["__scopeId","data-v-7555450a"]]);export{$a as default};
