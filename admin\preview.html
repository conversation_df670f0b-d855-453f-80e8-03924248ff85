<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨鑫流量变现系统 - 管理后台预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #1f2937;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.1rem;
            color: #6b7280;
            margin-bottom: 20px;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }
        
        .preview-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .preview-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: white;
            font-size: 20px;
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }
        
        .card-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .card-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        
        .btn-secondary {
            background: rgba(107, 114, 128, 0.1);
            color: #374151;
            border: 1px solid rgba(107, 114, 128, 0.2);
        }
        
        .btn-secondary:hover {
            background: rgba(107, 114, 128, 0.15);
        }
        
        .quick-access {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
        }
        
        .quick-access h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }
        
        .quick-links {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .quick-link {
            padding: 10px 16px;
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            color: #374151;
            text-decoration: none;
            border-radius: 10px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .quick-link:hover {
            background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
            transform: translateY(-1px);
        }
        
        .info-section {
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .info-section h4 {
            color: #1e40af;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .info-section p {
            color: #3730a3;
            font-size: 0.9rem;
            line-height: 1.5;
            margin: 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        @media (max-width: 768px) {
            .preview-container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .preview-grid {
                grid-template-columns: 1fr;
            }
            
            .card-actions {
                flex-direction: column;
            }
            
            .quick-links {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="header">
            <h1>🚀 管理后台预览模式</h1>
            <p>晨鑫流量变现系统 - 智能社群营销与多级分销平台</p>
            <div class="status-badge">
                <div class="status-dot"></div>
                预览模式已启用 - 无需登录
            </div>
        </div>
        
        <div class="quick-access">
            <h3>🔥 快速访问</h3>
            <div class="quick-links">
                <a href="./#/dashboard" class="quick-link">📊 数据看板</a>
                <a href="./#/community/groups" class="quick-link">👥 社群管理</a>
                <a href="./#/distribution/distributors" class="quick-link">🔗 分销管理</a>
                <a href="./#/finance/dashboard" class="quick-link">💰 财务管理</a>
                <a href="./#/anti-block/dashboard" class="quick-link">🛡️ 防红系统</a>
                <a href="./#/system/settings" class="quick-link">⚙️ 系统设置</a>
            </div>
        </div>
        
        <div class="preview-grid">
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);">📊</div>
                    <h3 class="card-title">数据看板</h3>
                </div>
                <p class="card-description">
                    实时监控系统运营数据，包括用户活跃度、收益统计、社群数据等核心指标的可视化展示。
                </p>
                <div class="card-actions">
                    <a href="./#/dashboard" class="btn btn-primary">进入看板</a>
                    <a href="./#/data-screen" class="btn btn-secondary">数据大屏</a>
                </div>
            </div>
            
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">👥</div>
                    <h3 class="card-title">社群管理</h3>
                </div>
                <p class="card-description">
                    管理微信群组、设置自动化规则、内容审核、活动管理等社群运营核心功能。
                </p>
                <div class="card-actions">
                    <a href="./#/community/groups" class="btn btn-primary">群组列表</a>
                    <a href="./#/community/rules" class="btn btn-secondary">自动化规则</a>
                </div>
            </div>
            
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">🔗</div>
                    <h3 class="card-title">分销管理</h3>
                </div>
                <p class="card-description">
                    管理分销员体系、佣金设置、推广链接生成、分销数据统计等多级分销功能。
                </p>
                <div class="card-actions">
                    <a href="./#/distribution/distributors" class="btn btn-primary">分销员管理</a>
                    <a href="./#/distributor/dashboard" class="btn btn-secondary">分销员工作台</a>
                </div>
            </div>
            
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">💰</div>
                    <h3 class="card-title">财务管理</h3>
                </div>
                <p class="card-description">
                    财务数据统计、佣金明细查看、交易记录管理、提现审核等财务相关功能。
                </p>
                <div class="card-actions">
                    <a href="./#/finance/dashboard" class="btn btn-primary">财务总览</a>
                    <a href="./#/finance/commission-logs" class="btn btn-secondary">佣金明细</a>
                </div>
            </div>
            
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">🛡️</div>
                    <h3 class="card-title">防红系统</h3>
                </div>
                <p class="card-description">
                    域名管理、短链接生成、防封检测、链接跳转统计等防红防封核心功能。
                </p>
                <div class="card-actions">
                    <a href="./#/anti-block/dashboard" class="btn btn-primary">系统概览</a>
                    <a href="./#/anti-block/domains" class="btn btn-secondary">域名管理</a>
                </div>
            </div>
            
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">👤</div>
                    <h3 class="card-title">用户管理</h3>
                </div>
                <p class="card-description">
                    用户信息管理、权限配置、用户行为分析、账户状态管理等用户相关功能。
                </p>
                <div class="card-actions">
                    <a href="./#/user/list" class="btn btn-primary">用户列表</a>
                    <a href="./#/user/analytics" class="btn btn-secondary">用户分析</a>
                </div>
            </div>
            
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%);">📝</div>
                    <h3 class="card-title">内容管理</h3>
                </div>
                <p class="card-description">
                    内容模板管理、AI内容生成、素材库管理等内容创作和管理功能。
                </p>
                <div class="card-actions">
                    <a href="./#/content/management" class="btn btn-primary">内容管理</a>
                    <a href="./#/content/ai-generator" class="btn btn-secondary">AI生成</a>
                </div>
            </div>
            
            <div class="preview-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);">⚙️</div>
                    <h3 class="card-title">系统管理</h3>
                </div>
                <p class="card-description">
                    系统设置、监控管理、操作日志、权限配置等系统级管理功能。
                </p>
                <div class="card-actions">
                    <a href="./#/system/settings" class="btn btn-primary">系统设置</a>
                    <a href="./#/system/monitor" class="btn btn-secondary">系统监控</a>
                </div>
            </div>
        </div>
        
        <div class="info-section">
            <h4>💡 预览模式说明</h4>
            <p>
                当前处于预览模式，所有功能均可正常访问和操作，但数据为模拟数据。
                预览模式下跳过了登录验证，可以直接体验管理后台的完整功能。
                如需正式使用，请通过正常登录流程进入系统。
            </p>
        </div>
        
        <div class="footer">
            <p>© 2024 晨鑫流量变现系统. 智能社群营销与多级分销平台</p>
        </div>
    </div>

    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.preview-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            // 添加点击统计
            const links = document.querySelectorAll('a[href^="./"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    const module = this.textContent.trim();
                    console.log(`预览访问: ${module}`);
                    
                    // 可以在这里添加访问统计逻辑
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'preview_access', {
                            'module_name': module,
                            'event_category': 'preview'
                        });
                    }
                });
            });
        });
        
        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + 数字键快速访问
            if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '6') {
                e.preventDefault();
                const quickLinks = document.querySelectorAll('.quick-link');
                const index = parseInt(e.key) - 1;
                if (quickLinks[index]) {
                    quickLinks[index].click();
                }
            }
        });
    </script>
</body>
</html>