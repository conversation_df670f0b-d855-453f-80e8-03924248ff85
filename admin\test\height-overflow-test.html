<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面高度溢出修复验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .resolution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .resolution-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: white;
        }
        
        .resolution-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }
        
        .resolution-card.critical {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .resolution-card.critical:hover {
            border-color: #dc2626;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.25);
        }
        
        .resolution-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .resolution-desc {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        
        .test-result {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .result-pass {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .result-fail {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .result-pending {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        
        .current-status {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 6px;
            text-align: center;
        }
        
        .status-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 16px;
            font-weight: 600;
        }
        
        .test-iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #f3f4f6;
        }
        
        .btn.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        
        .btn.primary:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 登录页面高度溢出修复验证</h1>
            <p>专门测试1366x768等主流分辨率下的滚动问题修复效果</p>
        </div>
        
        <div class="current-status">
            <h3>📊 当前页面状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">窗口分辨率</div>
                    <div class="status-value" id="currentResolution">检测中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">页面高度</div>
                    <div class="status-value" id="pageHeight">检测中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">视窗高度</div>
                    <div class="status-value" id="viewportHeight">检测中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">滚动状态</div>
                    <div class="status-value" id="scrollStatus">检测中...</div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn primary" onclick="runFullTest()">🚀 运行完整测试</button>
            <button class="btn" onclick="openLoginPage()">🔗 打开登录页面</button>
            <button class="btn" onclick="refreshTest()">🔄 刷新测试</button>
        </div>
        
        <h3>🎯 关键分辨率测试</h3>
        <div class="resolution-grid">
            <div class="resolution-card critical" onclick="testResolution(1366, 768)">
                <div class="resolution-title">1366×768</div>
                <div class="resolution-desc">最关键测试 - 主流笔记本分辨率</div>
                <div class="test-result result-pending" id="result-1366-768">等待测试</div>
            </div>
            
            <div class="resolution-card" onclick="testResolution(1920, 1080)">
                <div class="resolution-title">1920×1080</div>
                <div class="resolution-desc">桌面端标准分辨率</div>
                <div class="test-result result-pending" id="result-1920-1080">等待测试</div>
            </div>
            
            <div class="resolution-card" onclick="testResolution(1280, 720)">
                <div class="resolution-title">1280×720</div>
                <div class="resolution-desc">小屏笔记本分辨率</div>
                <div class="test-result result-pending" id="result-1280-720">等待测试</div>
            </div>
            
            <div class="resolution-card" onclick="testResolution(1024, 768)">
                <div class="resolution-title">1024×768</div>
                <div class="resolution-desc">传统4:3分辨率</div>
                <div class="test-result result-pending" id="result-1024-768">等待测试</div>
            </div>
            
            <div class="resolution-card" onclick="testResolution(1024, 600)">
                <div class="resolution-title">1024×600</div>
                <div class="resolution-desc">上网本分辨率</div>
                <div class="test-result result-pending" id="result-1024-600">等待测试</div>
            </div>
            
            <div class="resolution-card" onclick="testResolution(1440, 900)">
                <div class="resolution-title">1440×900</div>
                <div class="resolution-desc">宽屏笔记本分辨率</div>
                <div class="test-result result-pending" id="result-1440-900">等待测试</div>
            </div>
        </div>
        
        <iframe id="testFrame" class="test-iframe" src="http://localhost:3001/admin/" title="登录页面测试"></iframe>
    </div>
    
    <script>
        let testResults = {};
        
        function updateCurrentStatus() {
            document.getElementById('currentResolution').textContent = `${window.innerWidth}×${window.innerHeight}`;
            document.getElementById('viewportHeight').textContent = `${window.innerHeight}px`;
            
            // 检测页面高度和滚动状态
            const iframe = document.getElementById('testFrame');
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const pageHeight = Math.max(
                    iframeDoc.body.scrollHeight,
                    iframeDoc.body.offsetHeight,
                    iframeDoc.documentElement.clientHeight,
                    iframeDoc.documentElement.scrollHeight,
                    iframeDoc.documentElement.offsetHeight
                );
                
                document.getElementById('pageHeight').textContent = `${pageHeight}px`;
                
                const needsScroll = pageHeight > window.innerHeight;
                const scrollStatus = needsScroll ? '❌ 需要滚动' : '✅ 无需滚动';
                document.getElementById('scrollStatus').textContent = scrollStatus;
                
            } catch (e) {
                document.getElementById('pageHeight').textContent = '无法检测';
                document.getElementById('scrollStatus').textContent = '无法检测';
            }
        }
        
        function testResolution(width, height) {
            // 调整窗口大小（仅在支持的浏览器中）
            try {
                window.resizeTo(width, height);
            } catch (e) {
                console.log('无法调整窗口大小，请手动调整');
            }
            
            setTimeout(() => {
                const needsScroll = document.documentElement.scrollHeight > height;
                const resultId = `result-${width}-${height}`;
                const resultElement = document.getElementById(resultId);
                
                if (needsScroll) {
                    resultElement.textContent = '❌ 需要滚动';
                    resultElement.className = 'test-result result-fail';
                    testResults[`${width}x${height}`] = false;
                } else {
                    resultElement.textContent = '✅ 无需滚动';
                    resultElement.className = 'test-result result-pass';
                    testResults[`${width}x${height}`] = true;
                }
                
                updateCurrentStatus();
            }, 500);
        }
        
        function runFullTest() {
            const resolutions = [
                [1366, 768],
                [1920, 1080],
                [1280, 720],
                [1024, 768],
                [1024, 600],
                [1440, 900]
            ];
            
            let index = 0;
            function testNext() {
                if (index < resolutions.length) {
                    const [width, height] = resolutions[index];
                    testResolution(width, height);
                    index++;
                    setTimeout(testNext, 1000);
                } else {
                    showTestSummary();
                }
            }
            
            testNext();
        }
        
        function showTestSummary() {
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(result => result).length;
            const failed = total - passed;
            
            alert(`测试完成！\n总测试数: ${total}\n通过: ${passed}\n失败: ${failed}\n成功率: ${((passed/total)*100).toFixed(1)}%`);
        }
        
        function openLoginPage() {
            window.open('http://localhost:3001/admin/', '_blank');
        }
        
        function refreshTest() {
            location.reload();
        }
        
        // 初始化
        window.addEventListener('load', () => {
            updateCurrentStatus();
            setInterval(updateCurrentStatus, 2000);
        });
        
        window.addEventListener('resize', updateCurrentStatus);
    </script>
</body>
</html>
