<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑用户' : '创建用户'"
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="user-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入用户名"
              maxlength="50"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入真实姓名"
              maxlength="50"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="formData.email"
              placeholder="请输入邮箱地址"
              type="email"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入手机号"
              maxlength="11"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户角色" prop="role">
            <el-select v-model="formData.role" placeholder="请选择角色" style="width: 100%">
              <el-option label="普通用户" value="user" />
              <el-option label="分销员" value="distributor" />
              <el-option label="代理商" value="agent" />
              <el-option label="分站管理员" value="substation" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
              <el-option label="待审核" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="!isEdit">
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="账户余额" prop="balance">
            <el-input-number
              v-model="formData.balance"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="0.00"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="冻结余额" prop="frozen_balance">
            <el-input-number
              v-model="formData.frozen_balance"
              :min="0"
              :precision="2"
              style="width: 100%"
              placeholder="0.00"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 分销员专用字段 -->
      <template v-if="formData.role === 'distributor'">
        <el-divider content-position="left">分销员信息</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分销员账号" prop="distributor_account">
              <el-input
                v-model="formData.distributor_account"
                placeholder="请输入分销员账号"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分销员手机" prop="distributor_phone">
              <el-input
                v-model="formData.distributor_phone"
                placeholder="请输入分销员手机号"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="支付宝账号" prop="distributor_alipay">
              <el-input
                v-model="formData.distributor_alipay"
                placeholder="请输入支付宝账号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信号" prop="distributor_wechat">
              <el-input
                v-model="formData.distributor_wechat"
                placeholder="请输入微信号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="真实姓名" prop="distributor_real_name">
          <el-input
            v-model="formData.distributor_real_name"
            placeholder="请输入真实姓名"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="上级分销员" prop="parent_distributor_id">
          <el-select
            v-model="formData.parent_distributor_id"
            placeholder="请选择上级分销员"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="distributor in distributorList"
              :key="distributor.id"
              :label="`${distributor.name} (${distributor.username})`"
              :value="distributor.id"
            />
          </el-select>
        </el-form-item>
      </template>

      <el-form-item label="头像">
        <div class="avatar-upload">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            accept="image/*"
          >
            <img v-if="formData.avatar" :src="formData.avatar" class="avatar" alt="用户头像" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议尺寸：200x200px</div>
        </div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { createUser, updateUser } from '@/api/user'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const userStore = useUserStore()
const formRef = ref(null)
const loading = ref(false)
const distributorList = ref([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 是否为编辑模式
const isEdit = computed(() => props.userData && props.userData.id)

// 上传配置
const uploadUrl = computed(() => import.meta.env.VITE_API_BASE_URL + '/upload/avatar')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

// 表单数据
const formData = reactive({
  username: '',
  name: '',
  email: '',
  phone: '',
  role: 'user',
  status: 1,
  password: '',
  confirmPassword: '',
  balance: 0,
  frozen_balance: 0,
  avatar: '',
  remark: '',
  // 分销员专用字段
  distributor_account: '',
  distributor_phone: '',
  distributor_alipay: '',
  distributor_wechat: '',
  distributor_real_name: '',
  parent_distributor_id: null
})

// 自定义验证规则
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== formData.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const formRules = computed(() => {
  const rules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    phone: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    role: [
      { required: true, message: '请选择用户角色', trigger: 'change' }
    ],
    status: [
      { required: true, message: '请选择用户状态', trigger: 'change' }
    ]
  }

  // 如果不是编辑模式，添加密码验证
  if (!isEdit.value) {
    rules.password = [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
    ]
    rules.confirmPassword = [
      { required: true, message: '请再次输入密码', trigger: 'blur' },
      { validator: validateConfirmPassword, trigger: 'blur' }
    ]
  }

  return rules
})

// 监听用户数据变化
watch(() => props.userData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      username: newData.username || '',
      name: newData.name || '',
      email: newData.email || '',
      phone: newData.phone || '',
      role: newData.role || 'user',
      status: newData.status !== undefined ? newData.status : 1,
      balance: newData.balance || 0,
      frozen_balance: newData.frozen_balance || 0,
      avatar: newData.avatar || '',
      remark: newData.remark || '',
      distributor_account: newData.distributor_account || '',
      distributor_phone: newData.distributor_phone || '',
      distributor_alipay: newData.distributor_alipay || '',
      distributor_wechat: newData.distributor_wechat || '',
      distributor_real_name: newData.distributor_real_name || '',
      parent_distributor_id: newData.parent_distributor_id || null
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      username: '',
      name: '',
      email: '',
      phone: '',
      role: 'user',
      status: 1,
      password: '',
      confirmPassword: '',
      balance: 0,
      frozen_balance: 0,
      avatar: '',
      remark: '',
      distributor_account: '',
      distributor_phone: '',
      distributor_alipay: '',
      distributor_wechat: '',
      distributor_real_name: '',
      parent_distributor_id: null
    })
  }
}, { immediate: true, deep: true })

// 头像上传成功
const handleAvatarSuccess = (response) => {
  if (response.success) {
    formData.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 获取分销员列表
const getDistributorList = async () => {
  try {
    // 这里应该调用获取分销员列表的API
    // const { data } = await getDistributors()
    // distributorList.value = data.list
    distributorList.value = [] // 临时空数组
  } catch (error) {
    console.error('获取分销员列表失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = { ...formData }
    
    // 移除确认密码字段
    delete submitData.confirmPassword
    
    // 如果是编辑模式且没有修改密码，移除密码字段
    if (isEdit.value && !submitData.password) {
      delete submitData.password
    }

    if (isEdit.value) {
      await updateUser(props.userData.id, submitData)
      ElMessage.success('用户更新成功')
    } else {
      await createUser(submitData)
      ElMessage.success('用户创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  dialogVisible.value = false
}

onMounted(() => {
  getDistributorList()
})
</script>

<style lang="scss" scoped>
.user-form {
  .avatar-upload {
    text-align: center;
  }

  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      width: 120px;
      height: 120px;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .upload-tip {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .user-form {
    .avatar-uploader :deep(.el-upload) {
      width: 100px;
      height: 100px;
    }
  }
}
</style>