import{_ as e,u as s}from"./index-D2bI4m-v.js";/* empty css                         */import{ag as a,r as t,e as i,k as l,l as n,t as c,F as o,Y as r,E as u,z as d,y as p,D as y,C as v,u as m}from"./vue-vendor-DGsK9sC4.js";import{ao as g,a7 as h,ai as k,a6 as b,a0 as f,Q as _,ba as C,U as T,bb as j,o as z,T as w,a_ as x,aO as D,as as q}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";const E={class:"simple-dashboard"},F={class:"stats-grid"},I={class:"stat-content"},O={class:"stat-value"},Q={class:"stat-label"},S={class:"quick-actions"},U={class:"actions-grid"},Y={class:"recent-activities"},$=e({__name:"SimpleDashboard",setup(e){const $=a();s();const A=t([{key:"users",label:"总用户数",value:"8,924",change:"+12.5%",changeType:"positive",icon:g,iconClass:"user-icon"},{key:"orders",label:"今日订单",value:"156",change:"+8.2%",changeType:"positive",icon:h,iconClass:"order-icon"},{key:"revenue",label:"今日收入",value:"¥25,680",change:"+15.8%",changeType:"positive",icon:k,iconClass:"money-icon"},{key:"growth",label:"增长率",value:"23.5%",change:"+2.1%",changeType:"positive",icon:b,iconClass:"growth-icon"}]),B=t([{key:"users",label:"用户管理",icon:g,type:"primary"},{key:"orders",label:"订单管理",icon:h,type:"success"},{key:"finance",label:"财务管理",icon:k,type:"warning"},{key:"settings",label:"系统设置",icon:f,type:"info"}]),G=t([{id:1,content:"新用户注册：张三",time:"2024-01-15 14:30",type:"primary"},{id:2,content:"订单完成：订单号 #12345",time:"2024-01-15 14:25",type:"success"},{id:3,content:"系统维护完成",time:"2024-01-15 14:20",type:"info"}]);return i(()=>{_.success("仪表板加载成功")}),(e,s)=>{const a=w,t=q,i=C,g=j;return n(),l("div",E,[s[2]||(s[2]=c("div",{class:"page-header"},[c("h1",null,"📊 数据看板"),c("p",null,"管理后台核心数据展示")],-1)),c("div",F,[(n(!0),l(o,null,r(A.value,e=>(n(),l("div",{class:"stat-card",key:e.key},[c("div",{class:z(["stat-icon",e.iconClass])},[u(a,{size:24},{default:d(()=>[(n(),p(v(e.icon)))]),_:2},1024)],2),c("div",I,[c("div",O,T(e.value),1),c("div",Q,T(e.label),1),c("div",{class:z(["stat-change",e.changeType])},[u(a,{size:12},{default:d(()=>["positive"===e.changeType?(n(),p(m(x),{key:0})):(n(),p(m(D),{key:1}))]),_:2},1024),y(" "+T(e.change),1)],2)])]))),128))]),c("div",S,[s[0]||(s[0]=c("h2",null,"🚀 快速操作",-1)),c("div",U,[(n(!0),l(o,null,r(B.value,e=>(n(),p(t,{key:e.key,type:e.type,size:"large",onClick:s=>(e=>{const s={users:"/user/list",orders:"/orders/list",finance:"/finance/dashboard",settings:"/system/settings"};s[e]?$.push(s[e]):_.info(`跳转到${e}页面`)})(e.key)},{default:d(()=>[u(a,null,{default:d(()=>[(n(),p(v(e.icon)))]),_:2},1024),y(" "+T(e.label),1)]),_:2},1032,["type","onClick"]))),128))])]),c("div",Y,[s[1]||(s[1]=c("h2",null,"📋 最近活动",-1)),u(g,null,{default:d(()=>[(n(!0),l(o,null,r(G.value,e=>(n(),p(i,{key:e.id,timestamp:e.time,type:e.type},{default:d(()=>[y(T(e.content),1)]),_:2},1032,["timestamp","type"]))),128))]),_:1})])])}}},[["__scopeId","data-v-5f3d4a2b"]]);export{$ as default};
