const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    // 设置 CORS 头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    let filePath = '.' + req.url;
    
    // 处理资源路径
    if (req.url.startsWith('/admin/assets/')) {
        filePath = './admin/dist' + req.url;
    } else if (req.url.startsWith('/assets/')) {
        filePath = './admin/dist/assets' + req.url.substring(7);
    }
    
    // 默认页面
    if (filePath === './') {
        filePath = './admin-direct.html';
    }
    
    // 获取文件扩展名
    const extname = String(path.extname(filePath)).toLowerCase();
    
    // MIME 类型映射
    const mimeTypes = {
        '.html': 'text/html',
        '.js': 'text/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.wav': 'audio/wav',
        '.mp4': 'video/mp4',
        '.woff': 'application/font-woff',
        '.ttf': 'application/font-ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'application/font-otf',
        '.wasm': 'application/wasm'
    };

    const contentType = mimeTypes[extname] || 'application/octet-stream';

    // 检查路径是否为目录
    fs.stat(filePath, (err, stats) => {
        if (err) {
            // 如果文件不存在，尝试直接读取文件
            fs.readFile(filePath, (error, content) => {
                if (error) {
                    if (error.code === 'ENOENT') {
                        // 404 页面
                        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
                        res.end(`
                            <!DOCTYPE html>
                            <html lang="zh-CN">
                            <head>
                                <meta charset="UTF-8">
                                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                <title>页面未找到 - LinkHub Pro</title>
                                <style>
                                    body {
                                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                        min-height: 100vh;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        margin: 0;
                                        color: white;
                                        text-align: center;
                                    }
                                    .error-container {
                                        background: rgba(255, 255, 255, 0.1);
                                        backdrop-filter: blur(20px);
                                        border-radius: 20px;
                                        padding: 40px;
                                        border: 1px solid rgba(255, 255, 255, 0.2);
                                    }
                                    h1 { font-size: 4rem; margin: 0 0 20px 0; }
                                    p { font-size: 1.2rem; margin: 0 0 30px 0; opacity: 0.9; }
                                    a {
                                        color: white;
                                        text-decoration: none;
                                        background: rgba(255, 255, 255, 0.2);
                                        padding: 12px 24px;
                                        border-radius: 12px;
                                        transition: all 0.3s ease;
                                        display: inline-block;
                                    }
                                    a:hover {
                                        background: rgba(255, 255, 255, 0.3);
                                        transform: translateY(-2px);
                                    }
                                </style>
                            </head>
                            <body>
                                <div class="error-container">
                                    <h1>404</h1>
                                    <p>抱歉，您访问的页面不存在</p>
                                    <a href="/">返回首页</a>
                                </div>
                            </body>
                            </html>
                        `);
                    } else {
                        // 500 错误
                        res.writeHead(500);
                        res.end('服务器内部错误: ' + error.code);
                    }
                } else {
                    // 成功返回文件
                    res.writeHead(200, { 'Content-Type': contentType + '; charset=utf-8' });
                    res.end(content, 'utf-8');
                }
            });
            return;
        }
        
        // 如果是目录，则尝试读取index.html或显示目录列表
        if (stats.isDirectory()) {
            const indexPath = path.join(filePath, 'index.html');
            fs.access(indexPath, fs.constants.F_OK, (err) => {
                if (!err) {
                    // 存在index.html，读取它
                    fs.readFile(indexPath, (error, content) => {
                        if (error) {
                            res.writeHead(500);
                            res.end('服务器内部错误: ' + error.code);
                        } else {
                            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8', 'Cache-Control': 'no-cache' });
                            res.end(content, 'utf-8');
                        }
                    });
                } else {
                    // 不存在index.html，重定向到默认页面
                    res.writeHead(302, { 'Location': '/' });
                    res.end();
                }
            });
            return;
        }
        
        // 读取文件
        fs.readFile(filePath, (error, content) => {
            if (error) {
                if (error.code === 'ENOENT') {
                    // 404 页面
                    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
                    res.end(`
                        <!DOCTYPE html>
                        <html lang="zh-CN">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>页面未找到 - LinkHub Pro</title>
                            <style>
                                body {
                                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                    min-height: 100vh;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    margin: 0;
                                    color: white;
                                    text-align: center;
                                }
                                .error-container {
                                    background: rgba(255, 255, 255, 0.1);
                                    backdrop-filter: blur(20px);
                                    border-radius: 20px;
                                    padding: 40px;
                                    border: 1px solid rgba(255, 255, 255, 0.2);
                                }
                                h1 { font-size: 4rem; margin: 0 0 20px 0; }
                                p { font-size: 1.2rem; margin: 0 0 30px 0; opacity: 0.9; }
                                a {
                                    color: white;
                                    text-decoration: none;
                                    background: rgba(255, 255, 255, 0.2);
                                    padding: 12px 24px;
                                    border-radius: 12px;
                                    transition: all 0.3s ease;
                                    display: inline-block;
                                }
                                a:hover {
                                    background: rgba(255, 255, 255, 0.3);
                                    transform: translateY(-2px);
                                }
                            </style>
                        </head>
                        <body>
                            <div class="error-container">
                                <h1>404</h1>
                                <p>抱歉，您访问的页面不存在</p>
                                <a href="/">返回首页</a>
                            </div>
                        </body>
                        </html>
                    `);
                } else {
                    // 500 错误
                    res.writeHead(500);
                    res.end('服务器内部错误: ' + error.code);
                }
            } else {
                // 成功返回文件
                res.writeHead(200, { 'Content-Type': contentType + '; charset=utf-8' });
                res.end(content, 'utf-8');
            }
        });
    });
});

const PORT = process.env.PORT || 9000;

server.listen(PORT, () => {
    console.log('🚀 晨鑫流量变现 后台管理系统预览服务器已启动!');
    console.log(`📱 本地访问地址: http://localhost:${PORT}`);
    console.log(`🌐 网络访问地址: http://127.0.0.1:${PORT}`);
    console.log('');
    console.log('📋 可用页面:');
    console.log(`   • 后台管理系统 (默认): http://localhost:${PORT}/`);
    console.log(`   • 登录页面展示: http://localhost:${PORT}/登录页面美化效果展示.html`);
    console.log('');
    console.log('💡 提示: 按 Ctrl+C 停止服务器');
    console.log('');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n👋 服务器已停止');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 服务器已停止');
    process.exit(0);
});