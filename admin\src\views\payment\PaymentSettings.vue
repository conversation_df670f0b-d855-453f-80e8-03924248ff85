<template>
  <div class="payment-settings">
    <PageLayout title="支付设置" subtitle="管理系统支付配置和支付方式">
      <!-- 支付方式配置 -->
      <div class="settings-section">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <h3>支付方式配置</h3>
              <el-button type="primary" @click="saveAllSettings" :loading="saving">
                <el-icon><Check /></el-icon>
                保存所有设置
              </el-button>
            </div>
          </template>

          <el-tabs v-model="activePaymentTab" class="payment-tabs">
            <!-- 支付宝配置 -->
            <el-tab-pane label="支付宝" name="alipay">
              <div class="payment-config">
                <div class="config-header">
                  <div class="header-info">
                    <el-icon class="payment-icon alipay"><CreditCard /></el-icon>
                    <div class="payment-info">
                      <h4>支付宝支付</h4>
                      <p>接入支付宝官方支付接口，支持扫码支付、手机支付等</p>
                    </div>
                  </div>
                  <el-switch
                    v-model="paymentConfig.alipay.enabled"
                    size="large"
                    @change="togglePaymentMethod('alipay', $event)"
                  />
                </div>

                <div v-if="paymentConfig.alipay.enabled" class="config-form">
                  <el-form :model="paymentConfig.alipay" label-width="120px">
                    <el-form-item label="应用ID" required>
                      <el-input
                        v-model="paymentConfig.alipay.app_id"
                        placeholder="请输入支付宝应用ID"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="商户私钥" required>
                      <el-input
                        v-model="paymentConfig.alipay.private_key"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入商户私钥"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="支付宝公钥" required>
                      <el-input
                        v-model="paymentConfig.alipay.public_key"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入支付宝公钥"
                      />
                    </el-form-item>
                    
                    <el-form-item label="网关地址">
                      <el-select v-model="paymentConfig.alipay.gateway" style="width: 100%">
                        <el-option label="正式环境" value="https://openapi.alipay.com/gateway.do" />
                        <el-option label="沙箱环境" value="https://openapi.alipaydev.com/gateway.do" />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="回调地址">
                      <el-input
                        v-model="paymentConfig.alipay.notify_url"
                        placeholder="支付结果异步通知地址"
                      />
                    </el-form-item>
                    
                    <el-form-item label="返回地址">
                      <el-input
                        v-model="paymentConfig.alipay.return_url"
                        placeholder="支付完成后返回地址"
                      />
                    </el-form-item>

                    <el-form-item>
                      <el-button @click="testPayment('alipay')" :loading="testing.alipay">
                        <el-icon><Connection /></el-icon>
                        测试连接
                      </el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-tab-pane>

            <!-- 微信支付配置 -->
            <el-tab-pane label="微信支付" name="wechat">
              <div class="payment-config">
                <div class="config-header">
                  <div class="header-info">
                    <el-icon class="payment-icon wechat"><ChatDotRound /></el-icon>
                    <div class="payment-info">
                      <h4>微信支付</h4>
                      <p>接入微信官方支付接口，支持扫码支付、公众号支付等</p>
                    </div>
                  </div>
                  <el-switch
                    v-model="paymentConfig.wechat.enabled"
                    size="large"
                    @change="togglePaymentMethod('wechat', $event)"
                  />
                </div>

                <div v-if="paymentConfig.wechat.enabled" class="config-form">
                  <el-form :model="paymentConfig.wechat" label-width="120px">
                    <el-form-item label="应用ID" required>
                      <el-input
                        v-model="paymentConfig.wechat.app_id"
                        placeholder="请输入微信应用ID"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="商户号" required>
                      <el-input
                        v-model="paymentConfig.wechat.mch_id"
                        placeholder="请输入微信商户号"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="商户密钥" required>
                      <el-input
                        v-model="paymentConfig.wechat.key"
                        placeholder="请输入商户密钥"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="证书文件">
                      <el-upload
                        class="cert-upload"
                        :action="uploadUrl"
                        :headers="uploadHeaders"
                        :on-success="handleCertUpload"
                        :before-upload="beforeCertUpload"
                        accept=".pem,.p12"
                      >
                        <el-button>
                          <el-icon><Upload /></el-icon>
                          上传证书
                        </el-button>
                        <template #tip>
                          <div class="el-upload__tip">
                            支持 .pem 和 .p12 格式的证书文件
                          </div>
                        </template>
                      </el-upload>
                    </el-form-item>
                    
                    <el-form-item label="回调地址">
                      <el-input
                        v-model="paymentConfig.wechat.notify_url"
                        placeholder="支付结果异步通知地址"
                      />
                    </el-form-item>

                    <el-form-item>
                      <el-button @click="testPayment('wechat')" :loading="testing.wechat">
                        <el-icon><Connection /></el-icon>
                        测试连接
                      </el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-tab-pane>

            <!-- 易支付配置 -->
            <el-tab-pane label="易支付" name="easypay">
              <div class="payment-config">
                <div class="config-header">
                  <div class="header-info">
                    <el-icon class="payment-icon easypay"><Wallet /></el-icon>
                    <div class="payment-info">
                      <h4>易支付</h4>
                      <p>第三方聚合支付平台，支持多种支付方式</p>
                    </div>
                  </div>
                  <el-switch
                    v-model="paymentConfig.easypay.enabled"
                    size="large"
                    @change="togglePaymentMethod('easypay', $event)"
                  />
                </div>

                <div v-if="paymentConfig.easypay.enabled" class="config-form">
                  <el-form :model="paymentConfig.easypay" label-width="120px">
                    <el-form-item label="商户ID" required>
                      <el-input
                        v-model="paymentConfig.easypay.pid"
                        placeholder="请输入易支付商户ID"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="商户密钥" required>
                      <el-input
                        v-model="paymentConfig.easypay.key"
                        placeholder="请输入商户密钥"
                        show-password
                      />
                    </el-form-item>
                    
                    <el-form-item label="API地址" required>
                      <el-input
                        v-model="paymentConfig.easypay.api_url"
                        placeholder="请输入易支付API地址"
                      />
                    </el-form-item>
                    
                    <el-form-item label="回调地址">
                      <el-input
                        v-model="paymentConfig.easypay.notify_url"
                        placeholder="支付结果异步通知地址"
                      />
                    </el-form-item>
                    
                    <el-form-item label="返回地址">
                      <el-input
                        v-model="paymentConfig.easypay.return_url"
                        placeholder="支付完成后返回地址"
                      />
                    </el-form-item>

                    <el-form-item>
                      <el-button @click="testPayment('easypay')" :loading="testing.easypay">
                        <el-icon><Connection /></el-icon>
                        测试连接
                      </el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-tab-pane>

            <!-- 银行卡支付 -->
            <el-tab-pane label="银行卡支付" name="bank">
              <div class="payment-config">
                <div class="config-header">
                  <div class="header-info">
                    <el-icon class="payment-icon bank"><CreditCard /></el-icon>
                    <div class="payment-info">
                      <h4>银行卡支付</h4>
                      <p>支持各大银行的网银支付和快捷支付</p>
                    </div>
                  </div>
                  <el-switch
                    v-model="paymentConfig.bank.enabled"
                    size="large"
                    @change="togglePaymentMethod('bank', $event)"
                  />
                </div>

                <div v-if="paymentConfig.bank.enabled" class="config-form">
                  <el-form :model="paymentConfig.bank" label-width="120px">
                    <el-form-item label="支持银行">
                      <el-checkbox-group v-model="paymentConfig.bank.supported_banks">
                        <el-checkbox label="ICBC">工商银行</el-checkbox>
                        <el-checkbox label="ABC">农业银行</el-checkbox>
                        <el-checkbox label="BOC">中国银行</el-checkbox>
                        <el-checkbox label="CCB">建设银行</el-checkbox>
                        <el-checkbox label="COMM">交通银行</el-checkbox>
                        <el-checkbox label="CMB">招商银行</el-checkbox>
                        <el-checkbox label="CITIC">中信银行</el-checkbox>
                        <el-checkbox label="CEB">光大银行</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    
                    <el-form-item label="手续费率">
                      <el-input-number
                        v-model="paymentConfig.bank.fee_rate"
                        :min="0"
                        :max="10"
                        :precision="2"
                        :step="0.01"
                      />
                      <span class="input-suffix">%</span>
                    </el-form-item>
                    
                    <el-form-item label="最小金额">
                      <el-input-number
                        v-model="paymentConfig.bank.min_amount"
                        :min="0.01"
                        :precision="2"
                      />
                      <span class="input-suffix">元</span>
                    </el-form-item>
                    
                    <el-form-item label="最大金额">
                      <el-input-number
                        v-model="paymentConfig.bank.max_amount"
                        :min="1"
                        :precision="2"
                      />
                      <span class="input-suffix">元</span>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>

      <!-- 支付安全设置 -->
      <div class="settings-section">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <h3>安全设置</h3>
              <el-tag type="warning">
                <el-icon><Lock /></el-icon>
                安全级别：高
              </el-tag>
            </div>
          </template>

          <el-form :model="securitySettings" label-width="150px">
            <el-form-item label="支付密码验证">
              <el-switch v-model="securitySettings.require_payment_password" />
              <span class="form-tip">开启后用户支付时需要输入支付密码</span>
            </el-form-item>
            
            <el-form-item label="短信验证">
              <el-switch v-model="securitySettings.require_sms_verification" />
              <span class="form-tip">大额支付时需要短信验证码确认</span>
            </el-form-item>
            
            <el-form-item label="大额支付阈值">
              <el-input-number
                v-model="securitySettings.large_amount_threshold"
                :min="100"
                :max="50000"
                :step="100"
              />
              <span class="input-suffix">元</span>
            </el-form-item>
            
            <el-form-item label="IP白名单">
              <el-input
                v-model="securitySettings.ip_whitelist"
                type="textarea"
                :rows="3"
                placeholder="每行一个IP地址，支持CIDR格式"
              />
            </el-form-item>
            
            <el-form-item label="风控规则">
              <el-checkbox-group v-model="securitySettings.risk_rules">
                <el-checkbox label="frequency_limit">频率限制</el-checkbox>
                <el-checkbox label="amount_limit">金额限制</el-checkbox>
                <el-checkbox label="device_binding">设备绑定</el-checkbox>
                <el-checkbox label="geo_restriction">地域限制</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 支付统计 -->
      <div class="settings-section">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <h3>支付统计</h3>
              <el-button @click="refreshStats" :loading="loadingStats">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card success">
                <div class="stat-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">¥{{ formatMoney(paymentStats.total_amount) }}</div>
                  <div class="stat-label">总交易金额</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card primary">
                <div class="stat-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ paymentStats.total_orders }}</div>
                  <div class="stat-label">总订单数</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card warning">
                <div class="stat-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ paymentStats.success_rate }}%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card info">
                <div class="stat-icon">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ paymentStats.avg_time }}s</div>
                  <div class="stat-label">平均处理时间</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </PageLayout>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check, CreditCard, ChatDotRound, Wallet, Connection, Upload,
  Lock, Refresh, Money, Document, TrendCharts, Clock
} from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'
import {
  getPaymentConfig,
  updatePaymentConfig,
  togglePaymentMethod as togglePaymentMethodApi,
  testPaymentChannel,
  getPaymentStats,
  updateSecuritySettings
} from '@/api/payment'

// 响应式数据
const activePaymentTab = ref('alipay')
const saving = ref(false)
const loadingStats = ref(false)

const testing = reactive({
  alipay: false,
  wechat: false,
  easypay: false,
  bank: false
})

const paymentConfig = reactive({
  alipay: {
    enabled: false,
    app_id: '',
    private_key: '',
    public_key: '',
    gateway: 'https://openapi.alipay.com/gateway.do',
    notify_url: '',
    return_url: ''
  },
  wechat: {
    enabled: false,
    app_id: '',
    mch_id: '',
    key: '',
    cert_path: '',
    notify_url: ''
  },
  easypay: {
    enabled: true, // 默认启用易支付
    pid: '',
    key: '',
    api_url: '',
    notify_url: '',
    return_url: ''
  },
  bank: {
    enabled: false,
    supported_banks: ['ICBC', 'ABC', 'BOC', 'CCB'],
    fee_rate: 0.6,
    min_amount: 0.01,
    max_amount: 50000
  }
})

const securitySettings = reactive({
  require_payment_password: true,
  require_sms_verification: true,
  large_amount_threshold: 1000,
  ip_whitelist: '',
  risk_rules: ['frequency_limit', 'amount_limit']
})

const paymentStats = reactive({
  total_amount: 0,
  total_orders: 0,
  success_rate: 0,
  avg_time: 0
})

// 上传配置
const uploadUrl = ref('/api/upload/cert')
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})

// 方法
const loadPaymentConfig = async () => {
  try {
    const response = await getPaymentConfig()
    Object.assign(paymentConfig, response.data)
  } catch (error) {
    // 使用模拟数据
    console.log('使用模拟支付配置数据')
  }
}

const saveAllSettings = async () => {
  saving.value = true
  try {
    await updatePaymentConfig(paymentConfig)
    await updateSecuritySettings(securitySettings)
    ElMessage.success('支付设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  } finally {
    saving.value = false
  }
}

const togglePaymentMethod = async (method, enabled) => {
  try {
    await togglePaymentMethodApi(method, enabled)
    ElMessage.success(`${method} 支付方式已${enabled ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('操作失败：' + error.message)
    // 回滚状态
    paymentConfig[method].enabled = !enabled
  }
}

const testPayment = async (method) => {
  testing[method] = true
  try {
    const testData = {
      amount: 0.01,
      subject: '测试支付',
      out_trade_no: `test_${Date.now()}`
    }
    
    await testPaymentChannel(method, testData)
    ElMessage.success(`${method} 支付通道测试成功`)
  } catch (error) {
    ElMessage.error(`${method} 支付通道测试失败：` + error.message)
  } finally {
    testing[method] = false
  }
}

const handleCertUpload = (response, file) => {
  if (response.code === 200) {
    paymentConfig.wechat.cert_path = response.data.path
    ElMessage.success('证书上传成功')
  } else {
    ElMessage.error('证书上传失败')
  }
}

const beforeCertUpload = (file) => {
  const isValidType = file.type === 'application/x-pkcs12' || file.name.endsWith('.pem')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    ElMessage.error('证书文件格式不正确')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('证书文件大小不能超过 2MB')
    return false
  }
  return true
}

const refreshStats = async () => {
  loadingStats.value = true
  try {
    const response = await getPaymentStats()
    Object.assign(paymentStats, response.data)
  } catch (error) {
    // 使用模拟数据
    Object.assign(paymentStats, {
      total_amount: 1234567.89,
      total_orders: 5678,
      success_rate: 98.5,
      avg_time: 2.3
    })
  } finally {
    loadingStats.value = false
  }
}

const formatMoney = (amount) => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

// 生命周期
onMounted(() => {
  loadPaymentConfig()
  refreshStats()
})
</script>

<style lang="scss" scoped>
.payment-settings {
  .settings-section {
    margin-bottom: 24px;

    .settings-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }

  .payment-tabs {
    :deep(.el-tabs__header) {
      margin: 0 0 24px 0;
    }

    .payment-config {
      .config-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background: #f8fafc;
        border-radius: 8px;
        margin-bottom: 24px;

        .header-info {
          display: flex;
          align-items: center;
          gap: 16px;

          .payment-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;

            &.alipay {
              background: linear-gradient(135deg, #1677ff, #69c0ff);
            }

            &.wechat {
              background: linear-gradient(135deg, #52c41a, #95de64);
            }

            &.easypay {
              background: linear-gradient(135deg, #722ed1, #b37feb);
            }

            &.bank {
              background: linear-gradient(135deg, #fa8c16, #ffc53d);
            }
          }

          .payment-info {
            h4 {
              margin: 0 0 4px 0;
              font-size: 16px;
              font-weight: 600;
              color: #303133;
            }

            p {
              margin: 0;
              font-size: 14px;
              color: #909399;
            }
          }
        }
      }

      .config-form {
        .form-tip {
          margin-left: 12px;
          font-size: 12px;
          color: #909399;
        }

        .input-suffix {
          margin-left: 8px;
          font-size: 14px;
          color: #909399;
        }

        .cert-upload {
          :deep(.el-upload) {
            .el-button {
              margin-right: 12px;
            }
          }
        }
      }
    }
  }

  .stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    &.success .stat-icon {
      background: linear-gradient(135deg, #52c41a, #95de64);
    }

    &.primary .stat-icon {
      background: linear-gradient(135deg, #1677ff, #69c0ff);
    }

    &.warning .stat-icon {
      background: linear-gradient(135deg, #fa8c16, #ffc53d);
    }

    &.info .stat-icon {
      background: linear-gradient(135deg, #722ed1, #b37feb);
    }

    .stat-content {
      flex: 1;

      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #303133;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .payment-settings {
    .payment-config {
      .config-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .payment-settings {
    .stat-card {
      flex-direction: column;
      text-align: center;
    }

    .payment-config {
      .config-form {
        :deep(.el-form) {
          .el-form-item {
            .el-form-item__label {
              width: 100px !important;
            }
          }
        }
      }
    }
  }
}
</style>
