import{L as e}from"./LineChart-Ba008-uu.js";import{D as a}from"./DoughnutChart-JEDVUFw0.js";import{i as t}from"./echarts-DTArWCqr.js";import{r as l,d as s,e as i,n as r,H as o,k as n,l as c,ao as d,c as u,t as v,m as p,B as f,ai as m,D as h,F as g,Y as y,E as b}from"./vue-vendor-DGsK9sC4.js";import{p as k,U as x,o as w}from"./element-plus-DcSKpKA8.js";import{_ as z}from"./index-D2bI4m-v.js";import"./chart-Bup65vvO.js";import"./utils-4VKArNEK.js";const C={__name:"BarChart",props:{data:{type:Object,required:!0},width:{type:String,default:"100%"},height:{type:String,default:"400px"},autoResize:{type:Boolean,default:!0}},setup(e,{expose:a}){const d=e,u=l(null);let v=null;const p=()=>{if(!u.value)return;v=t(u.value);const e={title:{text:d.data.title||"",left:"center",textStyle:{color:"#333",fontSize:16}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:d.data.legend||[],top:30},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:d.data.xAxis||[]},yAxis:{type:"value"},series:d.data.series||[]};v.setOption(e)},f=()=>{v&&v.resize()};return s(()=>d.data,()=>{v&&p()},{deep:!0}),i(()=>{r(()=>{p(),d.autoResize&&window.addEventListener("resize",f)})}),o(()=>{v&&(v.dispose(),v=null),d.autoResize&&window.removeEventListener("resize",f)}),a({chart:v,resizeChart:f}),(a,t)=>(c(),n("div",{ref_key:"chartRef",ref:u,style:k({height:e.height,width:e.width})},null,4))}},S=z({__name:"CountTo",props:{startVal:{type:Number,default:0},endVal:{type:Number,required:!0},duration:{type:Number,default:3e3},autoplay:{type:Boolean,default:!0},decimals:{type:Number,default:0,validator:e=>e>=0},decimal:{type:String,default:"."},separator:{type:String,default:","},prefix:{type:String,default:""},suffix:{type:String,default:""},useEasing:{type:Boolean,default:!0},easingFn:{type:Function,default:(e,a,t,l)=>0===e?a:e===l?a+t:(e/=l/2)<1?t/2*Math.pow(2,10*(e-1))+a:t/2*(2-Math.pow(2,-10*--e))+a}},emits:["mountedCallback","callback"],setup(e,{expose:a,emit:t}){const r=e,o=t,d=l(""),u=l(!1);let v=r.startVal,p=r.duration,f=null,m=!1,h=null,g=null,y=null;const b=e=>{const a=e.toFixed(r.decimals).split(".");return a[0]=a[0].replace(/\B(?=(\d{3})+(?!\d))/g,r.separator),r.prefix+a.join(r.decimal)+r.suffix},k=e=>{h||(h=e);const a=e-h;g=p-a,f=r.useEasing?v===r.endVal?r.endVal:r.easingFn(a,v,r.endVal-v,p):v+(r.endVal-v)*(a/p),f=v>r.endVal?f<r.endVal?r.endVal:f:f>r.endVal?r.endVal:f,d.value=b(f),a<p?y=requestAnimationFrame(k):(u.value=!1,o("callback"))},z=()=>{u.value||(u.value=!0,h=null,p=r.duration,v=r.startVal,y=requestAnimationFrame(k))},C=()=>{h=null,cancelAnimationFrame(y),d.value=b(r.startVal),u.value=!1},S=()=>{C(),z()};return s(()=>r.endVal,(e,a)=>{e!==a&&(r.autoplay?S():d.value=b(e))}),s(()=>r.startVal,()=>{r.autoplay&&S()}),i(()=>{r.autoplay?z():d.value=b(r.startVal),o("mountedCallback")}),a({start:z,pause:()=>{u.value&&(m=!0,cancelAnimationFrame(y))},resume:()=>{m&&(m=!1,h=null,p=g,v=f,y=requestAnimationFrame(k))},reset:C,restart:S}),(e,a)=>(c(),n("span",{class:w(["count-to",{counting:u.value}])},x(d.value),3))}},[["__scopeId","data-v-7e4720d3"]]),V={__name:"MiniLineChart",props:{data:{type:Object,required:!0},width:{type:String,default:"100%"},height:{type:String,default:"60px"},color:{type:String,default:"#409EFF"},autoResize:{type:Boolean,default:!0}},setup(e,{expose:a}){const d=e,u=l(null);let v=null;const p=()=>{if(!u.value)return;v=t(u.value);const e={grid:{left:0,right:0,top:0,bottom:0},xAxis:{type:"category",show:!1,data:d.data.xAxis||[]},yAxis:{type:"value",show:!1},series:[{type:"line",data:d.data.data||[],smooth:!0,symbol:"none",lineStyle:{color:d.color,width:2},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:d.color+"40"},{offset:1,color:d.color+"00"}]}}}]};v.setOption(e)},f=()=>{v&&v.resize()};return s(()=>d.data,()=>{v&&p()},{deep:!0}),s(()=>d.color,()=>{v&&p()}),i(()=>{r(()=>{p(),d.autoResize&&window.addEventListener("resize",f)})}),o(()=>{v&&(v.dispose(),v=null),d.autoResize&&window.removeEventListener("resize",f)}),a({chart:v,resizeChart:f}),(a,t)=>(c(),n("div",{ref_key:"chartRef",ref:u,style:k({height:e.height,width:e.width})},null,4))}},_=["width","height"],A=["cx","cy","r","stroke","stroke-width"],F=["cx","cy","r","stroke","stroke-width","stroke-dasharray","stroke-dashoffset"],R={class:"progress-content"},j={class:"progress-text"},D={class:"progress-percentage"},E={key:0,class:"progress-label"},L=z({__name:"CircularProgress",props:{percentage:{type:Number,default:0,validator:e=>e>=0&&e<=100},size:{type:Number,default:120},strokeWidth:{type:Number,default:8},strokeColor:{type:String,default:"#409EFF"},trackColor:{type:String,default:"#e5e9f2"},label:{type:String,default:""},animated:{type:Boolean,default:!0}},setup(e){d(e=>({"1b747056":s.value+"px","5dc21138":i.value+"px"}));const a=e,t=u(()=>a.size/2),l=u(()=>(a.size-a.strokeWidth)/2),s=u(()=>2*Math.PI*l.value),i=u(()=>s.value-a.percentage/100*s.value);return(a,r)=>(c(),n("div",{class:"circular-progress",style:k({width:e.size+"px",height:e.size+"px"})},[(c(),n("svg",{width:e.size,height:e.size,class:"progress-svg"},[v("circle",{cx:t.value,cy:t.value,r:l.value,stroke:e.trackColor,"stroke-width":e.strokeWidth,fill:"none",class:"progress-track"},null,8,A),v("circle",{cx:t.value,cy:t.value,r:l.value,stroke:e.strokeColor,"stroke-width":e.strokeWidth,"stroke-dasharray":s.value,"stroke-dashoffset":i.value,fill:"none",class:w(["progress-bar",{"progress-animation":e.animated}])},null,10,F)],8,_)),v("div",R,[p(a.$slots,"default",{},()=>[v("div",j,[v("div",D,x(e.percentage)+"%",1),e.label?(c(),n("div",E,x(e.label),1)):f("",!0)])],!0)])],4))}},[["__scopeId","data-v-bcfd7fc7"]]),B={class:"data-screen"},N={class:"screen-header"},P={class:"header-right"},I={class:"current-time"},q={class:"metrics-section"},M={class:"metric-icon"},O={class:"metric-content"},W={class:"metric-value"},U={class:"metric-unit"},T={class:"metric-label"},Z={class:"metric-chart"},H={class:"charts-section"},Y={class:"chart-container large"},$={class:"chart-header"},G={class:"chart-controls"},J=["onClick"],K={class:"chart-content"},Q={class:"chart-container medium"},X={class:"chart-content"},ee={class:"chart-container medium"},ae={class:"chart-content"},te={class:"details-section"},le={class:"detail-card"},se={class:"card-content"},ie={class:"region-list"},re={class:"region-rank"},oe={class:"region-name"},ne={class:"region-bar"},ce={class:"region-value"},de={class:"detail-card"},ue={class:"card-content"},ve={class:"activity-list"},pe={class:"activity-icon"},fe={class:"activity-content"},me={class:"activity-text"},he={class:"activity-time"},ge={class:"detail-card"},ye={class:"card-content"},be={class:"monitor-grid"},ke={class:"monitor-name"},xe={class:"monitor-gauge"},we={class:"monitor-value"},ze={class:"bottom-metrics"},Ce={class:"metrics-scroll"},Se={class:"scroll-label"},Ve={class:"scroll-value"},_e=z({__name:"DataScreen",setup(t){const s=l(""),r=l("7d");l(!1);const d=[{label:"今日",value:"1d"},{label:"7天",value:"7d"},{label:"30天",value:"30d"},{label:"90天",value:"90d"}],u=l([{key:"totalRevenue",label:"总收入",value:1254890,unit:"元",icon:"icon-money",color:"#1890ff",trend:{type:"up",icon:"icon-arrow-up",value:12.5},chartData:[20,35,28,45,38,52,48,60,55,70]},{key:"activeUsers",label:"活跃用户",value:8652,unit:"人",icon:"icon-user",color:"#52c41a",trend:{type:"up",icon:"icon-arrow-up",value:8.3},chartData:[30,25,40,35,50,45,60,55,65,58]},{key:"totalOrders",label:"订单总数",value:15428,unit:"单",icon:"icon-order",color:"#722ed1",trend:{type:"up",icon:"icon-arrow-up",value:15.2},chartData:[25,30,35,28,42,38,55,50,58,62]},{key:"conversionRate",label:"转化率",value:23.8,unit:"%",icon:"icon-chart",color:"#fa8c16",trend:{type:"down",icon:"icon-arrow-down",value:2.1},chartData:[45,42,48,40,38,35,40,38,42,45]}]),p=l({labels:["01/01","01/02","01/03","01/04","01/05","01/06","01/07"],datasets:[{label:"收入",data:[12e3,15e3,13500,18e3,16500,2e4,22e3],borderColor:"#1890ff",backgroundColor:"rgba(24, 144, 255, 0.1)",fill:!0,tension:.4},{label:"目标",data:[15e3,15e3,15e3,15e3,15e3,15e3,15e3],borderColor:"#ff4d4f",borderDash:[5,5],fill:!1}]}),f={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,color:"#ffffff"}}},scales:{y:{beginAtZero:!0,ticks:{color:"#ffffff",callback:function(e){return e/1e3+"k"}},grid:{color:"rgba(255, 255, 255, 0.1)"}},x:{ticks:{color:"#ffffff"},grid:{color:"rgba(255, 255, 255, 0.1)"}}}},z=l({labels:["活跃用户","新用户","流失用户"],datasets:[{data:[65,25,10],backgroundColor:["#52c41a","#1890ff","#ff4d4f"],borderWidth:0}]}),_={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{color:"#ffffff",usePointStyle:!0,padding:20}}}},A=l({labels:["待支付","已支付","处理中","已完成","已取消"],datasets:[{label:"订单数量",data:[120,890,450,1200,80],backgroundColor:["#faad14","#52c41a","#1890ff","#722ed1","#ff4d4f"]}]}),F={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{color:"#ffffff"},grid:{color:"rgba(255, 255, 255, 0.1)"}},x:{ticks:{color:"#ffffff"},grid:{display:!1}}}},R=l([{name:"广东省",count:2156,percentage:100},{name:"江苏省",count:1847,percentage:85.7},{name:"浙江省",count:1632,percentage:75.7},{name:"北京市",count:1425,percentage:66.1},{name:"上海市",count:1298,percentage:60.2},{name:"山东省",count:1134,percentage:52.6},{name:"河南省",count:976,percentage:45.3},{name:"四川省",count:845,percentage:39.2},{name:"湖北省",count:723,percentage:33.5},{name:"福建省",count:658,percentage:30.5}]),j=l([{id:1,icon:"icon-user-add",text:"新用户注册",time:"2分钟前",value:"+1",type:"success"},{id:2,icon:"icon-order",text:"订单创建",time:"3分钟前",value:"+5",type:"info"},{id:3,icon:"icon-money",text:"支付完成",time:"5分钟前",value:"+¥1,200",type:"success"},{id:4,icon:"icon-warning",text:"系统告警",time:"8分钟前",value:"处理中",type:"warning"}]),D=l([{name:"CPU",value:45,color:"#1890ff"},{name:"内存",value:68,color:"#52c41a"},{name:"磁盘",value:32,color:"#faad14"},{name:"网络",value:78,color:"#722ed1"}]),E=l([{key:"pv",label:"今日PV",value:"125,432"},{key:"uv",label:"今日UV",value:"23,156"},{key:"bounce",label:"跳出率",value:"32.5%"},{key:"duration",label:"平均时长",value:"4m 23s"},{key:"conversion",label:"转化率",value:"12.8%"},{key:"satisfaction",label:"满意度",value:"94.2%"}]),_e=()=>{const e=new Date;s.value=e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},Ae=()=>{console.log("刷新地域数据")};let Fe=null;return i(()=>{_e(),Fe=setInterval(_e,1e3),setInterval(()=>{u.value.forEach(e=>{e.value+=Math.floor(10*Math.random())})},5e3)}),o(()=>{Fe&&clearInterval(Fe)}),(t,l)=>(c(),n("div",B,[v("div",N,[l[1]||(l[1]=m('<div class="header-left" data-v-bc6b73a4><div class="logo" data-v-bc6b73a4><div class="logo-icon" data-v-bc6b73a4>📊</div><span class="logo-text" data-v-bc6b73a4>晨鑫流量变现系统 数据中心</span></div></div><div class="header-center" data-v-bc6b73a4><h1 class="screen-title" data-v-bc6b73a4>实时运营数据大屏</h1></div>',2)),v("div",P,[v("div",I,x(s.value),1),l[0]||(l[0]=v("div",{class:"refresh-info"},[v("span",{class:"refresh-dot"}),h(" 实时更新 ")],-1))])]),v("div",q,[(c(!0),n(g,null,y(u.value,e=>(c(),n("div",{class:"metric-card",key:e.key},[v("div",M,[v("i",{class:w(e.icon)},null,2)]),v("div",O,[v("div",W,[b(S,{"end-val":e.value,duration:2e3},null,8,["end-val"]),v("span",U,x(e.unit),1)]),v("div",T,x(e.label),1),v("div",{class:w(["metric-trend",e.trend.type])},[v("i",{class:w(e.trend.icon)},null,2),v("span",null,x(e.trend.value)+"%",1)],2)]),v("div",Z,[b(V,{data:e.chartData,color:e.color},null,8,["data","color"])])]))),128))]),v("div",H,[v("div",Y,[v("div",$,[l[2]||(l[2]=v("h3",null,"收入趋势分析",-1)),v("div",G,[(c(),n(g,null,y(d,e=>v("button",{key:e.value,class:w(["period-btn",{active:r.value===e.value}]),onClick:a=>r.value=e.value},x(e.label),11,J)),64))])]),v("div",K,[b(e,{data:p.value,options:f,height:"300"},null,8,["data"])])]),v("div",Q,[l[3]||(l[3]=v("div",{class:"chart-header"},[v("h3",null,"用户活跃度")],-1)),v("div",X,[b(a,{data:z.value,options:_,height:"250"},null,8,["data"])])]),v("div",ee,[l[4]||(l[4]=v("div",{class:"chart-header"},[v("h3",null,"订单状态分布")],-1)),v("div",ae,[b(C,{data:A.value,options:F,height:"250"},null,8,["data"])])])]),v("div",te,[v("div",le,[v("div",{class:"card-header"},[l[6]||(l[6]=v("h3",null,"地域分布TOP10",-1)),v("div",{class:"header-action"},[v("button",{class:"refresh-btn",onClick:Ae},l[5]||(l[5]=[v("i",{class:"icon-refresh"},null,-1)]))])]),v("div",se,[v("div",ie,[(c(!0),n(g,null,y(R.value,(e,a)=>(c(),n("div",{class:"region-item",key:e.name},[v("div",re,x(a+1),1),v("div",oe,x(e.name),1),v("div",ne,[v("div",{class:"region-progress",style:k({width:e.percentage+"%"})},null,4)]),v("div",ce,x(e.count),1)]))),128))])])]),v("div",de,[l[7]||(l[7]=v("div",{class:"card-header"},[v("h3",null,"实时动态")],-1)),v("div",ue,[v("div",ve,[(c(!0),n(g,null,y(j.value,e=>(c(),n("div",{class:"activity-item",key:e.id},[v("div",pe,[v("i",{class:w(e.icon)},null,2)]),v("div",fe,[v("div",me,x(e.text),1),v("div",he,x(e.time),1)]),v("div",{class:w(["activity-value",e.type])},x(e.value),3)]))),128))])])]),v("div",ge,[l[8]||(l[8]=v("div",{class:"card-header"},[v("h3",null,"系统监控")],-1)),v("div",ye,[v("div",be,[(c(!0),n(g,null,y(D.value,e=>(c(),n("div",{class:"monitor-item",key:e.name},[v("div",ke,x(e.name),1),v("div",xe,[b(L,{percentage:e.value,color:e.color,size:80},null,8,["percentage","color"])]),v("div",we,x(e.value)+"%",1)]))),128))])])])]),v("div",ze,[v("div",Ce,[(c(!0),n(g,null,y(E.value,e=>(c(),n("div",{class:"scroll-item",key:e.key},[v("span",Se,x(e.label)+"：",1),v("span",Ve,x(e.value),1)]))),128))])])]))}},[["__scopeId","data-v-bc6b73a4"]]);export{_e as default};
