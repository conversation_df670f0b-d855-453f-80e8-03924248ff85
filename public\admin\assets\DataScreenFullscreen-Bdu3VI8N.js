import{_ as s}from"./index-D2bI4m-v.js";import{ag as a,k as t,l as r,t as e,E as o,z as n,u as l}from"./vue-vendor-DGsK9sC4.js";import{aS as c,T as m}from"./element-plus-DcSKpKA8.js";import u from"./DataScreen-BsKps5zq.js";import"./utils-4VKArNEK.js";import"./LineChart-Ba008-uu.js";import"./chart-Bup65vvO.js";import"./DoughnutChart-JEDVUFw0.js";import"./echarts-DTArWCqr.js";const i={class:"fullscreen-data-screen"},p=s({__name:"DataScreenFullscreen",setup(s){const p=a(),d=()=>{p.go(-1)};return(s,a)=>{const p=m;return r(),t("div",i,[e("div",{class:"back-button",onClick:d},[o(p,null,{default:n(()=>[o(l(c))]),_:1}),a[0]||(a[0]=e("span",null,"返回",-1))]),o(u)])}}},[["__scopeId","data-v-1a1a586c"]]);export{p as default};
