<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 分站模型
 */
class Substation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'manager_id',
        'domain',
        'status',
        'commission_rate',
        'description',
        'contact_phone',
        'contact_email',
        'address',
        'settings',
    ];

    protected $casts = [
        'commission_rate' => 'decimal:4',
        'settings' => 'array',
    ];

    /**
     * 关联管理员
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * 关联用户
     */
    public function users(): Has<PERSON>any
    {
        return $this->hasMany(User::class, 'substation_id');
    }

    /**
     * 关联微信群
     */
    public function wechatGroups(): HasMany
    {
        return $this->hasMany(WechatGroup::class);
    }

    /**
     * 关联订单
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute(): string
    {
        return $this->status === 1 ? '正常' : '禁用';
    }

    /**
     * 检查是否启用
     */
    public function isActive(): bool
    {
        return $this->status === 1;
    }

    /**
     * 获取设置值
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * 设置配置值
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->settings = $settings;
        $this->save();
    }

    /**
     * 获取统计数据
     */
    public function getStats(): array
    {
        return [
            'total_users' => $this->users()->count(),
            'total_groups' => $this->wechatGroups()->count(),
            'total_orders' => $this->orders()->count(),
            'total_income' => $this->orders()->where('status', Order::STATUS_PAID_INT)->sum('amount'),
            'today_orders' => $this->orders()->whereDate('created_at', today())->count(),
            'today_income' => $this->orders()->whereDate('created_at', today())->where('status', Order::STATUS_PAID_INT)->sum('amount'),
        ];
    }

    /**
     * 查询作用域：启用的分站
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 查询作用域：有域名的分站
     */
    public function scopeWithDomain($query)
    {
        return $query->whereNotNull('domain');
    }
}