<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserRetentionStat extends Model
{
    protected $fillable = [
        'cohort_date',
        'period_type',
        'period_number',
        'cohort_size',
        'retained_users',
        'retention_rate',
        'churn_rate',
        'retention_data',
    ];

    protected $casts = [
        'cohort_date' => 'date',
        'period_number' => 'integer',
        'cohort_size' => 'integer',
        'retained_users' => 'integer',
        'retention_rate' => 'decimal:2',
        'churn_rate' => 'decimal:2',
        'retention_data' => 'array',
    ];

    /**
     * 按队列日期查询
     */
    public function scopeByCohort($query, $cohortDate)
    {
        return $query->where('cohort_date', $cohortDate);
    }

    /**
     * 按期间类型查询
     */
    public function scopeByPeriodType($query, $periodType)
    {
        return $query->where('period_type', $periodType);
    }

    /**
     * 获取留存率报告
     */
    public static function getRetentionReport($startDate, $endDate, $periodType = 'day')
    {
        return static::whereBetween('cohort_date', [$startDate, $endDate])
            ->where('period_type', $periodType)
            ->orderBy('cohort_date')
            ->orderBy('period_number')
            ->get()
            ->groupBy('cohort_date');
    }

    /**
     * 计算平均留存率
     */
    public static function getAverageRetention($periodNumber, $periodType = 'day')
    {
        return static::where('period_type', $periodType)
            ->where('period_number', $periodNumber)
            ->avg('retention_rate');
    }
} 