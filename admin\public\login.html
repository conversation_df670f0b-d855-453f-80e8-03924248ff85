<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户登录 - 管理系统</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      position: relative;
      overflow-x: hidden;
    }
    
    /* 背景动画效果 */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      animation: backgroundMove 20s linear infinite;
    }
    
    @keyframes backgroundMove {
      0% { transform: translateX(0) translateY(0); }
      100% { transform: translateX(-20px) translateY(-20px); }
    }
    
    .login-container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      width: 100%;
      max-width: 900px;
      display: flex;
      position: relative;
      z-index: 1;
      backdrop-filter: blur(10px);
    }
    
    .login-left {
      flex: 1;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 60px 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      color: white;
      position: relative;
    }
    
    .login-left::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="80" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
      opacity: 0.3;
    }
    
    .login-left h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 20px;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      position: relative;
    }
    
    .login-left p {
      font-size: 1.1rem;
      line-height: 1.6;
      opacity: 0.9;
      position: relative;
    }
    
    .login-right {
      flex: 1;
      padding: 60px 50px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    .login-header h2 {
      color: #2d3748;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .login-header p {
      color: #718096;
      font-size: 1rem;
    }
    
    .login-form {
      width: 100%;
    }
    
    .form-group {
      margin-bottom: 24px;
      position: relative;
    }
    
    .form-label {
      display: block;
      color: #4a5568;
      font-weight: 500;
      margin-bottom: 8px;
      font-size: 0.9rem;
    }
    
    .form-input {
      width: 100%;
      padding: 14px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background-color: #f7fafc;
    }
    
    .form-input:focus {
      outline: none;
      border-color: #667eea;
      background-color: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-input.error {
      border-color: #e53e3e;
      background-color: #fed7d7;
    }
    
    .form-input.success {
      border-color: #38a169;
      background-color: #c6f6d5;
    }
    
    .error-message {
      color: #e53e3e;
      font-size: 0.85rem;
      margin-top: 6px;
      display: none;
    }
    
    .error-message.show {
      display: block;
    }
    
    .password-toggle {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #718096;
      cursor: pointer;
      font-size: 1.1rem;
      padding: 4px;
    }
    
    .password-toggle:hover {
      color: #4a5568;
    }
    
    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      font-size: 0.9rem;
    }
    
    .remember-me {
      display: flex;
      align-items: center;
      color: #4a5568;
    }
    
    .remember-me input {
      margin-right: 8px;
      transform: scale(1.1);
    }
    
    .forgot-password {
      color: #667eea;
      text-decoration: none;
      transition: color 0.3s ease;
    }
    
    .forgot-password:hover {
      color: #5a67d8;
      text-decoration: underline;
    }
    
    .login-button {
      width: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 16px;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .login-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    
    .login-button:active {
      transform: translateY(0);
    }
    
    .login-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .login-button .loading {
      display: none;
    }
    
    .login-button.loading .loading {
      display: inline-block;
      animation: spin 1s linear infinite;
    }
    
    .login-button.loading .text {
      display: none;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .divider {
      text-align: center;
      margin: 30px 0;
      position: relative;
      color: #a0aec0;
      font-size: 0.9rem;
    }
    
    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e2e8f0;
    }
    
    .divider span {
      background: white;
      padding: 0 20px;
      position: relative;
    }
    
    .social-login {
      display: flex;
      gap: 12px;
      margin-bottom: 30px;
    }
    
    .social-button {
      flex: 1;
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      background: white;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
      color: #4a5568;
    }
    
    .social-button:hover {
      border-color: #cbd5e0;
      background: #f7fafc;
    }
    
    .register-link {
      text-align: center;
      color: #718096;
      font-size: 0.9rem;
    }
    
    .register-link a {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }
    
    .register-link a:hover {
      text-decoration: underline;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .login-container {
        flex-direction: column;
        max-width: 400px;
      }
      
      .login-left {
        padding: 40px 30px;
      }
      
      .login-left h1 {
        font-size: 2rem;
      }
      
      .login-right {
        padding: 40px 30px;
      }
      
      .login-header h2 {
        font-size: 1.6rem;
      }
      
      .social-login {
        flex-direction: column;
      }
    }
    
    @media (max-width: 480px) {
      body {
        padding: 10px;
      }
      
      .login-left, .login-right {
        padding: 30px 20px;
      }
    }
    
    /* 安全提示 */
    .security-notice {
      background: #edf2f7;
      border-left: 4px solid #667eea;
      padding: 12px 16px;
      margin-top: 20px;
      border-radius: 0 8px 8px 0;
      font-size: 0.85rem;
      color: #4a5568;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <!-- 左侧品牌区域 -->
    <div class="login-left">
      <h1>管理系统</h1>
      <p>安全、高效、专业的后台管理平台<br>为您的业务提供全面的数据管理解决方案</p>
    </div>
    
    <!-- 右侧登录表单 -->
    <div class="login-right">
      <div class="login-header">
        <h2>欢迎回来</h2>
        <p>请登录您的账户</p>
      </div>
      
      <form class="login-form" id="loginForm">
        <!-- 用户名输入 -->
        <div class="form-group">
          <label class="form-label" for="username">用户名</label>
          <input 
            type="text" 
            id="username" 
            name="username" 
            class="form-input" 
            placeholder="请输入用户名"
            autocomplete="username"
            required
          >
          <div class="error-message" id="usernameError"></div>
        </div>
        
        <!-- 密码输入 -->
        <div class="form-group" style="position: relative;">
          <label class="form-label" for="password">密码</label>
          <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-input" 
            placeholder="请输入密码"
            autocomplete="current-password"
            required
          >
          <button type="button" class="password-toggle" id="passwordToggle">👁️</button>
          <div class="error-message" id="passwordError"></div>
        </div>
        
        <!-- 选项 -->
        <div class="form-options">
          <label class="remember-me">
            <input type="checkbox" id="rememberMe" name="rememberMe">
            记住我
          </label>
          <a href="#" class="forgot-password" id="forgotPassword">忘记密码？</a>
        </div>
        
        <!-- 登录按钮 -->
        <button type="submit" class="login-button" id="loginButton">
          <span class="loading">⏳</span>
          <span class="text">登录</span>
        </button>
        
        <!-- 分割线 -->
        <div class="divider">
          <span>或者</span>
        </div>
        
        <!-- 第三方登录 -->
        <div class="social-login">
          <button type="button" class="social-button" id="wechatLogin">
            💬 微信登录
          </button>
          <button type="button" class="social-button" id="qqLogin">
            🐧 QQ登录
          </button>
        </div>
        
        <!-- 注册链接 -->
        <div class="register-link">
          还没有账户？ <a href="#" id="registerLink">立即注册</a>
        </div>
        
        <!-- 安全提示 -->
        <div class="security-notice">
          🔒 为了您的账户安全，请不要在公共场所登录，并定期更换密码。
        </div>
      </form>
    </div>
  </div>

  <script>
    // 表单验证和交互逻辑
    class LoginForm {
      constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.passwordToggle = document.getElementById('passwordToggle');
        this.loginButton = document.getElementById('loginButton');
        this.rememberMe = document.getElementById('rememberMe');
        
        this.initEventListeners();
        this.loadRememberedCredentials();
      }
      
      initEventListeners() {
        // 表单提交
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // 密码显示/隐藏
        this.passwordToggle.addEventListener('click', () => this.togglePassword());
        
        // 实时验证
        this.usernameInput.addEventListener('blur', () => this.validateUsername());
        this.passwordInput.addEventListener('blur', () => this.validatePassword());
        this.usernameInput.addEventListener('input', () => this.clearError('username'));
        this.passwordInput.addEventListener('input', () => this.clearError('password'));
        
        // 第三方登录
        document.getElementById('wechatLogin').addEventListener('click', () => this.handleSocialLogin('wechat'));
        document.getElementById('qqLogin').addEventListener('click', () => this.handleSocialLogin('qq'));
        
        // 忘记密码
        document.getElementById('forgotPassword').addEventListener('click', (e) => {
          e.preventDefault();
          this.handleForgotPassword();
        });
        
        // 注册链接
        document.getElementById('registerLink').addEventListener('click', (e) => {
          e.preventDefault();
          this.handleRegister();
        });
      }
      
      validateUsername() {
        const username = this.usernameInput.value.trim();
        const errorElement = document.getElementById('usernameError');
        
        if (!username) {
          this.showError('username', '请输入用户名');
          return false;
        }
        
        if (username.length < 3) {
          this.showError('username', '用户名至少需要3个字符');
          return false;
        }
        
        if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
          this.showError('username', '用户名只能包含字母、数字、下划线和中文');
          return false;
        }
        
        this.showSuccess('username');
        return true;
      }
      
      validatePassword() {
        const password = this.passwordInput.value;
        const errorElement = document.getElementById('passwordError');
        
        if (!password) {
          this.showError('password', '请输入密码');
          return false;
        }
        
        if (password.length < 6) {
          this.showError('password', '密码至少需要6个字符');
          return false;
        }
        
        if (password.length > 20) {
          this.showError('password', '密码不能超过20个字符');
          return false;
        }
        
        this.showSuccess('password');
        return true;
      }
      
      showError(field, message) {
        const input = document.getElementById(field);
        const errorElement = document.getElementById(field + 'Error');
        
        input.classList.add('error');
        input.classList.remove('success');
        errorElement.textContent = message;
        errorElement.classList.add('show');
      }
      
      showSuccess(field) {
        const input = document.getElementById(field);
        const errorElement = document.getElementById(field + 'Error');
        
        input.classList.remove('error');
        input.classList.add('success');
        errorElement.classList.remove('show');
      }
      
      clearError(field) {
        const input = document.getElementById(field);
        const errorElement = document.getElementById(field + 'Error');
        
        input.classList.remove('error');
        errorElement.classList.remove('show');
      }
      
      togglePassword() {
        const type = this.passwordInput.type === 'password' ? 'text' : 'password';
        this.passwordInput.type = type;
        this.passwordToggle.textContent = type === 'password' ? '👁️' : '🙈';
      }
      
      async handleSubmit(e) {
        e.preventDefault();
        
        // 验证表单
        const isUsernameValid = this.validateUsername();
        const isPasswordValid = this.validatePassword();
        
        if (!isUsernameValid || !isPasswordValid) {
          return;
        }
        
        // 显示加载状态
        this.setLoading(true);
        
        try {
          // 模拟登录请求
          const formData = new FormData(this.form);
          const loginData = {
            username: formData.get('username'),
            password: formData.get('password'),
            rememberMe: formData.get('rememberMe') === 'on'
          };
          
          // 这里应该是实际的登录API调用
          const result = await this.simulateLogin(loginData);
          
          if (result.success) {
            // 保存登录状态
            if (loginData.rememberMe) {
              this.saveCredentials(loginData.username);
            } else {
              this.clearSavedCredentials();
            }
            
            // 登录成功
            this.handleLoginSuccess(result);
          } else {
            // 登录失败
            this.handleLoginError(result.message);
          }
        } catch (error) {
          this.handleLoginError('网络错误，请稍后重试');
        } finally {
          this.setLoading(false);
        }
      }
      
      async simulateLogin(loginData) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 模拟登录验证
        if (loginData.username === 'admin' && loginData.password === '123456') {
          return {
            success: true,
            token: 'mock_jwt_token_' + Date.now(),
            user: {
              id: 1,
              username: loginData.username,
              nickname: '管理员',
              avatar: '',
              role: 'admin'
            }
          };
        } else {
          return {
            success: false,
            message: '用户名或密码错误'
          };
        }
      }
      
      handleLoginSuccess(result) {
        // 保存用户信息和token
        localStorage.setItem('userToken', result.token);
        localStorage.setItem('userInfo', JSON.stringify(result.user));
        
        // 显示成功消息
        this.showSuccessMessage('登录成功，正在跳转...');
        
        // 跳转到仪表盘
        setTimeout(() => {
          window.location.href = '/admin/dashboard';
        }, 1000);
      }
      
      handleLoginError(message) {
        this.showError('password', message);
        
        // 清空密码
        this.passwordInput.value = '';
        this.passwordInput.focus();
      }
      
      setLoading(loading) {
        this.loginButton.disabled = loading;
        if (loading) {
          this.loginButton.classList.add('loading');
        } else {
          this.loginButton.classList.remove('loading');
        }
      }
      
      showSuccessMessage(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: #48bb78;
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          z-index: 1000;
          animation: slideIn 0.3s ease;
        `;
        successDiv.textContent = message;
        document.body.appendChild(successDiv);
        
        setTimeout(() => {
          successDiv.remove();
        }, 3000);
      }
      
      saveCredentials(username) {
        localStorage.setItem('rememberedUsername', username);
      }
      
      clearSavedCredentials() {
        localStorage.removeItem('rememberedUsername');
      }
      
      loadRememberedCredentials() {
        const rememberedUsername = localStorage.getItem('rememberedUsername');
        if (rememberedUsername) {
          this.usernameInput.value = rememberedUsername;
          this.rememberMe.checked = true;
        }
      }
      
      handleSocialLogin(provider) {
        alert(`${provider === 'wechat' ? '微信' : 'QQ'}登录功能开发中...`);
      }
      
      handleForgotPassword() {
        alert('忘记密码功能开发中...\n请联系管理员重置密码。');
      }
      
      handleRegister() {
        alert('注册功能开发中...\n请联系管理员创建账户。');
      }
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', () => {
      new LoginForm();
      
      // 添加CSS动画
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `;
      document.head.appendChild(style);
    });
  </script>
</body>
</html>