<?php

// 简单测试防红系统控制器
echo "=== 防红系统控制器测试 ===\n";

// 检查控制器文件是否存在
$controllerPath = 'app/Http/Controllers/Api/Admin/AntiBlockController.php';
if (file_exists($controllerPath)) {
    echo "✓ 控制器文件存在: {$controllerPath}\n";
    
    // 检查文件内容
    $content = file_get_contents($controllerPath);
    
    // 检查关键方法是否存在
    $methods = [
        'getStats',
        'overview', 
        'index',
        'store',
        'show',
        'update',
        'destroy',
        'batchDeleteDomains',
        'getShortLinks',
        'createShortLink',
        'deleteShortLink',
        'getAccessStats',
        'getAccessLogs',
        'getClickTrends',
        'getRegionStats',
        'getPlatformStats',
        'generateQRCode'
    ];
    
    foreach ($methods as $method) {
        if (strpos($content, "function {$method}") !== false) {
            echo "✓ 方法存在: {$method}\n";
        } else {
            echo "✗ 方法缺失: {$method}\n";
        }
    }
    
} else {
    echo "✗ 控制器文件不存在: {$controllerPath}\n";
}

// 检查服务文件
$servicePath = 'app/Services/AntiBlockService.php';
if (file_exists($servicePath)) {
    echo "✓ 服务文件存在: {$servicePath}\n";
} else {
    echo "✗ 服务文件不存在: {$servicePath}\n";
}

// 检查模型文件
$models = [
    'app/Models/DomainPool.php',
    'app/Models/ShortLink.php', 
    'app/Models/LinkAccessLog.php',
    'app/Models/DomainCheckLog.php'
];

foreach ($models as $model) {
    if (file_exists($model)) {
        echo "✓ 模型文件存在: {$model}\n";
    } else {
        echo "✗ 模型文件不存在: {$model}\n";
    }
}

echo "\n测试完成！\n";