import{_ as e}from"./index-D2bI4m-v.js";/* empty css               *//* empty css                  *//* empty css                     *//* empty css                        *//* empty css                *//* empty css                    *//* empty css                    *//* empty css               */import{T as a,aM as l,as as s,bi as t,b2 as i,ao as o,U as d,aj as r,ai as n,a0 as u,b4 as c,b9 as m,b5 as p,bh as _,b3 as v,bc as b,bd as f,be as h,aH as w,aW as g,aV as V,b8 as x,Q as y,R as j}from"./element-plus-DcSKpKA8.js";import{r as k,L as U,e as C,k as z,l as T,t as E,E as O,z as A,D as B,u as S,F as N,Y as P,y as $}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const D={class:"substation-permissions"},I={class:"page-header"},M={class:"header-actions"},W={class:"overview-card"},Y={class:"card-icon user-icon"},q={class:"card-content"},F={class:"card-value"},H={class:"overview-card"},L={class:"card-icon agent-icon"},Q={class:"card-content"},R={class:"card-value"},G={class:"overview-card"},J={class:"card-icon finance-icon"},K={class:"card-content"},X={class:"card-value"},Z={class:"overview-card"},ee={class:"card-icon system-icon"},ae={class:"card-content"},le={class:"card-value"},se={class:"card-header"},te={class:"header-controls"},ie={class:"permission-module"},oe={class:"module-header"},de={class:"module-description"},re={class:"permission-grid"},ne={class:"permission-description"},ue={class:"permission-preview"},ce={class:"preview-permissions"},me=e({__name:"SubstationPermissions",setup(e){const me=k(!1),pe=k("user"),_e=k({user:{label:"用户管理",description:"管理分站用户的创建、编辑、删除等操作权限",permissions:{view:"查看用户",create:"创建用户",edit:"编辑用户",delete:"删除用户",export:"导出用户数据",batch_operation:"批量操作",reset_password:"重置密码",adjust_balance:"调整余额"}},agent:{label:"代理商管理",description:"管理代理商的审核、等级调整、佣金设置等权限",permissions:{view:"查看代理商",create:"创建代理商",edit:"编辑代理商",delete:"删除代理商",approve:"审核代理商",commission_adjust:"调整佣金",level_manage:"等级管理",renew:"续费管理"}},group:{label:"群组管理",description:"管理群组的创建、审核、模板等权限",permissions:{view:"查看群组",create:"创建群组",edit:"编辑群组",delete:"删除群组",audit:"审核群组",template_manage:"模板管理",batch_operation:"批量操作"}},order:{label:"订单管理",description:"管理订单查看、退款、统计等权限",permissions:{view:"查看订单",edit:"编辑订单",refund:"订单退款",export:"导出订单",statistics:"订单统计"}},finance:{label:"财务管理",description:"管理财务查看、结算、报表等权限",permissions:{view:"查看财务",settle:"结算佣金",withdraw_approve:"提现审批",report:"财务报表",export:"导出财务数据"}},system:{label:"系统管理",description:"管理系统设置、监控、备份等权限",permissions:{settings:"系统设置",monitor:"系统监控",logs:"日志查看",backup:"数据备份"}}}),ve=U({user:{view:!0,create:!0,edit:!0,delete:!1,export:!0,batch_operation:!0,reset_password:!0,adjust_balance:!1},agent:{view:!0,create:!0,edit:!0,delete:!1,approve:!0,commission_adjust:!1,level_manage:!1,renew:!0},group:{view:!0,create:!0,edit:!0,delete:!0,audit:!0,template_manage:!0,batch_operation:!0},order:{view:!0,edit:!0,refund:!0,export:!0,statistics:!0},finance:{view:!0,settle:!1,withdraw_approve:!1,report:!0,export:!1},system:{settings:!1,monitor:!0,logs:!0,backup:!1}}),be=U({max_users:1e3,max_agents:100,max_groups:500,max_daily_orders:1e3,max_monthly_revenue:1e5,storage_limit:1024,api_rate_limit:1e3}),fe=U({substation_name:"",contact_email:"",service_phone:"",work_hours:"9:00-18:00",timezone:"Asia/Shanghai",language:"zh-CN"}),he=e=>{const a=ve[e];return a?Object.values(a).filter(Boolean).length:0},we=async()=>{try{y.success("权限配置加载成功")}catch(e){y.error("加载权限配置失败")}},ge=async()=>{try{await j.confirm("确定要保存当前权限配置吗？保存后将立即生效。","确认保存",{confirmButtonText:"确定保存",cancelButtonText:"取消",type:"warning"}),me.value=!0,await new Promise(e=>setTimeout(e,1e3)),y.success("权限配置保存成功")}catch(e){"cancel"!==e&&y.error("保存权限配置失败")}finally{me.value=!1}},Ve=()=>{Object.keys(ve).forEach(e=>{Object.keys(ve[e]).forEach(a=>{ve[e][a]=!0})}),y.success("已选择所有权限")},xe=()=>{Object.keys(ve).forEach(e=>{Object.keys(ve[e]).forEach(a=>{ve[e][a]=!1})}),y.success("已清空所有权限")},ye=()=>{Object.assign(ve,{user:{view:!0,create:!0,edit:!0,delete:!1,export:!0,batch_operation:!0,reset_password:!0,adjust_balance:!1},agent:{view:!0,create:!0,edit:!0,delete:!1,approve:!0,commission_adjust:!1,level_manage:!1,renew:!0},group:{view:!0,create:!0,edit:!0,delete:!0,audit:!0,template_manage:!0,batch_operation:!0},order:{view:!0,edit:!0,refund:!0,export:!0,statistics:!0},finance:{view:!0,settle:!1,withdraw_approve:!1,report:!0,export:!1},system:{settings:!1,monitor:!0,logs:!0,backup:!1}}),y.success("已恢复默认权限配置")},je=(e,a)=>({user:{view:"查看用户列表和详细信息",create:"创建新用户账户",edit:"修改用户基本信息",delete:"删除用户账户",export:"导出用户数据到Excel",batch_operation:"批量操作用户",reset_password:"重置用户密码",adjust_balance:"调整用户账户余额"},agent:{view:"查看代理商列表和信息",create:"创建新代理商",edit:"编辑代理商信息",delete:"删除代理商账户",approve:"审核代理商申请",commission_adjust:"调整代理商佣金比例",level_manage:"管理代理商等级",renew:"处理代理商续费"}}[e]?.[a]||"暂无描述");return C(()=>{we()}),(e,y)=>{const j=a,k=s,U=i,C=c,ke=_,Ue=p,Ce=m,ze=v,Te=h,Ee=f,Oe=b,Ae=w,Be=V,Se=g,Ne=x;return T(),z("div",D,[E("div",I,[y[16]||(y[16]=E("h2",null,"分站权限配置",-1)),E("div",M,[O(k,{onClick:we},{default:A(()=>[O(j,null,{default:A(()=>[O(S(l))]),_:1}),y[14]||(y[14]=B(" 刷新配置 ",-1))]),_:1,__:[14]}),O(k,{type:"primary",onClick:ge,loading:me.value},{default:A(()=>[O(j,null,{default:A(()=>[O(S(t))]),_:1}),y[15]||(y[15]=B(" 保存配置 ",-1))]),_:1,__:[15]},8,["loading"])])]),O(C,{gutter:20,class:"overview-row"},{default:A(()=>[O(U,{span:6},{default:A(()=>[E("div",W,[E("div",Y,[O(j,null,{default:A(()=>[O(S(o))]),_:1})]),E("div",q,[y[17]||(y[17]=E("div",{class:"card-title"},"用户管理权限",-1)),E("div",F,d(he("user"))+"/8",1)])])]),_:1}),O(U,{span:6},{default:A(()=>[E("div",H,[E("div",L,[O(j,null,{default:A(()=>[O(S(r))]),_:1})]),E("div",Q,[y[18]||(y[18]=E("div",{class:"card-title"},"代理商管理权限",-1)),E("div",R,d(he("agent"))+"/8",1)])])]),_:1}),O(U,{span:6},{default:A(()=>[E("div",G,[E("div",J,[O(j,null,{default:A(()=>[O(S(n))]),_:1})]),E("div",K,[y[19]||(y[19]=E("div",{class:"card-title"},"财务管理权限",-1)),E("div",X,d(he("finance"))+"/5",1)])])]),_:1}),O(U,{span:6},{default:A(()=>[E("div",Z,[E("div",ee,[O(j,null,{default:A(()=>[O(S(u))]),_:1})]),E("div",ae,[y[20]||(y[20]=E("div",{class:"card-title"},"系统管理权限",-1)),E("div",le,d(he("system"))+"/4",1)])])]),_:1})]),_:1}),O(ze,{class:"permissions-card"},{header:A(()=>[E("div",se,[y[24]||(y[24]=E("span",null,"详细权限配置",-1)),E("div",te,[O(k,{size:"small",onClick:Ve},{default:A(()=>y[21]||(y[21]=[B("全选",-1)])),_:1,__:[21]}),O(k,{size:"small",onClick:xe},{default:A(()=>y[22]||(y[22]=[B("清空",-1)])),_:1,__:[22]}),O(k,{size:"small",onClick:ye},{default:A(()=>y[23]||(y[23]=[B("恢复默认",-1)])),_:1,__:[23]})])])]),default:A(()=>[O(Ce,{modelValue:pe.value,"onUpdate:modelValue":y[0]||(y[0]=e=>pe.value=e),class:"permission-tabs"},{default:A(()=>[(T(!0),z(N,null,P(_e.value,(e,a)=>(T(),$(Ue,{key:a,label:e.label,name:a},{default:A(()=>[E("div",ie,[E("div",oe,[E("h3",null,d(e.label),1),E("p",de,d(e.description),1)]),E("div",re,[(T(!0),z(N,null,P(e.permissions,(e,l)=>(T(),z("div",{key:l,class:"permission-item"},[O(ke,{modelValue:ve[a][l],"onUpdate:modelValue":e=>ve[a][l]=e,label:e,onChange:e=>((e,a)=>{console.log(`权限变更: ${e}.${a} = ${ve[e][a]}`)})(a,l)},null,8,["modelValue","onUpdate:modelValue","label","onChange"]),E("div",ne,d(je(a,l)),1)]))),128))])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1}),O(ze,{class:"limits-card"},{header:A(()=>y[25]||(y[25]=[E("span",null,"功能限制配置",-1)])),default:A(()=>[O(C,{gutter:20},{default:A(()=>[O(U,{span:12},{default:A(()=>[O(Oe,{model:be,"label-width":"120px"},{default:A(()=>[O(Ee,{label:"最大用户数"},{default:A(()=>[O(Te,{modelValue:be.max_users,"onUpdate:modelValue":y[1]||(y[1]=e=>be.max_users=e),min:1,max:1e4,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),O(Ee,{label:"最大代理商数"},{default:A(()=>[O(Te,{modelValue:be.max_agents,"onUpdate:modelValue":y[2]||(y[2]=e=>be.max_agents=e),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),O(Ee,{label:"最大群组数"},{default:A(()=>[O(Te,{modelValue:be.max_groups,"onUpdate:modelValue":y[3]||(y[3]=e=>be.max_groups=e),min:1,max:5e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),O(Ee,{label:"日订单限制"},{default:A(()=>[O(Te,{modelValue:be.max_daily_orders,"onUpdate:modelValue":y[4]||(y[4]=e=>be.max_daily_orders=e),min:1,max:5e4,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),O(U,{span:12},{default:A(()=>[O(Oe,{model:be,"label-width":"120px"},{default:A(()=>[O(Ee,{label:"月收入限制"},{default:A(()=>[O(Te,{modelValue:be.max_monthly_revenue,"onUpdate:modelValue":y[5]||(y[5]=e=>be.max_monthly_revenue=e),min:1e3,max:1e7,precision:2,style:{width:"100%"}},null,8,["modelValue"]),y[26]||(y[26]=E("div",{class:"form-item-tip"},"单位：元",-1))]),_:1,__:[26]}),O(Ee,{label:"存储限制"},{default:A(()=>[O(Te,{modelValue:be.storage_limit,"onUpdate:modelValue":y[6]||(y[6]=e=>be.storage_limit=e),min:100,max:102400,style:{width:"100%"}},null,8,["modelValue"]),y[27]||(y[27]=E("div",{class:"form-item-tip"},"单位：MB",-1))]),_:1,__:[27]}),O(Ee,{label:"API调用限制"},{default:A(()=>[O(Te,{modelValue:be.api_rate_limit,"onUpdate:modelValue":y[7]||(y[7]=e=>be.api_rate_limit=e),min:100,max:1e5,style:{width:"100%"}},null,8,["modelValue"]),y[28]||(y[28]=E("div",{class:"form-item-tip"},"单位：次/小时",-1))]),_:1,__:[28]})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1}),O(ze,{class:"custom-settings-card"},{header:A(()=>y[29]||(y[29]=[E("span",null,"自定义设置",-1)])),default:A(()=>[O(Oe,{model:fe,"label-width":"120px"},{default:A(()=>[O(C,{gutter:20},{default:A(()=>[O(U,{span:12},{default:A(()=>[O(Ee,{label:"分站名称"},{default:A(()=>[O(Ae,{modelValue:fe.substation_name,"onUpdate:modelValue":y[8]||(y[8]=e=>fe.substation_name=e),placeholder:"请输入分站名称"},null,8,["modelValue"])]),_:1}),O(Ee,{label:"联系邮箱"},{default:A(()=>[O(Ae,{modelValue:fe.contact_email,"onUpdate:modelValue":y[9]||(y[9]=e=>fe.contact_email=e),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1}),O(Ee,{label:"客服电话"},{default:A(()=>[O(Ae,{modelValue:fe.service_phone,"onUpdate:modelValue":y[10]||(y[10]=e=>fe.service_phone=e),placeholder:"请输入客服电话"},null,8,["modelValue"])]),_:1})]),_:1}),O(U,{span:12},{default:A(()=>[O(Ee,{label:"工作时间"},{default:A(()=>[O(Ae,{modelValue:fe.work_hours,"onUpdate:modelValue":y[11]||(y[11]=e=>fe.work_hours=e),placeholder:"例如：9:00-18:00"},null,8,["modelValue"])]),_:1}),O(Ee,{label:"时区设置"},{default:A(()=>[O(Se,{modelValue:fe.timezone,"onUpdate:modelValue":y[12]||(y[12]=e=>fe.timezone=e),placeholder:"请选择时区"},{default:A(()=>[O(Be,{label:"北京时间 (UTC+8)",value:"Asia/Shanghai"}),O(Be,{label:"东京时间 (UTC+9)",value:"Asia/Tokyo"}),O(Be,{label:"纽约时间 (UTC-5)",value:"America/New_York"})]),_:1},8,["modelValue"])]),_:1}),O(Ee,{label:"语言设置"},{default:A(()=>[O(Se,{modelValue:fe.language,"onUpdate:modelValue":y[13]||(y[13]=e=>fe.language=e),placeholder:"请选择语言"},{default:A(()=>[O(Be,{label:"简体中文",value:"zh-CN"}),O(Be,{label:"繁体中文",value:"zh-TW"}),O(Be,{label:"English",value:"en-US"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),O(ze,{class:"preview-card"},{header:A(()=>y[30]||(y[30]=[E("span",null,"权限配置预览",-1)])),default:A(()=>[E("div",ue,[(T(!0),z(N,null,P(ve,(e,a)=>(T(),z("div",{class:"preview-section",key:a},[E("h4",null,d(_e.value[a]?.label),1),E("div",ce,[(T(!0),z(N,null,P(e,(e,l)=>(T(),$(Ne,{key:l,type:e?"success":"info",size:"small",class:"permission-tag"},{default:A(()=>[B(d(_e.value[a]?.permissions[l]),1)]),_:2},1032,["type"]))),128))])]))),128))])]),_:1})])}}},[["__scopeId","data-v-0f348e6d"]]);export{me as default};
