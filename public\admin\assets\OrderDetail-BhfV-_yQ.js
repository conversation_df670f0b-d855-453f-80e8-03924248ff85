import{_ as a}from"./index-D2bI4m-v.js";/* empty css                         *//* empty css                        *//* empty css                    *//* empty css               *//* empty css                *//* empty css               */import{ag as e,ah as l,r as t,L as s,e as n,y as r,l as d,z as i,k as o,B as u,E as c,t as p,D as m,F as f,Y as _,u as h}from"./vue-vendor-DGsK9sC4.js";import{b3 as v,b4 as b,b2 as g,U as w,b8 as y,b6 as k,b7 as j,bb as A,ba as x,as as T,T as N,aS as P,bV as q,Q as z}from"./element-plus-DcSKpKA8.js";import{P as D}from"./PageLayout-OFR6SHfu.js";import"./utils-4VKArNEK.js";const V={key:0,class:"order-detail"},Z={class:"card-header"},C={class:"info-item"},I={class:"info-item"},L={class:"amount"},M={class:"info-item"},O={class:"info-item"},S={class:"info-item"},E={class:"info-item"},F={class:"info-item"},U={class:"info-item"},$={class:"info-item"},B={class:"log-content"},K={class:"log-action"},Q={class:"log-operator"},R={key:0,class:"log-remark"},Y=a({__name:"OrderDetail",setup(a){e();const Y=l(),G=t(!0),H=s({id:"",orderNo:"",amount:0,status:"",paymentMethod:"",createdAt:"",paidAt:"",completedAt:"",user:{username:"",phone:"",email:""},items:[],logs:[]}),J=a=>({pending:"待支付",paid:"已支付",completed:"已完成",cancelled:"已取消",refunded:"已退款"}[a]||"未知"),W=a=>a?new Date(a).toLocaleString("zh-CN"):"-",X=()=>{window.print()};return n(()=>{(async()=>{try{G.value=!0,await new Promise(a=>setTimeout(a,1e3)),Object.assign(H,{id:Y.params.id,orderNo:"ORD202408020001",amount:99,status:"paid",paymentMethod:"wechat",createdAt:"2024-08-02T10:30:00Z",paidAt:"2024-08-02T10:35:00Z",completedAt:null,user:{username:"张三",phone:"13800138000",email:"<EMAIL>"},items:[{id:1,name:"VIP群组入群费",sku:"VIP-001",price:99,quantity:1}],logs:[{id:1,action:"订单创建",operator:"系统",createdAt:"2024-08-02T10:30:00Z",remark:"用户下单成功"},{id:2,action:"支付成功",operator:"系统",createdAt:"2024-08-02T10:35:00Z",remark:"微信支付成功"}]})}catch(a){z.error("加载订单详情失败"),console.error(a)}finally{G.value=!1}})()}),(a,e)=>{const l=N,t=T,s=y,n=g,z=b,Y=v,aa=j,ea=k,la=x,ta=A;return d(),r(D,{title:`订单详情 #${H.orderNo||""}`,subtitle:"查看订单详细信息",icon:"View",loading:G.value},{actions:i(()=>[c(t,{onClick:e[0]||(e[0]=e=>a.$router.go(-1))},{default:i(()=>[c(l,null,{default:i(()=>[c(h(P))]),_:1}),e[1]||(e[1]=m(" 返回 ",-1))]),_:1,__:[1]}),c(t,{type:"primary",onClick:X},{default:i(()=>[c(l,null,{default:i(()=>[c(h(q))]),_:1}),e[2]||(e[2]=m(" 打印订单 ",-1))]),_:1,__:[2]})]),default:i(()=>[G.value?u("",!0):(d(),o("div",V,[c(Y,{class:"info-card",shadow:"never"},{header:i(()=>{return[p("div",Z,[e[3]||(e[3]=p("h3",null,"订单信息",-1)),c(s,{type:(a=H.status,{pending:"warning",paid:"success",completed:"success",cancelled:"danger",refunded:"info"}[a]||""),size:"large"},{default:i(()=>[m(w(J(H.status)),1)]),_:1},8,["type"])])];var a}),default:i(()=>[c(z,{gutter:24},{default:i(()=>[c(n,{span:8},{default:i(()=>[p("div",C,[e[4]||(e[4]=p("label",null,"订单号：",-1)),p("span",null,w(H.orderNo),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",I,[e[5]||(e[5]=p("label",null,"订单金额：",-1)),p("span",L,"¥"+w(H.amount),1)])]),_:1}),c(n,{span:8},{default:i(()=>{return[p("div",M,[e[6]||(e[6]=p("label",null,"支付方式：",-1)),p("span",null,w((a=H.paymentMethod,{wechat:"微信支付",alipay:"支付宝",bank:"银行卡",balance:"余额支付"}[a]||"未知")),1)])];var a}),_:1})]),_:1}),c(z,{gutter:24},{default:i(()=>[c(n,{span:8},{default:i(()=>[p("div",O,[e[7]||(e[7]=p("label",null,"创建时间：",-1)),p("span",null,w(W(H.createdAt)),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",S,[e[8]||(e[8]=p("label",null,"支付时间：",-1)),p("span",null,w(W(H.paidAt)),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",E,[e[9]||(e[9]=p("label",null,"完成时间：",-1)),p("span",null,w(W(H.completedAt)),1)])]),_:1})]),_:1})]),_:1}),c(Y,{class:"user-card",shadow:"never"},{header:i(()=>e[10]||(e[10]=[p("div",{class:"card-header"},[p("h3",null,"用户信息")],-1)])),default:i(()=>[c(z,{gutter:24},{default:i(()=>[c(n,{span:8},{default:i(()=>[p("div",F,[e[11]||(e[11]=p("label",null,"用户名：",-1)),p("span",null,w(H.user.username),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",U,[e[12]||(e[12]=p("label",null,"手机号：",-1)),p("span",null,w(H.user.phone),1)])]),_:1}),c(n,{span:8},{default:i(()=>[p("div",$,[e[13]||(e[13]=p("label",null,"邮箱：",-1)),p("span",null,w(H.user.email),1)])]),_:1})]),_:1})]),_:1}),c(Y,{class:"items-card",shadow:"never"},{header:i(()=>e[14]||(e[14]=[p("div",{class:"card-header"},[p("h3",null,"商品信息")],-1)])),default:i(()=>[c(ea,{data:H.items,style:{width:"100%"}},{default:i(()=>[c(aa,{prop:"name",label:"商品名称"}),c(aa,{prop:"sku",label:"SKU"}),c(aa,{prop:"price",label:"单价",width:"120"},{default:i(({row:a})=>[m(" ¥"+w(a.price),1)]),_:1}),c(aa,{prop:"quantity",label:"数量",width:"80"}),c(aa,{label:"小计",width:"120"},{default:i(({row:a})=>[m(" ¥"+w((a.price*a.quantity).toFixed(2)),1)]),_:1})]),_:1},8,["data"])]),_:1}),c(Y,{class:"logs-card",shadow:"never"},{header:i(()=>e[15]||(e[15]=[p("div",{class:"card-header"},[p("h3",null,"操作记录")],-1)])),default:i(()=>[c(ta,null,{default:i(()=>[(d(!0),o(f,null,_(H.logs,a=>(d(),r(la,{key:a.id,timestamp:W(a.createdAt),placement:"top"},{default:i(()=>[p("div",B,[p("div",K,w(a.action),1),p("div",Q,"操作人："+w(a.operator),1),a.remark?(d(),o("div",R,w(a.remark),1)):u("",!0)])]),_:2},1032,["timestamp"]))),128))]),_:1})]),_:1})]))]),_:1},8,["title","loading"])}}},[["__scopeId","data-v-af268548"]]);export{Y as default};
