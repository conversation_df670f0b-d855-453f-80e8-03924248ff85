<?php

// 简单测试API端点
echo "=== 测试防红系统API端点 ===\n";

// 测试本地API
$apiUrl = 'http://localhost:8000/api/v1/admin/anti-block/stats';

echo "测试URL: {$apiUrl}\n";

// 使用cURL测试
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: Bearer test-token' // 模拟token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "✗ cURL错误: {$error}\n";
} else {
    echo "✓ HTTP状态码: {$httpCode}\n";
    echo "响应内容: {$response}\n";
}

echo "\n测试完成！\n";