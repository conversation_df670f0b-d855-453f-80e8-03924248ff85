<template>
  <div class="app-container">
    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="搜索角色名称、描述"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
        clearable
      />
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="全部" value="" />
        <el-option label="启用" value="active" />
        <el-option label="禁用" value="inactive" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="Search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" type="success" icon="Plus" @click="handleCreate">
        创建角色
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <stat-card
          type="primary"
          :icon="UserFilled"
          :value="stats.total_roles"
          label="总角色数"
          :trend="{ type: 'up', value: '+2', desc: '较上月' }"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="success"
          :icon="Check"
          :value="stats.active_roles"
          label="启用角色"
          :trend="{ type: 'flat', value: '0', desc: '较上月' }"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="warning"
          :icon="Key"
          :value="stats.total_permissions"
          label="总权限数"
          :trend="{ type: 'up', value: '+5', desc: '较上月' }"
        />
      </el-col>
      <el-col :span="6">
        <stat-card
          type="danger"
          :icon="User"
          :value="stats.users_with_roles"
          label="已分配用户"
          :trend="{ type: 'up', value: '+12', desc: '较上月' }"
        />
      </el-col>
    </el-row>

    <!-- 角色列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>角色列表</h3>
          <div>
            <el-button type="primary" size="small" @click="handleBatchOperation">批量操作</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="角色ID" prop="id" width="80" />
        <el-table-column label="角色名称" width="150">
          <template #default="{ row }">
            <div class="role-info">
              <el-icon class="role-icon" :style="{ color: getRoleColor(row.name) }">
                <component :is="getRoleIcon(row.name)" />
              </el-icon>
              <span class="role-name">{{ row.display_name || row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="角色标识" prop="name" width="120" />
        <el-table-column label="描述" prop="description" width="200" />
        <el-table-column label="权限数量" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ row.permissions_count || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="用户数量" width="100">
          <template #default="{ row }">
            <el-tag type="primary">{{ row.users_count || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handlePermissions(row)">
              权限配置
            </el-button>
            <el-button type="info" size="small" @click="handleUsers(row)">
              用户管理
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button type="warning" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`copy-${row.id}`">复制角色</el-dropdown-item>
                  <el-dropdown-item :command="`disable-${row.id}`" v-if="row.status === 'active'">禁用角色</el-dropdown-item>
                  <el-dropdown-item :command="`enable-${row.id}`" v-if="row.status === 'inactive'">启用角色</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${row.id}`" divided v-if="!row.is_system">删除角色</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 角色编辑对话框 -->
    <RoleDialog
      v-model="dialogVisible"
      :role-data="currentRole"
      @success="handleDialogSuccess"
    />

    <!-- 权限配置对话框 -->
    <PermissionConfigDialog
      v-model="permissionDialogVisible"
      :role-data="currentRole"
      @success="handlePermissionSuccess"
    />

    <!-- 用户管理对话框 -->
    <RoleUsersDialog
      v-model="usersDialogVisible"
      :role-data="currentRole"
      @success="handleUsersSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  UserFilled, 
  Check, 
  Key, 
  User, 
  Search, 
  Plus, 
  ArrowDown,
  Star,
  Setting
} from '@element-plus/icons-vue'
import StatCard from '@/components/StatCard.vue'
import RoleDialog from './components/RoleDialog.vue'
import PermissionConfigDialog from './components/PermissionConfigDialog.vue'
import RoleUsersDialog from './components/RoleUsersDialog.vue'
import { getRoleList, deleteRole, updateRoleStatus, getRoleStats } from '@/api/permission'
import { formatDate } from '@/utils/format'

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const dialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const usersDialogVisible = ref(false)
const currentRole = ref({})
const multipleSelection = ref([])

// 统计数据
const stats = ref({
  total_roles: 0,
  active_roles: 0,
  total_permissions: 0,
  users_with_roles: 0
})

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  status: ''
})

// 获取角色列表
const getList = async () => {
  listLoading.value = true
  try {
    const { data } = await getRoleList(listQuery)
    list.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
  } finally {
    listLoading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const { data } = await getRoleStats()
    stats.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 筛选
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 创建角色
const handleCreate = () => {
  currentRole.value = {}
  dialogVisible.value = true
}

// 编辑角色
const handleEdit = (row) => {
  currentRole.value = { ...row }
  dialogVisible.value = true
}

// 权限配置
const handlePermissions = (row) => {
  currentRole.value = { ...row }
  permissionDialogVisible.value = true
}

// 用户管理
const handleUsers = (row) => {
  currentRole.value = { ...row }
  usersDialogVisible.value = true
}

// 复制角色
const handleCopy = async (roleId) => {
  try {
    await ElMessageBox.confirm('确定要复制这个角色吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    ElMessage.success('角色复制成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('复制失败')
    }
  }
}

// 更新角色状态
const handleUpdateStatus = async (roleId, status) => {
  try {
    const action = status === 'inactive' ? '禁用' : '启用'
    await ElMessageBox.confirm(`确定要${action}这个角色吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await updateRoleStatus(roleId, status)
    ElMessage.success(`${action}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 删除角色
const handleDelete = async (roleId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个角色吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await deleteRole(roleId)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  const [action, roleId] = command.split('-')
  const id = parseInt(roleId)
  
  switch (action) {
    case 'copy':
      handleCopy(id)
      break
    case 'disable':
      handleUpdateStatus(id, 'inactive')
      break
    case 'enable':
      handleUpdateStatus(id, 'active')
      break
    case 'delete':
      handleDelete(id)
      break
  }
}

// 批量操作
const handleBatchOperation = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要操作的角色')
    return
  }
  
  ElMessageBox.confirm('请选择批量操作类型', '批量操作', {
    distinguishCancelAndClose: true,
    confirmButtonText: '批量启用',
    cancelButtonText: '批量禁用'
  }).then(() => {
    batchUpdateStatus('active')
  }).catch((action) => {
    if (action === 'cancel') {
      batchUpdateStatus('inactive')
    }
  })
}

// 批量更新状态
const batchUpdateStatus = async (status) => {
  try {
    const roleIds = multipleSelection.value.map(role => role.id)
    ElMessage.success('批量操作成功')
    getList()
  } catch (error) {
    ElMessage.error('批量操作失败')
  }
}

// 选择变化
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 分页
const handleSizeChange = (val) => {
  listQuery.limit = val
  getList()
}

const handleCurrentChange = (val) => {
  listQuery.page = val
  getList()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  getList()
  getStats()
}

const handlePermissionSuccess = () => {
  getList()
}

const handleUsersSuccess = () => {
  getList()
}

// 工具函数
const getRoleIcon = (roleName) => {
  const icons = {
    admin: UserFilled,
    substation: Key,
    distributor: Star,
    user: User
  }
  return icons[roleName] || Setting
}

const getRoleColor = (roleName) => {
  const colors = {
    admin: '#f5222d',
    substation: '#722ed1',
    distributor: '#faad14',
    user: '#52c41a'
  }
  return colors[roleName] || '#1890ff'
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    inactive: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '启用',
    inactive: '禁用'
  }
  return texts[status] || '未知'
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 24px;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .role-icon {
    font-size: 18px;
  }
  
  .role-name {
    font-weight: 600;
  }
}

.pagination-container {
  padding: 32px 16px;
  text-align: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }
}
</style>