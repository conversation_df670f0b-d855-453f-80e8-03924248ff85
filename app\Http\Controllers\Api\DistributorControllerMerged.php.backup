<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WechatGroup;
use App\Models\GroupTemplate;
use App\Models\Order;
use App\Models\CommissionLog;
use App\Services\CommissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

/**
 * 分销商控制器 - 合并版本
 * 整合了原来的 DistributorController、DistributorDashboardController、DistributorGroupController
 */
class DistributorControllerMerged extends Controller
{
    protected $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
        $this->middleware('role:admin')->only(['adminIndex', 'adminShow', 'adminUpdate', 'adminDestroy', 'updateStatus']);
    }

    // ==================== 分销商登录相关 ====================
    
    /**
     * 分销商登录
     */
    public function login(Request $request)
    {
        $request->validate([
            'account' => 'required|string',
            'password' => 'required|string',
        ]);

        $user = User::where('distributor_account', $request->account)
                   ->where('is_distributor', true)
                   ->where('distributor_status', 1)
                   ->first();

        if (!$user || !Hash::check($request->password, $user->distributor_password)) {
            // 记录登录失败日志
            if ($user) {
                DB::table('distributor_login_logs')->insert([
                    'distributor_id' => $user->id,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'login_at' => now(),
                    'login_result' => 'failed',
                    'failure_reason' => 'invalid_password',
                ]);
            }
            
            return $this->error('账号或密码错误', 401);
        }

        // 记录登录成功日志
        DB::table('distributor_login_logs')->insert([
            'distributor_id' => $user->id,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'login_at' => now(),
            'login_result' => 'success',
        ]);

        // 生成JWT Token
        $token = auth('api')->login($user);

        return $this->success([
            'token' => $token,
            'user' => $user,
            'expires_in' => auth('api')->factory()->getTTL() * 60
        ], '登录成功');
    }

    // ==================== 分销商仪表板 ====================
    
    /**
     * 分销商仪表板数据
     */
    public function dashboard(Request $request)
    {
        $user = $request->user();
        
        // 基础统计
        $stats = [
            'total_groups' => $user->wechatGroups()->count(),
            'active_groups' => $user->wechatGroups()->where('status', 1)->count(),
            'total_orders' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->count(),
            'total_revenue' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('status', 'paid')->sum('amount'),
            'total_commission' => $user->commissionLogs()->sum('amount'),
            'pending_commission' => $user->commissionLogs()->where('status', 'pending')->sum('amount'),
        ];

        // 今日数据
        $today = Carbon::today();
        $todayStats = [
            'today_orders' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->whereDate('created_at', $today)->count(),
            'today_revenue' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->whereDate('created_at', $today)->where('status', 'paid')->sum('amount'),
        ];

        // 最近订单
        $recentOrders = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->with(['wechatGroup:id,title', 'user:id,username'])
          ->latest()
          ->limit(10)
          ->get();

        // 热门群组
        $topGroups = $user->wechatGroups()
            ->withCount('orders')
            ->withSum('orders', 'amount')
            ->orderBy('orders_count', 'desc')
            ->limit(5)
            ->get();

        return $this->success([
            'stats' => $stats,
            'today_stats' => $todayStats,
            'recent_orders' => $recentOrders,
            'top_groups' => $topGroups,
        ]);
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->distributor_password)) {
            return $this->error('当前密码错误', 422);
        }

        $user->update([
            'distributor_password' => Hash::make($request->new_password)
        ]);

        return $this->success(null, '密码修改成功');
    }

    // ==================== 分销商群组管理 ====================
    
    /**
     * 分销商群组列表
     */
    public function groups(Request $request)
    {
        $user = $request->user();
        
        $query = WechatGroup::where('user_id', $user->id)
            ->with(['template:id,name', 'orders'])
            ->withCount('orders')
            ->withSum('orders', 'amount');

        // 搜索过滤
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $groups = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->paginate($groups);
    }

    /**
     * 创建群组
     */
    public function createGroup(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'template_id' => 'nullable|exists:group_templates,id',
            'cover_image' => 'nullable|string',
            'qr_code' => 'nullable|string',
        ]);

        $user = $request->user();

        $group = WechatGroup::create([
            'user_id' => $user->id,
            'title' => $request->title,
            'subtitle' => $request->subtitle,
            'description' => $request->description,
            'price' => $request->price,
            'template_id' => $request->template_id,
            'cover_image' => $request->cover_image,
            'qr_code' => $request->qr_code,
            'status' => 1,
        ]);

        return $this->success($group, '群组创建成功');
    }

    /**
     * 更新群组
     */
    public function updateGroup(Request $request, $id)
    {
        $user = $request->user();
        $group = WechatGroup::where('user_id', $user->id)->findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'cover_image' => 'nullable|string',
            'qr_code' => 'nullable|string',
            'status' => 'required|in:0,1',
        ]);

        $group->update($request->only([
            'title', 'subtitle', 'description', 'price', 
            'cover_image', 'qr_code', 'status'
        ]));

        return $this->success($group, '群组更新成功');
    }

    /**
     * 删除群组
     */
    public function deleteGroup(Request $request, $id)
    {
        $user = $request->user();
        $group = WechatGroup::where('user_id', $user->id)->findOrFail($id);

        // 检查是否有未完成的订单
        if ($group->orders()->where('status', 'pending')->exists()) {
            return $this->error('该群组有未完成的订单，无法删除', 422);
        }

        $group->delete();

        return $this->success(null, '群组删除成功');
    }

    /**
     * 复制群组
     */
    public function duplicateGroup(Request $request, $id)
    {
        $user = $request->user();
        $originalGroup = WechatGroup::where('user_id', $user->id)->findOrFail($id);

        $newGroup = $originalGroup->replicate();
        $newGroup->title = $originalGroup->title . ' (副本)';
        $newGroup->created_at = now();
        $newGroup->updated_at = now();
        $newGroup->save();

        return $this->success($newGroup, '群组复制成功');
    }

    /**
     * 批量操作群组
     */
    public function batchGroupAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:enable,disable,delete',
            'group_ids' => 'required|array',
            'group_ids.*' => 'exists:wechat_groups,id',
        ]);

        $user = $request->user();
        $groups = WechatGroup::where('user_id', $user->id)
            ->whereIn('id', $request->group_ids);

        switch ($request->action) {
            case 'enable':
                $groups->update(['status' => 1]);
                $message = '群组已启用';
                break;
            case 'disable':
                $groups->update(['status' => 0]);
                $message = '群组已禁用';
                break;
            case 'delete':
                $groups->delete();
                $message = '群组已删除';
                break;
        }

        return $this->success(null, $message);
    }

    // ==================== 模板管理 ====================
    
    /**
     * 获取可用模板
     */
    public function availableTemplates(Request $request)
    {
        $templates = GroupTemplate::where('status', 1)
            ->where(function($query) use ($request) {
                $query->where('is_public', true)
                      ->orWhere('created_by', $request->user()->id);
            })
            ->orderBy('created_at', 'desc')
            ->get();

        return $this->success($templates);
    }

    /**
     * 从模板创建群组
     */
    public function createGroupFromTemplate(Request $request, $templateId)
    {
        $template = GroupTemplate::findOrFail($templateId);
        $user = $request->user();

        $request->validate([
            'title' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
        ]);

        $group = WechatGroup::create([
            'user_id' => $user->id,
            'template_id' => $template->id,
            'title' => $request->title,
            'subtitle' => $template->subtitle,
            'description' => $template->description,
            'price' => $request->price,
            'cover_image' => $template->cover_image,
            'status' => 1,
        ]);

        return $this->success($group, '从模板创建群组成功');
    }

    // ==================== 提现管理 ====================
    
    /**
     * 申请提现
     */
    public function requestWithdrawal(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|in:alipay,wechat,bank',
            'payment_account' => 'required|string',
            'payment_name' => 'required|string',
        ]);

        $user = $request->user();

        // 检查余额
        if ($user->distributor_balance < $request->amount) {
            return $this->error('余额不足', 422);
        }

        // 创建提现申请
        $withdrawal = DB::table('distributor_withdrawals')->insertGetId([
            'distributor_id' => $user->id,
            'amount' => $request->amount,
            'payment_method' => $request->payment_method,
            'payment_account' => $request->payment_account,
            'payment_name' => $request->payment_name,
            'status' => 'pending',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 冻结余额
        $user->decrement('distributor_balance', $request->amount);

        return $this->success(['withdrawal_id' => $withdrawal], '提现申请已提交');
    }

    /**
     * 提现历史
     */
    public function withdrawalHistory(Request $request)
    {
        $user = $request->user();
        
        $withdrawals = DB::table('distributor_withdrawals')
            ->where('distributor_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return $this->paginate($withdrawals);
    }

    // ==================== 管理员功能 ====================
    
    /**
     * 管理员获取分销商列表
     */
    public function adminIndex(Request $request)
    {
        $query = User::where('is_distributor', true)
            ->with(['distributionGroup'])
            ->withCount('children as children_count')
            ->withSum('commissionLogs as total_commission', 'amount');

        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('username', 'like', "%{$request->search}%")
                  ->orWhere('email', 'like', "%{$request->search}%")
                  ->orWhere('phone', 'like', "%{$request->search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('distributor_status', $request->status);
        }

        $distributors = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->paginate($distributors);
    }

    /**
     * 管理员查看分销商详情
     */
    public function adminShow($id)
    {
        $distributor = User::where('is_distributor', true)
            ->with(['distributionGroup', 'children', 'commissionLogs'])
            ->findOrFail($id);

        return $this->success($distributor);
    }

    /**
     * 管理员更新分销商状态
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:0,1',
        ]);

        $distributor = User::where('is_distributor', true)->findOrFail($id);
        $distributor->update(['distributor_status' => $request->status]);

        return $this->success(null, '状态更新成功');
    }

    /**
     * 获取分销商统计数据
     */
    public function getStats(Request $request)
    {
        $stats = [
            'total_distributors' => User::where('is_distributor', true)->count(),
            'active_distributors' => User::where('is_distributor', true)->where('distributor_status', 1)->count(),
            'total_commission' => CommissionLog::sum('amount'),
            'pending_commission' => CommissionLog::where('status', 'pending')->sum('amount'),
        ];

        return $this->success($stats);
    }

    // ==================== 分销商个人信息管理 ====================
    
    /**
     * 获取分销商个人资料
     */
    public function profile(Request $request)
    {
        $user = $request->user();
        
        return $this->success([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
            'avatar' => $user->avatar,
            'level' => $user->distributor_level ?? 1,
            'status' => $user->distributor_status,
            'balance' => $user->balance ?? 0,
            'total_commission' => $user->commissionLogs()->sum('amount'),
            'created_at' => $user->created_at,
        ]);
    }

    /**
     * 更新分销商个人资料
     */
    public function updateProfile(Request $request)
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $request->user()->id,
            'phone' => 'sometimes|string|max:20|unique:users,phone,' . $request->user()->id,
            'avatar' => 'sometimes|string|max:500',
        ]);

        $user = $request->user();
        $user->update($request->only(['name', 'email', 'phone', 'avatar']));

        return $this->success($user->fresh(), '资料更新成功');
    }

    /**
     * 获取分销商订单列表
     */
    public function orders(Request $request)
    {
        $user = $request->user();
        
        $query = Order::whereHas('wechatGroup', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->with(['wechatGroup:id,title', 'user:id,username']);

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 时间范围过滤
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->paginate($orders);
    }

    /**
     * 获取分销商统计数据
     */
    public function statistics(Request $request)
    {
        $user = $request->user();
        
        // 基础统计
        $stats = [
            'total_groups' => $user->wechatGroups()->count(),
            'active_groups' => $user->wechatGroups()->where('status', 1)->count(),
            'total_orders' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->count(),
            'total_revenue' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('status', 'paid')->sum('amount'),
            'total_commission' => $user->commissionLogs()->sum('amount'),
            'pending_commission' => $user->commissionLogs()->where('status', 'pending')->sum('amount'),
            'settled_commission' => $user->commissionLogs()->where('status', 'settled')->sum('amount'),
        ];

        // 本月统计
        $thisMonth = Carbon::now()->startOfMonth();
        $monthStats = [
            'month_orders' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('created_at', '>=', $thisMonth)->count(),
            'month_revenue' => Order::whereHas('wechatGroup', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->where('created_at', '>=', $thisMonth)->where('status', 'paid')->sum('amount'),
            'month_commission' => $user->commissionLogs()->where('created_at', '>=', $thisMonth)->sum('amount'),
        ];

        return $this->success([
            'stats' => $stats,
            'month_stats' => $monthStats,
        ]);
    }

    /**
     * 获取佣金记录
     */
    public function commissions(Request $request)
    {
        $user = $request->user();
        
        $query = $user->commissionLogs()->with(['order:id,order_no,amount', 'fromUser:id,username']);

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 时间范围过滤
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $commissions = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->paginate($commissions);
    }

    /**
     * 获取提现记录
     */
    public function withdrawals(Request $request)
    {
        $user = $request->user();
        
        $query = $user->withdrawals();

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $withdrawals = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->paginate($withdrawals);
    }

    /**
     * 创建提现申请
     */
    public function createWithdrawal(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'account_type' => 'required|in:alipay,wechat,bank',
            'account_info' => 'required|array',
            'remark' => 'sometimes|string|max:500',
        ]);

        $user = $request->user();
        
        // 检查余额
        if ($user->balance < $request->amount) {
            return $this->error('余额不足', 422);
        }

        // 检查最小提现金额
        $minAmount = 10; // 可以从配置中读取
        if ($request->amount < $minAmount) {
            return $this->error("最小提现金额为 {$minAmount} 元", 422);
        }

        DB::beginTransaction();
        try {
            // 创建提现记录
            $withdrawal = $user->withdrawals()->create([
                'amount' => $request->amount,
                'account_type' => $request->account_type,
                'account_info' => $request->account_info,
                'status' => 'pending',
                'remark' => $request->remark,
            ]);

            // 扣除余额
            $user->decrement('balance', $request->amount);

            // 记录余额变动
            $user->balanceLogs()->create([
                'type' => 'withdraw',
                'amount' => -$request->amount,
                'balance_after' => $user->balance,
                'description' => '申请提现',
                'related_id' => $withdrawal->id,
                'related_type' => 'withdrawal',
            ]);

            DB::commit();

            return $this->success($withdrawal, '提现申请提交成功');
        } catch (\Exception $e) {
            DB::rollback();
            return $this->error('提现申请失败，请重试', 500);
        }
    }

    /**
     * 获取下级分销商列表
     */
    public function subDistributors(Request $request)
    {
        $user = $request->user();
        
        $query = User::where('parent_id', $user->id)
            ->where('is_distributor', true)
            ->with(['distributionGroup:id,name'])
            ->withCount('children as sub_count')
            ->withSum('commissionLogs as total_commission', 'amount');

        // 搜索过滤
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('username', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%');
            });
        }

        // 状态过滤
        if ($request->filled('status')) {
            $query->where('distributor_status', $request->status);
        }

        $distributors = $query->orderBy('created_at', 'desc')->paginate(15);

        return $this->paginate($distributors);
    }

    /**
     * 创建下级分销商
     */
    public function createSubDistributor(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => 'required|string|min:6',
            'distributor_level' => 'sometimes|integer|min:1|max:10',
        ]);

        $user = $request->user();

        DB::beginTransaction();
        try {
            $subDistributor = User::create([
                'name' => $request->name,
                'username' => $request->username,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'distributor_password' => Hash::make($request->password),
                'parent_id' => $user->id,
                'is_distributor' => true,
                'distributor_status' => 1,
                'distributor_level' => $request->distributor_level ?? 1,
                'distribution_group_id' => $user->distribution_group_id,
            ]);

            DB::commit();

            return $this->success($subDistributor, '下级分销商创建成功');
        } catch (\Exception $e) {
            DB::rollback();
            return $this->error('创建失败，请重试', 500);
        }
    }

    /**
     * 获取可用模板列表
     */
    public function templates(Request $request)
    {
        $user = $request->user();
        
        $query = GroupTemplate::where('status', 1);

        // 如果有分组限制，只显示允许的模板
        if ($user->distribution_group_id) {
            $query->where(function($q) use ($user) {
                $q->whereNull('distribution_group_id')
                  ->orWhere('distribution_group_id', $user->distribution_group_id);
            });
        }

        $templates = $query->orderBy('sort_order', 'asc')
                          ->orderBy('created_at', 'desc')
                          ->paginate(15);

        return $this->paginate($templates);
    }

    // ==================== 管理员分销商管理方法 ====================
    
    /**
     * 管理员更新分销商信息
     */
    public function adminUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $id,
            'phone' => 'sometimes|string|max:20|unique:users,phone,' . $id,
            'distributor_level' => 'sometimes|integer|min:1|max:10',
            'distribution_group_id' => 'sometimes|nullable|exists:distribution_groups,id',
            'distributor_status' => 'sometimes|in:0,1',
        ]);

        $distributor = User::where('is_distributor', true)->findOrFail($id);
        $distributor->update($request->only([
            'name', 'email', 'phone', 'distributor_level', 
            'distribution_group_id', 'distributor_status'
        ]));

        return $this->success($distributor->fresh(), '分销商信息更新成功');
    }

    /**
     * 管理员删除分销商
     */
    public function adminDestroy($id)
    {
        $distributor = User::where('is_distributor', true)->findOrFail($id);
        
        // 检查是否有下级分销商
        if ($distributor->children()->where('is_distributor', true)->exists()) {
            return $this->error('该分销商还有下级分销商，无法删除', 422);
        }

        // 检查是否有未结算的佣金
        if ($distributor->commissionLogs()->where('status', 'pending')->exists()) {
            return $this->error('该分销商还有未结算的佣金，无法删除', 422);
        }

        $distributor->update(['is_distributor' => false, 'distributor_status' => 0]);

        return $this->success(null, '分销商删除成功');
    }

    /**
     * 升级分销商等级
     */
    public function upgrade(Request $request, $id)
    {
        $request->validate([
            'distributor_level' => 'required|integer|min:1|max:10',
            'remark' => 'sometimes|string|max:500',
        ]);

        $distributor = User::where('is_distributor', true)->findOrFail($id);
        $oldLevel = $distributor->distributor_level;
        
        $distributor->update([
            'distributor_level' => $request->distributor_level
        ]);

        // 记录等级变更日志
        $distributor->distributorLevelRecords()->create([
            'old_level' => $oldLevel,
            'new_level' => $request->distributor_level,
            'operator_id' => $request->user()->id,
            'remark' => $request->remark ?? '管理员手动升级',
        ]);

        return $this->success($distributor->fresh(), '分销商等级升级成功');
    }

    /**
     * 管理分销商余额
     */
    public function manageBalance(Request $request, $id)
    {
        $request->validate([
            'type' => 'required|in:add,subtract',
            'amount' => 'required|numeric|min:0.01',
            'remark' => 'required|string|max:500',
        ]);

        $distributor = User::where('is_distributor', true)->findOrFail($id);
        $amount = $request->type === 'add' ? $request->amount : -$request->amount;

        DB::beginTransaction();
        try {
            // 检查余额是否足够（扣除时）
            if ($request->type === 'subtract' && $distributor->balance < $request->amount) {
                return $this->error('分销商余额不足', 422);
            }

            // 更新余额
            $distributor->increment('balance', $amount);

            // 记录余额变动
            $distributor->balanceLogs()->create([
                'type' => $request->type === 'add' ? 'admin_add' : 'admin_subtract',
                'amount' => $amount,
                'balance_after' => $distributor->balance,
                'description' => $request->remark,
                'operator_id' => $request->user()->id,
            ]);

            DB::commit();

            return $this->success([
                'balance' => $distributor->balance,
                'change_amount' => $amount,
            ], '余额调整成功');
        } catch (\Exception $e) {
            DB::rollback();
            return $this->error('余额调整失败，请重试', 500);
        }
    }

    /**
     * 更新分销商等级
     */
    public function updateLevel(Request $request, $id)
    {
        $request->validate([
            'distributor_level' => 'required|integer|min:1|max:10',
        ]);

        $distributor = User::where('is_distributor', true)->findOrFail($id);
        $distributor->update(['distributor_level' => $request->distributor_level]);

        return $this->success($distributor->fresh(), '分销商等级更新成功');
    }

    /**
     * 更新分销商分组
     */
    public function updateDistributionGroup(Request $request, $id)
    {
        $request->validate([
            'distribution_group_id' => 'nullable|exists:distribution_groups,id',
        ]);

        $distributor = User::where('is_distributor', true)->findOrFail($id);
        $distributor->update(['distribution_group_id' => $request->distribution_group_id]);

        return $this->success($distributor->fresh()->load('distributionGroup'), '分销商分组更新成功');
    }

    /**
     * 为分销商分配模板
     */
    public function assignTemplates(Request $request, $id)
    {
        $request->validate([
            'template_ids' => 'required|array',
            'template_ids.*' => 'exists:group_templates,id',
        ]);

        $distributor = User::where('is_distributor', true)->findOrFail($id);
        
        // 这里可以实现模板分配逻辑，比如创建分销商-模板关联表
        // 暂时返回成功响应
        
        return $this->success(null, '模板分配成功');
    }

    /**
     * 获取分销商详细统计
     */
    public function adminStatistics($id)
    {
        $distributor = User::where('is_distributor', true)->findOrFail($id);
        
        $stats = [
            'basic_info' => [
                'id' => $distributor->id,
                'name' => $distributor->name,
                'level' => $distributor->distributor_level,
                'status' => $distributor->distributor_status,
                'created_at' => $distributor->created_at,
            ],
            'business_stats' => [
                'total_groups' => $distributor->wechatGroups()->count(),
                'active_groups' => $distributor->wechatGroups()->where('status', 1)->count(),
                'total_orders' => Order::whereHas('wechatGroup', function($q) use ($distributor) {
                    $q->where('user_id', $distributor->id);
                })->count(),
                'total_revenue' => Order::whereHas('wechatGroup', function($q) use ($distributor) {
                    $q->where('user_id', $distributor->id);
                })->where('status', 'paid')->sum('amount'),
            ],
            'commission_stats' => [
                'total_commission' => $distributor->commissionLogs()->sum('amount'),
                'pending_commission' => $distributor->commissionLogs()->where('status', 'pending')->sum('amount'),
                'settled_commission' => $distributor->commissionLogs()->where('status', 'settled')->sum('amount'),
            ],
            'team_stats' => [
                'direct_children' => $distributor->children()->where('is_distributor', true)->count(),
                'total_team_size' => $this->getTeamSize($distributor),
            ],
        ];

        return $this->success($stats);
    }

    /**
     * 获取招募相关方法
     */
    public function generateInviteCode(Request $request)
    {
        $user = $request->user();
        
        $inviteCode = 'INV_' . $user->id . '_' . time() . '_' . rand(1000, 9999);
        
        // 这里可以将邀请码保存到数据库
        
        return $this->success([
            'invite_code' => $inviteCode,
            'invite_url' => url('/register?invite=' . $inviteCode),
        ], '邀请码生成成功');
    }

    public function getInviteStats(Request $request)
    {
        $user = $request->user();
        
        $stats = [
            'total_invites' => $user->children()->count(),
            'successful_invites' => $user->children()->where('is_distributor', true)->count(),
            'this_month_invites' => $user->children()->whereMonth('created_at', now()->month)->count(),
        ];

        return $this->success($stats);
    }

    public function getInviteList(Request $request)
    {
        $user = $request->user();
        
        $invites = $user->children()
            ->select(['id', 'name', 'username', 'phone', 'is_distributor', 'created_at'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return $this->paginate($invites);
    }

    public function getPromotionMaterials()
    {
        $materials = [
            'images' => [
                ['title' => '推广海报1', 'url' => '/images/promotion/poster1.jpg'],
                ['title' => '推广海报2', 'url' => '/images/promotion/poster2.jpg'],
            ],
            'texts' => [
                ['title' => '推广文案1', 'content' => '加入我们，开启财富之路...'],
                ['title' => '推广文案2', 'content' => '专业团队，丰厚回报...'],
            ],
        ];

        return $this->success($materials);
    }

    public function setRecruitTarget(Request $request)
    {
        $request->validate([
            'monthly_target' => 'required|integer|min:1',
            'reward_amount' => 'sometimes|numeric|min:0',
        ]);

        $user = $request->user();
        
        // 这里可以保存招募目标到数据库
        
        return $this->success(null, '招募目标设置成功');
    }

    public function substationDistributors(Request $request)
    {
        // 获取分站下的分销商列表
        $substationId = $request->user()->substation_id;
        
        if (!$substationId) {
            return $this->error('您不属于任何分站', 403);
        }

        $distributors = User::where('is_distributor', true)
            ->where('substation_id', $substationId)
            ->with(['distributionGroup:id,name'])
            ->withCount('children as sub_count')
            ->withSum('commissionLogs as total_commission', 'amount')
            ->paginate(15);

        return $this->paginate($distributors);
    }

    /**
     * 获取团队总人数（递归计算）
     */
    private function getTeamSize($user)
    {
        $count = $user->children()->where('is_distributor', true)->count();
        
        foreach ($user->children()->where('is_distributor', true)->get() as $child) {
            $count += $this->getTeamSize($child);
        }
        
        return $count;
    }
}