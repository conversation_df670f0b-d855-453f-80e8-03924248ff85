# FFJQ项目增强功能部署脚本 (PowerShell版本)
# 部署城市定位、营销功能、防封系统等增强功能

param(
    [switch]$SkipBackup = $false,
    [switch]$SkipTest = $false
)

Write-Host "=== FFJQ项目增强功能部署开始 ===" -ForegroundColor Green

# 检查环境
Write-Host "1. 检查部署环境..." -ForegroundColor Yellow
if (-not (Test-Path "artisan")) {
    Write-Host "错误: 请在Laravel项目根目录下运行此脚本" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path ".env")) {
    Write-Host "错误: .env文件不存在" -ForegroundColor Red
    exit 1
}

# 备份数据库
if (-not $SkipBackup) {
    Write-Host "2. 备份数据库..." -ForegroundColor Yellow
    $backupFile = "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
    
    try {
        # 读取数据库配置
        $envContent = Get-Content .env
        $dbName = ($envContent | Where-Object { $_ -match "^DB_DATABASE=" }) -replace "DB_DATABASE=", ""
        $dbUser = ($envContent | Where-Object { $_ -match "^DB_USERNAME=" }) -replace "DB_USERNAME=", ""
        $dbPass = ($envContent | Where-Object { $_ -match "^DB_PASSWORD=" }) -replace "DB_PASSWORD=", ""
        
        if (Get-Command mysqldump -ErrorAction SilentlyContinue) {
            & mysqldump -u $dbUser -p$dbPass $dbName > $backupFile
            Write-Host "数据库已备份到: $backupFile" -ForegroundColor Green
        } else {
            Write-Host "警告: mysqldump未找到，跳过数据库备份" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "警告: 数据库备份失败 - $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# 安装依赖
Write-Host "3. 更新Composer依赖..." -ForegroundColor Yellow
try {
    & composer install --optimize-autoloader --no-dev
    Write-Host "Composer依赖更新完成" -ForegroundColor Green
} catch {
    Write-Host "错误: Composer依赖更新失败 - $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 运行数据库迁移
Write-Host "4. 执行数据库迁移..." -ForegroundColor Yellow
try {
    & php artisan migrate --force
    Write-Host "数据库迁移完成" -ForegroundColor Green
} catch {
    Write-Host "错误: 数据库迁移失败 - $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 运行数据填充
Write-Host "5. 填充营销数据..." -ForegroundColor Yellow
try {
    & php artisan db:seed --class=MarketingDataSeeder --force
    Write-Host "营销数据填充完成" -ForegroundColor Green
} catch {
    Write-Host "警告: 营销数据填充失败，请手动检查" -ForegroundColor Yellow
}

# 清除缓存
Write-Host "6. 清除应用缓存..." -ForegroundColor Yellow
$cacheCommands = @(
    "cache:clear",
    "config:clear", 
    "route:clear",
    "view:clear"
)

foreach ($cmd in $cacheCommands) {
    try {
        & php artisan $cmd
    } catch {
        Write-Host "警告: $cmd 执行失败" -ForegroundColor Yellow
    }
}

# 重新生成缓存
Write-Host "7. 重新生成缓存..." -ForegroundColor Yellow
$cacheGenCommands = @(
    "config:cache",
    "route:cache",
    "view:cache"
)

foreach ($cmd in $cacheGenCommands) {
    try {
        & php artisan $cmd
    } catch {
        Write-Host "警告: $cmd 执行失败" -ForegroundColor Yellow
    }
}

# 创建存储链接
Write-Host "8. 创建存储链接..." -ForegroundColor Yellow
try {
    & php artisan storage:link
    Write-Host "存储链接创建完成" -ForegroundColor Green
} catch {
    Write-Host "警告: 存储链接创建失败" -ForegroundColor Yellow
}

# 检查IP定位服务配置
Write-Host "10. 检查IP定位服务配置..." -ForegroundColor Yellow
$envContent = Get-Content .env -Raw
if ($envContent -notmatch "IP_LOCATION_SERVICE") {
    Write-Host "添加IP定位服务配置到.env文件..." -ForegroundColor Yellow
    $newConfig = @"

# IP定位服务配置
IP_LOCATION_SERVICE=ip2region
IP2REGION_DB_PATH=storage/app/ip2region/ip2region.xdb
AMAP_API_KEY=
BAIDU_API_KEY=
"@
    Add-Content -Path .env -Value $newConfig
    Write-Host "IP定位配置已添加" -ForegroundColor Green
}

# 创建必要目录
Write-Host "11. 创建必要目录..." -ForegroundColor Yellow
$directories = @(
    "storage/app/ip2region",
    "public/face/qq", 
    "public/face/za"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "创建目录: $dir" -ForegroundColor Green
    }
}

# 下载IP数据库文件
Write-Host "12. 检查IP地理位置数据库..." -ForegroundColor Yellow
$ipDbPath = "storage/app/ip2region/ip2region.xdb"
if (-not (Test-Path $ipDbPath)) {
    Write-Host "下载ip2region数据库文件..." -ForegroundColor Yellow
    try {
        $url = "https://github.com/lionsoul2014/ip2region/raw/master/data/ip2region.xdb"
        Invoke-WebRequest -Uri $url -OutFile $ipDbPath
        Write-Host "IP数据库下载完成" -ForegroundColor Green
    } catch {
        Write-Host "警告: IP数据库下载失败，请手动下载" -ForegroundColor Yellow
    }
}

# 测试增强功能
if (-not $SkipTest) {
    Write-Host "13. 测试增强功能..." -ForegroundColor Yellow
    try {
        & php test-enhanced-features.php
        Write-Host "功能测试通过" -ForegroundColor Green
    } catch {
        Write-Host "警告: 部分功能测试失败，请检查日志" -ForegroundColor Yellow
    }
}

# 生成部署报告
Write-Host "14. 生成部署报告..." -ForegroundColor Yellow
$reportFile = "deployment_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$reportContent = @"
FFJQ项目增强功能部署报告
部署时间: $(Get-Date)
部署版本: Enhanced Features v2.0

已部署功能:
✓ 城市定位功能增强
✓ 群组营销功能优化  
✓ 防封系统升级
✓ 浏览器检测服务
✓ 访问验证系统
✓ 域名池管理
✓ 虚拟数据生成
✓ 智能标题替换

数据库变更:
- 添加营销字段到wechat_groups表
- 创建群组访问日志表
- 创建IP城市缓存表
- 增强域名池表结构

新增API端点:
- /api/location/* - 城市定位API
- /api/groups/{id}/marketing-config - 营销配置API
- /api/groups/{id}/virtual-members - 虚拟成员API
- /api/groups/{id}/preview - 群组预览API

配置文件:
- config/ip_location.php - IP定位服务配置
- .env - 新增IP定位相关配置

注意事项:
1. 请配置高德地图API密钥以启用精确定位
2. 定期检查域名池健康状态
3. 监控访问日志和统计数据
4. 及时更新IP地理位置数据库
"@

Set-Content -Path $reportFile -Value $reportContent
Write-Host "部署报告已生成: $reportFile" -ForegroundColor Green

Write-Host ""
Write-Host "=== 增强功能部署完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 恭喜！FFJQ项目增强功能已成功部署" -ForegroundColor Cyan
Write-Host ""
Write-Host "接下来的步骤:" -ForegroundColor Yellow
Write-Host "1. 配置高德地图API密钥 (可选，用于精确定位)"
Write-Host "2. 上传群组头像资源到 public/face/ 目录"
Write-Host "3. 在管理后台测试新增的营销功能"
Write-Host "4. 检查防封系统的域名配置"
Write-Host "5. 监控系统日志确保功能正常运行"
Write-Host ""
Write-Host "如有问题，请查看部署报告: $reportFile" -ForegroundColor Cyan
Write-Host "技术支持: 请检查 storage/logs/laravel.log 获取详细错误信息" -ForegroundColor Cyan