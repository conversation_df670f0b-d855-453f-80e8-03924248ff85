import{_ as t}from"./index-D2bI4m-v.js";/* empty css                        *//* empty css                    *//* empty css               *//* empty css                 *//* empty css                         *//* empty css               *//* empty css                */import{s as e}from"./system-CZ_rLt3R.js";import{R as a,as as n,b2 as s,b3 as i,o as r,U as o,b4 as l,b8 as u,by as c,bz as d,bm as m,b6 as h,b7 as f,Q as g}from"./element-plus-DcSKpKA8.js";import{r as v,L as p,c as y,e as b,k as w,l as _,t as M,E as k,z as x,D as W,F as P,Y as D,B as S,y as j}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const C=43200,O=Symbol.for("constructDateFrom");function X(t,e){return"function"==typeof t?t(e):t&&"object"==typeof t&&O in t?t[O](e):t instanceof Date?new t.constructor(e):new Date(e)}function z(t,e){return X(e||t,t)}let T={};function F(){return T}function Y(t,e){const a=F(),n=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??a.weekStartsOn??a.locale?.options?.weekStartsOn??0,s=z(t,e?.in),i=s.getDay(),r=(i<n?7:0)+i-n;return s.setDate(s.getDate()-r),s.setHours(0,0,0,0),s}function A(t){const e=z(t),a=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return a.setUTCFullYear(e.getFullYear()),+t-+a}function H(t,...e){const a=X.bind(null,t||e.find(t=>"object"==typeof t));return e.map(a)}function N(t,e){const a=+z(t)-+z(e);return a<0?-1:a>0?1:a}function q(t,e){const a=z(t,e?.in);return+function(t,e){const a=z(t,e?.in);return a.setHours(23,59,59,999),a}(a,e)===+function(t,e){const a=z(t,e?.in),n=a.getMonth();return a.setFullYear(a.getFullYear(),n+1,0),a.setHours(23,59,59,999),a}(a,e)}function E(t,e,a){const[n,s,i]=H(a?.in,t,t,e),r=N(s,i),o=Math.abs(function(t,e,a){const[n,s]=H(a?.in,t,e);return 12*(n.getFullYear()-s.getFullYear())+(n.getMonth()-s.getMonth())}(s,i));if(o<1)return 0;1===s.getMonth()&&s.getDate()>27&&s.setDate(30),s.setMonth(s.getMonth()-r*o);let l=N(s,i)===-r;q(n)&&1===o&&1===N(n,i)&&(l=!1);const u=r*(o-+l);return 0===u?0:u}function J(t,e,a){const n=function(t,e){return+z(t)-+z(e)}(t,e)/1e3;return(s=a?.roundingMethod,t=>{const e=(s?Math[s]:Math.trunc)(t);return 0===e?0:e})(n);var s}const L={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function R(t){return(e={})=>{const a=e.width?String(e.width):t.defaultWidth;return t.formats[a]||t.formats[t.defaultWidth]}}const U={date:R({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:R({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:R({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},B={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function I(t){return(e,a)=>{let n;if("formatting"===(a?.context?String(a.context):"standalone")&&t.formattingValues){const e=t.defaultFormattingWidth||t.defaultWidth,s=a?.width?String(a.width):e;n=t.formattingValues[s]||t.formattingValues[e]}else{const e=t.defaultWidth,s=a?.width?String(a.width):t.defaultWidth;n=t.values[s]||t.values[e]}return n[t.argumentCallback?t.argumentCallback(e):e]}}function $(t){return(e,a={})=>{const n=a.width,s=n&&t.matchPatterns[n]||t.matchPatterns[t.defaultMatchWidth],i=e.match(s);if(!i)return null;const r=i[0],o=n&&t.parsePatterns[n]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(o)?function(t,e){for(let a=0;a<t.length;a++)if(e(t[a]))return a;return}(o,t=>t.test(r)):function(t,e){for(const a in t)if(Object.prototype.hasOwnProperty.call(t,a)&&e(t[a]))return a;return}(o,t=>t.test(r));let u;u=t.valueCallback?t.valueCallback(l):l,u=a.valueCallback?a.valueCallback(u):u;return{value:u,rest:e.slice(r.length)}}}function Q(t){return(e,a={})=>{const n=e.match(t.matchPattern);if(!n)return null;const s=n[0],i=e.match(t.parsePattern);if(!i)return null;let r=t.valueCallback?t.valueCallback(i[0]):i[0];r=a.valueCallback?a.valueCallback(r):r;return{value:r,rest:e.slice(s.length)}}}const V={code:"en-US",formatDistance:(t,e,a)=>{let n;const s=L[t];return n="string"==typeof s?s:1===e?s.one:s.other.replace("{{count}}",e.toString()),a?.addSuffix?a.comparison&&a.comparison>0?"in "+n:n+" ago":n},formatLong:U,formatRelative:(t,e,a,n)=>B[t],localize:{ordinalNumber:(t,e)=>{const a=Number(t),n=a%100;if(n>20||n<10)switch(n%10){case 1:return a+"st";case 2:return a+"nd";case 3:return a+"rd"}return a+"th"},era:I({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:I({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:I({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:I({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:I({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:Q({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:$({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:$({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:$({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:$({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:$({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function G(t,e){return function(t,e,a){const n=F(),s=a?.locale??n.locale??V,i=N(t,e);if(isNaN(i))throw new RangeError("Invalid time value");const r=Object.assign({},a,{addSuffix:a?.addSuffix,comparison:i}),[o,l]=H(a?.in,...i>0?[e,t]:[t,e]),u=J(l,o),c=(A(l)-A(o))/1e3,d=Math.round((u-c)/60);let m;if(d<2)return a?.includeSeconds?u<5?s.formatDistance("lessThanXSeconds",5,r):u<10?s.formatDistance("lessThanXSeconds",10,r):u<20?s.formatDistance("lessThanXSeconds",20,r):u<40?s.formatDistance("halfAMinute",0,r):u<60?s.formatDistance("lessThanXMinutes",1,r):s.formatDistance("xMinutes",1,r):0===d?s.formatDistance("lessThanXMinutes",1,r):s.formatDistance("xMinutes",d,r);if(d<45)return s.formatDistance("xMinutes",d,r);if(d<90)return s.formatDistance("aboutXHours",1,r);if(d<1440){const t=Math.round(d/60);return s.formatDistance("aboutXHours",t,r)}if(d<2520)return s.formatDistance("xDays",1,r);if(d<C){const t=Math.round(d/1440);return s.formatDistance("xDays",t,r)}if(d<86400)return m=Math.round(d/C),s.formatDistance("aboutXMonths",m,r);if(m=E(l,o),m<12){const t=Math.round(d/C);return s.formatDistance("xMonths",t,r)}{const t=m%12,e=Math.trunc(m/12);return t<3?s.formatDistance("aboutXYears",e,r):t<9?s.formatDistance("overXYears",e,r):s.formatDistance("almostXYears",e+1,r)}}(t,function(t){return X(t,Date.now())}(t),e)}const K={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},Z={date:R({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:R({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:R({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};function tt(t,e,a){const n="eeee p";return function(t,e,a){const[n,s]=H(a?.in,t,e);return+Y(n,a)===+Y(s,a)}(t,e,a)?n:t.getTime()>e.getTime()?"'下个'"+n:"'上个'"+n}const et={lastWeek:tt,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:tt,other:"PP p"},at={code:"zh-CN",formatDistance:(t,e,a)=>{let n;const s=K[t];return n="string"==typeof s?s:1===e?s.one:s.other.replace("{{count}}",String(e)),a?.addSuffix?a.comparison&&a.comparison>0?n+"内":n+"前":n},formatLong:Z,formatRelative:(t,e,a,n)=>{const s=et[t];return"function"==typeof s?s(e,a,n):s},localize:{ordinalNumber:(t,e)=>{const a=Number(t);switch(e?.unit){case"date":return a.toString()+"日";case"hour":return a.toString()+"时";case"minute":return a.toString()+"分";case"second":return a.toString()+"秒";default:return"第 "+a.toString()}},era:I({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:I({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:I({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:I({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:I({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:Q({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:$({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:$({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:$({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:$({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:$({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},nt={class:"deployment-monitor"},st={class:"page-header"},it={class:"quick-actions"},rt={class:"status-item"},ot={class:"status-content"},lt={class:"status-item"},ut={class:"status-content"},ct={class:"status-item"},dt={class:"status-content"},mt={class:"status-value text-success"},ht={class:"status-item"},ft={class:"status-content"},gt={class:"status-value text-info"},vt={class:"card-header"},pt={class:"detection-results"},yt={class:"detection-header"},bt={class:"detection-category"},wt={class:"detection-message"},_t={key:0,class:"detection-details"},Mt={class:"detail-key"},kt={class:"detail-value"},xt={class:"card-header"},Wt={class:"performance-metrics"},Pt={class:"metric-item"},Dt={class:"metric-value"},St={class:"metric-item"},jt={class:"metric-value"},Ct={class:"metric-item"},Ot={class:"metric-value"},Xt={class:"metric-item"},zt={class:"metric-value"},Tt={class:"metric-item"},Ft={class:"metric-value"},Yt={class:"metric-item"},At={class:"metric-value"},Ht={class:"deployment-suggestions"},Nt={key:0,class:"no-suggestions"},qt={key:1},Et={class:"suggestion-header"},Jt={class:"suggestion-title"},Lt={class:"suggestion-content"},Rt={key:0,class:"suggestion-action"},Ut={class:"card-header"},Bt={key:1,class:"text-success"},It=t({__name:"DeploymentMonitor",setup(t){const C=v(!1),O=v(!1),X=v({}),z=p({php_version:"8.2.9",laravel_version:"10.x",db_response_time:0,memory_usage:0,disk_usage:0,cache_status:"healthy"}),T=v([]),F=v([]),Y=y(()=>Object.values(X.value).some(t=>"fail"===t.status)?{text:"需要关注",class:"text-warning"}:{text:"运行正常",class:"text-success"}),A=y(()=>{const t=Object.keys(X.value).length;if(0===t)return{score:0,class:"text-info"};const e=Object.values(X.value).filter(t=>"pass"===t.status).length,a=Math.round(e/t*100);let n="text-success";return a<60?n="text-danger":a<80&&(n="text-warning"),{score:a,class:n}}),H=y(()=>Object.values(X.value).filter(t=>"pass"===t.status).length),N=y(()=>Object.keys(X.value).length),q=y(()=>{if(0===F.value.length)return"未检查";const t=F.value[0]?.check_time;return t?G(new Date(t),{addSuffix:!0,locale:at}):"未检查"});b(()=>{E()});const E=async()=>{await Promise.all([J(),L(),I()])},J=async()=>{try{const{data:t}=await e.getSystemInfo();Object.assign(z,t)}catch(t){console.error("加载系统信息失败:",t)}},L=async()=>{try{const{data:t}=await e.getHealthStatus();X.value=t.components||{},$()}catch(t){console.error("加载检测结果失败:",t)}},R=async()=>{C.value=!0;try{const{data:t}=await e.runHealthCheck();X.value=t.results||{},g.success("健康检查完成"),await I(),$()}catch(t){g.error("健康检查失败")}finally{C.value=!1}},U=async()=>{O.value=!0;try{const{data:t}=await e.runSystemDetection();X.value=t.results||{},g.success("系统检测完成"),await I(),$()}catch(t){g.error("系统检测失败")}finally{O.value=!1}},B=async()=>{await J(),g.success("性能指标已刷新")},I=async()=>{try{const{data:t}=await e.getDetectionHistory();F.value=t.history||[]}catch(t){console.error("加载检测历史失败:",t)}},$=()=>{const t=[];Object.entries(X.value).forEach(([e,a])=>{"fail"===a.status?t.push({priority:"high",title:`修复${Q(e)}问题`,description:a.message,action:{text:"查看解决方案",handler:()=>et(e)}}):"warning"===a.status&&t.push({priority:"medium",title:`优化${Q(e)}`,description:a.message,action:{text:"查看建议",handler:()=>It(e)}})}),T.value=t},Q=t=>({php_environment:"PHP环境",database:"数据库",cache:"缓存系统",storage:"存储系统",permissions:"文件权限",laravel_config:"Laravel配置",system_services:"系统服务"}[t]||t),V=t=>({pass:"success",warning:"warning",fail:"danger"}[t]||"info"),K=t=>({pass:"正常",warning:"警告",fail:"异常"}[t]||"未知"),Z=()=>{const t=Object.values(X.value).some(t=>"fail"===t.status),e=Object.values(X.value).some(t=>"warning"===t.status);return t?"danger":e?"warning":"success"},tt=()=>{const t=Object.values(X.value).some(t=>"fail"===t.status),e=Object.values(X.value).some(t=>"warning"===t.status);return t?"存在问题":e?"需要关注":"运行正常"},et=t=>{a.alert(`请查看${Q(t)}的解决方案`,"解决方案",{confirmButtonText:"确定"})},It=t=>{a.alert(`请查看${Q(t)}的优化建议`,"优化建议",{confirmButtonText:"确定"})},$t=()=>{const t={timestamp:(new Date).toISOString(),deployment_status:Y.value,health_score:A.value.score,detection_results:X.value,system_info:z,suggestions:T.value},e=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),a=URL.createObjectURL(e),n=document.createElement("a");n.href=a,n.download=`deployment-report-${Date.now()}.json`,n.click(),URL.revokeObjectURL(a),g.success("报告已导出")};return(t,e)=>{const a=n,g=i,v=s,p=l,y=u,b=d,E=c,J=m,L=f,$=h;return _(),w("div",nt,[M("div",st,[e[3]||(e[3]=M("div",{class:"page-title"},[M("h1",null,"🚀 部署监控中心"),M("p",{class:"page-desc"},"实时监控系统部署状态，检测系统健康状况")],-1)),M("div",it,[k(a,{type:"primary",onClick:R,loading:C.value},{default:x(()=>e[0]||(e[0]=[M("i",{class:"el-icon-refresh"},null,-1),W(" 运行健康检查 ",-1)])),_:1,__:[0]},8,["loading"]),k(a,{type:"success",onClick:U,loading:O.value},{default:x(()=>e[1]||(e[1]=[M("i",{class:"el-icon-search"},null,-1),W(" 系统检测 ",-1)])),_:1,__:[1]},8,["loading"]),k(a,{type:"info",onClick:$t},{default:x(()=>e[2]||(e[2]=[M("i",{class:"el-icon-download"},null,-1),W(" 导出报告 ",-1)])),_:1,__:[2]})])]),k(p,{gutter:20,class:"status-overview"},{default:x(()=>[k(v,{span:6},{default:x(()=>[k(g,{class:"status-card"},{default:x(()=>[M("div",rt,[e[5]||(e[5]=M("div",{class:"status-icon deployment-icon"},[M("i",{class:"el-icon-success"})],-1)),M("div",ot,[e[4]||(e[4]=M("div",{class:"status-title"},"部署状态",-1)),M("div",{class:r(["status-value",Y.value.class])},o(Y.value.text),3)])])]),_:1})]),_:1}),k(v,{span:6},{default:x(()=>[k(g,{class:"status-card"},{default:x(()=>[M("div",lt,[e[7]||(e[7]=M("div",{class:"status-icon health-icon"},[M("i",{class:"el-icon-monitor"})],-1)),M("div",ut,[e[6]||(e[6]=M("div",{class:"status-title"},"系统健康",-1)),M("div",{class:r(["status-value",A.value.class])},o(A.value.score)+"% ",3)])])]),_:1})]),_:1}),k(v,{span:6},{default:x(()=>[k(g,{class:"status-card"},{default:x(()=>[M("div",ct,[e[9]||(e[9]=M("div",{class:"status-icon service-icon"},[M("i",{class:"el-icon-setting"})],-1)),M("div",dt,[e[8]||(e[8]=M("div",{class:"status-title"},"服务状态",-1)),M("div",mt,o(H.value)+"/"+o(N.value),1)])])]),_:1})]),_:1}),k(v,{span:6},{default:x(()=>[k(g,{class:"status-card"},{default:x(()=>[M("div",ht,[e[11]||(e[11]=M("div",{class:"status-icon check-icon"},[M("i",{class:"el-icon-circle-check"})],-1)),M("div",ft,[e[10]||(e[10]=M("div",{class:"status-title"},"最后检查",-1)),M("div",gt,o(q.value),1)])])]),_:1})]),_:1})]),_:1}),k(p,{gutter:20,style:{"margin-top":"20px"}},{default:x(()=>[k(v,{span:12},{default:x(()=>[k(g,null,{header:x(()=>[M("div",vt,[e[12]||(e[12]=M("span",null,"🔍 系统检测结果",-1)),k(y,{type:Z(),size:"small"},{default:x(()=>[W(o(tt()),1)]),_:1},8,["type"])])]),default:x(()=>[M("div",pt,[(_(!0),w(P,null,D(X.value,(t,e)=>(_(),w("div",{key:e,class:"detection-item"},[M("div",yt,[M("span",bt,o(Q(e)),1),k(y,{type:V(t.status),size:"mini"},{default:x(()=>[W(o(K(t.status)),1)]),_:2},1032,["type"])]),M("div",wt,o(t.message),1),t.details?(_(),w("div",_t,[k(E,{accordion:""},{default:x(()=>[k(b,{title:`查看详情 (${Object.keys(t.details).length}项)`},{default:x(()=>[(_(!0),w(P,null,D(t.details,(t,e)=>(_(),w("div",{key:e,class:"detail-item"},[M("span",Mt,o(e)+":",1),M("span",kt,o(t),1)]))),128))]),_:2},1032,["title"])]),_:2},1024)])):S("",!0)]))),128))])]),_:1})]),_:1}),k(v,{span:12},{default:x(()=>[k(g,null,{header:x(()=>[M("div",xt,[e[14]||(e[14]=M("span",null,"📊 性能指标",-1)),k(a,{type:"text",onClick:B},{default:x(()=>e[13]||(e[13]=[W("刷新",-1)])),_:1,__:[13]})])]),default:x(()=>[M("div",Wt,[M("div",Pt,[e[15]||(e[15]=M("div",{class:"metric-label"},"PHP版本",-1)),M("div",Dt,o(z.php_version),1)]),M("div",St,[e[16]||(e[16]=M("div",{class:"metric-label"},"Laravel版本",-1)),M("div",jt,o(z.laravel_version),1)]),M("div",Ct,[e[17]||(e[17]=M("div",{class:"metric-label"},"数据库响应时间",-1)),M("div",Ot,o(z.db_response_time)+"ms",1)]),M("div",Xt,[e[18]||(e[18]=M("div",{class:"metric-label"},"内存使用",-1)),M("div",zt,o(z.memory_usage)+"%",1)]),M("div",Tt,[e[19]||(e[19]=M("div",{class:"metric-label"},"磁盘使用",-1)),M("div",Ft,o(z.disk_usage)+"%",1)]),M("div",Yt,[e[20]||(e[20]=M("div",{class:"metric-label"},"缓存状态",-1)),M("div",At,[k(y,{type:"healthy"===z.cache_status?"success":"danger",size:"mini"},{default:x(()=>[W(o(z.cache_status),1)]),_:1},8,["type"])])])])]),_:1})]),_:1})]),_:1}),k(g,{style:{"margin-top":"20px"}},{header:x(()=>e[21]||(e[21]=[M("div",{class:"card-header"},[M("span",null,"💡 部署建议")],-1)])),default:x(()=>[M("div",Ht,[0===T.value.length?(_(),w("div",Nt,[k(J,{description:"系统运行良好，暂无建议"})])):(_(),w("div",qt,[(_(!0),w(P,null,D(T.value,(t,e)=>{return _(),w("div",{key:e,class:"suggestion-item"},[M("div",Et,[k(y,{type:(n=t.priority,{high:"danger",medium:"warning",low:"info"}[n]||"info"),size:"small"},{default:x(()=>[W(o(t.priority),1)]),_:2},1032,["type"]),M("span",Jt,o(t.title),1)]),M("div",Lt,o(t.description),1),t.action?(_(),w("div",Rt,[k(a,{size:"mini",type:"primary",onClick:e=>(t=>{t.action&&t.action.handler&&t.action.handler()})(t)},{default:x(()=>[W(o(t.action.text),1)]),_:2},1032,["onClick"])])):S("",!0)]);var n}),128))]))])]),_:1}),k(g,{style:{"margin-top":"20px"}},{header:x(()=>[M("div",Ut,[e[23]||(e[23]=M("span",null,"📈 检测历史",-1)),k(a,{type:"text",onClick:I},{default:x(()=>e[22]||(e[22]=[W("刷新历史",-1)])),_:1,__:[22]})])]),default:x(()=>[k($,{data:F.value,style:{width:"100%"}},{default:x(()=>[k(L,{prop:"check_time",label:"检测时间",width:"180"},{default:x(t=>{return[W(o((e=t.row.check_time,new Date(e).toLocaleString("zh-CN"))),1)];var e}),_:1}),k(L,{prop:"overall_status",label:"整体状态",width:"120"},{default:x(t=>[k(y,{type:V(t.row.overall_status),size:"mini"},{default:x(()=>[W(o(K(t.row.overall_status)),1)]),_:2},1032,["type"])]),_:1}),k(L,{prop:"health_score",label:"健康评分",width:"120"},{default:x(t=>{return[M("span",{class:r((e=t.row.health_score,e>=90?"text-success":e>=70?"text-warning":"text-danger"))},o(t.row.health_score)+"% ",3)];var e}),_:1}),k(L,{prop:"issues_count",label:"问题数量",width:"120"},{default:x(t=>[t.row.issues_count>0?(_(),j(y,{key:0,type:"warning",size:"mini"},{default:x(()=>[W(o(t.row.issues_count),1)]),_:2},1024)):(_(),w("span",Bt,"0"))]),_:1}),k(L,{prop:"duration",label:"检测耗时",width:"120"},{default:x(t=>[W(o(t.row.duration)+"ms ",1)]),_:1}),k(L,{label:"操作",width:"150"},{default:x(t=>[k(a,{size:"mini",type:"text",onClick:e=>{return a=t.row,void console.log("查看历史详情:",a);var a}},{default:x(()=>e[24]||(e[24]=[W(" 查看详情 ",-1)])),_:2,__:[24]},1032,["onClick"]),k(a,{size:"mini",type:"text",onClick:e=>{return a=t.row,void console.log("下载报告:",a);var a}},{default:x(()=>e[25]||(e[25]=[W(" 下载报告 ",-1)])),_:2,__:[25]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},[["__scopeId","data-v-ce2226ee"]]);export{It as default};
