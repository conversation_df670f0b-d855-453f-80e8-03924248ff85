import{_ as e,u as l}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                 *//* empty css                    *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       *//* empty css                */import{e as a,f as t,h as s,i as o,j as i,k as u,s as r,l as n,m as d}from"./anti-block-BQ2PwvXK.js";import{Q as c,as as p,b3 as m,bc as _,bd as v,aW as y,aV as f,bl as h,aH as g,U as b,b6 as w,b7 as k,V,b8 as x,bj as C,bk as U,au as z,b9 as j,b5 as L,bR as R,R as q}from"./element-plus-DcSKpKA8.js";import{r as S,c as M,e as D,k as $,l as I,t as B,E as H,z as E,D as T,u as O,F as Q,Y as F,y as P,A,B as G}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const N={class:"short-link-list"},W={class:"page-header"},Y={class:"page-actions"},J={slot:"header",class:"card-header"},K={slot:"header",class:"card-header"},X={"slot-scope":"scope"},Z={class:"short-code-info"},ee={class:"short-code"},le={"slot-scope":"scope"},ae={class:"full-url-info"},te={class:"full-url"},se={"slot-scope":"scope"},oe={class:"original-url"},ie={"slot-scope":"scope"},ue={class:"domain-info"},re={class:"domain-text"},ne={"slot-scope":"scope"},de={"slot-scope":"scope"},ce={"slot-scope":"scope"},pe={class:"click-count"},me={"slot-scope":"scope"},_e={"slot-scope":"scope"},ve={"slot-scope":"scope"},ye={class:"pagination-wrapper"},fe={slot:"footer"},he={class:"switch-domain-content"},ge={slot:"footer"},be={class:"stats-content"},we={class:"stats-header"},ke={class:"stats-summary"},Ve={class:"summary-item"},xe={class:"summary-value"},Ce={class:"summary-item"},Ue={class:"summary-value"},ze={class:"summary-item"},je={class:"summary-value"},Le={"slot-scope":"scope"},Re={"slot-scope":"scope"},qe={"slot-scope":"scope"},Se={"slot-scope":"scope"},Me={"slot-scope":"scope"},De={class:"qrcode-content"},$e={key:0,class:"qrcode-image"},Ie=["src"],Be={class:"qrcode-info"},He={slot:"footer"},Ee={class:"help-detail"},Te=e({__name:"ShortLinkList",setup(e){const Te=l(),Oe=S(!1),Qe=S(!1),Fe=S(!1),Pe=S(!1),Ae=S(!1),Ge=S(!1),Ne=S(!1),We=S(!1),Ye=S(!1),Je=S(!1),Ke=S([]),Xe=S([]),Ze=S([]),el=S([]),ll=S({current:1,pageSize:20,total:0}),al=S({link_type:"",status:"",domain_id:"",date_range:"",keyword:""}),tl=S({original_url:"",link_type:"recruit",domain_id:"",custom_code:"",expires_at:"",remarks:""}),sl={original_url:[{required:!0,message:"请输入原始URL",trigger:"blur"},{type:"url",message:"请输入有效的URL地址",trigger:"blur"}],link_type:[{required:!0,message:"请选择链接类型",trigger:"change"}]},ol=S({}),il=S(""),ul=S({total_clicks:0,today_clicks:0,unique_visitors:0}),rl=S([]),nl=S("trend"),dl=S(""),cl=[{type:"recruit",name:"推广链接",description:"用于分销员推广的链接"},{type:"payment",name:"支付链接",description:"用于支付页面的链接"},{type:"other",name:"其他链接",description:"其他用途的链接"}],pl=M(()=>"admin"===Te.userInfo?.role);D(()=>{ml(),_l()});const ml=async()=>{Oe.value=!0;try{const e={page:ll.value.current,per_page:ll.value.pageSize,...al.value},{data:l}=await a(e);Ke.value=l.data||[],ll.value.total=l.total||0}catch(e){c.error("加载短链接列表失败")}finally{Oe.value=!1}},_l=async()=>{try{const{data:e}=await t({per_page:100,status:1});Ze.value=e.data||[],el.value=e.data||[]}catch(e){console.error("加载域名选项失败")}},vl=()=>{Ne.value=!1,Qe.value=!0,zl()},yl=async()=>{try{We.value=!0,Ne.value?(await i(tl.value.id,tl.value),c.success("短链接更新成功")):(await u(tl.value),c.success("短链接创建成功")),Qe.value=!1,ml()}catch(e){if(e.fields)return;c.error(Ne.value?"更新失败":"创建失败")}finally{We.value=!1}},fl=async()=>{if(il.value){Ye.value=!0;try{await r(ol.value.id,il.value),c.success("域名切换成功"),Fe.value=!1,ml()}catch(e){c.error("域名切换失败")}finally{Ye.value=!1}}else c.warning("请选择新域名")},hl=async e=>{Je.value=!0;try{ul.value={total_clicks:1250,today_clicks:45,unique_visitors:890};const{data:l}=await n({link_id:e,limit:50});rl.value=l.data||[]}catch(l){c.error("加载统计数据失败")}finally{Je.value=!1}},gl=()=>{if(!dl.value)return;const e=document.createElement("a");e.href=dl.value,e.download=`qrcode_${ol.value.short_code}.png`,e.click()},bl=e=>{Xe.value=e},wl=async()=>{0!==Xe.value.length&&q.prompt("请输入新域名ID","批量切换域名",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^\d+$/,inputErrorMessage:"请输入有效的域名ID"}).then(async({value:e})=>{try{Xe.value.map(e=>e.id);c.success("批量切换成功"),ml()}catch(l){c.error("批量切换失败")}})},kl=e=>{navigator.clipboard.writeText(e).then(()=>{c.success("复制成功")}).catch(()=>{c.error("复制失败")})},Vl=async()=>{try{const e=await d(al.value),l=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=window.URL.createObjectURL(l),t=document.createElement("a");t.href=a,t.download=`short_links_${(new Date).toISOString().split("T")[0]}.xlsx`,t.click(),window.URL.revokeObjectURL(a)}catch(e){c.error("导出失败")}},xl=()=>{al.value={link_type:"",status:"",domain_id:"",date_range:"",keyword:""},ml()},Cl=e=>{ll.value.pageSize=e,ml()},Ul=e=>{ll.value.current=e,ml()},zl=()=>{tl.value={original_url:"",link_type:"recruit",domain_id:"",custom_code:"",expires_at:"",remarks:""}},jl=()=>{Ge.value=!0},Ll=e=>({recruit:"推广链接",payment:"支付链接",other:"其他链接"}[e]||e),Rl=e=>({1:"正常",2:"异常",3:"禁用",4:"过期"}[e]||"未知"),ql=e=>e?new Date(e).toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-";return(e,l)=>{const a=p,t=m,i=f,u=y,r=v,n=h,d=g,c=_,q=k,S=V,M=x,D=w,Te=U,_l=z,Sl=L,Ml=j,Dl=R,$l=C;return I(),$("div",N,[B("div",W,[l[25]||(l[25]=B("div",{class:"page-title"},[B("h1",null,"🔗 短链接管理"),B("p",{class:"page-desc"},"管理系统生成的防红短链接，监控访问情况和域名使用")],-1)),B("div",Y,[H(a,{type:"primary",onClick:vl},{default:E(()=>l[22]||(l[22]=[B("i",{class:"el-icon-plus"},null,-1),T(" 创建短链接 ",-1)])),_:1,__:[22]}),H(a,{type:"success",onClick:O(s),disabled:1!==Xe.value.length},{default:E(()=>l[23]||(l[23]=[B("i",{class:"el-icon-s-grid"},null,-1),T(" 生成二维码 ",-1)])),_:1,__:[23]},8,["onClick","disabled"]),H(a,{type:"info",onClick:Vl},{default:E(()=>l[24]||(l[24]=[B("i",{class:"el-icon-download"},null,-1),T(" 导出数据 ",-1)])),_:1,__:[24]})])]),H(t,{class:"help-card",style:{"margin-bottom":"20px"}},{default:E(()=>[B("div",J,[l[27]||(l[27]=B("span",null,"💡 短链接使用说明",-1)),H(a,{type:"text",onClick:jl},{default:E(()=>l[26]||(l[26]=[T("查看详情",-1)])),_:1,__:[26]})]),l[28]||(l[28]=B("div",{class:"help-content"},[B("div",{class:"help-tips"},[B("div",{class:"tip-item"},[B("i",{class:"el-icon-info",style:{color:"#409eff"}}),B("span",null,[B("strong",null,"自动生成："),T("分销员推广链接会自动生成防红短链接")])]),B("div",{class:"tip-item"},[B("i",{class:"el-icon-success",style:{color:"#67c23a"}}),B("span",null,[B("strong",null,"智能切换："),T("域名异常时自动切换到备用域名")])]),B("div",{class:"tip-item"},[B("i",{class:"el-icon-view",style:{color:"#e6a23c"}}),B("span",null,[B("strong",null,"实时统计："),T("详细记录每次访问的数据和来源")])])])],-1))]),_:1,__:[28]}),H(t,{class:"filter-card"},{default:E(()=>[H(c,{inline:!0,model:al.value,class:"filter-form"},{default:E(()=>[H(r,{label:"链接类型"},{default:E(()=>[H(u,{modelValue:al.value.link_type,"onUpdate:modelValue":l[0]||(l[0]=e=>al.value.link_type=e),placeholder:"全部类型",clearable:""},{default:E(()=>[H(i,{label:"推广链接",value:"recruit"}),H(i,{label:"支付链接",value:"payment"}),H(i,{label:"其他链接",value:"other"})]),_:1},8,["modelValue"])]),_:1}),H(r,{label:"链接状态"},{default:E(()=>[H(u,{modelValue:al.value.status,"onUpdate:modelValue":l[1]||(l[1]=e=>al.value.status=e),placeholder:"全部状态",clearable:""},{default:E(()=>[H(i,{label:"正常",value:1}),H(i,{label:"异常",value:2}),H(i,{label:"禁用",value:3}),H(i,{label:"过期",value:4})]),_:1},8,["modelValue"])]),_:1}),H(r,{label:"域名"},{default:E(()=>[H(u,{modelValue:al.value.domain_id,"onUpdate:modelValue":l[2]||(l[2]=e=>al.value.domain_id=e),placeholder:"全部域名",clearable:""},{default:E(()=>[(I(!0),$(Q,null,F(Ze.value,e=>(I(),P(i,{key:e.id,label:e.domain,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),H(r,{label:"创建时间"},{default:E(()=>[H(n,{modelValue:al.value.date_range,"onUpdate:modelValue":l[3]||(l[3]=e=>al.value.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},null,8,["modelValue"])]),_:1}),H(r,{label:"关键词"},{default:E(()=>[H(d,{modelValue:al.value.keyword,"onUpdate:modelValue":l[4]||(l[4]=e=>al.value.keyword=e),placeholder:"短链接代码或原始URL",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),H(r,null,{default:E(()=>[H(a,{type:"primary",onClick:ml},{default:E(()=>l[29]||(l[29]=[T("查询",-1)])),_:1,__:[29]}),H(a,{onClick:xl},{default:E(()=>l[30]||(l[30]=[T("重置",-1)])),_:1,__:[30]})]),_:1})]),_:1},8,["model"])]),_:1}),H(t,null,{default:E(()=>[B("div",K,[B("span",null,"短链接列表 ("+b(ll.value.total)+")",1),B("div",null,[pl.value?(I(),P(a,{key:0,type:"warning",size:"small",onClick:wl,disabled:0===Xe.value.length},{default:E(()=>l[31]||(l[31]=[T(" 批量切换域名 ",-1)])),_:1,__:[31]},8,["disabled"])):G("",!0),pl.value?(I(),P(a,{key:1,type:"danger",size:"small",onClick:e.batchDelete,disabled:0===Xe.value.length},{default:E(()=>l[32]||(l[32]=[T(" 批量删除 ",-1)])),_:1,__:[32]},8,["onClick","disabled"])):G("",!0)])]),A((I(),P(D,{data:Ke.value,onSelectionChange:bl,stripe:""},{default:E(()=>[H(q,{type:"selection",width:"55"}),H(q,{prop:"short_code",label:"短链接",width:"120"},{default:E(()=>[B("template",X,[B("div",Z,[B("span",ee,b(e.scope.row.short_code),1),H(a,{type:"text",size:"mini",onClick:l[5]||(l[5]=l=>{return a=e.scope.row,void kl(a.full_url);var a}),style:{"margin-left":"5px"}},{default:E(()=>l[33]||(l[33]=[B("i",{class:"el-icon-copy-document"},null,-1)])),_:1,__:[33]})])])]),_:1}),H(q,{prop:"full_url",label:"完整链接",width:"200"},{default:E(()=>[B("template",le,[B("div",ae,[B("span",te,b(e.scope.row.full_url),1),H(a,{type:"text",size:"mini",onClick:l[6]||(l[6]=l=>{return a=e.scope.row,void kl(a.full_url);var a}),style:{"margin-left":"5px"}},{default:E(()=>l[34]||(l[34]=[B("i",{class:"el-icon-copy-document"},null,-1)])),_:1,__:[34]})])])]),_:1}),H(q,{prop:"original_url",label:"原始URL","min-width":"250"},{default:E(()=>[B("template",se,[B("div",oe,[H(S,{content:e.scope.row.original_url,placement:"top"},{default:E(()=>{return[B("span",null,b((l=e.scope.row.original_url,a=40,l?l.length>a?l.substring(0,a)+"...":l:"")),1)];var l,a}),_:1},8,["content"])])])]),_:1}),H(q,{prop:"domain",label:"使用域名",width:"150"},{default:E(()=>{return[B("template",ie,[B("div",ue,[B("span",re,b(e.scope.row.domain.domain),1),H(M,{type:(l=e.scope.row.domain.health_score,l>=90?"success":l>=80?"primary":l>=60?"warning":"danger"),size:"mini",style:{"margin-left":"5px"}},{default:E(()=>[T(b(e.scope.row.domain.health_score)+"% ",1)]),_:1},8,["type"])])])];var l}),_:1}),H(q,{prop:"link_type",label:"类型",width:"100"},{default:E(()=>{return[B("template",ne,[H(M,{size:"small",type:(l=e.scope.row.link_type,{recruit:"primary",payment:"success",other:"info"}[l]||"")},{default:E(()=>[T(b(Ll(e.scope.row.link_type)),1)]),_:1},8,["type"])])];var l}),_:1}),H(q,{prop:"status",label:"状态",width:"80"},{default:E(()=>{return[B("template",de,[H(M,{type:(l=e.scope.row.status,{1:"success",2:"warning",3:"danger",4:"info"}[l]||""),size:"small"},{default:E(()=>[T(b(Rl(e.scope.row.status)),1)]),_:1},8,["type"])])];var l}),_:1}),H(q,{prop:"click_count",label:"点击量",width:"80"},{default:E(()=>[B("template",ce,[B("span",pe,b(e.scope.row.click_count),1)])]),_:1}),H(q,{prop:"created_at",label:"创建时间",width:"140"},{default:E(()=>[B("template",me,[B("span",null,b(ql(e.scope.row.created_at)),1)])]),_:1}),H(q,{prop:"last_click_at",label:"最后访问",width:"140"},{default:E(()=>[B("template",_e,[B("span",null,b(ql(e.scope.row.last_click_at)),1)])]),_:1}),H(q,{label:"操作",width:"180",fixed:"right"},{default:E(()=>[B("template",ve,[H(a,{type:"text",size:"small",onClick:l[7]||(l[7]=l=>(async e=>{ol.value=e,Pe.value=!0,await hl(e.id)})(e.scope.row))},{default:E(()=>l[35]||(l[35]=[T(" 统计 ",-1)])),_:1,__:[35]}),pl.value?(I(),P(a,{key:0,type:"text",size:"small",onClick:l[8]||(l[8]=l=>{return a=e.scope.row,ol.value=a,il.value=a.domain.id,void(Fe.value=!0);var a})},{default:E(()=>l[36]||(l[36]=[T(" 切换域名 ",-1)])),_:1,__:[36]})):G("",!0),pl.value?(I(),P(a,{key:1,type:"text",size:"small",onClick:l[9]||(l[9]=l=>{return a=e.scope.row,Ne.value=!0,Qe.value=!0,void(tl.value={...a,domain_id:a.domain.id});var a})},{default:E(()=>l[37]||(l[37]=[T(" 编辑 ",-1)])),_:1,__:[37]})):G("",!0),pl.value?(I(),P(a,{key:2,type:"text",size:"small",onClick:l[10]||(l[10]=l=>O(o)(e.scope.row)),style:{color:"#f56c6c"}},{default:E(()=>l[38]||(l[38]=[T(" 删除 ",-1)])),_:1,__:[38]})):G("",!0)])]),_:1})]),_:1},8,["data"])),[[$l,Oe.value]]),B("div",ye,[H(Te,{onSizeChange:Cl,onCurrentChange:Ul,"current-page":ll.value.current,"page-sizes":[10,20,50,100],"page-size":ll.value.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:ll.value.total},null,8,["current-page","page-size","total"])])]),_:1}),H(_l,{title:Ne.value?"编辑短链接":"创建短链接",visible:Qe.value,width:"500px",onClose:zl},{default:E(()=>[H(c,{model:tl.value,rules:sl,ref_key:"linkForm",ref:tl,"label-width":"100px"},{default:E(()=>[H(r,{label:"原始URL",prop:"original_url"},{default:E(()=>[H(d,{modelValue:tl.value.original_url,"onUpdate:modelValue":l[11]||(l[11]=e=>tl.value.original_url=e),placeholder:"请输入完整的URL地址",type:"textarea",rows:"2"},null,8,["modelValue"]),l[39]||(l[39]=B("div",{class:"form-tip"}," 🔗 请输入完整的URL地址，包含 http:// 或 https:// ",-1))]),_:1,__:[39]}),H(r,{label:"链接类型",prop:"link_type"},{default:E(()=>[H(u,{modelValue:tl.value.link_type,"onUpdate:modelValue":l[12]||(l[12]=e=>tl.value.link_type=e),placeholder:"选择链接类型",style:{width:"100%"}},{default:E(()=>[H(i,{label:"推广链接",value:"recruit"}),H(i,{label:"支付链接",value:"payment"}),H(i,{label:"其他链接",value:"other"})]),_:1},8,["modelValue"])]),_:1}),H(r,{label:"选择域名",prop:"domain_id"},{default:E(()=>[H(u,{modelValue:tl.value.domain_id,"onUpdate:modelValue":l[13]||(l[13]=e=>tl.value.domain_id=e),placeholder:"选择域名",style:{width:"100%"}},{default:E(()=>[(I(!0),$(Q,null,F(el.value,e=>(I(),P(i,{key:e.id,label:`${e.domain} (健康度: ${e.health_score}%)`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l[40]||(l[40]=B("div",{class:"form-tip"}," 💡 系统会自动选择最佳域名，也可手动指定 ",-1))]),_:1,__:[40]}),H(r,{label:"自定义代码"},{default:E(()=>[H(d,{modelValue:tl.value.custom_code,"onUpdate:modelValue":l[14]||(l[14]=e=>tl.value.custom_code=e),placeholder:"留空则自动生成",maxlength:"20"},null,8,["modelValue"]),l[41]||(l[41]=B("div",{class:"form-tip"}," 🎯 自定义短链接代码，仅支持字母数字，留空则自动生成 ",-1))]),_:1,__:[41]}),H(r,{label:"有效期"},{default:E(()=>[H(n,{modelValue:tl.value.expires_at,"onUpdate:modelValue":l[15]||(l[15]=e=>tl.value.expires_at=e),type:"datetime",placeholder:"选择过期时间",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"]),l[42]||(l[42]=B("div",{class:"form-tip"}," ⏰ 留空则永久有效，过期后链接将无法访问 ",-1))]),_:1,__:[42]}),H(r,{label:"备注"},{default:E(()=>[H(d,{modelValue:tl.value.remarks,"onUpdate:modelValue":l[16]||(l[16]=e=>tl.value.remarks=e),type:"textarea",rows:"2",placeholder:"链接用途说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),B("div",fe,[H(a,{onClick:l[17]||(l[17]=e=>Qe.value=!1)},{default:E(()=>l[43]||(l[43]=[T("取消",-1)])),_:1,__:[43]}),H(a,{type:"primary",onClick:yl,loading:We.value},{default:E(()=>[T(b(Ne.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),_:1},8,["title","visible"]),H(_l,{title:"切换域名",visible:Fe.value,width:"400px"},{default:E(()=>[B("div",he,[B("p",null,[l[44]||(l[44]=T("为短链接 ",-1)),B("strong",null,b(ol.value.short_code),1),l[45]||(l[45]=T(" 切换域名：",-1))]),H(u,{modelValue:il.value,"onUpdate:modelValue":l[18]||(l[18]=e=>il.value=e),placeholder:"选择新域名",style:{width:"100%"}},{default:E(()=>[(I(!0),$(Q,null,F(el.value,e=>(I(),P(i,{key:e.id,label:`${e.domain} (健康度: ${e.health_score}%)`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l[46]||(l[46]=B("div",{class:"form-tip",style:{"margin-top":"10px"}}," 💡 切换域名后，原链接将重定向到新域名 ",-1))]),B("div",ge,[H(a,{onClick:l[19]||(l[19]=e=>Fe.value=!1)},{default:E(()=>l[47]||(l[47]=[T("取消",-1)])),_:1,__:[47]}),H(a,{type:"primary",onClick:fl,loading:Ye.value},{default:E(()=>l[48]||(l[48]=[T("切换",-1)])),_:1,__:[48]},8,["loading"])])]),_:1},8,["visible"]),H(_l,{title:"访问统计",visible:Pe.value,width:"800px"},{default:E(()=>[B("div",be,[B("div",we,[B("h4",null,b(ol.value.short_code)+" 的访问统计",1),B("div",ke,[B("div",Ve,[l[49]||(l[49]=B("span",{class:"summary-label"},"总访问量",-1)),B("span",xe,b(ul.value.total_clicks),1)]),B("div",Ce,[l[50]||(l[50]=B("span",{class:"summary-label"},"今日访问",-1)),B("span",Ue,b(ul.value.today_clicks),1)]),B("div",ze,[l[51]||(l[51]=B("span",{class:"summary-label"},"独立访客",-1)),B("span",je,b(ul.value.unique_visitors),1)])])]),H(Ml,{modelValue:nl.value,"onUpdate:modelValue":l[20]||(l[20]=e=>nl.value=e)},{default:E(()=>[H(Sl,{label:"访问趋势",name:"trend"},{default:E(()=>l[52]||(l[52]=[B("div",{class:"chart-container"},[B("p",null,"访问趋势图表")],-1)])),_:1,__:[52]}),H(Sl,{label:"访问记录",name:"logs"},{default:E(()=>[A((I(),P(D,{data:rl.value,size:"small"},{default:E(()=>[H(q,{prop:"access_time",label:"访问时间",width:"140"},{default:E(()=>[B("template",Le,[B("span",null,b(ql(e.scope.row.access_time)),1)])]),_:1}),H(q,{prop:"ip_address",label:"IP地址",width:"120"},{default:E(()=>[B("template",Re,[B("span",null,b(e.scope.row.ip_address),1)])]),_:1}),H(q,{prop:"user_agent",label:"设备信息","min-width":"200"},{default:E(()=>[B("template",qe,[H(S,{content:e.scope.row.user_agent,placement:"top"},{default:E(()=>{return[B("span",null,b((l=e.scope.row.user_agent,a=30,l?l.length>a?l.substring(0,a)+"...":l:"")),1)];var l,a}),_:1},8,["content"])])]),_:1}),H(q,{prop:"referer",label:"来源","min-width":"150"},{default:E(()=>[B("template",Se,[B("span",null,b(e.scope.row.referer||"直接访问"),1)])]),_:1}),H(q,{prop:"region",label:"地区",width:"100"},{default:E(()=>[B("template",Me,[B("span",null,b(e.scope.row.region||"-"),1)])]),_:1})]),_:1},8,["data"])),[[$l,Je.value]])]),_:1})]),_:1},8,["modelValue"])])]),_:1},8,["visible"]),H(_l,{title:"二维码",visible:Ae.value,width:"400px"},{default:E(()=>[B("div",De,[dl.value?(I(),$("div",$e,[B("img",{src:dl.value,alt:"二维码"},null,8,Ie)])):G("",!0),B("div",Be,[B("p",null,[l[53]||(l[53]=B("strong",null,"短链接：",-1)),T(b(ol.value.full_url),1)]),B("p",null,[l[54]||(l[54]=B("strong",null,"创建时间：",-1)),T(b(ql(new Date)),1)])])]),B("div",He,[H(a,{onClick:l[21]||(l[21]=e=>Ae.value=!1)},{default:E(()=>l[55]||(l[55]=[T("关闭",-1)])),_:1,__:[55]}),H(a,{type:"primary",onClick:gl},{default:E(()=>l[56]||(l[56]=[T("下载二维码",-1)])),_:1,__:[56]})])]),_:1},8,["visible"]),H(_l,{title:"短链接使用说明",visible:Ge.value,width:"700px"},{default:E(()=>[B("div",Ee,[l[58]||(l[58]=B("h3",null,"🔗 什么是防红短链接？",-1)),l[59]||(l[59]=B("p",null,"防红短链接是经过特殊处理的短链接，能够有效避免被微信、QQ等平台检测和封禁。",-1)),l[60]||(l[60]=B("h3",null,"🚀 主要功能",-1)),l[61]||(l[61]=B("ul",null,[B("li",null,[B("strong",null,"智能域名选择："),T("系统自动选择最健康的域名生成短链接")]),B("li",null,[B("strong",null,"自动域名切换："),T("当域名异常时自动切换到备用域名")]),B("li",null,[B("strong",null,"访问统计分析："),T("详细记录每次访问的数据和来源")]),B("li",null,[B("strong",null,"二维码生成："),T("一键生成短链接二维码")]),B("li",null,[B("strong",null,"批量管理："),T("支持批量操作和数据导出")])],-1)),l[62]||(l[62]=B("h3",null,"📊 链接类型说明",-1)),H(D,{data:cl,size:"small"},{default:E(()=>[H(q,{prop:"type",label:"类型",width:"100"}),H(q,{prop:"name",label:"名称",width:"100"}),H(q,{prop:"description",label:"说明"})]),_:1}),l[63]||(l[63]=B("h3",null,"⚠️ 使用注意事项",-1)),H(Dl,{type:"warning",closable:!1},{default:E(()=>l[57]||(l[57]=[B("ul",{style:{margin:"0","padding-left":"20px"}},[B("li",null,"原始URL必须是完整的地址，包含协议头（http://或https://）"),B("li",null,"自定义代码仅支持字母和数字，建议使用有意义的代码"),B("li",null,"设置合理的过期时间，避免链接长期有效造成安全风险"),B("li",null,"定期检查链接状态，及时处理异常链接")],-1)])),_:1,__:[57]})])]),_:1},8,["visible"])])}}},[["__scopeId","data-v-58d807ae"]]);export{Te as default};
