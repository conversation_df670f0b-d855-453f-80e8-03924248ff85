import{_ as e}from"./index-D2bI4m-v.js";/* empty css                     *//* empty css                  *//* empty css                *//* empty css                  *//* empty css                    *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  *//* empty css               *//* empty css                        */import{ag as a,r as l,L as t,e as u,k as s,l as r,t as o,E as d,z as _,D as i,u as n,y as c,B as m,F as p,Y as v}from"./vue-vendor-DGsK9sC4.js";import{T as f,aS as b,as as g,Y as y,bi as h,b3 as w,b4 as V,b2 as x,bd as q,aH as k,be as U,aW as j,aV as C,bo as P,bp as $,bn as z,aU as I,$ as A,bf as Q,U as B,bO as D,bJ as F,am as H,aT as J,bP as K,a0 as L,bc as O,au as S,Q as T}from"./element-plus-DcSKpKA8.js";import{c as W}from"./community-Cx7BK33_.js";import"./utils-4VKArNEK.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const Y={class:"group-add-container"},Z={class:"page-header"},E={class:"header-content"},G={class:"header-actions"},M={class:"form-container"},R={class:"card-header"},N=["src"],X={class:"card-header"},ee={key:0,class:"test-result"},ae={class:"card-header"},le={class:"card-header"},te={class:"card-header"},ue={class:"card-header"},se=["src"],re=["src"],oe=["src"],de={class:"card-header"},_e={key:0,class:"test-result"},ie={key:0,class:"preview-content"},ne={class:"preview-header"},ce={class:"preview-stats"},me={class:"preview-price"},pe={key:0,class:"preview-intro"},ve={key:1,class:"preview-members"},fe={class:"member-list"},be=["src","alt"],ge={class:"preview-button"},ye=e({__name:"GroupAdd",setup(e){const ye=a(),he=l();l(!1);const we=l(!1),Ve=l(!1),xe=l(null),qe=l(""),ke=l([]),Ue=l(""),je=l("北京"),Ce=l(13),Pe=l("/api/upload/image"),$e=t({title:"",price:0,type:"normal",status:"active",description:"",qr_code:"",auto_city_replace:0,city_insert_strategy:"auto",read_count_display:"10万+",like_count:888,want_see_count:666,button_title:"立即加入群聊",avatar_library:"qq",display_type:1,wx_accessible:1,group_intro_title:"群简介",group_intro_content:"",faq_title:"常见问题",faq_content:"",member_reviews:"",virtual_members:100,virtual_orders:50,virtual_income:5e3,today_views:1200,show_virtual_activity:1,show_member_avatars:1,show_member_reviews:1,show_customer_service:1,customer_service_title:"",customer_service_desc:"",customer_service_avatar:"",customer_service_qr:"",ad_qr_code:""}),ze={title:[{required:!0,message:"请输入群组名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],price:[{required:!0,message:"请输入群组价格",trigger:"blur"},{type:"number",min:0,message:"价格不能小于0",trigger:"blur"}],type:[{required:!0,message:"请选择群组类型",trigger:"change"}]},Ie=e=>{e&&($e.title.includes("xxx")||T.info('建议在群组名称中使用"xxx"作为城市占位符，如："xxx交流群"'))},Ae=()=>{if(!$e.title)return void T.warning("请先输入群组名称");let e=$e.title;if($e.auto_city_replace)switch($e.city_insert_strategy){case"prefix":e=je.value+$e.title.replace(/^xxx/,"");break;case"suffix":e=$e.title.replace(/xxx/,"")+"("+je.value+"版)";break;case"natural":default:e=$e.title.replace(/xxx/g,je.value);break;case"auto":e=$e.title.includes("xxx")?$e.title.replace(/xxx/g,je.value):je.value+$e.title}Ue.value=e,T.success("城市替换测试完成")},Qe=()=>{if(!qe.value)return void T.warning("请选择营销模板");const e=ke.value.find(e=>e.id===qe.value);e&&e.config&&(Object.assign($e,e.config),T.success(`已应用${e.name}模板配置`))},Be=()=>{const e=["最美的太阳花","孤海的浪漫","薰衣草","木槿，花","森林小巷少女与狐@","冬日暖阳","午後の夏天","嘴角的美人痣。","朽梦挽歌","心淡然","青春不散场","时光不老我们不散","岁月如歌","梦想起航","阳光少年"],a=Array.from({length:Ce.value},(a,l)=>({nickname:e[l%e.length]+(l>e.length-1?l:""),avatar:`/face/${$e.avatar_library}/${l%41+1}.${"qq"===$e.avatar_library?"jpg":"jpeg"}`,join_time:(new Date).toLocaleString()}));return T.success(`成功生成 ${Ce.value} 个虚拟成员`),a},De=()=>{xe.value={title:Ue.value||$e.title,price:$e.price,read_count_display:$e.read_count_display,like_count:$e.like_count,want_see_count:$e.want_see_count,button_title:$e.button_title,group_intro_title:$e.group_intro_title,group_intro_content:$e.group_intro_content,virtual_members:Be()},Ve.value=!0},Fe=()=>{ye.go(-1)},He=e=>{$e.qr_code=e.data.url,T.success("二维码上传成功")},Je=e=>{$e.customer_service_avatar=e.data.url,T.success("客服头像上传成功")},Ke=e=>{$e.customer_service_qr=e.data.url,T.success("客服二维码上传成功")},Le=e=>{$e.ad_qr_code=e.data.url,T.success("广告二维码上传成功")},Oe=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<2;return a?!!l||(T.error("图片大小不能超过 2MB!"),!1):(T.error("只能上传图片文件!"),!1)},Se=Oe,Te=async()=>{try{await he.value.validate(),we.value=!0;const e={title:$e.title,price:$e.price,description:$e.description,qr_code:$e.qr_code,status:"active"===$e.status?1:0,auto_city_replace:$e.auto_city_replace,city_insert_strategy:$e.city_insert_strategy,read_count_display:$e.read_count_display,like_count:$e.like_count,want_see_count:$e.want_see_count,button_title:$e.button_title,avatar_library:$e.avatar_library,display_type:$e.display_type,wx_accessible:$e.wx_accessible,group_intro_title:$e.group_intro_title,group_intro_content:$e.group_intro_content,faq_title:$e.faq_title,faq_content:$e.faq_content,member_reviews:$e.member_reviews,virtual_members:$e.virtual_members,virtual_orders:$e.virtual_orders,virtual_income:$e.virtual_income,today_views:$e.today_views,show_virtual_activity:$e.show_virtual_activity,show_member_avatars:$e.show_member_avatars,show_member_reviews:$e.show_member_reviews,show_customer_service:$e.show_customer_service,customer_service_title:$e.customer_service_title,customer_service_desc:$e.customer_service_desc,customer_service_avatar:$e.customer_service_avatar,customer_service_qr:$e.customer_service_qr,ad_qr_code:$e.ad_qr_code},a=await W(e);200===a.code?(T.success("群组创建成功！所有营销配置已保存"),ye.push("/community/groups")):T.error(a.message||"创建失败")}catch(e){console.error("创建群组失败:",e),T.error("创建失败，请重试")}finally{we.value=!1}};return u(()=>{(async()=>{try{ke.value=[{id:1,name:"商务交流模板",config:{read_count_display:"5万+",like_count:1200,want_see_count:800,button_title:"立即加入商务群",group_intro_title:"商务交流群简介",group_intro_content:"专业的商务交流平台，汇聚各行业精英",virtual_members:150,virtual_orders:80}},{id:2,name:"社交娱乐模板",config:{read_count_display:"10万+",like_count:2e3,want_see_count:1500,button_title:"快来聊天吧",group_intro_title:"欢乐聊天群",group_intro_content:"轻松愉快的聊天环境，结识更多朋友",virtual_members:200,virtual_orders:120}},{id:3,name:"学习教育模板",config:{read_count_display:"8万+",like_count:1500,want_see_count:1e3,button_title:"加入学习群",group_intro_title:"学习交流群",group_intro_content:"专业的学习交流平台，共同进步成长",virtual_members:180,virtual_orders:90}}]}catch(e){console.error("获取营销模板失败:",e)}})()}),(e,a)=>{const l=f,t=g,u=k,T=q,W=x,ye=U,We=V,Ye=C,Ze=j,Ee=$,Ge=P,Me=z,Re=w,Ne=Q,Xe=O,ea=S;return r(),s("div",Y,[o("div",Z,[o("div",E,[a[36]||(a[36]=o("div",{class:"header-left"},[o("h1",{class:"page-title"},"创建群组"),o("p",{class:"page-subtitle"},"创建新的微信群组并配置完整的营销功能")],-1)),o("div",G,[d(t,{onClick:Fe},{default:_(()=>[d(l,null,{default:_(()=>[d(n(b))]),_:1}),a[33]||(a[33]=i(" 返回 ",-1))]),_:1,__:[33]}),d(t,{type:"success",onClick:De,disabled:!$e.title},{default:_(()=>[d(l,null,{default:_(()=>[d(n(y))]),_:1}),a[34]||(a[34]=i(" 预览效果 ",-1))]),_:1,__:[34]},8,["disabled"]),d(t,{type:"primary",onClick:Te,loading:we.value},{default:_(()=>[d(l,null,{default:_(()=>[d(n(h))]),_:1}),a[35]||(a[35]=i(" 创建群组 ",-1))]),_:1,__:[35]},8,["loading"])])])]),o("div",M,[d(Xe,{ref_key:"formRef",ref:he,model:$e,rules:ze,"label-width":"120px",size:"default"},{default:_(()=>[d(Re,{class:"config-card",shadow:"never"},{header:_(()=>[o("div",R,[d(l,null,{default:_(()=>[d(n(A))]),_:1}),a[37]||(a[37]=o("span",null,"基础信息",-1))])]),default:_(()=>[d(We,{gutter:24},{default:_(()=>[d(W,{span:12},{default:_(()=>[d(T,{label:"群组名称",prop:"title"},{default:_(()=>[d(u,{modelValue:$e.title,"onUpdate:modelValue":a[0]||(a[0]=e=>$e.title=e),placeholder:"请输入群组名称，支持xxx占位符",maxlength:"200","show-word-limit":""},null,8,["modelValue"]),a[38]||(a[38]=o("div",{class:"form-tip"},' 💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市 ',-1))]),_:1,__:[38]})]),_:1}),d(W,{span:12},{default:_(()=>[d(T,{label:"群组价格",prop:"price"},{default:_(()=>[d(ye,{modelValue:$e.price,"onUpdate:modelValue":a[1]||(a[1]=e=>$e.price=e),min:0,precision:2,placeholder:"0.00",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(We,{gutter:24},{default:_(()=>[d(W,{span:12},{default:_(()=>[d(T,{label:"群组类型",prop:"type"},{default:_(()=>[d(Ze,{modelValue:$e.type,"onUpdate:modelValue":a[2]||(a[2]=e=>$e.type=e),placeholder:"请选择群组类型",style:{width:"100%"}},{default:_(()=>[d(Ye,{label:"普通群",value:"normal"}),d(Ye,{label:"VIP群",value:"vip"}),d(Ye,{label:"分销群",value:"distribution"}),d(Ye,{label:"测试群",value:"test"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(W,{span:12},{default:_(()=>[d(T,{label:"群组状态",prop:"status"},{default:_(()=>[d(Ge,{modelValue:$e.status,"onUpdate:modelValue":a[3]||(a[3]=e=>$e.status=e)},{default:_(()=>[d(Ee,{value:"active"},{default:_(()=>a[39]||(a[39]=[i("启用",-1)])),_:1,__:[39]}),d(Ee,{value:"inactive"},{default:_(()=>a[40]||(a[40]=[i("禁用",-1)])),_:1,__:[40]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(T,{label:"群组描述"},{default:_(()=>[d(u,{modelValue:$e.description,"onUpdate:modelValue":a[4]||(a[4]=e=>$e.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(T,{label:"群组二维码"},{default:_(()=>[d(Me,{class:"qr-uploader",action:Pe.value,"show-file-list":!1,"on-success":He,"before-upload":Oe},{default:_(()=>[$e.qr_code?(r(),s("img",{key:0,src:$e.qr_code,class:"qr-image"},null,8,N)):(r(),c(l,{key:1,class:"qr-uploader-icon"},{default:_(()=>[d(n(I))]),_:1}))]),_:1},8,["action"]),a[41]||(a[41]=o("div",{class:"form-tip"},"建议上传 400x400 像素的二维码图片",-1))]),_:1,__:[41]})]),_:1}),d(Re,{class:"config-card",shadow:"never"},{header:_(()=>[o("div",X,[d(l,null,{default:_(()=>[d(n(D))]),_:1}),a[42]||(a[42]=o("span",null,"城市定位配置",-1))])]),default:_(()=>[d(T,{label:"启用城市定位"},{default:_(()=>[d(Ne,{modelValue:$e.auto_city_replace,"onUpdate:modelValue":a[5]||(a[5]=e=>$e.auto_city_replace=e),"active-value":1,"inactive-value":0,onChange:Ie},null,8,["modelValue"]),a[43]||(a[43]=o("span",{class:"form-tip"},"启用后，系统会根据用户IP自动替换标题中的城市信息",-1))]),_:1,__:[43]}),$e.auto_city_replace?(r(),s(p,{key:0},[d(T,{label:"城市插入策略"},{default:_(()=>[d(Ze,{modelValue:$e.city_insert_strategy,"onUpdate:modelValue":a[6]||(a[6]=e=>$e.city_insert_strategy=e),style:{width:"100%"}},{default:_(()=>[d(Ye,{label:"智能判断（推荐）",value:"auto"}),d(Ye,{label:"前缀模式（北京+标题）",value:"prefix"}),d(Ye,{label:"后缀模式（标题+北京）",value:"suffix"}),d(Ye,{label:"自然插入（智能融入）",value:"natural"}),d(Ye,{label:"不插入",value:"none"})]),_:1},8,["modelValue"])]),_:1}),d(T,{label:"城市定位测试"},{default:_(()=>[d(We,{gutter:12},{default:_(()=>[d(W,{span:8},{default:_(()=>[d(u,{modelValue:je.value,"onUpdate:modelValue":a[7]||(a[7]=e=>je.value=e),placeholder:"输入测试城市"},null,8,["modelValue"])]),_:1}),d(W,{span:8},{default:_(()=>[d(t,{onClick:Ae},{default:_(()=>a[44]||(a[44]=[i("测试替换效果",-1)])),_:1,__:[44]})]),_:1}),d(W,{span:8},{default:_(()=>[Ue.value?(r(),s("span",ee,B(Ue.value),1)):m("",!0)]),_:1})]),_:1})]),_:1})],64)):m("",!0)]),_:1}),d(Re,{class:"config-card",shadow:"never"},{header:_(()=>[o("div",ae,[d(l,null,{default:_(()=>[d(n(F))]),_:1}),a[45]||(a[45]=o("span",null,"营销展示配置",-1))])]),default:_(()=>[d(We,{gutter:24},{default:_(()=>[d(W,{span:8},{default:_(()=>[d(T,{label:"阅读数显示"},{default:_(()=>[d(u,{modelValue:$e.read_count_display,"onUpdate:modelValue":a[8]||(a[8]=e=>$e.read_count_display=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:8},{default:_(()=>[d(T,{label:"点赞数"},{default:_(()=>[d(ye,{modelValue:$e.like_count,"onUpdate:modelValue":a[9]||(a[9]=e=>$e.like_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:8},{default:_(()=>[d(T,{label:"想看数"},{default:_(()=>[d(ye,{modelValue:$e.want_see_count,"onUpdate:modelValue":a[10]||(a[10]=e=>$e.want_see_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(We,{gutter:24},{default:_(()=>[d(W,{span:12},{default:_(()=>[d(T,{label:"入群按钮文案"},{default:_(()=>[d(u,{modelValue:$e.button_title,"onUpdate:modelValue":a[11]||(a[11]=e=>$e.button_title=e),placeholder:"如：立即加入群聊"},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:12},{default:_(()=>[d(T,{label:"头像库选择"},{default:_(()=>[d(Ze,{modelValue:$e.avatar_library,"onUpdate:modelValue":a[12]||(a[12]=e=>$e.avatar_library=e),style:{width:"100%"}},{default:_(()=>[d(Ye,{label:"QQ风格（扩列交友）",value:"qq"}),d(Ye,{label:"综合随机",value:"za"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(We,{gutter:24},{default:_(()=>[d(W,{span:12},{default:_(()=>[d(T,{label:"展示类型"},{default:_(()=>[d(Ge,{modelValue:$e.display_type,"onUpdate:modelValue":a[13]||(a[13]=e=>$e.display_type=e)},{default:_(()=>[d(Ee,{value:1},{default:_(()=>a[46]||(a[46]=[i("文字+图片",-1)])),_:1,__:[46]}),d(Ee,{value:2},{default:_(()=>a[47]||(a[47]=[i("纯图片",-1)])),_:1,__:[47]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(W,{span:12},{default:_(()=>[d(T,{label:"微信访问控制"},{default:_(()=>[d(Ge,{modelValue:$e.wx_accessible,"onUpdate:modelValue":a[14]||(a[14]=e=>$e.wx_accessible=e)},{default:_(()=>[d(Ee,{value:1},{default:_(()=>a[48]||(a[48]=[i("微信能打开",-1)])),_:1,__:[48]}),d(Ee,{value:2},{default:_(()=>a[49]||(a[49]=[i("微信内不能打开",-1)])),_:1,__:[49]})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),d(Re,{class:"config-card",shadow:"never"},{header:_(()=>[o("div",le,[d(l,null,{default:_(()=>[d(n(H))]),_:1}),a[50]||(a[50]=o("span",null,"内容配置",-1))])]),default:_(()=>[d(We,{gutter:24},{default:_(()=>[d(W,{span:12},{default:_(()=>[d(T,{label:"群简介标题"},{default:_(()=>[d(u,{modelValue:$e.group_intro_title,"onUpdate:modelValue":a[15]||(a[15]=e=>$e.group_intro_title=e),placeholder:"如：群简介"},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:12},{default:_(()=>[d(T,{label:"FAQ标题"},{default:_(()=>[d(u,{modelValue:$e.faq_title,"onUpdate:modelValue":a[16]||(a[16]=e=>$e.faq_title=e),placeholder:"如：常见问题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(T,{label:"群简介内容"},{default:_(()=>[d(u,{type:"textarea",modelValue:$e.group_intro_content,"onUpdate:modelValue":a[17]||(a[17]=e=>$e.group_intro_content=e),rows:3,placeholder:"请输入群简介内容",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(T,{label:"常见问题"},{default:_(()=>[d(u,{type:"textarea",modelValue:$e.faq_content,"onUpdate:modelValue":a[18]||(a[18]=e=>$e.faq_content=e),rows:4,placeholder:"格式：问题1----答案1\n问题2----答案2",maxlength:"1000","show-word-limit":""},null,8,["modelValue"]),a[51]||(a[51]=o("div",{class:"form-tip"},'每行一个问答，问题和答案用"----"分隔',-1))]),_:1,__:[51]}),d(T,{label:"群友评论"},{default:_(()=>[d(u,{type:"textarea",modelValue:$e.member_reviews,"onUpdate:modelValue":a[19]||(a[19]=e=>$e.member_reviews=e),rows:3,placeholder:"格式：评论内容1----点赞数1\n评论内容2----点赞数2",maxlength:"800","show-word-limit":""},null,8,["modelValue"]),a[52]||(a[52]=o("div",{class:"form-tip"},'每行一个评论，评论和点赞数用"----"分隔',-1))]),_:1,__:[52]})]),_:1}),d(Re,{class:"config-card",shadow:"never"},{header:_(()=>[o("div",te,[d(l,null,{default:_(()=>[d(n(J))]),_:1}),a[53]||(a[53]=o("span",null,"虚拟数据配置",-1))])]),default:_(()=>[d(We,{gutter:24},{default:_(()=>[d(W,{span:6},{default:_(()=>[d(T,{label:"虚拟成员数"},{default:_(()=>[d(ye,{modelValue:$e.virtual_members,"onUpdate:modelValue":a[20]||(a[20]=e=>$e.virtual_members=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:6},{default:_(()=>[d(T,{label:"虚拟订单数"},{default:_(()=>[d(ye,{modelValue:$e.virtual_orders,"onUpdate:modelValue":a[21]||(a[21]=e=>$e.virtual_orders=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:6},{default:_(()=>[d(T,{label:"虚拟收入"},{default:_(()=>[d(ye,{modelValue:$e.virtual_income,"onUpdate:modelValue":a[22]||(a[22]=e=>$e.virtual_income=e),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:6},{default:_(()=>[d(T,{label:"今日浏览量"},{default:_(()=>[d(ye,{modelValue:$e.today_views,"onUpdate:modelValue":a[23]||(a[23]=e=>$e.today_views=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(We,{gutter:24},{default:_(()=>[d(W,{span:8},{default:_(()=>[d(T,{label:"显示虚拟活动"},{default:_(()=>[d(Ne,{modelValue:$e.show_virtual_activity,"onUpdate:modelValue":a[24]||(a[24]=e=>$e.show_virtual_activity=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:8},{default:_(()=>[d(T,{label:"显示成员头像"},{default:_(()=>[d(Ne,{modelValue:$e.show_member_avatars,"onUpdate:modelValue":a[25]||(a[25]=e=>$e.show_member_avatars=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:8},{default:_(()=>[d(T,{label:"显示群友评论"},{default:_(()=>[d(Ne,{modelValue:$e.show_member_reviews,"onUpdate:modelValue":a[26]||(a[26]=e=>$e.show_member_reviews=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(T,{label:"生成虚拟成员"},{default:_(()=>[d(We,{gutter:12},{default:_(()=>[d(W,{span:8},{default:_(()=>[d(ye,{modelValue:Ce.value,"onUpdate:modelValue":a[27]||(a[27]=e=>Ce.value=e),min:1,max:50},null,8,["modelValue"])]),_:1}),d(W,{span:8},{default:_(()=>[d(t,{onClick:Be},{default:_(()=>a[54]||(a[54]=[i("生成成员数据",-1)])),_:1,__:[54]})]),_:1}),d(W,{span:8},{default:_(()=>a[55]||(a[55]=[o("span",{class:"form-tip"},"将自动生成指定数量的虚拟成员",-1)])),_:1,__:[55]})]),_:1})]),_:1})]),_:1}),d(Re,{class:"config-card",shadow:"never"},{header:_(()=>[o("div",ue,[d(l,null,{default:_(()=>[d(n(K))]),_:1}),a[56]||(a[56]=o("span",null,"客服配置",-1))])]),default:_(()=>[d(T,{label:"显示客服信息"},{default:_(()=>[d(Ge,{modelValue:$e.show_customer_service,"onUpdate:modelValue":a[28]||(a[28]=e=>$e.show_customer_service=e)},{default:_(()=>[d(Ee,{value:1},{default:_(()=>a[57]||(a[57]=[i("不显示",-1)])),_:1,__:[57]}),d(Ee,{value:2},{default:_(()=>a[58]||(a[58]=[i("显示",-1)])),_:1,__:[58]})]),_:1},8,["modelValue"])]),_:1}),2===$e.show_customer_service?(r(),s(p,{key:0},[d(We,{gutter:24},{default:_(()=>[d(W,{span:12},{default:_(()=>[d(T,{label:"客服标题"},{default:_(()=>[d(u,{modelValue:$e.customer_service_title,"onUpdate:modelValue":a[29]||(a[29]=e=>$e.customer_service_title=e),placeholder:"如：VIP专属客服"},null,8,["modelValue"])]),_:1})]),_:1}),d(W,{span:12},{default:_(()=>[d(T,{label:"客服描述"},{default:_(()=>[d(u,{modelValue:$e.customer_service_desc,"onUpdate:modelValue":a[30]||(a[30]=e=>$e.customer_service_desc=e),placeholder:"如：有问题请联系客服"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(We,{gutter:24},{default:_(()=>[d(W,{span:12},{default:_(()=>[d(T,{label:"客服头像"},{default:_(()=>[d(Me,{class:"avatar-uploader",action:Pe.value,"show-file-list":!1,"on-success":Je,"before-upload":n(Se)},{default:_(()=>[$e.customer_service_avatar?(r(),s("img",{key:0,src:$e.customer_service_avatar,class:"avatar"},null,8,se)):(r(),c(l,{key:1,class:"avatar-uploader-icon"},{default:_(()=>[d(n(I))]),_:1}))]),_:1},8,["action","before-upload"])]),_:1})]),_:1}),d(W,{span:12},{default:_(()=>[d(T,{label:"客服二维码"},{default:_(()=>[d(Me,{class:"qr-uploader",action:Pe.value,"show-file-list":!1,"on-success":Ke,"before-upload":Oe},{default:_(()=>[$e.customer_service_qr?(r(),s("img",{key:0,src:$e.customer_service_qr,class:"qr-image"},null,8,re)):(r(),c(l,{key:1,class:"qr-uploader-icon"},{default:_(()=>[d(n(I))]),_:1}))]),_:1},8,["action"])]),_:1})]),_:1})]),_:1})],64)):m("",!0),d(T,{label:"广告二维码"},{default:_(()=>[d(Me,{class:"qr-uploader",action:Pe.value,"show-file-list":!1,"on-success":Le,"before-upload":Oe},{default:_(()=>[$e.ad_qr_code?(r(),s("img",{key:0,src:$e.ad_qr_code,class:"qr-image"},null,8,oe)):(r(),c(l,{key:1,class:"qr-uploader-icon"},{default:_(()=>[d(n(I))]),_:1}))]),_:1},8,["action"]),a[59]||(a[59]=o("div",{class:"form-tip"},"可选：用于在群组页面显示广告",-1))]),_:1,__:[59]})]),_:1}),d(Re,{class:"config-card",shadow:"never"},{header:_(()=>[o("div",de,[d(l,null,{default:_(()=>[d(n(L))]),_:1}),a[60]||(a[60]=o("span",null,"快速配置",-1))])]),default:_(()=>[d(T,{label:"应用营销模板"},{default:_(()=>[d(We,{gutter:12},{default:_(()=>[d(W,{span:8},{default:_(()=>[d(Ze,{modelValue:qe.value,"onUpdate:modelValue":a[31]||(a[31]=e=>qe.value=e),placeholder:"选择营销模板"},{default:_(()=>[(r(!0),s(p,null,v(ke.value,e=>(r(),c(Ye,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(W,{span:8},{default:_(()=>[d(t,{onClick:Qe},{default:_(()=>a[61]||(a[61]=[i("应用模板",-1)])),_:1,__:[61]})]),_:1}),d(W,{span:8},{default:_(()=>[d(t,{onClick:Ae,disabled:!$e.title},{default:_(()=>a[62]||(a[62]=[i("测试城市替换",-1)])),_:1,__:[62]},8,["disabled"])]),_:1})]),_:1}),Ue.value?(r(),s("div",_e,[o("span",null,"测试结果："+B(Ue.value),1)])):m("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),d(ea,{modelValue:Ve.value,"onUpdate:modelValue":a[32]||(a[32]=e=>Ve.value=e),title:"群组预览",width:"60%"},{default:_(()=>[xe.value?(r(),s("div",ie,[o("div",ne,[o("h3",null,B(xe.value.title),1),o("div",ce,[o("span",null,"阅读 "+B(xe.value.read_count_display||"0"),1),o("span",null,"点赞 "+B(xe.value.like_count||0),1),o("span",null,"想看 "+B(xe.value.want_see_count||0),1)]),o("div",me,"¥"+B(xe.value.price||$e.price),1)]),xe.value.group_intro_content?(r(),s("div",pe,[o("h4",null,B(xe.value.group_intro_title||"群简介"),1),o("p",null,B(xe.value.group_intro_content),1)])):m("",!0),xe.value.virtual_members&&xe.value.virtual_members.length?(r(),s("div",ve,[o("h4",null,"群成员 ("+B(xe.value.virtual_members.length)+"人)",1),o("div",fe,[(r(!0),s(p,null,v(xe.value.virtual_members.slice(0,8),e=>(r(),s("div",{key:e.nickname,class:"member-item"},[o("img",{src:e.avatar,alt:e.nickname},null,8,be),o("span",null,B(e.nickname),1)]))),128))])])):m("",!0),o("div",ge,[d(t,{type:"primary",size:"large"},{default:_(()=>[i(B(xe.value.button_title||"立即加入群聊"),1)]),_:1})])])):m("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-0fe033ba"]]);export{ye as default};
