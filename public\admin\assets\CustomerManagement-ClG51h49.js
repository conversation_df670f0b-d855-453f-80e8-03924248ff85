import{c as e,_ as l}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                         *//* empty css                 *//* empty css                       *//* empty css                             *//* empty css                     *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css               */import{T as a,aQ as t,as as u,aU as o,a4 as s,b2 as d,b4 as n,aH as r,b1 as i,aW as c,aV as p,bh as m,b3 as _,b6 as v,b7 as f,U as h,b8 as b,av as g,aO as y,ax as w,ay as V,bj as k,bk as C,aM as U,bc as x,bd as j,au as D,bM as z,bN as B,bl as A,ao as F,X as S,a6 as q,by as T,bz as Y,bR as $,Q as M,R as E}from"./element-plus-DcSKpKA8.js";import{S as H}from"./StatCard-WpSR56Tk.js";import{r as I,L,e as N,k as O,l as R,t as Q,E as P,z as W,D as X,u as G,F as J,Y as K,y as Z,A as ee,B as le}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";/* empty css                                                                 */const ae={getList:l=>e({url:"/distributor/customers",method:"get",params:l}),getDetail:l=>e({url:`/distributor/customers/${l}`,method:"get"}),create:l=>e({url:"/distributor/customers",method:"post",data:l}),update:(l,a)=>e({url:`/distributor/customers/${l}`,method:"put",data:a}),delete:l=>e({url:`/distributor/customers/${l}`,method:"delete"}),getStats:()=>e({url:"/distributor/customers/stats",method:"get"}),addFollowUp:(l,a)=>e({url:`/distributor/customers/${l}/follow-ups`,method:"post",data:a}),getFollowUps:(l,a)=>e({url:`/distributor/customers/${l}/follow-ups`,method:"get",params:a}),batchUpdateStatus:l=>e({url:"/distributor/customers/batch-status",method:"post",data:l}),export:l=>e({url:"/distributor/customers/export",method:"get",params:l,responseType:"blob"}),getNeedFollowUp:l=>e({url:"/distributor/customers/need-follow-up",method:"get",params:l}),getTags:()=>e({url:"/distributor/customers/tags",method:"get"})},te={class:"customer-management"},ue={class:"page-header"},oe={class:"header-actions"},se={class:"card-header"},de={class:"header-actions"},ne={class:"customer-name"},re={key:0,class:"customer-tags"},ie={class:"amount"},ce={key:0},pe={key:1,class:"no-contact"},me={key:0},_e={key:1,class:"no-follow-up"},ve={class:"pagination-wrapper"},fe={class:"customer-form"},he={key:0,class:"customer-detail"},be={class:"follow-up-form"},ge={class:"batch-operation"},ye={class:"help-content"},we={class:"help-section"},Ve={class:"feature-item"},ke={class:"feature-icon"},Ce={class:"feature-item"},Ue={class:"feature-icon"},xe={class:"feature-item"},je={class:"feature-icon"},De={class:"feature-item"},ze={class:"feature-icon"},Be={class:"help-section"},Ae={class:"help-section"},Fe={class:"help-section"},Se={class:"guide-content"},qe={class:"guide-content"},Te={class:"guide-content"},Ye={class:"guide-content"},$e={class:"help-section"},Me=l({__name:"CustomerManagement",setup(e){const l=ae,Me=I(!1),Ee=I(!1),He=I(!1),Ie=I(!1),Le=I(!1),Ne=I(!1),Oe=I(!1),Re=I(!1),Qe=I(!1),Pe=I(["add-customer"]),We=I([]),Xe=I([{level:"A级",color:"danger",name:"核心客户",criteria:"月消费≥5000元，订单频次高，忠诚度高",strategy:"专人对接，每周跟进，提供VIP服务"},{level:"B级",color:"warning",name:"重要客户",criteria:"月消费1000-5000元，有稳定需求",strategy:"定期维护，每两周跟进，重点培养"},{level:"C级",color:"primary",name:"潜力客户",criteria:"月消费500-1000元，有增长潜力",strategy:"适度关注，每月跟进，挖掘需求"},{level:"D级",color:"info",name:"普通客户",criteria:"月消费<500元，偶尔购买",strategy:"基础维护，季度跟进，群发营销"}]),Ge=I([{status:"活跃",color:"success",description:"近30天内有购买行为或主动联系",action:"保持现有服务水平，及时响应需求"},{status:"不活跃",color:"warning",description:"30-90天内无购买行为，但有互动",action:"主动联系了解情况，提供优惠激活"},{status:"潜在",color:"primary",description:"有意向但未成交，处于考虑阶段",action:"持续跟进，提供专业建议和方案"},{status:"流失",color:"danger",description:"超过90天无任何互动或明确拒绝",action:"尝试挽回，了解流失原因，改进服务"}]),Je=I([]),Ke=I({}),Ze=I([]),el=I([]),ll=I(null),al=I(null),tl=L({keyword:"",status:"",level:"",source:"",tag:"",need_follow_up:!1}),ul=L({current_page:1,per_page:20,total:0}),ol=L({action:"",status:"",level:"",tag:""}),sl=L({name:"",phone:"",wechat:"",level:"",company:"",remark:""}),dl={name:[{required:!0,message:"请输入客户姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],level:[{required:!0,message:"请选择客户等级",trigger:"change"}]},nl=L({content:"",method:"",next_follow_up:"",remark:""}),rl={content:[{required:!0,message:"请输入跟进内容",trigger:"blur"}],method:[{required:!0,message:"请选择跟进方式",trigger:"change"}]},il=I(),cl=I(),pl=async()=>{try{Me.value=!0;const e={page:ul.current_page,limit:ul.per_page,...tl},a=await l.getList(e);Je.value=a.data.data,ul.total=a.data.total,ul.current_page=a.data.current_page,ul.per_page=a.data.per_page}catch(e){console.error("加载客户列表失败:",e),M.error("加载客户列表失败")}finally{Me.value=!1}},ml=async()=>{try{const e=await l.getStats();Ke.value=e.data}catch(e){console.error("加载统计数据失败:",e),M.error("加载统计数据失败")}},_l=async()=>{try{const e=await l.getTags();Ze.value=e.data}catch(e){console.error("加载标签失败:",e)}},vl=function(e,l){let a;return function(...t){clearTimeout(a),a=setTimeout(()=>{clearTimeout(a),e(...t)},l)}}(()=>{ul.current_page=1,pl()},500),fl=e=>{el.value=e},hl=e=>{ll.value=e,Object.keys(sl).forEach(l=>{void 0!==e[l]&&(sl[l]=e[l])}),Le.value=!0},bl=e=>{al.value=e,Oe.value=!0},gl=async e=>{try{await E.confirm(`确定要删除客户"${e.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await l.delete(e.id),M.success("客户删除成功"),pl(),ml()}catch(a){"cancel"!==a&&(console.error("删除失败:",a),M.error("删除失败"))}},yl=async()=>{try{await il.value.validate(),He.value=!0,ll.value?(await l.update(ll.value.id,sl),M.success("客户信息更新成功")):(await l.create(sl),M.success("客户创建成功")),Vl()}catch(e){console.error("保存客户失败:",e),M.error("保存客户失败")}finally{He.value=!1}},wl=async()=>{try{await cl.value.validate(),Ie.value=!0;const e={customer_id:al.value.id,...nl};await l.addFollowUp(e),M.success("跟进记录添加成功"),kl()}catch(e){console.error("保存跟进记录失败:",e),M.error("保存跟进记录失败")}finally{Ie.value=!1}},Vl=()=>{Le.value=!1,ll.value=null,Object.keys(sl).forEach(e=>{sl[e]=""}),pl(),ml(),_l()},kl=()=>{Oe.value=!1,Object.keys(nl).forEach(e=>{nl[e]=""}),pl()},Cl=()=>{0!==el.value.length?Re.value=!0:M.warning("请先选择客户")},Ul=async()=>{if(ol.action)try{Ee.value=!0;const e=el.value.map(e=>e.id);"status"===ol.action&&await l.batchUpdateStatus({customer_ids:e,status:ol.status}),M.success("批量操作成功"),Re.value=!1,pl(),ml()}catch(e){console.error("批量操作失败:",e),M.error("批量操作失败")}finally{Ee.value=!1}else M.warning("请选择操作类型")},xl=async()=>{try{await l.export(tl);M.success("数据导出成功")}catch(e){console.error("导出失败:",e),M.error("导出失败")}},jl=e=>({A:"danger",B:"warning",C:"primary",D:"info"}[e]||"info"),Dl=e=>({active:"success",inactive:"warning",potential:"primary",lost:"danger"}[e]||"info"),zl=e=>e<0?`逾期${Math.abs(e)}天`:0===e?"今日跟进":1===e?"明日跟进":`${e}天后`,Bl=e=>new Date(e).toLocaleDateString("zh-CN");return N(()=>{pl(),ml(),_l()}),(e,l)=>{const M=a,E=u,I=d,L=n,N=r,ae=p,ml=c,_l=m,Vl=_,kl=f,Al=b,Fl=V,Sl=w,ql=g,Tl=v,Yl=C,$l=j,Ml=x,El=D,Hl=B,Il=z,Ll=A,Nl=$,Ol=Y,Rl=T,Ql=k;return R(),O("div",te,[Q("div",ue,[l[40]||(l[40]=Q("div",{class:"header-left"},[Q("h2",null,"客户管理"),Q("p",{class:"page-description"},"管理您的客户信息，跟踪客户状态，提升客户转化率")],-1)),Q("div",oe,[P(E,{type:"info",onClick:l[0]||(l[0]=e=>Qe.value=!0)},{default:W(()=>[P(M,null,{default:W(()=>[P(G(t))]),_:1}),l[37]||(l[37]=X(" 功能说明 ",-1))]),_:1,__:[37]}),P(E,{type:"primary",onClick:l[1]||(l[1]=e=>Le.value=!0)},{default:W(()=>[P(M,null,{default:W(()=>[P(G(o))]),_:1}),l[38]||(l[38]=X(" 新增客户 ",-1))]),_:1,__:[38]}),P(E,{onClick:xl},{default:W(()=>[P(M,null,{default:W(()=>[P(G(s))]),_:1}),l[39]||(l[39]=X(" 导出数据 ",-1))]),_:1,__:[39]})])]),P(L,{gutter:20,class:"stats-row"},{default:W(()=>[P(I,{span:6},{default:W(()=>[P(H,{title:"总客户数",value:Ke.value.total_customers||0,icon:"User",color:"#409EFF"},null,8,["value"])]),_:1}),P(I,{span:6},{default:W(()=>[P(H,{title:"活跃客户",value:Ke.value.active_customers||0,icon:"UserFilled",color:"#67C23A"},null,8,["value"])]),_:1}),P(I,{span:6},{default:W(()=>[P(H,{title:"潜在客户",value:Ke.value.potential_customers||0,icon:"View",color:"#E6A23C"},null,8,["value"])]),_:1}),P(I,{span:6},{default:W(()=>[P(H,{title:"需要跟进",value:Ke.value.need_follow_up||0,icon:"Bell",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),P(Vl,{class:"filter-card"},{default:W(()=>[P(L,{gutter:20},{default:W(()=>[P(I,{span:6},{default:W(()=>[P(N,{modelValue:tl.keyword,"onUpdate:modelValue":l[2]||(l[2]=e=>tl.keyword=e),placeholder:"搜索客户姓名、手机号、微信号",clearable:"",onInput:G(vl)},{prefix:W(()=>[P(M,null,{default:W(()=>[P(G(i))]),_:1})]),_:1},8,["modelValue","onInput"])]),_:1}),P(I,{span:4},{default:W(()=>[P(ml,{modelValue:tl.status,"onUpdate:modelValue":l[3]||(l[3]=e=>tl.status=e),placeholder:"客户状态",clearable:"",onChange:pl},{default:W(()=>[P(ae,{label:"活跃",value:"active"}),P(ae,{label:"不活跃",value:"inactive"}),P(ae,{label:"潜在",value:"potential"}),P(ae,{label:"流失",value:"lost"})]),_:1},8,["modelValue"])]),_:1}),P(I,{span:4},{default:W(()=>[P(ml,{modelValue:tl.level,"onUpdate:modelValue":l[4]||(l[4]=e=>tl.level=e),placeholder:"客户等级",clearable:"",onChange:pl},{default:W(()=>[P(ae,{label:"A级客户",value:"A"}),P(ae,{label:"B级客户",value:"B"}),P(ae,{label:"C级客户",value:"C"}),P(ae,{label:"D级客户",value:"D"})]),_:1},8,["modelValue"])]),_:1}),P(I,{span:4},{default:W(()=>[P(ml,{modelValue:tl.source,"onUpdate:modelValue":l[5]||(l[5]=e=>tl.source=e),placeholder:"客户来源",clearable:"",onChange:pl},{default:W(()=>[P(ae,{label:"推荐",value:"referral"}),P(ae,{label:"广告",value:"advertisement"}),P(ae,{label:"社交媒体",value:"social_media"}),P(ae,{label:"直接访问",value:"direct"}),P(ae,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),P(I,{span:3},{default:W(()=>[P(ml,{modelValue:tl.tag,"onUpdate:modelValue":l[6]||(l[6]=e=>tl.tag=e),placeholder:"客户标签",clearable:"",onChange:pl},{default:W(()=>[(R(!0),O(J,null,K(Ze.value,e=>(R(),Z(ae,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),P(I,{span:3},{default:W(()=>[P(_l,{modelValue:tl.need_follow_up,"onUpdate:modelValue":l[7]||(l[7]=e=>tl.need_follow_up=e),onChange:pl},{default:W(()=>l[41]||(l[41]=[X(" 需要跟进 ",-1)])),_:1,__:[41]},8,["modelValue"])]),_:1})]),_:1})]),_:1}),P(Vl,null,{header:W(()=>[Q("div",se,[l[44]||(l[44]=Q("span",null,"客户列表",-1)),Q("div",de,[P(E,{size:"small",onClick:Cl,disabled:0===el.value.length},{default:W(()=>l[42]||(l[42]=[X(" 批量操作 ",-1)])),_:1,__:[42]},8,["disabled"]),P(E,{size:"small",onClick:pl},{default:W(()=>[P(M,null,{default:W(()=>[P(G(U))]),_:1}),l[43]||(l[43]=X(" 刷新 ",-1))]),_:1,__:[43]})])])]),default:W(()=>[ee((R(),Z(Tl,{data:Je.value,onSelectionChange:fl,stripe:""},{default:W(()=>[P(kl,{type:"selection",width:"55"}),P(kl,{prop:"name",label:"姓名",width:"120"},{default:W(({row:e})=>[Q("div",ne,[Q("span",null,h(e.name),1),e.tags&&e.tags.length?(R(),O("div",re,[(R(!0),O(J,null,K(e.tags.slice(0,2),e=>(R(),Z(Al,{key:e,size:"small",type:"info"},{default:W(()=>[X(h(e),1)]),_:2},1024))),128))])):le("",!0)])]),_:1}),P(kl,{prop:"phone",label:"手机号",width:"130"}),P(kl,{prop:"wechat",label:"微信号",width:"120"}),P(kl,{label:"等级",width:"80"},{default:W(({row:e})=>[P(Al,{type:jl(e.level)},{default:W(()=>[X(h(e.level_text),1)]),_:2},1032,["type"])]),_:1}),P(kl,{label:"状态",width:"80"},{default:W(({row:e})=>[P(Al,{type:Dl(e.status)},{default:W(()=>[X(h(e.status_text),1)]),_:2},1032,["type"])]),_:1}),P(kl,{prop:"company",label:"公司",width:"150","show-overflow-tooltip":""}),P(kl,{label:"消费金额",width:"100"},{default:W(({row:e})=>[Q("span",ie,"¥"+h(e.total_spent),1)]),_:1}),P(kl,{prop:"order_count",label:"订单数",width:"80"}),P(kl,{label:"最后联系",width:"120"},{default:W(({row:e})=>[e.last_contact_at?(R(),O("span",ce,h(Bl(e.last_contact_at)),1)):(R(),O("span",pe,"未联系"))]),_:1}),P(kl,{label:"跟进状态",width:"120"},{default:W(({row:e})=>{return[e.next_follow_up?(R(),O("div",me,[P(Al,{type:(l=e.follow_up_days,l<0?"danger":l<=1?"warning":"success"),size:"small"},{default:W(()=>[X(h(zl(e.follow_up_days)),1)]),_:2},1032,["type"])])):(R(),O("span",_e,"无计划"))];var l}),_:1}),P(kl,{label:"操作",width:"200",fixed:"right"},{default:W(({row:e})=>[P(E,{size:"small",onClick:l=>{return a=e,al.value=a,void(Ne.value=!0);var a}},{default:W(()=>l[45]||(l[45]=[X(" 详情 ",-1)])),_:2,__:[45]},1032,["onClick"]),P(E,{size:"small",onClick:l=>bl(e)},{default:W(()=>l[46]||(l[46]=[X(" 跟进 ",-1)])),_:2,__:[46]},1032,["onClick"]),P(ql,{onCommand:l=>((e,l)=>{switch(e){case"edit":hl(l);break;case"delete":gl(l)}})(l,e)},{dropdown:W(()=>[P(Sl,null,{default:W(()=>[P(Fl,{command:"edit"},{default:W(()=>l[48]||(l[48]=[X("编辑",-1)])),_:1,__:[48]}),P(Fl,{command:"delete",divided:""},{default:W(()=>l[49]||(l[49]=[X("删除",-1)])),_:1,__:[49]})]),_:1})]),default:W(()=>[P(E,{size:"small"},{default:W(()=>[l[47]||(l[47]=X(" 更多",-1)),P(M,{class:"el-icon--right"},{default:W(()=>[P(G(y))]),_:1})]),_:1,__:[47]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[Ql,Me.value]]),Q("div",ve,[P(Yl,{"current-page":ul.current_page,"onUpdate:currentPage":l[8]||(l[8]=e=>ul.current_page=e),"page-size":ul.per_page,"onUpdate:pageSize":l[9]||(l[9]=e=>ul.per_page=e),total:ul.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pl,onCurrentChange:pl},null,8,["current-page","page-size","total"])])]),_:1}),P(El,{modelValue:Le.value,"onUpdate:modelValue":l[17]||(l[17]=e=>Le.value=e),title:ll.value?"编辑客户":"新增客户",width:"800px"},{footer:W(()=>[P(E,{onClick:l[16]||(l[16]=e=>Le.value=!1)},{default:W(()=>l[50]||(l[50]=[X("取消",-1)])),_:1,__:[50]}),P(E,{type:"primary",onClick:yl,loading:He.value},{default:W(()=>l[51]||(l[51]=[X(" 保存 ",-1)])),_:1,__:[51]},8,["loading"])]),default:W(()=>[Q("div",fe,[P(Ml,{model:sl,rules:dl,ref_key:"customerFormRef",ref:il,"label-width":"100px"},{default:W(()=>[P(L,{gutter:20},{default:W(()=>[P(I,{span:12},{default:W(()=>[P($l,{label:"客户姓名",prop:"name"},{default:W(()=>[P(N,{modelValue:sl.name,"onUpdate:modelValue":l[10]||(l[10]=e=>sl.name=e),placeholder:"请输入客户姓名"},null,8,["modelValue"])]),_:1})]),_:1}),P(I,{span:12},{default:W(()=>[P($l,{label:"手机号",prop:"phone"},{default:W(()=>[P(N,{modelValue:sl.phone,"onUpdate:modelValue":l[11]||(l[11]=e=>sl.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),P(I,{span:12},{default:W(()=>[P($l,{label:"微信号",prop:"wechat"},{default:W(()=>[P(N,{modelValue:sl.wechat,"onUpdate:modelValue":l[12]||(l[12]=e=>sl.wechat=e),placeholder:"请输入微信号"},null,8,["modelValue"])]),_:1})]),_:1}),P(I,{span:12},{default:W(()=>[P($l,{label:"客户等级",prop:"level"},{default:W(()=>[P(ml,{modelValue:sl.level,"onUpdate:modelValue":l[13]||(l[13]=e=>sl.level=e),placeholder:"选择客户等级"},{default:W(()=>[P(ae,{label:"A级客户",value:"A"}),P(ae,{label:"B级客户",value:"B"}),P(ae,{label:"C级客户",value:"C"}),P(ae,{label:"D级客户",value:"D"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),P(I,{span:24},{default:W(()=>[P($l,{label:"公司名称"},{default:W(()=>[P(N,{modelValue:sl.company,"onUpdate:modelValue":l[14]||(l[14]=e=>sl.company=e),placeholder:"请输入公司名称"},null,8,["modelValue"])]),_:1})]),_:1}),P(I,{span:24},{default:W(()=>[P($l,{label:"备注"},{default:W(()=>[P(N,{modelValue:sl.remark,"onUpdate:modelValue":l[15]||(l[15]=e=>sl.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),P(El,{modelValue:Ne.value,"onUpdate:modelValue":l[21]||(l[21]=e=>Ne.value=e),title:"客户详情",width:"1000px"},{footer:W(()=>[P(E,{onClick:l[18]||(l[18]=e=>Ne.value=!1)},{default:W(()=>l[52]||(l[52]=[X("关闭",-1)])),_:1,__:[52]}),P(E,{type:"primary",onClick:l[19]||(l[19]=e=>hl(al.value))},{default:W(()=>l[53]||(l[53]=[X(" 编辑 ",-1)])),_:1,__:[53]}),P(E,{onClick:l[20]||(l[20]=e=>bl(al.value))},{default:W(()=>l[54]||(l[54]=[X(" 添加跟进 ",-1)])),_:1,__:[54]})]),default:W(()=>[al.value?(R(),O("div",he,[P(Il,{column:3,border:""},{default:W(()=>[P(Hl,{label:"客户姓名"},{default:W(()=>[X(h(al.value.name),1)]),_:1}),P(Hl,{label:"手机号"},{default:W(()=>[X(h(al.value.phone),1)]),_:1}),P(Hl,{label:"微信号"},{default:W(()=>[X(h(al.value.wechat||"未填写"),1)]),_:1}),P(Hl,{label:"客户等级"},{default:W(()=>[P(Al,{type:jl(al.value.level)},{default:W(()=>[X(h(al.value.level_text),1)]),_:1},8,["type"])]),_:1}),P(Hl,{label:"客户状态"},{default:W(()=>[P(Al,{type:Dl(al.value.status)},{default:W(()=>[X(h(al.value.status_text),1)]),_:1},8,["type"])]),_:1}),P(Hl,{label:"公司名称"},{default:W(()=>[X(h(al.value.company||"未填写"),1)]),_:1}),P(Hl,{label:"消费金额"},{default:W(()=>[X("¥"+h(al.value.total_spent),1)]),_:1}),P(Hl,{label:"订单数量"},{default:W(()=>[X(h(al.value.order_count),1)]),_:1}),P(Hl,{label:"最后联系"},{default:W(()=>[X(h(al.value.last_contact_at?Bl(al.value.last_contact_at):"未联系"),1)]),_:1}),P(Hl,{label:"创建时间",span:"2"},{default:W(()=>[X(h(Bl(al.value.created_at)),1)]),_:1}),P(Hl,{label:"备注",span:"3"},{default:W(()=>[X(h(al.value.remark||"无"),1)]),_:1})]),_:1})])):le("",!0)]),_:1},8,["modelValue"]),P(El,{modelValue:Oe.value,"onUpdate:modelValue":l[27]||(l[27]=e=>Oe.value=e),title:"添加跟进记录",width:"600px"},{footer:W(()=>[P(E,{onClick:l[26]||(l[26]=e=>Oe.value=!1)},{default:W(()=>l[55]||(l[55]=[X("取消",-1)])),_:1,__:[55]}),P(E,{type:"primary",onClick:wl,loading:Ie.value},{default:W(()=>l[56]||(l[56]=[X(" 保存 ",-1)])),_:1,__:[56]},8,["loading"])]),default:W(()=>[Q("div",be,[P(Ml,{model:nl,rules:rl,ref_key:"followUpFormRef",ref:cl,"label-width":"100px"},{default:W(()=>[P($l,{label:"跟进内容",prop:"content"},{default:W(()=>[P(N,{modelValue:nl.content,"onUpdate:modelValue":l[22]||(l[22]=e=>nl.content=e),type:"textarea",rows:4,placeholder:"请输入跟进内容"},null,8,["modelValue"])]),_:1}),P($l,{label:"跟进方式",prop:"method"},{default:W(()=>[P(ml,{modelValue:nl.method,"onUpdate:modelValue":l[23]||(l[23]=e=>nl.method=e),placeholder:"选择跟进方式"},{default:W(()=>[P(ae,{label:"电话",value:"phone"}),P(ae,{label:"微信",value:"wechat"}),P(ae,{label:"邮件",value:"email"}),P(ae,{label:"面谈",value:"meeting"}),P(ae,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),P($l,{label:"下次跟进",prop:"next_follow_up"},{default:W(()=>[P(Ll,{modelValue:nl.next_follow_up,"onUpdate:modelValue":l[24]||(l[24]=e=>nl.next_follow_up=e),type:"datetime",placeholder:"选择下次跟进时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),P($l,{label:"备注"},{default:W(()=>[P(N,{modelValue:nl.remark,"onUpdate:modelValue":l[25]||(l[25]=e=>nl.remark=e),type:"textarea",rows:2,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"]),P(El,{modelValue:Re.value,"onUpdate:modelValue":l[33]||(l[33]=e=>Re.value=e),title:"批量操作",width:"400px"},{footer:W(()=>[P(E,{onClick:l[32]||(l[32]=e=>Re.value=!1)},{default:W(()=>l[57]||(l[57]=[X("取消",-1)])),_:1,__:[57]}),P(E,{type:"primary",onClick:Ul,loading:Ee.value},{default:W(()=>l[58]||(l[58]=[X(" 确定 ",-1)])),_:1,__:[58]},8,["loading"])]),default:W(()=>[Q("div",ge,[Q("p",null,"已选择 "+h(el.value.length)+" 个客户",1),P(Ml,{model:ol,"label-width":"80px"},{default:W(()=>[P($l,{label:"操作类型"},{default:W(()=>[P(ml,{modelValue:ol.action,"onUpdate:modelValue":l[28]||(l[28]=e=>ol.action=e),placeholder:"请选择操作"},{default:W(()=>[P(ae,{label:"更新状态",value:"status"}),P(ae,{label:"更新等级",value:"level"}),P(ae,{label:"添加标签",value:"tag"})]),_:1},8,["modelValue"])]),_:1}),"status"===ol.action?(R(),Z($l,{key:0,label:"新状态"},{default:W(()=>[P(ml,{modelValue:ol.status,"onUpdate:modelValue":l[29]||(l[29]=e=>ol.status=e),placeholder:"选择状态"},{default:W(()=>[P(ae,{label:"活跃",value:"active"}),P(ae,{label:"不活跃",value:"inactive"}),P(ae,{label:"潜在",value:"potential"}),P(ae,{label:"流失",value:"lost"})]),_:1},8,["modelValue"])]),_:1})):le("",!0),"level"===ol.action?(R(),Z($l,{key:1,label:"新等级"},{default:W(()=>[P(ml,{modelValue:ol.level,"onUpdate:modelValue":l[30]||(l[30]=e=>ol.level=e),placeholder:"选择等级"},{default:W(()=>[P(ae,{label:"A级客户",value:"A"}),P(ae,{label:"B级客户",value:"B"}),P(ae,{label:"C级客户",value:"C"}),P(ae,{label:"D级客户",value:"D"})]),_:1},8,["modelValue"])]),_:1})):le("",!0),"tag"===ol.action?(R(),Z($l,{key:2,label:"标签"},{default:W(()=>[P(N,{modelValue:ol.tag,"onUpdate:modelValue":l[31]||(l[31]=e=>ol.tag=e),placeholder:"输入标签名称"},null,8,["modelValue"])]),_:1})):le("",!0)]),_:1},8,["model"])])]),_:1},8,["modelValue"]),P(El,{modelValue:Qe.value,"onUpdate:modelValue":l[36]||(l[36]=e=>Qe.value=e),title:"客户管理功能说明",width:"900px",class:"help-dialog"},{default:W(()=>[Q("div",ye,[l[81]||(l[81]=Q("div",{class:"help-section"},[Q("h3",null,"📋 功能概述"),Q("p",null,"客户管理系统帮助您有效管理和跟踪客户信息，提升客户转化率和维护效率。通过客户分级、状态跟踪、跟进提醒等功能，让您的客户管理更加专业和高效。")],-1)),Q("div",we,[l[63]||(l[63]=Q("h3",null,"🚀 核心功能",-1)),P(L,{gutter:20},{default:W(()=>[P(I,{span:12},{default:W(()=>[Q("div",Ve,[Q("div",ke,[P(M,null,{default:W(()=>[P(G(F))]),_:1})]),l[59]||(l[59]=Q("div",{class:"feature-content"},[Q("h4",null,"客户信息管理"),Q("p",null,"完整记录客户基本信息、联系方式、公司信息等")],-1))])]),_:1}),P(I,{span:12},{default:W(()=>[Q("div",Ce,[Q("div",Ue,[P(M,null,{default:W(()=>[P(G(S))]),_:1})]),l[60]||(l[60]=Q("div",{class:"feature-content"},[Q("h4",null,"跟进提醒"),Q("p",null,"设置跟进计划，系统自动提醒，不错过任何商机")],-1))])]),_:1}),P(I,{span:12},{default:W(()=>[Q("div",xe,[Q("div",je,[P(M,null,{default:W(()=>[P(G(q))]),_:1})]),l[61]||(l[61]=Q("div",{class:"feature-content"},[Q("h4",null,"客户分级"),Q("p",null,"A/B/C/D四级分类，精准定位客户价值")],-1))])]),_:1}),P(I,{span:12},{default:W(()=>[Q("div",De,[Q("div",ze,[P(M,null,{default:W(()=>[P(G(q))]),_:1})]),l[62]||(l[62]=Q("div",{class:"feature-content"},[Q("h4",null,"数据统计"),Q("p",null,"实时统计客户数量、状态分布、转化情况")],-1))])]),_:1})]),_:1})]),Q("div",Be,[l[64]||(l[64]=Q("h3",null,"🏆 客户等级说明",-1)),P(Tl,{data:Xe.value,style:{width:"100%"}},{default:W(()=>[P(kl,{prop:"level",label:"等级",width:"80"},{default:W(({row:e})=>[P(Al,{type:e.color},{default:W(()=>[X(h(e.level),1)]),_:2},1032,["type"])]),_:1}),P(kl,{prop:"name",label:"等级名称",width:"120"}),P(kl,{prop:"criteria",label:"评定标准"}),P(kl,{prop:"strategy",label:"维护策略"})]),_:1},8,["data"])]),Q("div",Ae,[l[65]||(l[65]=Q("h3",null,"📊 客户状态说明",-1)),P(Tl,{data:Ge.value,style:{width:"100%"}},{default:W(()=>[P(kl,{prop:"status",label:"状态",width:"100"},{default:W(({row:e})=>[P(Al,{type:e.color,size:"small"},{default:W(()=>[X(h(e.status),1)]),_:2},1032,["type"])]),_:1}),P(kl,{prop:"description",label:"状态描述"}),P(kl,{prop:"action",label:"建议操作"})]),_:1},8,["data"])]),Q("div",Fe,[l[75]||(l[75]=Q("h3",null,"📝 操作指南",-1)),P(Rl,{modelValue:Pe.value,"onUpdate:modelValue":l[34]||(l[34]=e=>Pe.value=e)},{default:W(()=>[P(Ol,{title:"如何新增客户？",name:"add-customer"},{default:W(()=>[Q("div",Se,[l[67]||(l[67]=Q("ol",null,[Q("li",null,'点击页面右上角的"新增客户"按钮'),Q("li",null,"填写客户基本信息（姓名、手机号为必填项）"),Q("li",null,"选择客户等级和来源"),Q("li",null,"添加客户标签（可选）"),Q("li",null,'点击"保存"完成客户创建')],-1)),P(Nl,{type:"info",closable:!1},{default:W(()=>l[66]||(l[66]=[X(" 💡 建议：新增客户时尽量填写完整信息，有助于后续的客户管理和跟进 ",-1)])),_:1,__:[66]})])]),_:1}),P(Ol,{title:"如何设置跟进提醒？",name:"follow-up"},{default:W(()=>[Q("div",qe,[l[69]||(l[69]=Q("ol",null,[Q("li",null,"在客户列表中找到需要跟进的客户"),Q("li",null,'点击"跟进"按钮'),Q("li",null,"填写跟进内容和下次跟进时间"),Q("li",null,"选择跟进方式（电话、微信、邮件等）"),Q("li",null,"保存后系统会在指定时间提醒您")],-1)),P(Nl,{type:"warning",closable:!1},{default:W(()=>l[68]||(l[68]=[X(" ⚠️ 注意：跟进提醒会在系统消息中显示，请及时查看处理 ",-1)])),_:1,__:[68]})])]),_:1}),P(Ol,{title:"如何进行批量操作？",name:"batch-operation"},{default:W(()=>[Q("div",Te,[l[71]||(l[71]=Q("ol",null,[Q("li",null,"在客户列表中勾选需要操作的客户"),Q("li",null,'点击"批量操作"按钮'),Q("li",null,"选择操作类型（更新状态、等级或标签）"),Q("li",null,"设置新的值"),Q("li",null,"确认执行批量操作")],-1)),P(Nl,{type:"success",closable:!1},{default:W(()=>l[70]||(l[70]=[X(" ✅ 提示：批量操作可以大大提高工作效率，适用于客户状态统一更新的场景 ",-1)])),_:1,__:[70]})])]),_:1}),P(Ol,{title:"如何使用筛选功能？",name:"filter"},{default:W(()=>[Q("div",Ye,[l[73]||(l[73]=Q("p",null,[Q("strong",null,"可用筛选条件：")],-1)),l[74]||(l[74]=Q("ul",null,[Q("li",null,[Q("strong",null,"关键词搜索"),X("：支持按姓名、手机号、微信号搜索")]),Q("li",null,[Q("strong",null,"客户状态"),X("：活跃、不活跃、潜在、流失")]),Q("li",null,[Q("strong",null,"客户等级"),X("：A/B/C/D级客户")]),Q("li",null,[Q("strong",null,"客户来源"),X("：推荐、广告、社交媒体等")]),Q("li",null,[Q("strong",null,"客户标签"),X("：自定义标签筛选")]),Q("li",null,[Q("strong",null,"跟进状态"),X("：需要跟进的客户")])],-1)),P(Nl,{type:"info",closable:!1},{default:W(()=>l[72]||(l[72]=[X(" 💡 技巧：多个筛选条件可以组合使用，帮助您快速找到目标客户 ",-1)])),_:1,__:[72]})])]),_:1})]),_:1},8,["modelValue"])]),l[82]||(l[82]=Q("div",{class:"help-section"},[Q("h3",null,"💡 使用技巧"),Q("div",{class:"tips-grid"},[Q("div",{class:"tip-item"},[Q("div",{class:"tip-icon"},"🎯"),Q("div",{class:"tip-content"},[Q("h4",null,"精准分级"),Q("p",null,"根据客户消费能力和购买意向进行分级，A级客户重点维护，D级客户适度跟进")])]),Q("div",{class:"tip-item"},[Q("div",{class:"tip-icon"},"⏰"),Q("div",{class:"tip-content"},[Q("h4",null,"定期跟进"),Q("p",null,"建议A级客户每周跟进，B级客户每两周跟进，C/D级客户每月跟进")])]),Q("div",{class:"tip-item"},[Q("div",{class:"tip-icon"},"🏷️"),Q("div",{class:"tip-content"},[Q("h4",null,"标签管理"),Q("p",null,'使用标签标记客户特征，如"高价值"、"决策者"、"价格敏感"等')])]),Q("div",{class:"tip-item"},[Q("div",{class:"tip-icon"},"📊"),Q("div",{class:"tip-content"},[Q("h4",null,"数据分析"),Q("p",null,"定期查看客户统计数据，分析客户结构和转化趋势")])])])],-1)),Q("div",$e,[l[80]||(l[80]=Q("h3",null,"❓ 常见问题",-1)),P(Rl,{modelValue:We.value,"onUpdate:modelValue":l[35]||(l[35]=e=>We.value=e)},{default:W(()=>[P(Ol,{title:"为什么我的客户状态没有自动更新？",name:"faq1"},{default:W(()=>l[76]||(l[76]=[Q("p",null,"客户状态需要手动更新或通过系统规则自动判断。建议定期检查客户最后联系时间和订单情况，及时更新状态。",-1)])),_:1,__:[76]}),P(Ol,{title:"如何批量导入客户信息？",name:"faq2"},{default:W(()=>l[77]||(l[77]=[Q("p",null,'点击"批量导入"按钮，下载模板文件，按照模板格式填写客户信息后上传。支持Excel格式文件。',-1)])),_:1,__:[77]}),P(Ol,{title:"客户等级可以自动调整吗？",name:"faq3"},{default:W(()=>l[78]||(l[78]=[Q("p",null,"目前客户等级需要手动调整。系统会根据客户的消费金额和订单数量提供等级建议，您可以参考后手动调整。",-1)])),_:1,__:[78]}),P(Ol,{title:"跟进提醒在哪里查看？",name:"faq4"},{default:W(()=>l[79]||(l[79]=[Q("p",null,'跟进提醒会在系统消息中显示，同时在客户列表的"跟进状态"列中也会显示提醒信息。',-1)])),_:1,__:[79]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-f8690795"]]);export{Me as default};
