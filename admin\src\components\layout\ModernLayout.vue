<template>
  <div class="modern-admin-layout">
    <!-- 现代化侧边栏 -->
    <aside class="modern-sidebar" :class="{ 'collapsed': isCollapsed }">
      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#3b82f6"/>
                  <stop offset="100%" style="stop-color:#8b5cf6"/>
                </linearGradient>
              </defs>
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" 
                    stroke="url(#logoGradient)" 
                    stroke-width="2" 
                    fill="none" 
                    stroke-linecap="round" 
                    stroke-linejoin="round"/>
            </svg>
          </div>
          <transition name="fade">
            <div v-if="!isCollapsed" class="logo-text">
              <h1>LinkHub Pro</h1>
              <p>晨鑫流量变现系统</p>
            </div>
          </transition>
        </div>
      </div>

      <!-- 用户信息卡片 -->
      <transition name="slide-fade">
        <div v-if="!isCollapsed" class="user-profile-card">
          <div class="user-avatar">
            <img :src="userStore.avatar || '/default-avatar.png'" alt="用户头像" />
            <div class="status-indicator online"></div>
          </div>
          <div class="user-info">
            <div class="user-name">{{ userStore.nickname || '管理员' }}</div>
            <div class="user-role">{{ getRoleDisplayName(userStore.userInfo?.role) }}</div>
          </div>
          <div class="user-actions">
            <el-dropdown trigger="click">
              <el-button text class="user-menu-btn">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="$router.push('/user/profile')">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item @click="$router.push('/user/settings')">
                    <el-icon><Setting /></el-icon>
                    账户设置
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </transition>

      <!-- 导航菜单 -->
      <nav class="navigation-menu">
        <el-scrollbar class="menu-scrollbar">
          <el-menu
            :default-active="activeRoute"
            :collapse="isCollapsed"
            :unique-opened="true"
            background-color="transparent"
            text-color="rgba(255, 255, 255, 0.8)"
            active-text-color="#ffffff"
            class="sidebar-menu"
            router
          >
            <ModernMenuItem 
              v-for="route in filteredRoutes" 
              :key="route.path" 
              :item="route" 
              :base-path="route.path"
              :collapsed="isCollapsed"
            />
          </el-menu>
        </el-scrollbar>
      </nav>

      <!-- 底部信息 -->
      <div class="sidebar-footer">
        <transition name="fade">
          <div v-if="!isCollapsed" class="system-status">
            <div class="status-item">
              <div class="status-dot success"></div>
              <span>系统运行正常</span>
            </div>
            <div class="version-info">v2.0.1</div>
          </div>
        </transition>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content" :class="{ 'expanded': isCollapsed }">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <el-button 
            text 
            class="collapse-btn"
            @click="toggleSidebar"
          >
            <el-icon size="20">
              <Fold v-if="!isCollapsed" />
              <Expand v-else />
            </el-icon>
          </el-button>

          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb-nav">
            <el-breadcrumb-item :to="{ path: '/dashboard' }">
              <el-icon><HomeFilled /></el-icon>
              控制台
            </el-breadcrumb-item>
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
              {{ item.meta.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 搜索框 -->
          <div class="search-container">
            <el-input
              v-model="searchQuery"
              placeholder="搜索功能、用户、订单..."
              prefix-icon="Search"
              class="search-input"
              @keyup.enter="handleSearch"
            />
          </div>

          <!-- 功能按钮组 -->
          <div class="header-actions">
            <!-- 通知中心 -->
            <el-badge :value="notificationCount" :hidden="notificationCount === 0">
              <el-button text class="action-btn" @click="showNotifications = true">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>

            <!-- 全屏切换 -->
            <el-button text class="action-btn" @click="toggleFullscreen">
              <el-icon><FullScreen /></el-icon>
            </el-button>

            <!-- 主题切换 -->
            <el-button text class="action-btn" @click="toggleTheme">
              <el-icon><Sunny v-if="isDarkMode" /><Moon v-else /></el-icon>
            </el-button>

            <!-- 刷新页面 -->
            <el-button text class="action-btn" @click="refreshPage">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>

          <!-- 用户菜单 -->
          <div class="user-menu">
            <el-dropdown trigger="click">
              <div class="user-trigger">
                <el-avatar :src="userStore.avatar" size="small">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ userStore.nickname || '管理员' }}</span>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="user-dropdown-menu">
                  <div class="user-info-header">
                    <el-avatar :src="userStore.avatar" size="large">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <div class="user-details">
                      <div class="name">{{ userStore.nickname || '管理员' }}</div>
                      <div class="role">{{ getRoleDisplayName(userStore.userInfo?.role) }}</div>
                    </div>
                  </div>
                  <el-divider style="margin: 12px 0;" />
                  <el-dropdown-item @click="$router.push('/user/profile')">
                    <el-icon><User /></el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item @click="$router.push('/user/settings')">
                    <el-icon><Setting /></el-icon>
                    账户设置
                  </el-dropdown-item>
                  <el-dropdown-item @click="$router.push('/system/help')">
                    <el-icon><QuestionFilled /></el-icon>
                    帮助中心
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-content">
        <router-view v-slot="{ Component, route }">
          <transition name="page-transition" mode="out-in">
            <keep-alive>
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </main>

    <!-- 通知中心抽屉 -->
    <NotificationDrawer v-model="showNotifications" />

    <!-- 快捷键帮助 -->
    <ShortcutHelp v-model="showShortcuts" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, Setting, SwitchButton, HomeFilled, Bell, FullScreen, 
  Refresh, Search, ArrowDown, Fold, Expand, MoreFilled,
  Sunny, Moon, QuestionFilled, Monitor, DataLine, Comment,
  UserFilled, Document, Share, Grid, Avatar, Money, List,
  OfficeBuilding, Lock, Connection, Tools,
  Edit, DocumentCopy, MagicStick, Key, Link, Tickets,
  TrendCharts, Upload, Download, Goods, Medal, Cpu,
  InfoFilled, CreditCard, Folder, View
} from '@element-plus/icons-vue'
import ModernMenuItem from './ModernMenuItem.vue'
import NotificationDrawer from '@/components/NotificationDrawer.vue'
import ShortcutHelp from '@/components/ShortcutHelp.vue'
import { filterRoutesByRole, getRoleDisplayName } from '@/config/navigation'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isCollapsed = ref(false)
const searchQuery = ref('')
const showNotifications = ref(false)
const showShortcuts = ref(false)
const notificationCount = ref(3)
const isDarkMode = ref(false)

// 计算属性
const activeRoute = computed(() => route.path)

const breadcrumbs = computed(() => {
  return route.matched.filter(item => 
    item.meta && item.meta.title && item.path !== '/dashboard'
  )
})

const filteredRoutes = computed(() => {
  const userRole = userStore.userInfo?.role
  console.log('🔍 导航过滤调试:', {
    userInfo: userStore.userInfo,
    userRole,
    hasToken: !!userStore.token
  })

  if (!userRole) {
    console.log('⚠️ 用户角色为空，返回空路由')
    return []
  }

  const allRoutes = router.options.routes.filter(route => {
    return route.path !== '/login' &&
           route.path !== '/404' &&
           route.path !== '/403' &&
           route.path !== '/' &&
           !route.meta?.hidden
  })

  console.log('📋 所有可用路由:', allRoutes.map(r => ({ path: r.path, title: r.meta?.title })))

  const filtered = filterRoutesByRole(allRoutes, userRole)
  console.log('✅ 过滤后的路由:', filtered.map(r => ({ path: r.path, title: r.meta?.title })))

  return filtered
})

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark', isDarkMode.value)
}

const refreshPage = () => {
  window.location.reload()
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // 实现搜索功能
    console.log('搜索:', searchQuery.value)
  }
}

const handleLogout = () => {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '确认退出',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await userStore.logout()
      ElMessage.success('退出登录成功')
      router.push('/login')
    } catch (error) {
      ElMessage.error('退出登录失败')
    }
  })
}

// 快捷键处理
const handleKeydown = (e) => {
  // Ctrl/Cmd + K 打开搜索
  if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
    e.preventDefault()
    document.querySelector('.search-input input')?.focus()
  }
  
  // Ctrl/Cmd + B 切换侧边栏
  if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
    e.preventDefault()
    toggleSidebar()
  }
  
  // F11 全屏
  if (e.key === 'F11') {
    e.preventDefault()
    toggleFullscreen()
  }
  
  // ? 显示快捷键帮助
  if (e.key === '?' && !e.ctrlKey && !e.metaKey) {
    showShortcuts.value = true
  }
}

// 监听用户信息变化
watch(() => userStore.userInfo, (newUserInfo) => {
  console.log('👤 用户信息变化:', newUserInfo)
  if (newUserInfo) {
    console.log('✅ 用户信息已设置，角色:', newUserInfo.role)
  }
}, { immediate: true, deep: true })

// 监听预览模式状态
watch(() => localStorage.getItem('preview-mode'), (previewMode) => {
  console.log('🎭 预览模式状态变化:', previewMode)
  if (previewMode === 'true' && !userStore.userInfo) {
    console.log('⚠️ 预览模式已启用但用户信息未设置，尝试手动设置')
    // 手动设置预览用户信息
    userStore.setUserInfo({
      id: 'preview-user',
      username: 'admin',
      nickname: '超级管理员 (预览)',
      name: '预览用户',
      email: '<EMAIL>',
      avatar: '/default-avatar.png',
      role: 'admin',
      roles: ['admin'],
      permissions: ['*']
    })
  }
}, { immediate: true })

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)

  // 检查预览模式状态
  const isPreviewMode = localStorage.getItem('preview-mode') === 'true'
  console.log('🔍 组件挂载时检查:', {
    isPreviewMode,
    hasUserInfo: !!userStore.userInfo,
    userRole: userStore.userInfo?.role
  })
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.modern-admin-layout {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow: hidden;
}

// 现代化侧边栏
.modern-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 4px 0 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 100;
  display: flex;
  flex-direction: column;

  &.collapsed {
    width: 80px;
  }

  // Logo区域
  .logo-section {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);

    .logo-container {
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);
        transition: all 0.3s ease;

        svg {
          width: 24px;
          height: 24px;
          color: white;
        }

        &:hover {
          transform: translateY(-2px) scale(1.05);
          box-shadow: 0 12px 40px rgba(59, 130, 246, 0.6);
        }
      }

      .logo-text {
        h1 {
          color: #ffffff;
          font-size: 18px;
          font-weight: 700;
          margin: 0 0 4px 0;
          background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        p {
          color: rgba(255, 255, 255, 0.6);
          font-size: 12px;
          margin: 0;
          font-weight: 500;
        }
      }
    }
  }

  // 用户信息卡片
  .user-profile-card {
    margin: 20px 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-2px);
    }

    .user-avatar {
      position: relative;
      margin-right: 12px;

      img {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        object-fit: cover;
      }

      .status-indicator {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #1e293b;

        &.online {
          background: #10b981;
        }
      }
    }

    .user-info {
      flex: 1;

      .user-name {
        color: #ffffff;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 2px;
      }

      .user-role {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
      }
    }

    .user-actions {
      .user-menu-btn {
        color: rgba(255, 255, 255, 0.6);
        
        &:hover {
          color: #ffffff;
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  // 导航菜单
  .navigation-menu {
    flex: 1;
    overflow: hidden;

    .menu-scrollbar {
      height: 100%;

      :deep(.el-scrollbar__view) {
        padding: 0 16px 20px 16px;
      }
    }

    .sidebar-menu {
      border: none;
      background: transparent;
    }
  }

  // 底部信息
  .sidebar-footer {
    padding: 16px 20px 20px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .system-status {
      .status-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 8px;

          &.success {
            background: #10b981;
            animation: pulse 2s infinite;
          }
        }
      }

      .version-info {
        text-align: center;
        color: rgba(255, 255, 255, 0.5);
        font-size: 11px;
        font-weight: 500;
      }
    }
  }
}

// 主内容区域
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &.expanded {
    margin-left: 0;
  }
}

// 顶部导航栏
.top-header {
  height: 70px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: relative;
  z-index: 90;

  .header-left {
    display: flex;
    align-items: center;

    .collapse-btn {
      width: 44px;
      height: 44px;
      border-radius: 12px;
      margin-right: 20px;
      color: #64748b;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
        color: #3b82f6;
        transform: scale(1.05);
      }
    }

    .breadcrumb-nav {
      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          display: flex;
          align-items: center;
          color: #64748b;
          font-weight: 500;
          transition: color 0.3s ease;

          .el-icon {
            margin-right: 6px;
          }

          &:hover {
            color: #3b82f6;
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: #1e293b;
          font-weight: 600;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .search-container {
      .search-input {
        width: 300px;

        :deep(.el-input__wrapper) {
          background: rgba(248, 250, 252, 0.8);
          border: 1px solid #e2e8f0;
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #3b82f6;
            background: rgba(255, 255, 255, 0.9);
          }

          &.is-focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        color: #64748b;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
          color: #3b82f6;
          transform: translateY(-1px);
        }
      }
    }

    .user-menu {
      .user-trigger {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
        }

        .username {
          margin: 0 8px;
          font-weight: 600;
          color: #1e293b;
        }

        .dropdown-icon {
          color: #64748b;
          transition: transform 0.3s ease;
        }

        &:hover .dropdown-icon {
          transform: rotate(180deg);
        }
      }
    }
  }
}

// 页面内容
.page-content {
  flex: 1;
  overflow-y: auto;
  background: transparent;
  min-height: calc(100vh - 70px); // 减去顶部导航栏高度
  position: relative;
  
  // 确保内容填充整个区域
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.1) 0%, 
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    pointer-events: none;
    z-index: -1;
  }
  
  // 添加装饰性元素
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: -1;
    animation: float 6s ease-in-out infinite;
  }
  
  // 滚动条样式优化
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 4px;
    transition: background 0.3s ease;
    
    &:hover {
      background: rgba(59, 130, 246, 0.5);
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(20px) scale(0.98);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px) scale(0.98);
}

// 脉冲动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 浮动动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-20px) translateX(10px);
  }
  66% {
    transform: translateY(10px) translateX(-10px);
  }
}

// 用户下拉菜单样式
:deep(.user-dropdown-menu) {
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  min-width: 240px;

  .user-info-header {
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;

    .user-details {
      margin-left: 12px;

      .name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 2px;
      }

      .role {
        font-size: 12px;
        color: #64748b;
      }
    }
  }

  .el-dropdown-menu__item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    font-weight: 500;
    transition: all 0.3s ease;

    .el-icon {
      margin-right: 12px;
      font-size: 16px;
    }

    &:hover {
      background: #f1f5f9;
      color: #3b82f6;
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .modern-sidebar {
    width: 80px;

    &.collapsed {
      width: 0;
      transform: translateX(-100%);
    }
  }

  .search-container {
    display: none;
  }
}

@media (max-width: 768px) {
  .top-header {
    padding: 0 16px;

    .header-actions {
      gap: 4px;

      .action-btn {
        width: 36px;
        height: 36px;
      }
    }
  }

  .breadcrumb-nav {
    display: none;
  }
}
</style>