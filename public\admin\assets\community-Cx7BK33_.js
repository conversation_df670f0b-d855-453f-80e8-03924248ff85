import{c as e}from"./index-D2bI4m-v.js";import{f as t}from"./chunk-KZPPZA2C-BZQYgWVq.js";const a=(e=>{const a=[],r=["startup","finance","tech","education","other"],s=["active","paused","full","pending"];for(let o=1;o<=e;o++)a.push({id:o,name:t.company.name()+"交流群",description:t.lorem.sentence(5),avatar:t.image.avatar(),owner_name:t.person.fullName(),owner_role:"群主",category:t.helpers.arrayElement(r),price:t.helpers.arrayElement([0,0,0,9.9,19.9,29.9]),current_members:t.number.int({min:50,max:500}),max_members:500,health_score:t.number.int({min:30,max:100}),status:t.helpers.arrayElement(s),created_at:t.date.past().toISOString()});return a})(123),r={getGroupList(e){const{page:t=1,limit:r=10,keyword:s,status:o,category:i}=e;let n=a.filter(e=>{let t=!0;return!s||e.name.includes(s)||(e.owner_name||"").includes(s)||(t=!1),o&&e.status!==o&&(t=!1),i&&e.category!==i&&(t=!1),t});const c=(t-1)*r,u=t*r,m=n.slice(c,u);return Promise.resolve({code:0,data:{list:m,total:n.length},message:"成功"})},getGroupStats:()=>Promise.resolve({code:0,data:{total_groups:a.length,active_groups:a.filter(e=>"active"===e.status).length,total_members:a.reduce((e,t)=>e+t.current_members,0),total_revenue:a.reduce((e,t)=>e+t.price*t.current_members,0)/100},message:"成功"}),getGroupDetail(e){const t=a.find(t=>t.id===e);return Promise.resolve({code:0,data:t||null,message:"成功"})},deleteGroup(e){const t=a.findIndex(t=>t.id===e);return-1!==t&&a.splice(t,1),Promise.resolve({code:0,message:"删除成功"})},updateGroupStatus(e,t){const r=a.find(t=>t.id===e);return r&&(r.status=t),Promise.resolve({code:0,message:"状态更新成功"})},getTemplates(e={}){const{page:t=1,per_page:a=10,keyword:r,category:s,is_preset:o,is_active:i}=e;let n=[{id:1,template_name:"高端商务群模板",template_code:"BUSINESS_001",description:"适用于商务交流、项目合作等高端群组",category:"business",category_name:"商务类",is_preset:!0,is_active:!0,usage_count:156,cover_image_url:"https://picsum.photos/200/120?random=1",creator:{username:"系统"},created_at:"2024-01-15T10:00:00Z",template_data:{title:"{{city}}商务精英交流群",description:"汇聚{{city}}地区商务精英，分享商机，共创未来",price:99,member_limit:500,virtual_members:328,virtual_orders:89,virtual_income:15680.5},sort_order:100},{id:2,template_name:"副业赚钱群模板",template_code:"SIDEJOB_001",description:"专为副业创业者设计的群组模板",category:"finance",category_name:"财经类",is_preset:!0,is_active:!0,usage_count:289,cover_image_url:"https://picsum.photos/200/120?random=2",creator:{username:"系统"},created_at:"2024-01-10T14:30:00Z",template_data:{title:"{{city}}副业赚钱交流群",description:"分享副业项目，交流赚钱经验，实现财务自由",price:58,member_limit:300,virtual_members:267,virtual_orders:156,virtual_income:8960},sort_order:90},{id:3,template_name:"学习成长群模板",template_code:"STUDY_001",description:"知识分享、技能提升类群组模板",category:"education",category_name:"教育类",is_preset:!1,is_active:!0,usage_count:78,cover_image_url:"https://picsum.photos/200/120?random=3",creator:{username:"admin"},created_at:"2024-01-20T09:15:00Z",template_data:{title:"{{city}}学习成长交流群",description:"一起学习，共同成长，分享知识与经验",price:29,member_limit:200,virtual_members:145,virtual_orders:67,virtual_income:1943},sort_order:80},{id:4,template_name:"健身运动群模板",template_code:"FITNESS_001",description:"健身爱好者交流群组模板",category:"lifestyle",category_name:"生活类",is_preset:!1,is_active:!0,usage_count:45,cover_image_url:"https://picsum.photos/200/120?random=4",creator:{username:"admin"},created_at:"2024-01-25T16:45:00Z",template_data:{title:"{{city}}健身运动交流群",description:"分享健身经验，制定运动计划，一起变得更健康",price:39,member_limit:150,virtual_members:89,virtual_orders:34,virtual_income:1326},sort_order:70}];r&&(n=n.filter(e=>e.template_name.includes(r)||e.description.includes(r)||e.template_code.includes(r))),s&&(n=n.filter(e=>e.category===s)),"boolean"==typeof o&&(n=n.filter(e=>e.is_preset===o)),"boolean"==typeof i&&(n=n.filter(e=>e.is_active===i));const c=n.length,u=(t-1)*a,m=u+a,d=n.slice(u,m);return Promise.resolve({code:200,data:{data:d,total:c,current_page:t,per_page:a,last_page:Math.ceil(c/a)},message:"获取成功"})},getTemplate(e){return this.getTemplates().then(t=>{const a=t.data.data.find(t=>t.id==e);return a?Promise.resolve({code:200,data:a,message:"获取成功"}):Promise.resolve({code:404,data:null,message:"模板不存在"})})},getTemplateCategories:()=>Promise.resolve({code:200,data:{business:"商务类",finance:"财经类",education:"教育类",lifestyle:"生活类",entertainment:"娱乐类",technology:"科技类"},message:"获取成功"}),createTemplate(e){const t={id:Date.now(),...e,usage_count:0,creator:{username:"admin"},created_at:(new Date).toISOString(),is_preset:!1};return Promise.resolve({code:200,data:t,message:"创建成功"})},updateTemplate:(e,t)=>Promise.resolve({code:200,data:{id:e,...t},message:"更新成功"}),deleteTemplate:e=>Promise.resolve({code:200,data:null,message:"删除成功"}),copyTemplate(e){return this.getTemplate(e).then(e=>{if(200===e.code){const t=e.data,a={...t,id:Date.now(),template_name:t.template_name+" (副本)",usage_count:0,created_at:(new Date).toISOString(),is_preset:!1};return Promise.resolve({code:200,data:a,message:"复制成功"})}return e})},toggleTemplateStatus:(e,t)=>Promise.resolve({code:200,data:{id:e,is_active:t},message:"状态更新成功"}),getMarketingConfigs(e={}){const{page:t=1,per_page:a=10,keyword:r,type:s,status:o}=e;let i=[{id:1,name:"春节营销活动",description:"春节期间的特殊营销配置",type:"festival",status:"active",start_date:"2024-02-01",end_date:"2024-02-29",config_data:{discount_rate:20,bonus_points:100,special_gifts:["红包","优惠券"],landing_page_theme:"festival"},usage_count:45,created_at:"2024-01-15T10:00:00Z"},{id:2,name:"新用户引流配置",description:"针对新用户的营销策略配置",type:"newuser",status:"active",start_date:"2024-01-01",end_date:"2024-12-31",config_data:{welcome_bonus:50,first_purchase_discount:15,referral_reward:30,trial_period:7},usage_count:128,created_at:"2024-01-10T14:30:00Z"},{id:3,name:"高价值用户留存",description:"针对高价值用户的留存营销配置",type:"retention",status:"active",start_date:"2024-01-01",end_date:"2024-12-31",config_data:{vip_discount:25,exclusive_content:!0,priority_support:!0,monthly_gifts:["会员专享资料","VIP群邀请"]},usage_count:67,created_at:"2024-01-20T09:15:00Z"}];r&&(i=i.filter(e=>e.name.includes(r)||e.description.includes(r))),s&&(i=i.filter(e=>e.type===s)),o&&(i=i.filter(e=>e.status===o));const n=i.length,c=(t-1)*a,u=c+a,m=i.slice(c,u);return Promise.resolve({code:200,data:{data:m,total:n,current_page:t,per_page:a,last_page:Math.ceil(n/a)},message:"获取成功"})},getMarketingConfig(e){return this.getMarketingConfigs().then(t=>{const a=t.data.data.find(t=>t.id==e);return a?Promise.resolve({code:200,data:a,message:"获取成功"}):Promise.resolve({code:404,data:null,message:"配置不存在"})})},createMarketingConfig(e){const t={id:Date.now(),...e,usage_count:0,created_at:(new Date).toISOString()};return Promise.resolve({code:200,data:t,message:"创建成功"})},updateMarketingConfig:(e,t)=>Promise.resolve({code:200,data:{id:e,...t},message:"更新成功"}),deleteMarketingConfig:e=>Promise.resolve({code:200,data:null,message:"删除成功"})};function s(e){return r.getGroupList(e)}function o(){return r.getGroupStats()}function i(e){return r.createGroup(e)}function n(t,a){return e({url:`/admin/groups/${t}`,method:"put",data:a})}function c(e){return r.deleteGroup(e)}function u(e,t){return r.updateGroupStatus(e,t)}function m(e){return r.exportGroups(e)}function d(e){return r.getTemplates(e)}function l(e){return r.getTemplate(e)}function p(e){return r.createTemplate(e)}function _(e,t){return r.updateTemplate(e,t)}function g(e){return r.deleteTemplate(e)}function f(){return r.getTemplateCategories()}function v(e,t){return r.toggleTemplateStatus(e,t)}function y(e,t){return r.getGroupContent(e,t)}function h(e,t){return r.updateGroupContent(e,t)}export{h as a,s as b,i as c,o as d,m as e,c as f,y as g,u as h,d as i,f as j,l as k,g as l,_ as m,p as n,v as t,n as u};
