# 群组营销功能优化建议

## 1. 数据库字段扩展

### 在 WechatGroup 模型中添加缺失字段：

```php
// 在 $fillable 数组中添加
'read_count_display',      // 阅读数显示文本（如"10万+"）
'like_count',              // 点赞数
'want_see_count',          // 想看数  
'button_title',            // 入群按钮文案
'group_intro_title',       // 群简介标题
'group_intro_content',     // 群简介内容
'faq_title',              // 常见问题标题
'faq_content',            // 常见问题内容（格式：问题----答案）
'member_reviews',         // 群友评论（格式：评论----点赞数）
'customer_service_qr',    // 客服二维码
'ad_qr_code',            // 广告二维码
'show_customer_service',  // 是否显示客服信息
'customer_service_avatar', // 客服头像
'customer_service_title', // 客服标题
'customer_service_desc',  // 客服描述
'avatar_library',         // 头像库选择（qq/za）
'wx_accessible',          // 微信浏览器访问控制
'display_type',           // 展示类型（1=文字+图片，2=纯图片）
```

## 2. 控制器功能增强

### 在 WechatGroupController 中添加营销配置方法：

```php
/**
 * 更新群组营销配置
 */
public function updateMarketingConfig(Request $request, $id)
{
    $group = WechatGroup::findOrFail($id);
    
    $validator = Validator::make($request->all(), [
        'read_count_display' => 'nullable|string|max:20',
        'like_count' => 'nullable|integer|min:0',
        'want_see_count' => 'nullable|integer|min:0',
        'button_title' => 'nullable|string|max:100',
        'group_intro_title' => 'nullable|string|max:60',
        'group_intro_content' => 'nullable|string',
        'faq_title' => 'nullable|string|max:60',
        'faq_content' => 'nullable|string',
        'member_reviews' => 'nullable|string',
        'avatar_library' => 'nullable|string|in:qq,za',
        'wx_accessible' => 'nullable|integer|in:1,2',
        'display_type' => 'nullable|integer|in:1,2',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'success' => false, 
            'message' => $validator->errors()->first()
        ], 422);
    }

    $group->update($request->all());

    return response()->json([
        'success' => true,
        'message' => '营销配置更新成功',
        'data' => $group
    ]);
}

/**
 * 生成虚拟群友数据
 */
public function generateVirtualMembers(Request $request, $id)
{
    $group = WechatGroup::findOrFail($id);
    
    $count = $request->input('count', 13);
    $avatarType = $group->avatar_library ?? 'qq';
    
    $members = $group->generateVirtualMembers($count);
    
    return response()->json([
        'success' => true,
        'data' => $members
    ]);
}
```

## 3. 前端界面优化

### 添加营销配置标签页：

```vue
<template>
  <el-tabs v-model="activeTab">
    <el-tab-pane label="基础设置" name="basic">
      <!-- 现有的基础设置 -->
    </el-tab-pane>
    
    <el-tab-pane label="营销展示" name="marketing">
      <el-form :model="marketingForm" label-width="120px">
        <el-form-item label="阅读数显示">
          <el-input v-model="marketingForm.read_count_display" placeholder="如：10万+" />
        </el-form-item>
        
        <el-form-item label="点赞数">
          <el-input-number v-model="marketingForm.like_count" :min="0" />
        </el-form-item>
        
        <el-form-item label="想看数">
          <el-input-number v-model="marketingForm.want_see_count" :min="0" />
        </el-form-item>
        
        <el-form-item label="入群按钮文案">
          <el-input v-model="marketingForm.button_title" placeholder="如：立即加入群聊" />
        </el-form-item>
        
        <el-form-item label="头像库选择">
          <el-select v-model="marketingForm.avatar_library">
            <el-option label="QQ风格（扩列交友）" value="qq" />
            <el-option label="综合随机" value="za" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="微信访问控制">
          <el-radio-group v-model="marketingForm.wx_accessible">
            <el-radio :label="1">微信能打开</el-radio>
            <el-radio :label="2">微信内不能打开</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    
    <el-tab-pane label="内容设置" name="content">
      <el-form :model="contentForm" label-width="120px">
        <el-form-item label="群简介标题">
          <el-input v-model="contentForm.group_intro_title" placeholder="如：群简介" />
        </el-form-item>
        
        <el-form-item label="群简介内容">
          <el-input type="textarea" v-model="contentForm.group_intro_content" :rows="4" />
        </el-form-item>
        
        <el-form-item label="常见问题标题">
          <el-input v-model="contentForm.faq_title" placeholder="如：常见问题" />
        </el-form-item>
        
        <el-form-item label="常见问题内容">
          <el-input type="textarea" v-model="contentForm.faq_content" :rows="6" 
                    placeholder="格式：问题1----答案1&#10;问题2----答案2" />
        </el-form-item>
        
        <el-form-item label="群友评论">
          <el-input type="textarea" v-model="contentForm.member_reviews" :rows="6"
                    placeholder="格式：评论内容1----点赞数1&#10;评论内容2----点赞数2" />
        </el-form-item>
      </el-form>
    </el-tab-pane>
    
    <el-tab-pane label="客服广告" name="service">
      <el-form :model="serviceForm" label-width="120px">
        <el-form-item label="显示客服信息">
          <el-radio-group v-model="serviceForm.show_customer_service">
            <el-radio :label="1">不显示</el-radio>
            <el-radio :label="2">显示</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="客服头像" v-if="serviceForm.show_customer_service === 2">
          <image-upload v-model="serviceForm.customer_service_avatar" />
        </el-form-item>
        
        <el-form-item label="客服标题" v-if="serviceForm.show_customer_service === 2">
          <el-input v-model="serviceForm.customer_service_title" placeholder="如：VIP专属客服" />
        </el-form-item>
        
        <el-form-item label="客服描述" v-if="serviceForm.show_customer_service === 2">
          <el-input v-model="serviceForm.customer_service_desc" 
                    placeholder="如：出现不能付款，不能入群等问题，请联系我！" />
        </el-form-item>
        
        <el-form-item label="客服二维码">
          <image-upload v-model="serviceForm.customer_service_qr" />
        </el-form-item>
        
        <el-form-item label="广告二维码">
          <image-upload v-model="serviceForm.ad_qr_code" />
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>
```

## 4. 模型方法增强

### 在 WechatGroup 模型中添加营销相关方法：

```php
/**
 * 获取格式化的FAQ内容
 */
public function getFormattedFaqAttribute(): array
{
    if (!$this->faq_content) {
        return [];
    }

    $faqs = [];
    $lines = explode("\n", $this->faq_content);
    
    foreach ($lines as $line) {
        if (strpos($line, '----') !== false) {
            $parts = explode('----', $line, 2);
            if (count($parts) == 2) {
                $faqs[] = [
                    'question' => trim($parts[0]),
                    'answer' => trim($parts[1])
                ];
            }
        }
    }

    return $faqs;
}

/**
 * 获取格式化的群友评论
 */
public function getFormattedMemberReviewsAttribute(): array
{
    if (!$this->member_reviews) {
        return [];
    }

    $reviews = [];
    $lines = explode("\n", $this->member_reviews);
    
    foreach ($lines as $line) {
        if (strpos($line, '----') !== false) {
            $parts = explode('----', $line, 2);
            if (count($parts) == 2) {
                $reviews[] = [
                    'content' => trim($parts[0]),
                    'likes' => intval(trim($parts[1]))
                ];
            }
        }
    }

    return $reviews;
}

/**
 * 生成虚拟成员数据（兼容源码包格式）
 */
public function generateVirtualMembers(int $count = 13): array
{
    $nicknames = [
        '最美的太阳花', '孤海的浪漫', '薰衣草', '木槿，花', '森林小巷少女与狐@',
        '冬日暖阳', '午後の夏天', '嘴角的美人痣。', '朽梦挽歌', '心淡然',
        '歇斯底里的狂笑', '夏，好温暖', '彼岸花开', '岸与海的距离', '猫味萝莉',
        '软甜阮甜', '枯酒无味', '寄个拥抱', '少女病', '江南酒馆',
        '淡尘轻烟', '过气软妹', '檬℃柠叶', '仙九', '且听',
        '风铃', '野性_萌', '樱桃小丸子', '少女の烦躁期', '无名小姐',
        '香味少女', '清澈的眼眸', '海草舞蹈', '淡淡de茶香味', '雨后彩虹',
        '安全等待你来', '薄荷蓝', '指尖上的星空', '雲朵兒', '准风月谈', '柠檬'
    ];

    $members = [];
    for ($i = 0; $i < $count; $i++) {
        $avatar_num = rand(1, 41);
        $avatar_ext = $this->avatar_library == 'qq' ? 'jpg' : 'jpeg';
        
        $members[] = [
            'nickname' => $nicknames[array_rand($nicknames)],
            'avatar' => "/face/{$this->avatar_library}/{$avatar_num}.{$avatar_ext}",
            'join_time' => now()->subDays(rand(1, 30))->format('Y-m-d H:i')
        ];
    }

    return $members;
}
```