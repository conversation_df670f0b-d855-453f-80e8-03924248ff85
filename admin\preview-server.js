import { createServer } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

// 模拟API数据
const mockData = {
  // 用户信息
  user: {
    id: 1,
    name: '管理员',
    email: '<EMAIL>',
    role: 'admin',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
  },
  
  // 群组列表数据
  groups: Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    title: `测试群组${i + 1}`,
    price: Math.floor(Math.random() * 100) + 10,
    current_members: Math.floor(Math.random() * 200) + 50,
    max_members: 500,
    status: Math.random() > 0.3 ? 1 : 2,
    auto_city_replace: Math.random() > 0.5 ? 1 : 0,
    avatar_library: Math.random() > 0.5 ? 'qq' : 'za',
    display_type: Math.random() > 0.5 ? 1 : 2,
    wx_accessible: Math.random() > 0.5 ? 1 : 2,
    virtual_members: Math.floor(Math.random() * 100),
    virtual_orders: Math.floor(Math.random() * 50),
    like_count: Math.floor(Math.random() * 1000),
    want_see_count: Math.floor(Math.random() * 500),
    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
  })),
  
  // 域名池数据
  domains: Array.from({ length: 15 }, (_, i) => ({
    id: i + 1,
    domain: `domain${i + 1}.example.com`,
    status: Math.random() > 0.7 ? (Math.random() > 0.5 ? 2 : 3) : 1,
    status_name: Math.random() > 0.7 ? (Math.random() > 0.5 ? '异常' : '封禁') : '正常',
    health_score: Math.floor(Math.random() * 100),
    use_count: Math.floor(Math.random() * 1000),
    last_check_time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
    last_use_time: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000).toISOString(),
    check_results: {
      accessible: Math.random() > 0.2,
      dns_resolved: Math.random() > 0.1,
      ssl_valid: Math.random() > 0.3,
      wechat_accessible: Math.random() > 0.4,
      qq_accessible: Math.random() > 0.3,
      response_time: Math.floor(Math.random() * 2000) + 100
    }
  })),
  
  // 营销模板数据
  marketingTemplates: [
    {
      id: 'business',
      name: '商务模板',
      description: '适合商务交流、职场学习类群组',
      config: {
        avatar_library: 'za',
        display_type: 1,
        button_title: '立即加入商务群',
        group_intro_title: '群简介',
        faq_title: '常见问题',
        city_insert_strategy: 'natural'
      }
    },
    {
      id: 'social',
      name: '社交模板',
      description: '适合交友、兴趣爱好类群组',
      config: {
        avatar_library: 'qq',
        display_type: 1,
        button_title: '加入我们的大家庭',
        group_intro_title: '关于我们',
        faq_title: '新人必看',
        city_insert_strategy: 'prefix'
      }
    },
    {
      id: 'education',
      name: '教育模板',
      description: '适合学习、培训类群组',
      config: {
        avatar_library: 'za',
        display_type: 1,
        button_title: '开始学习之旅',
        group_intro_title: '课程介绍',
        faq_title: '学习指南',
        city_insert_strategy: 'suffix'
      }
    }
  ],
  
  // 浏览器统计数据
  browserStats: [
    { browser_type: 'wechat', browser_name: '微信浏览器', count: 1250, percentage: 45.2 },
    { browser_type: 'chrome', browser_name: 'Chrome', count: 890, percentage: 32.1 },
    { browser_type: 'safari', browser_name: 'Safari', count: 340, percentage: 12.3 },
    { browser_type: 'qq', browser_name: 'QQ浏览器', count: 180, percentage: 6.5 },
    { browser_type: 'firefox', browser_name: 'Firefox', count: 110, percentage: 3.9 }
  ],
  
  // 城市列表
  cities: [
    '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都',
    '重庆', '天津', '西安', '苏州', '长沙', '沈阳', '青岛', '郑州',
    '大连', '东莞', '宁波', '厦门', '福州', '无锡', '合肥', '昆明'
  ]
}

// 创建模拟API中间件
function createMockApiMiddleware() {
  return (req, res, next) => {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    
    if (req.method === 'OPTIONS') {
      res.status(200).end()
      return
    }

    const url = req.url
    const method = req.method

    // 模拟API响应
    if (url.startsWith('/api/')) {
      res.setHeader('Content-Type', 'application/json')
      
      // 用户认证
      if (url === '/api/v1/auth/me') {
        return res.end(JSON.stringify({
          success: true,
          data: mockData.user
        }))
      }
      
      // 群组列表
      if (url.startsWith('/api/v1/wechat-groups')) {
        return res.end(JSON.stringify({
          success: true,
          data: mockData.groups.slice(0, 10),
          total: mockData.groups.length,
          current_page: 1,
          per_page: 10
        }))
      }
      
      // 营销配置
      if (url.includes('/marketing-config')) {
        const groupId = url.match(/groups\/(\d+)/)?.[1]
        return res.end(JSON.stringify({
          success: true,
          data: {
            basic: {
              read_count_display: '10万+',
              like_count: 888,
              want_see_count: 666,
              button_title: '立即加入群聊',
              avatar_library: 'qq',
              wx_accessible: 1,
              display_type: 1
            },
            content: {
              group_intro_title: '群简介',
              group_intro_content: '这是一个测试群组的简介内容...',
              faq_title: '常见问题',
              faq_content: '问题1----答案1\n问题2----答案2',
              member_reviews: '很好的群组----99\n学到了很多----88'
            },
            service: {
              show_customer_service: 2,
              customer_service_title: 'VIP专属客服',
              customer_service_desc: '如有问题请联系客服'
            },
            advanced: {
              auto_city_replace: 1,
              city_insert_strategy: 'auto',
              show_virtual_activity: 1
            }
          }
        }))
      }
      
      // 营销模板
      if (url === '/api/v1/marketing-templates') {
        return res.end(JSON.stringify({
          success: true,
          data: mockData.marketingTemplates
        }))
      }
      
      // 群组预览
      if (url.includes('/preview')) {
        return res.end(JSON.stringify({
          success: true,
          data: {
            title: '北京测试群组',
            original_title: 'xxx测试群组',
            price: 99,
            read_count_display: '10万+',
            like_count: 888,
            want_see_count: 666,
            button_title: '立即加入群聊',
            virtual_members: Array.from({ length: 13 }, (_, i) => ({
              nickname: `用户${i + 1}`,
              avatar: `/face/qq/${(i % 41) + 1}.jpg`,
              join_time: new Date().toLocaleString()
            }))
          }
        }))
      }
      
      // 城市定位测试
      if (url.includes('/test-city')) {
        return res.end(JSON.stringify({
          success: true,
          data: {
            test_city: '北京',
            original_title: 'xxx测试群组',
            replaced_title: '北京测试群组',
            strategy_name: '智能判断'
          }
        }))
      }
      
      // 虚拟成员生成
      if (url.includes('/virtual-members')) {
        return res.end(JSON.stringify({
          success: true,
          data: Array.from({ length: 13 }, (_, i) => ({
            nickname: `虚拟用户${i + 1}`,
            avatar: `/face/qq/${(i % 41) + 1}.jpg`,
            join_time: new Date().toLocaleString()
          }))
        }))
      }
      
      // 域名健康状态
      if (url === '/api/v1/anti-block/domain-health') {
        return res.end(JSON.stringify({
          success: true,
          data: {
            domains: mockData.domains,
            stats: {
              total: mockData.domains.length,
              normal: mockData.domains.filter(d => d.status === 1).length,
              abnormal: mockData.domains.filter(d => d.status === 2).length,
              blocked: mockData.domains.filter(d => d.status === 3).length,
              avg_health_score: Math.floor(mockData.domains.reduce((sum, d) => sum + d.health_score, 0) / mockData.domains.length)
            },
            total: mockData.domains.length
          }
        }))
      }
      
      // 浏览器统计
      if (url === '/api/v1/anti-block/browser-stats') {
        return res.end(JSON.stringify({
          success: true,
          data: mockData.browserStats
        }))
      }
      
      // 城市列表
      if (url === '/api/v1/location/cities') {
        return res.end(JSON.stringify({
          success: true,
          data: mockData.cities
        }))
      }
      
      // 默认成功响应
      return res.end(JSON.stringify({
        success: true,
        message: '操作成功',
        data: {}
      }))
    }

    next()
  }
}

// 启动预览服务器
async function startPreviewServer() {
  const server = await createServer({
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: ['vue', 'vue-router', 'pinia'],
        dts: true,
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: true,
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(process.cwd(), './src'),
      },
    },
    server: {
      port: 3001,
      host: '0.0.0.0',
      open: true,
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
          api: 'modern-compiler',
        },
      },
    },
  })

  // 添加模拟API中间件
  server.middlewares.use(createMockApiMiddleware())

  await server.listen()
  
  console.log('\n🎉 管理后台预览服务器已启动!')
  console.log('📱 访问地址: http://localhost:3001')
  console.log('🔧 模拟API已启用，无需后端服务')
  console.log('\n📋 可预览的功能:')
  console.log('  ✅ 群组营销配置 (/community/marketing)')
  console.log('  ✅ 防封系统管理 (/anti-block/enhanced)')
  console.log('  ✅ 社群管理列表 (/community/groups)')
  console.log('  ✅ 数据看板 (/dashboard)')
  console.log('\n💡 提示: 所有数据都是模拟数据，用于界面预览')
}

startPreviewServer().catch(console.error)