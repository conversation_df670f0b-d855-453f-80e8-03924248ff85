import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as loginApi, logout as logoutApi, getInfo } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref(getToken())
  const userInfo = ref(null)

  // 预览模式入口函数
  const enterPreviewMode = () => {
    const previewToken = 'preview-mode-token-' + Date.now()
    setToken(previewToken)
    token.value = previewToken
    
    userInfo.value = {
      id: 1,
      username: 'admin',
      nickname: '超级管理员 (预览)',
      email: '<EMAIL>',
      avatar: '/default-avatar.png',
      role: 'admin',
      roles: ['admin'],
      permissions: ['*']
    }
    console.log('🎭 预览模式已激活，用户信息已设置')
  }

  const roles = computed(() => userInfo.value?.roles || [])
  const nickname = computed(() => userInfo.value?.nickname || '')
  const avatar = computed(() => userInfo.value?.avatar || '')
  const userRole = computed(() => userInfo.value?.role || '')
  const isAdmin = computed(() => userRole.value === 'admin')
  const isSubstation = computed(() => userRole.value === 'substation')
  const isAgent = computed(() => userRole.value === 'agent')
  const isDistributor = computed(() => userRole.value === 'distributor')
  const isGroupOwner = computed(() => userRole.value === 'group_owner')
  const isUser = computed(() => userRole.value === 'user')

  // 会话信息
  const sessionInfo = computed(() => {
    try {
      const stored = localStorage.getItem('sessionInfo')
      return stored ? JSON.parse(stored) : null
    } catch {
      return null
    }
  })

  // 检查会话是否有效
  const isSessionValid = computed(() => {
    if (!token.value || !sessionInfo.value) return false

    const loginTime = new Date(sessionInfo.value.loginTime)
    const now = new Date()
    const sessionDuration = now - loginTime

    // 会话超过24小时视为无效（可配置）
    return sessionDuration < 24 * 60 * 60 * 1000
  })

  const login = async (credentials) => {
    try {
      const response = await loginApi(credentials)
      const data = response.data

      if (data.success) {
        // 保存认证信息
        setToken(data.data.token)
        token.value = data.data.token
        userInfo.value = data.data.user

        // 记录登录时间和会话信息
        const sessionInfo = {
          loginTime: new Date().toISOString(),
          userAgent: navigator.userAgent,
          role: data.data.user.role,
          userId: data.data.user.id,
          username: data.data.user.username
        }

        localStorage.setItem('sessionInfo', JSON.stringify(sessionInfo))

        // 设置自动登出定时器（可选，基于token过期时间）
        setupAutoLogout()

        return data
      } else {
        throw new Error(data.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)

      // 增强错误处理
      if (error.response) {
        const status = error.response.status
        const message = error.response.data?.message || error.message

        switch (status) {
          case 401:
            throw new Error('用户名或密码错误')
          case 403:
            throw new Error('账户已被禁用或权限不足')
          case 429:
            throw new Error('登录尝试过于频繁，请稍后再试')
          case 500:
            throw new Error('服务器内部错误，请稍后重试')
          default:
            throw new Error(message || '登录失败')
        }
      }

      throw error
    }
  }

  const getUserInfo = async () => {
    try {
      const response = await getInfo()
      const data = response.data
      
      if (data.success) {
        userInfo.value = data.data.user
        return data
      } else {
        throw new Error(data.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 自动登出设置
  const setupAutoLogout = () => {
    // 清除之前的定时器
    if (window.autoLogoutTimer) {
      clearTimeout(window.autoLogoutTimer)
    }

    // 设置24小时后自动登出（可配置）
    window.autoLogoutTimer = setTimeout(() => {
      logout()
      // 这里需要在组件中处理消息提示
    }, 24 * 60 * 60 * 1000)
  }

  // 检查用户权限
  const hasPermission = (permission) => {
    if (!userInfo.value) return false

    // 超级管理员拥有所有权限
    if (isAdmin.value) return true

    // 根据角色检查权限
    const rolePermissions = {
      substation: ['user_management', 'agent_management', 'order_management', 'group_management', 'finance_view'],
      agent: ['team_management', 'commission_view', 'performance_view', 'application_management'],
      distributor: ['customer_management', 'group_management', 'promotion_management', 'commission_view'],
      group_owner: ['group_management', 'content_management', 'template_management'],
      user: ['profile_management', 'order_view']
    }

    const userPermissions = rolePermissions[userRole.value] || []
    return userPermissions.includes(permission)
  }

  // 检查路由权限
  const hasRouteAccess = (routePath) => {
    if (!userInfo.value) return false

    // 超级管理员可以访问所有路由
    if (isAdmin.value) return true

    // 使用导航配置检查权限
    try {
      const { checkMenuPermission } = require('@/config/navigation')
      return checkMenuPermission({ path: routePath }, userRole.value)
    } catch {
      return false
    }
  }

  // 获取用户默认路由
  const getUserDefaultRoute = () => {
    try {
      const { getUserDefaultRoute } = require('@/config/navigation')
      return getUserDefaultRoute(userRole.value)
    } catch {
      return '/dashboard'
    }
  }

  const logout = async () => {
    try {
      await logoutApi()
    } catch (error) {
      console.warn('后端登出失败:', error)
    } finally {
      // 清理本地数据
      resetToken()
      userInfo.value = null
      localStorage.removeItem('sessionInfo')

      // 清除自动登出定时器
      if (window.autoLogoutTimer) {
        clearTimeout(window.autoLogoutTimer)
        window.autoLogoutTimer = null
      }
    }
  }

  const resetToken = () => {
    removeToken()
    token.value = ''
  }

  // 设置Token（用于预览模式）
  const setTokenValue = (newToken) => {
    setToken(newToken)
    token.value = newToken
  }

  // 设置用户信息（用于预览模式）
  const setUserInfo = (info) => {
    userInfo.value = info
  }

  return {
    // 状态
    token,
    userInfo,
    roles,
    nickname,
    avatar,
    userRole,
    sessionInfo,
    isSessionValid,

    // 角色检查
    isAdmin,
    isSubstation,
    isAgent,
    isDistributor,
    isGroupOwner,
    isUser,

    // 方法
    login,
    getUserInfo,
    logout,
    resetToken,
    setToken: setTokenValue,
    setUserInfo,
    setupAutoLogout,
    hasPermission,
    hasRouteAccess,
    getUserDefaultRoute,
    enterPreviewMode // 暴露预览模式函数
  }
})
