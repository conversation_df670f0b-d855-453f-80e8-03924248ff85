# 防封系统优化建议

## 1. 链接跳转验证系统

### 创建群组访问验证服务：

```php
<?php

namespace App\Services;

use App\Models\WechatGroup;
use App\Models\User;
use App\Models\Substation;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GroupAccessValidationService
{
    /**
     * 验证群组访问权限（基于源码包逻辑）
     */
    public function validateGroupAccess(int $groupId, string $domain = null): array
    {
        try {
            // 1. 检查群组是否存在
            $group = WechatGroup::find($groupId);
            if (!$group) {
                return $this->createErrorResponse('群组不存在', 'https://www.baidu.com/s?ie=UTF-8&wd=群组不存在');
            }

            // 2. 检查群组状态
            if ($group->status != WechatGroup::STATUS_ACTIVE) {
                $message = $group->status == WechatGroup::STATUS_DISABLED ? '群组被禁用' : '群组已满';
                return $this->createErrorResponse($message, "https://www.baidu.com/s?ie=UTF-8&wd={$message}");
            }

            // 3. 检查分销员状态
            $distributor = $group->user;
            if (!$distributor || $distributor->status != 'active') {
                return $this->createErrorResponse('分销商不存在或被禁用', 'https://www.baidu.com/s?ie=UTF-8&wd=分销商不可用');
            }

            // 4. 检查分站状态
            $substation = $group->substation;
            if (!$substation || $substation->status != 'active') {
                $message = !$substation ? '分站不存在' : 
                          ($substation->status == 'expired' ? '分站已到期' : '分站被禁用');
                return $this->createErrorResponse($message, "https://www.baidu.com/s?ie=UTF-8&wd={$message}");
            }

            // 5. 检查域名匹配（如果提供了域名）
            if ($domain && $substation->domain && $domain !== $substation->domain) {
                return $this->createErrorResponse('不存在的分站域名', 'https://www.baidu.com/s?ie=UTF-8&wd=域名错误');
            }

            // 6. 检查点卡余额（如果启用了扣费）
            if ($substation->deduction_rate > 0) {
                $requiredBalance = $group->price * ($substation->deduction_rate / 100);
                
                // 检查二级分站扣费
                if ($substation->parent_id) {
                    $parentSubstation = Substation::find($substation->parent_id);
                    if ($parentSubstation && $parentSubstation->deduction_rate > 0) {
                        $requiredBalance += $group->price * ($parentSubstation->deduction_rate / 100);
                    }
                }
                
                if ($substation->balance < $requiredBalance) {
                    return $this->createErrorResponse('点卡不足，请先充值', 'https://www.baidu.com/s?ie=UTF-8&wd=点卡不足');
                }
            }

            // 7. 检查支付功能
            $paymentChannels = $group->getAvailablePaymentMethods();
            if (empty($paymentChannels)) {
                return $this->createErrorResponse('分销商未开启支付功能', 'https://www.baidu.com/s?ie=UTF-8&wd=支付功能未开启');
            }

            // 8. 所有验证通过
            return [
                'valid' => true,
                'group' => $group,
                'distributor' => $distributor,
                'substation' => $substation,
                'payment_channels' => $paymentChannels,
            ];

        } catch (\Exception $e) {
            Log::error('群组访问验证失败', [
                'group_id' => $groupId,
                'domain' => $domain,
                'error' => $e->getMessage(),
            ]);

            return $this->createErrorResponse('系统错误', 'https://www.baidu.com/s?ie=UTF-8&wd=系统维护中');
        }
    }

    /**
     * 生成加密的群组访问链接
     */
    public function generateSecureGroupLink(WechatGroup $group): string
    {
        $timestamp = time();
        $token = md5($group->id . $timestamp . config('app.key'));
        
        $baseUrl = $group->substation->domain ?? config('app.url');
        
        return "https://{$baseUrl}/group/{$group->id}?t={$token}&time={$timestamp}";
    }

    /**
     * 验证访问令牌
     */
    public function validateAccessToken(int $groupId, string $token, int $timestamp): bool
    {
        // 检查时间戳是否过期（24小时）
        if (time() - $timestamp > 86400) {
            return false;
        }

        // 验证令牌
        $expectedToken = md5($groupId . $timestamp . config('app.key'));
        
        return hash_equals($expectedToken, $token);
    }

    /**
     * 创建错误响应
     */
    private function createErrorResponse(string $message, string $redirectUrl): array
    {
        return [
            'valid' => false,
            'message' => $message,
            'redirect_url' => $redirectUrl,
        ];
    }
}
```

## 2. 域名池管理系统

### 扩展 DomainPool 模型功能：

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class DomainPool extends Model
{
    protected $fillable = [
        'domain',
        'status',
        'substation_id',
        'health_score',
        'last_check_at',
        'failure_count',
        'is_primary',
        'usage_count',
        'max_daily_usage',
        'risk_level',
    ];

    protected $casts = [
        'last_check_at' => 'datetime',
        'is_primary' => 'boolean',
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_BLOCKED = 'blocked';
    const STATUS_MAINTENANCE = 'maintenance';

    const RISK_LOW = 'low';
    const RISK_MEDIUM = 'medium';
    const RISK_HIGH = 'high';

    /**
     * 获取可用的域名（智能轮换）
     */
    public static function getAvailableDomain(int $substationId = null): ?string
    {
        $cacheKey = "available_domain_{$substationId}";
        
        return Cache::remember($cacheKey, 300, function () use ($substationId) {
            $query = self::where('status', self::STATUS_ACTIVE)
                        ->where('risk_level', '!=', self::RISK_HIGH);
            
            if ($substationId) {
                $query->where(function ($q) use ($substationId) {
                    $q->where('substation_id', $substationId)
                      ->orWhereNull('substation_id');
                });
            }

            // 优先选择使用次数少的域名
            $domain = $query->orderBy('usage_count', 'asc')
                           ->orderBy('health_score', 'desc')
                           ->first();

            if ($domain) {
                // 增加使用次数
                $domain->increment('usage_count');
                return $domain->domain;
            }

            return null;
        });
    }

    /**
     * 检查域名健康状态
     */
    public function checkHealth(): array
    {
        $healthData = [
            'domain' => $this->domain,
            'accessible' => false,
            'response_time' => 0,
            'status_code' => 0,
            'ssl_valid' => false,
            'dns_resolved' => false,
        ];

        try {
            // DNS解析检查
            $ip = gethostbyname($this->domain);
            $healthData['dns_resolved'] = $ip !== $this->domain;

            // HTTP访问检查
            $startTime = microtime(true);
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Mozilla/5.0 (compatible; HealthChecker/1.0)',
                ],
            ]);

            $response = @file_get_contents("https://{$this->domain}", false, $context);
            $endTime = microtime(true);

            if ($response !== false) {
                $healthData['accessible'] = true;
                $healthData['response_time'] = round(($endTime - $startTime) * 1000, 2);
                
                // 获取HTTP状态码
                if (isset($http_response_header[0])) {
                    preg_match('/HTTP\/\d\.\d\s+(\d+)/', $http_response_header[0], $matches);
                    $healthData['status_code'] = intval($matches[1] ?? 0);
                }

                // SSL证书检查
                $sslContext = stream_context_create([
                    'ssl' => ['capture_peer_cert' => true],
                ]);
                $sslStream = @stream_socket_client(
                    "ssl://{$this->domain}:443",
                    $errno, $errstr, 10, STREAM_CLIENT_CONNECT, $sslContext
                );
                
                if ($sslStream) {
                    $healthData['ssl_valid'] = true;
                    fclose($sslStream);
                }
            }

        } catch (\Exception $e) {
            Log::warning("域名健康检查失败: {$this->domain}", ['error' => $e->getMessage()]);
        }

        // 更新健康分数
        $this->updateHealthScore($healthData);

        return $healthData;
    }

    /**
     * 更新健康分数
     */
    private function updateHealthScore(array $healthData): void
    {
        $score = 0;

        // DNS解析 (20分)
        if ($healthData['dns_resolved']) $score += 20;

        // 可访问性 (30分)
        if ($healthData['accessible']) $score += 30;

        // 响应时间 (20分)
        if ($healthData['response_time'] > 0) {
            if ($healthData['response_time'] < 1000) $score += 20;
            elseif ($healthData['response_time'] < 3000) $score += 15;
            elseif ($healthData['response_time'] < 5000) $score += 10;
            else $score += 5;
        }

        // HTTP状态码 (15分)
        if ($healthData['status_code'] == 200) $score += 15;
        elseif ($healthData['status_code'] >= 200 && $healthData['status_code'] < 400) $score += 10;

        // SSL证书 (15分)
        if ($healthData['ssl_valid']) $score += 15;

        // 更新数据库
        $this->update([
            'health_score' => $score,
            'last_check_at' => now(),
            'failure_count' => $healthData['accessible'] ? 0 : $this->failure_count + 1,
        ]);

        // 根据失败次数调整风险等级
        if ($this->failure_count >= 5) {
            $this->update(['risk_level' => self::RISK_HIGH]);
        } elseif ($this->failure_count >= 3) {
            $this->update(['risk_level' => self::RISK_MEDIUM]);
        } else {
            $this->update(['risk_level' => self::RISK_LOW]);
        }
    }
}
```

## 3. 微信浏览器检测增强

### 创建浏览器检测服务：

```php
<?php

namespace App\Services;

class BrowserDetectionService
{
    /**
     * 检测是否为微信浏览器
     */
    public function isWechatBrowser(): bool
    {
        $userAgent = request()->header('User-Agent', '');
        return strpos($userAgent, 'MicroMessenger') !== false;
    }

    /**
     * 检测浏览器类型
     */
    public function getBrowserType(): string
    {
        $userAgent = request()->header('User-Agent', '');

        if (strpos($userAgent, 'MicroMessenger') !== false) {
            return 'wechat';
        } elseif (strpos($userAgent, 'QQ/') !== false) {
            return 'qq';
        } elseif (strpos($userAgent, 'Alipay') !== false) {
            return 'alipay';
        } elseif (strpos($userAgent, 'Chrome') !== false) {
            return 'chrome';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            return 'safari';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            return 'firefox';
        }

        return 'unknown';
    }

    /**
     * 生成浏览器引导页面
     */
    public function generateBrowserGuidePage(WechatGroup $group): string
    {
        $groupUrl = route('group.show', ['id' => $group->id]);
        $fallbackUrl = 'https://www.baidu.com/s?wd=' . urlencode('今日头条');

        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
<script>
document.title = decodeURIComponent(atob("' . base64_encode(urlencode('加载中...')) . '"));

function redirectToGroup() {
    try {
        setTimeout(function() { 
            window.location.href = "' . $groupUrl . '";
        }, 500);
    } catch(e) {
        window.location.href = "' . $fallbackUrl . '";
    }
}

// 检查访问权限并跳转
var script = document.createElement("script");
script.src = "' . route('api.group.validate', ['id' => $group->id]) . '";
script.onload = function() {
    redirectToGroup();
};
script.onerror = function() {
    window.location.href = "' . $fallbackUrl . '";
};
document.head.appendChild(script);
</script>
</body>
</html>';
    }
}
```

## 4. 控制器集成

### 在群组控制器中集成防封功能：

```php
/**
 * 群组访问入口（带防封验证）
 */
public function accessGroup(Request $request, $id)
{
    $validationService = new GroupAccessValidationService();
    $browserService = new BrowserDetectionService();
    
    // 获取访问域名
    $domain = $request->getHost();
    
    // 验证访问权限
    $validation = $validationService->validateGroupAccess($id, $domain);
    
    if (!$validation['valid']) {
        // 生成错误跳转页面
        return response($browserService->generateBrowserGuidePage(null))
                ->header('Content-Type', 'text/html');
    }
    
    $group = $validation['group'];
    
    // 检查微信浏览器访问控制
    if ($group->wx_accessible == 2 && $browserService->isWechatBrowser()) {
        // 生成浏览器引导页面
        return response($browserService->generateBrowserGuidePage($group))
                ->header('Content-Type', 'text/html');
    }
    
    // 生成安全访问链接
    $secureLink = $validationService->generateSecureGroupLink($group);
    
    return redirect($secureLink);
}

/**
 * 验证群组访问API
 */
public function validateGroupAccess(Request $request, $id)
{
    $validationService = new GroupAccessValidationService();
    $validation = $validationService->validateGroupAccess($id);
    
    if ($validation['valid']) {
        return response()->json(['status' => 'success']);
    } else {
        return response()->json([
            'status' => 'error',
            'message' => $validation['message'],
            'redirect' => $validation['redirect_url']
        ], 403);
    }
}
```