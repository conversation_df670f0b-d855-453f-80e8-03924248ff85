<?php

require_once 'vendor/autoload.php';

// 设置Laravel环境
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 防红系统详细测试 ===\n";

try {
    // 测试数据库连接
    echo "1. 测试数据库连接...\n";
    $pdo = DB::connection()->getPdo();
    echo "✓ 数据库连接成功\n";
    
    // 测试模型
    echo "\n2. 测试模型...\n";
    
    // 测试DomainPool模型
    $domainPoolCount = App\Models\DomainPool::count();
    echo "✓ DomainPool模型正常，记录数: {$domainPoolCount}\n";
    
    // 测试ShortLink模型
    $shortLinkCount = App\Models\ShortLink::count();
    echo "✓ ShortLink模型正常，记录数: {$shortLinkCount}\n";
    
    // 测试LinkAccessLog模型
    $accessLogCount = App\Models\LinkAccessLog::count();
    echo "✓ LinkAccessLog模型正常，记录数: {$accessLogCount}\n";
    
    // 测试DomainCheckLog模型
    $checkLogCount = App\Models\DomainCheckLog::count();
    echo "✓ DomainCheckLog模型正常，记录数: {$checkLogCount}\n";
    
    // 测试AntiBlockService
    echo "\n3. 测试AntiBlockService...\n";
    $antiBlockService = new App\Services\AntiBlockService();
    $stats = $antiBlockService->getStats();
    echo "✓ AntiBlockService正常，统计数据:\n";
    print_r($stats);
    
    // 测试控制器
    echo "\n4. 测试控制器...\n";
    $controller = new App\Http\Controllers\Api\Admin\AntiBlockController(
        $antiBlockService,
        new App\Services\AntiBlockLinkService()
    );
    
    // 创建模拟请求
    $request = new Illuminate\Http\Request();
    $response = $controller->getStats();
    
    echo "✓ 控制器测试成功，响应状态: " . $response->getStatusCode() . "\n";
    echo "响应内容: " . $response->getContent() . "\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
}

echo "\n测试完成！\n";