#!/bin/bash

# FFJQ营销功能部署脚本
# 用于部署新增的营销功能和数据库更改

echo "🚀 开始部署FFJQ营销功能..."
echo "================================"

# 检查PHP和Composer
echo "📋 检查环境依赖..."
if ! command -v php &> /dev/null; then
    echo "❌ PHP未安装或不在PATH中"
    exit 1
fi

if ! command -v composer &> /dev/null; then
    echo "❌ Composer未安装或不在PATH中"
    exit 1
fi

echo "✅ 环境检查通过"

# 安装/更新依赖
echo ""
echo "📦 更新Composer依赖..."
composer install --optimize-autoloader --no-dev

# 清理缓存
echo ""
echo "🧹 清理应用缓存..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 运行数据库迁移
echo ""
echo "🗄️ 运行数据库迁移..."
php artisan migrate --force

if [ $? -ne 0 ]; then
    echo "❌ 数据库迁移失败"
    exit 1
fi

echo "✅ 数据库迁移完成"

# 运行数据填充
echo ""
echo "📊 填充营销数据..."
php artisan db:seed --class=MarketingDataSeeder --force

if [ $? -ne 0 ]; then
    echo "⚠️ 数据填充失败，但不影响主要功能"
fi

# 优化应用
echo ""
echo "⚡ 优化应用性能..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize

# 设置文件权限
echo ""
echo "🔐 设置文件权限..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# 运行功能测试
echo ""
echo "🧪 运行功能测试..."
php test-marketing-features.php

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 营销功能部署成功！"
    echo "================================"
    echo "新增功能："
    echo "  ✅ 群组营销展示功能"
    echo "  ✅ 城市定位和标题替换"
    echo "  ✅ 虚拟数据生成"
    echo "  ✅ 浏览器检测和防封"
    echo "  ✅ 访问日志和统计"
    echo "  ✅ IP城市缓存"
    echo "  ✅ 支付渠道管理"
    echo ""
    echo "📝 接下来的步骤："
    echo "  1. 配置支付渠道参数"
    echo "  2. 设置域名池（如需要）"
    echo "  3. 测试群组访问和支付流程"
    echo "  4. 配置前端路由和视图"
    echo ""
else
    echo ""
    echo "⚠️ 部署完成但测试发现问题，请检查日志"
fi

echo "部署脚本执行完成"