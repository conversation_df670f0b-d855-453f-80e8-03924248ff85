import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                 *//* empty css                *//* empty css                     *//* empty css               */import{b2 as a,b4 as l,bc as t,bd as u,aH as s,aW as n,aV as o,as as i,T as d,b1 as c,aM as r,b3 as p,bR as _,bi as v,bS as m,b6 as f,b7 as b,b8 as y,U as g,bj as h,bk as w,bM as k,bN as C,au as j,Q as V,R as x}from"./element-plus-DcSKpKA8.js";import{S as z}from"./StatCard-WpSR56Tk.js";import{b as U}from"./agent-SmxfvIrI.js";import{r as S,L as A,c as E,e as R,k as D,l as F,t as L,E as B,B as M,z as N,D as Q,u as W,A as q,y as H}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";/* empty css                                                                 */const I={class:"agent-applications"},O={key:0,class:"batch-actions"},P={class:"batch-buttons"},T={class:"pagination"},X={key:0,class:"application-detail"},$={class:"detail-section"},G={class:"detail-section"},J={class:"detail-section"},K={key:0,class:"detail-section"},Y={class:"review-info"},Z=e({__name:"AgentApplications",setup(e){const Z=S(!1),ee=S(1),ae=S(20),le=S(!1),te=S(!1),ue=S(!1),se=S({}),ne=S({data:[],total:0}),oe=S([]),ie=S(null),de=S(""),ce=A({keyword:"",status:"",agent_type:"",agent_level:""}),re=A({comment:""}),pe=E(()=>"approve"===de.value?"通过申请":"拒绝申请"),_e=async()=>{try{const e=await U.getStats();se.value=e.data}catch(e){V.error("加载统计数据失败")}},ve=async()=>{try{Z.value=!0;const e={page:ee.value,limit:ae.value,...ce},a=await U.getList(e);ne.value=a.data}catch(e){V.error("加载申请列表失败")}finally{Z.value=!1}},me=()=>{ee.value=1,ve()},fe=()=>{Object.keys(ce).forEach(e=>{ce[e]=""}),me()},be=e=>{ae.value=e,ve()},ye=e=>{ee.value=e,ve()},ge=e=>{oe.value=e},he=e=>{ie.value=e,de.value="approve",re.comment="",te.value=!0},we=e=>{ie.value=e,de.value="reject",re.comment="",te.value=!0},ke=async()=>{try{ue.value=!0,await U.review(ie.value.id,{action:de.value,comment:re.comment}),V.success("approve"===de.value?"申请已通过":"申请已拒绝"),te.value=!1,le.value=!1,ve(),_e()}catch(e){V.error("审核失败")}finally{ue.value=!1}},Ce=async()=>{try{await x.confirm("确定要批量通过选中的申请吗？","确认操作",{type:"warning"});const e=oe.value.map(e=>e.id);await U.batchReview({application_ids:e,action:"approve",comment:"批量通过"}),V.success("批量通过成功"),ve(),_e()}catch(e){"cancel"!==e&&V.error("批量通过失败")}},je=async()=>{try{await x.confirm("确定要批量拒绝选中的申请吗？","确认操作",{type:"warning"});const e=oe.value.map(e=>e.id);await U.batchReview({application_ids:e,action:"reject",comment:"批量拒绝"}),V.success("批量拒绝成功"),ve(),_e()}catch(e){"cancel"!==e&&V.error("批量拒绝失败")}},Ve=e=>e?new Date(e).toLocaleString("zh-CN"):"",xe=e=>({pending:"warning",approved:"success",rejected:"danger",cancelled:"info"}[e]||"info"),ze=e=>({individual:"primary",enterprise:"success",channel:"warning"}[e]||"info"),Ue=e=>({platform:"primary",substation:"success"}[e]||"info");return R(()=>{_e(),ve()}),(e,V)=>{const x=a,U=l,S=s,A=u,E=o,R=n,de=d,_e=i,ve=t,Se=p,Ae=_,Ee=b,Re=y,De=f,Fe=w,Le=C,Be=k,Me=j,Ne=h;return F(),D("div",I,[V[30]||(V[30]=L("div",{class:"page-header"},[L("h2",null,"代理商申请管理"),L("p",null,"审核和管理代理商申请，包括申请列表、审核流程和批量操作")],-1)),B(U,{gutter:20,class:"stats-row"},{default:N(()=>[B(x,{span:6},{default:N(()=>[B(z,{title:"总申请数",value:se.value.total||0,icon:"Document",color:"#409EFF"},null,8,["value"])]),_:1}),B(x,{span:6},{default:N(()=>[B(z,{title:"待审核",value:se.value.pending||0,icon:"Clock",color:"#E6A23C"},null,8,["value"])]),_:1}),B(x,{span:6},{default:N(()=>[B(z,{title:"已通过",value:se.value.approved||0,icon:"Check",color:"#67C23A"},null,8,["value"])]),_:1}),B(x,{span:6},{default:N(()=>[B(z,{title:"已拒绝",value:se.value.rejected||0,icon:"Close",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),B(Se,{class:"search-card"},{default:N(()=>[B(ve,{model:ce,inline:""},{default:N(()=>[B(A,{label:"关键词"},{default:N(()=>[B(S,{modelValue:ce.keyword,"onUpdate:modelValue":V[0]||(V[0]=e=>ce.keyword=e),placeholder:"搜索用户名、姓名或手机号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),B(A,{label:"申请状态"},{default:N(()=>[B(R,{modelValue:ce.status,"onUpdate:modelValue":V[1]||(V[1]=e=>ce.status=e),placeholder:"选择状态",clearable:""},{default:N(()=>[B(E,{label:"待审核",value:"pending"}),B(E,{label:"已通过",value:"approved"}),B(E,{label:"已拒绝",value:"rejected"}),B(E,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),B(A,{label:"代理商类型"},{default:N(()=>[B(R,{modelValue:ce.agent_type,"onUpdate:modelValue":V[2]||(V[2]=e=>ce.agent_type=e),placeholder:"选择类型",clearable:""},{default:N(()=>[B(E,{label:"个人代理",value:"individual"}),B(E,{label:"企业代理",value:"enterprise"}),B(E,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),B(A,{label:"代理商等级"},{default:N(()=>[B(R,{modelValue:ce.agent_level,"onUpdate:modelValue":V[3]||(V[3]=e=>ce.agent_level=e),placeholder:"选择等级",clearable:""},{default:N(()=>[B(E,{label:"平台代理商",value:"platform"}),B(E,{label:"分站代理商",value:"substation"})]),_:1},8,["modelValue"])]),_:1}),B(A,null,{default:N(()=>[B(_e,{type:"primary",onClick:me},{default:N(()=>[B(de,null,{default:N(()=>[B(W(c))]),_:1}),V[13]||(V[13]=Q(" 搜索 ",-1))]),_:1,__:[13]}),B(_e,{onClick:fe},{default:N(()=>[B(de,null,{default:N(()=>[B(W(r))]),_:1}),V[14]||(V[14]=Q(" 重置 ",-1))]),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),_:1}),oe.value.length>0?(F(),D("div",O,[B(Ae,{title:`已选择 ${oe.value.length} 个申请`,type:"info","show-icon":"",closable:!1},null,8,["title"]),L("div",P,[B(_e,{type:"success",onClick:Ce},{default:N(()=>[B(de,null,{default:N(()=>[B(W(v))]),_:1}),V[15]||(V[15]=Q(" 批量通过 ",-1))]),_:1,__:[15]}),B(_e,{type:"danger",onClick:je},{default:N(()=>[B(de,null,{default:N(()=>[B(W(m))]),_:1}),V[16]||(V[16]=Q(" 批量拒绝 ",-1))]),_:1,__:[16]})])])):M("",!0),B(Se,{class:"applications-table"},{header:N(()=>V[17]||(V[17]=[L("span",null,"申请列表",-1)])),default:N(()=>[q((F(),H(De,{data:ne.value.data,stripe:"",onSelectionChange:ge},{default:N(()=>[B(Ee,{type:"selection",width:"55"}),B(Ee,{prop:"user.username",label:"用户名"}),B(Ee,{prop:"user.name",label:"姓名"}),B(Ee,{prop:"user.phone",label:"手机号"}),B(Ee,{prop:"agent_type_text",label:"代理商类型"},{default:N(({row:e})=>[B(Re,{type:ze(e.agent_type)},{default:N(()=>[Q(g(e.agent_type_text),1)]),_:2},1032,["type"])]),_:1}),B(Ee,{prop:"agent_level_text",label:"代理商等级"},{default:N(({row:e})=>[B(Re,{type:Ue(e.agent_level)},{default:N(()=>[Q(g(e.agent_level_text),1)]),_:2},1032,["type"])]),_:1}),B(Ee,{prop:"expected_commission_rate",label:"期望佣金"},{default:N(({row:e})=>[Q(g(e.expected_commission_rate)+"% ",1)]),_:1}),B(Ee,{prop:"status_text",label:"状态"},{default:N(({row:e})=>[B(Re,{type:xe(e.status)},{default:N(()=>[Q(g(e.status_text),1)]),_:2},1032,["type"])]),_:1}),B(Ee,{prop:"created_at",label:"申请时间"},{default:N(({row:e})=>[Q(g(Ve(e.created_at)),1)]),_:1}),B(Ee,{label:"操作",width:"200"},{default:N(({row:e})=>[B(_e,{size:"small",onClick:a=>{return l=e,ie.value=l,void(le.value=!0);var l}},{default:N(()=>V[18]||(V[18]=[Q(" 查看详情 ",-1)])),_:2,__:[18]},1032,["onClick"]),"pending"===e.status?(F(),H(_e,{key:0,size:"small",type:"success",onClick:a=>he(e)},{default:N(()=>V[19]||(V[19]=[Q(" 通过 ",-1)])),_:2,__:[19]},1032,["onClick"])):M("",!0),"pending"===e.status?(F(),H(_e,{key:1,size:"small",type:"danger",onClick:a=>we(e)},{default:N(()=>V[20]||(V[20]=[Q(" 拒绝 ",-1)])),_:2,__:[20]},1032,["onClick"])):M("",!0)]),_:1})]),_:1},8,["data"])),[[Ne,Z.value]]),L("div",T,[B(Fe,{"current-page":ee.value,"onUpdate:currentPage":V[4]||(V[4]=e=>ee.value=e),"page-size":ae.value,"onUpdate:pageSize":V[5]||(V[5]=e=>ae.value=e),total:ne.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:be,onCurrentChange:ye},null,8,["current-page","page-size","total"])])]),_:1}),B(Me,{modelValue:le.value,"onUpdate:modelValue":V[9]||(V[9]=e=>le.value=e),title:"申请详情",width:"800px"},{footer:N(()=>[B(_e,{onClick:V[6]||(V[6]=e=>le.value=!1)},{default:N(()=>V[25]||(V[25]=[Q("关闭",-1)])),_:1,__:[25]}),"pending"===ie.value?.status?(F(),H(_e,{key:0,type:"success",onClick:V[7]||(V[7]=e=>he(ie.value))},{default:N(()=>V[26]||(V[26]=[Q(" 通过申请 ",-1)])),_:1,__:[26]})):M("",!0),"pending"===ie.value?.status?(F(),H(_e,{key:1,type:"danger",onClick:V[8]||(V[8]=e=>we(ie.value))},{default:N(()=>V[27]||(V[27]=[Q(" 拒绝申请 ",-1)])),_:1,__:[27]})):M("",!0)]),default:N(()=>[ie.value?(F(),D("div",X,[B(Be,{column:2,border:""},{default:N(()=>[B(Le,{label:"申请人"},{default:N(()=>[Q(g(ie.value.user?.name),1)]),_:1}),B(Le,{label:"用户名"},{default:N(()=>[Q(g(ie.value.user?.username),1)]),_:1}),B(Le,{label:"手机号"},{default:N(()=>[Q(g(ie.value.user?.phone),1)]),_:1}),B(Le,{label:"邮箱"},{default:N(()=>[Q(g(ie.value.user?.email),1)]),_:1}),B(Le,{label:"代理商类型"},{default:N(()=>[B(Re,{type:ze(ie.value.agent_type)},{default:N(()=>[Q(g(ie.value.agent_type_text),1)]),_:1},8,["type"])]),_:1}),B(Le,{label:"代理商等级"},{default:N(()=>[B(Re,{type:Ue(ie.value.agent_level)},{default:N(()=>[Q(g(ie.value.agent_level_text),1)]),_:1},8,["type"])]),_:1}),B(Le,{label:"期望佣金比例"},{default:N(()=>[Q(g(ie.value.expected_commission_rate)+"% ",1)]),_:1}),B(Le,{label:"申请状态"},{default:N(()=>[B(Re,{type:xe(ie.value.status)},{default:N(()=>[Q(g(ie.value.status_text),1)]),_:1},8,["type"])]),_:1})]),_:1}),L("div",$,[V[21]||(V[21]=L("h4",null,"企业信息",-1)),B(Be,{column:2,border:""},{default:N(()=>[B(Le,{label:"公司名称"},{default:N(()=>[Q(g(ie.value.business_info?.company_name||"无"),1)]),_:1}),B(Le,{label:"营业执照"},{default:N(()=>[Q(g(ie.value.business_info?.business_license||"无"),1)]),_:1}),B(Le,{label:"经营范围",span:2},{default:N(()=>[Q(g(ie.value.business_info?.business_scope||"无"),1)]),_:1})]),_:1})]),L("div",G,[V[22]||(V[22]=L("h4",null,"联系信息",-1)),B(Be,{column:2,border:""},{default:N(()=>[B(Le,{label:"联系人"},{default:N(()=>[Q(g(ie.value.contact_info?.contact_person),1)]),_:1}),B(Le,{label:"联系电话"},{default:N(()=>[Q(g(ie.value.contact_info?.contact_phone),1)]),_:1}),B(Le,{label:"联系邮箱"},{default:N(()=>[Q(g(ie.value.contact_info?.contact_email||"无"),1)]),_:1}),B(Le,{label:"联系地址"},{default:N(()=>[Q(g(ie.value.contact_info?.contact_address||"无"),1)]),_:1})]),_:1})]),L("div",J,[V[23]||(V[23]=L("h4",null,"申请理由",-1)),L("p",null,g(ie.value.application_reason),1)]),ie.value.review_comment?(F(),D("div",K,[V[24]||(V[24]=L("h4",null,"审核意见",-1)),L("p",null,g(ie.value.review_comment),1),L("p",Y," 审核人："+g(ie.value.reviewer?.name)+" 审核时间："+g(Ve(ie.value.reviewed_at)),1)])):M("",!0)])):M("",!0)]),_:1},8,["modelValue"]),B(Me,{modelValue:te.value,"onUpdate:modelValue":V[12]||(V[12]=e=>te.value=e),title:pe.value,width:"500px"},{footer:N(()=>[B(_e,{onClick:V[11]||(V[11]=e=>te.value=!1)},{default:N(()=>V[28]||(V[28]=[Q("取消",-1)])),_:1,__:[28]}),B(_e,{type:"primary",onClick:ke,loading:ue.value},{default:N(()=>V[29]||(V[29]=[Q(" 确认 ",-1)])),_:1,__:[29]},8,["loading"])]),default:N(()=>[B(ve,{model:re,"label-width":"80px"},{default:N(()=>[B(A,{label:"审核意见"},{default:N(()=>[B(S,{modelValue:re.comment,"onUpdate:modelValue":V[10]||(V[10]=e=>re.comment=e),type:"textarea",rows:4,placeholder:"请输入审核意见"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-26e5966c"]]);export{Z as default};
