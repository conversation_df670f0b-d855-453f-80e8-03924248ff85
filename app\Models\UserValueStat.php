<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserValueStat extends Model
{
    protected $fillable = [
        'user_id',
        'date',
        'total_spent',
        'order_count',
        'avg_order_value',
        'lifetime_value',
        'predicted_ltv',
        'value_segment',
        'last_purchase_date',
        'purchase_frequency',
        'value_data',
    ];

    protected $casts = [
        'date' => 'date',
        'total_spent' => 'decimal:2',
        'order_count' => 'integer',
        'avg_order_value' => 'decimal:2',
        'lifetime_value' => 'decimal:2',
        'predicted_ltv' => 'decimal:2',
        'last_purchase_date' => 'date',
        'purchase_frequency' => 'decimal:2',
        'value_data' => 'array',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 按价值分段查询
     */
    public function scopeByValueSegment($query, $segment)
    {
        return $query->where('value_segment', $segment);
    }

    /**
     * 按日期范围查询
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * 高价值用户
     */
    public function scopeHighValue($query)
    {
        return $query->where('value_segment', 'high');
    }

    /**
     * 获取价值分布
     */
    public static function getValueDistribution($date = null)
    {
        $query = static::query();
        
        if ($date) {
            $query->where('date', $date);
        } else {
            $query->where('date', now()->format('Y-m-d'));
        }

        return $query->selectRaw('value_segment, COUNT(*) as count, AVG(lifetime_value) as avg_ltv')
            ->groupBy('value_segment')
            ->get();
    }

    /**
     * 获取Top用户
     */
    public static function getTopValueUsers($limit = 10, $date = null)
    {
        $query = static::with('user');
        
        if ($date) {
            $query->where('date', $date);
        } else {
            $query->where('date', now()->format('Y-m-d'));
        }

        return $query->orderBy('lifetime_value', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 计算ARPU
     */
    public static function calculateARPU($startDate, $endDate)
    {
        $totalRevenue = static::whereBetween('date', [$startDate, $endDate])
            ->sum('total_spent');

        $userCount = static::whereBetween('date', [$startDate, $endDate])
            ->distinct('user_id')
            ->count();

        return $userCount > 0 ? round($totalRevenue / $userCount, 2) : 0;
    }

    /**
     * 计算ARPPU
     */
    public static function calculateARPPU($startDate, $endDate)
    {
        $totalRevenue = static::whereBetween('date', [$startDate, $endDate])
            ->where('total_spent', '>', 0)
            ->sum('total_spent');

        $payingUserCount = static::whereBetween('date', [$startDate, $endDate])
            ->where('total_spent', '>', 0)
            ->distinct('user_id')
            ->count();

        return $payingUserCount > 0 ? round($totalRevenue / $payingUserCount, 2) : 0;
    }

    /**
     * 更新用户价值分段
     */
    public function updateValueSegment()
    {
        $ltv = $this->lifetime_value;

        if ($ltv >= 10000) {
            $segment = 'vip';
        } elseif ($ltv >= 5000) {
            $segment = 'high';
        } elseif ($ltv >= 1000) {
            $segment = 'medium';
        } elseif ($ltv > 0) {
            $segment = 'low';
        } else {
            $segment = 'potential';
        }

        $this->update(['value_segment' => $segment]);
    }

    /**
     * 预测生命周期价值
     */
    public function predictLTV()
    {
        // 简单的LTV预测算法
        $avgOrderValue = $this->avg_order_value;
        $frequency = $this->purchase_frequency;
        $lifespan = 24; // 假设用户生命周期为24个月

        $predictedLTV = $avgOrderValue * $frequency * $lifespan;

        $this->update(['predicted_ltv' => $predictedLTV]);

        return $predictedLTV;
    }
} 