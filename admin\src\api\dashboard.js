import api from './index'

// 仪表板相关API
export const getDashboardStats = () => {
  return api.get('/admin/dashboard/full-stats')
}

export const getDashboardCharts = () => {
  return api.get('/admin/dashboard/charts')
}

export const getIncomeChart = (params) => {
  return api.get('/admin/dashboard/income-chart', { params })
}

export const getUserGrowthChart = (params) => {
  return api.get('/admin/dashboard/user-growth-chart', { params })
}

export const getOrderChart = (params) => {
  return api.get('/admin/dashboard/order-chart', { params })
}

export const getRegionChart = () => {
  return api.get('/admin/dashboard/region-chart')
}

export const getPopularGroups = () => {
  return api.get('/admin/dashboard/popular-groups')
}

export const getActiveUsers = () => {
  return api.get('/admin/dashboard/active-users')
}

export const getRecentActivities = () => {
  return api.get('/admin/dashboard/recent-activities')
}

export const getIncomeTrend = (params) => {
  return api.get('/admin/dashboard/income-trend', { params })
}

export const getOrderSourceDistribution = () => {
  return api.get('/admin/dashboard/order-source')
}

export const getUserGrowthData = (params) => {
  return api.get('/admin/dashboard/user-growth', { params })
}

export const getSystemStatus = () => {
  return api.get('/admin/dashboard/system-status')
}

export const getRecentOrders = () => {
  return api.get('/admin/dashboard/recent-orders')
}

export const getTopDistributors = () => {
  return api.get('/admin/dashboard/top-distributors')
}

export const getRealTimeData = () => {
  return api.get('/admin/dashboard/realtime')
}