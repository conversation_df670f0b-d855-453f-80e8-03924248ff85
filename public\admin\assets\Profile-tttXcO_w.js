import{_ as a,u as e,g as l,e as s,f as r,h as t}from"./index-D2bI4m-v.js";/* empty css               *//* empty css                *//* empty css                     *//* empty css               */import{A as o}from"./AvatarUpload-QrJM1H43.js";import{b2 as u,b3 as d,bc as n,bd as i,aH as p,b8 as c,U as m,as as v,b4 as _,Q as f}from"./element-plus-DcSKpKA8.js";import{r as w,L as g,e as b,k as h,l as V,E as y,z as k,D as j,t as U}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";/* empty css                  *//* empty css                    */const q={class:"app-container"},x={class:"balance"},I={class:"stat-item"},z={class:"stat-value"},A={class:"stat-item"},C={class:"stat-value"},E={class:"stat-item"},F={class:"stat-value"},B={class:"stat-item"},P={class:"stat-value"},R={class:"login-info"},$=a({__name:"Profile",setup(a){const $=e(),D=w(),H=w(),J=w(!1),L=w(!1),Q=w({id:null,username:"",nickname:"",email:"",phone:"",avatar:"",role:"",balance:0,created_at:"",last_login_at:"",last_login_ip:""}),S=w({current_password:"",password:"",password_confirmation:""}),G=w({total_orders:0,total_amount:0,total_commission:0,children_count:0});w("/api/v1/upload"),w({Authorization:`Bearer ${l()}`});const K=g({nickname:[{required:!0,message:"昵称不能为空",trigger:"blur"}],email:[{type:"email",message:"邮箱格式不正确",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}]}),M=g({current_password:[{required:!0,message:"当前密码不能为空",trigger:"blur"}],password:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],password_confirmation:[{required:!0,message:"确认密码不能为空",trigger:"blur"},{validator:(a,e,l)=>{e!==S.value.password?l(new Error("两次输入的密码不一致")):l()},trigger:"blur"}]}),N=async()=>{try{await D.value.validate(),J.value=!0;const{data:a}=await r({nickname:Q.value.nickname,email:Q.value.email,phone:Q.value.phone,avatar:Q.value.avatar});a.success&&(f.success("个人信息更新成功"),await $.getUserInfo())}catch(a){console.error("更新失败:",a)}finally{J.value=!1}},O=async()=>{try{await H.value.validate(),L.value=!0;const{data:a}=await t({current_password:S.value.current_password,password:S.value.password,password_confirmation:S.value.password_confirmation});a.success&&(f.success("密码修改成功"),T())}catch(a){console.error("密码修改失败:",a)}finally{L.value=!1}},T=()=>{S.value={current_password:"",password:"",password_confirmation:""},H.value?.resetFields()},W=a=>{Q.value.avatar=a.url,$.userInfo&&($.userInfo.avatar=a.url),f.success("头像上传成功!")},X=a=>{console.error("头像上传失败:",a),f.error("头像上传失败，请重试")},Y=a=>({admin:"超级管理员",substation:"分站管理员",distributor:"分销商",user:"普通用户"}[a]);return b(()=>{(async()=>{try{const{data:a}=await s();a.success&&(Q.value={...a.data.user},G.value=a.data.stats||{})}catch(a){f.error("获取用户信息失败")}})()}),(a,e)=>{const l=i,s=p,r=c,t=v,f=n,w=d,g=u,b=_;return V(),h("div",q,[y(b,{gutter:20},{default:k(()=>[y(g,{span:12},{default:k(()=>[y(w,null,{header:k(()=>e[8]||(e[8]=[U("div",{class:"card-header"},[U("span",null,"个人信息")],-1)])),default:k(()=>[y(f,{ref_key:"profileFormRef",ref:D,model:Q.value,rules:K,"label-width":"100px"},{default:k(()=>[y(l,{label:"头像"},{default:k(()=>[y(o,{modelValue:Q.value.avatar,"onUpdate:modelValue":e[0]||(e[0]=a=>Q.value.avatar=a),size:120,"max-size":5,"enable-preview":!0,onSuccess:W,onError:X},null,8,["modelValue"])]),_:1}),y(l,{label:"用户名",prop:"username"},{default:k(()=>[y(s,{modelValue:Q.value.username,"onUpdate:modelValue":e[1]||(e[1]=a=>Q.value.username=a),readonly:""},null,8,["modelValue"])]),_:1}),y(l,{label:"昵称",prop:"nickname"},{default:k(()=>[y(s,{modelValue:Q.value.nickname,"onUpdate:modelValue":e[2]||(e[2]=a=>Q.value.nickname=a),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),y(l,{label:"邮箱",prop:"email"},{default:k(()=>[y(s,{modelValue:Q.value.email,"onUpdate:modelValue":e[3]||(e[3]=a=>Q.value.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),y(l,{label:"手机号",prop:"phone"},{default:k(()=>[y(s,{modelValue:Q.value.phone,"onUpdate:modelValue":e[4]||(e[4]=a=>Q.value.phone=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),y(l,{label:"角色"},{default:k(()=>{return[y(r,{type:(a=Q.value.role,{admin:"danger",substation:"warning",distributor:"success",user:"info"}[a])},{default:k(()=>[j(m(Y(Q.value.role)),1)]),_:1},8,["type"])];var a}),_:1}),y(l,{label:"余额"},{default:k(()=>[U("span",x,m(Q.value.balance)+" 元",1)]),_:1}),y(l,{label:"注册时间"},{default:k(()=>[U("span",null,m(Q.value.created_at),1)]),_:1}),y(l,null,{default:k(()=>[y(t,{type:"primary",onClick:N,loading:J.value},{default:k(()=>e[9]||(e[9]=[j("更新信息",-1)])),_:1,__:[9]},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1}),y(g,{span:12},{default:k(()=>[y(w,null,{header:k(()=>e[10]||(e[10]=[U("div",{class:"card-header"},[U("span",null,"修改密码")],-1)])),default:k(()=>[y(f,{ref_key:"passwordFormRef",ref:H,model:S.value,rules:M,"label-width":"100px"},{default:k(()=>[y(l,{label:"当前密码",prop:"current_password"},{default:k(()=>[y(s,{modelValue:S.value.current_password,"onUpdate:modelValue":e[5]||(e[5]=a=>S.value.current_password=a),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),_:1}),y(l,{label:"新密码",prop:"password"},{default:k(()=>[y(s,{modelValue:S.value.password,"onUpdate:modelValue":e[6]||(e[6]=a=>S.value.password=a),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),y(l,{label:"确认密码",prop:"password_confirmation"},{default:k(()=>[y(s,{modelValue:S.value.password_confirmation,"onUpdate:modelValue":e[7]||(e[7]=a=>S.value.password_confirmation=a),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),y(l,null,{default:k(()=>[y(t,{type:"primary",onClick:O,loading:L.value},{default:k(()=>e[11]||(e[11]=[j("修改密码",-1)])),_:1,__:[11]},8,["loading"]),y(t,{onClick:T},{default:k(()=>e[12]||(e[12]=[j("重置",-1)])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1}),y(b,{gutter:20,class:"mt-4"},{default:k(()=>[y(g,{span:24},{default:k(()=>[y(w,null,{header:k(()=>e[13]||(e[13]=[U("div",{class:"card-header"},[U("span",null,"账户统计")],-1)])),default:k(()=>[y(b,{gutter:20,class:"stats-row"},{default:k(()=>[y(g,{span:6},{default:k(()=>[U("div",I,[U("div",z,m(G.value.total_orders||0),1),e[14]||(e[14]=U("div",{class:"stat-label"},"总订单数",-1))])]),_:1}),y(g,{span:6},{default:k(()=>[U("div",A,[U("div",C,m(G.value.total_amount||0)+" 元",1),e[15]||(e[15]=U("div",{class:"stat-label"},"总消费金额",-1))])]),_:1}),y(g,{span:6},{default:k(()=>[U("div",E,[U("div",F,m(G.value.total_commission||0)+" 元",1),e[16]||(e[16]=U("div",{class:"stat-label"},"总佣金收入",-1))])]),_:1}),y(g,{span:6},{default:k(()=>[U("div",B,[U("div",P,m(G.value.children_count||0),1),e[17]||(e[17]=U("div",{class:"stat-label"},"下级用户数",-1))])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),y(b,{class:"mt-4"},{default:k(()=>[y(g,{span:24},{default:k(()=>[y(w,null,{header:k(()=>e[18]||(e[18]=[U("div",{class:"card-header"},[U("span",null,"登录记录")],-1)])),default:k(()=>[U("div",R,[U("p",null,[e[19]||(e[19]=U("strong",null,"最后登录时间：",-1)),j(m(Q.value.last_login_at||"从未登录"),1)]),U("p",null,[e[20]||(e[20]=U("strong",null,"最后登录IP：",-1)),j(m(Q.value.last_login_ip||"未知"),1)])])]),_:1})]),_:1})]),_:1})])}}},[["__scopeId","data-v-a628de5a"]]);export{$ as default};
