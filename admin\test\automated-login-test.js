/**
 * 登录页面自动化测试脚本
 * 用于验证登录页面的各项功能和用户体验
 */

class LoginPageTester {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
    }

    // 记录测试结果
    recordTest(testName, passed, details = '') {
        this.totalTests++;
        if (passed) {
            this.passedTests++;
        } else {
            this.failedTests++;
        }
        
        this.testResults.push({
            name: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
        
        console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
    }

    // 测试页面基本元素存在性
    async testPageElements() {
        console.log('\n🔍 开始测试页面元素...');
        
        // 测试主容器
        const loginContainer = document.querySelector('.login-container');
        this.recordTest('主容器存在', !!loginContainer, '登录容器元素');
        
        // 测试登录盒子
        const loginBox = document.querySelector('.login-box');
        this.recordTest('登录盒子存在', !!loginBox, '登录表单容器');
        
        // 测试表单元素
        const usernameInput = document.querySelector('input[placeholder*="用户名"]');
        this.recordTest('用户名输入框', !!usernameInput, '用户名输入元素');
        
        const passwordInput = document.querySelector('input[type="password"], input[placeholder*="密码"]');
        this.recordTest('密码输入框', !!passwordInput, '密码输入元素');
        
        const loginButton = document.querySelector('.login-button, button[type="submit"]');
        this.recordTest('登录按钮', !!loginButton, '登录提交按钮');
        
        const rememberCheckbox = document.querySelector('input[type="checkbox"]');
        this.recordTest('记住我复选框', !!rememberCheckbox, '记住登录状态选项');
        
        const forgotLink = document.querySelector('a[href*="forgot"], .forgot-password');
        this.recordTest('忘记密码链接', !!forgotLink, '密码重置链接');
    }

    // 测试响应式布局
    async testResponsiveLayout() {
        console.log('\n📱 开始测试响应式布局...');
        
        const testResolutions = [
            { width: 1920, height: 1080, name: '桌面端大屏' },
            { width: 1366, height: 768, name: '桌面端标准' },
            { width: 1024, height: 600, name: '小屏笔记本' },
            { width: 768, height: 1024, name: '平板竖屏' },
            { width: 375, height: 667, name: 'iPhone' },
            { width: 360, height: 640, name: 'Android' }
        ];
        
        for (const resolution of testResolutions) {
            // 模拟屏幕尺寸变化
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: resolution.width
            });
            Object.defineProperty(window, 'innerHeight', {
                writable: true,
                configurable: true,
                value: resolution.height
            });
            
            // 触发resize事件
            window.dispatchEvent(new Event('resize'));
            
            // 等待响应式调整
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // 检查布局是否正确
            const loginBox = document.querySelector('.login-box');
            const isVisible = loginBox && window.getComputedStyle(loginBox).display !== 'none';
            const hasOverflow = loginBox && loginBox.scrollHeight > window.innerHeight;
            
            this.recordTest(
                `${resolution.name}布局测试`,
                isVisible,
                `${resolution.width}×${resolution.height} ${hasOverflow ? '(可滚动)' : '(完整显示)'}`
            );
        }
    }

    // 测试表单交互功能
    async testFormInteractions() {
        console.log('\n⚙️ 开始测试表单交互...');
        
        const usernameInput = document.querySelector('input[placeholder*="用户名"]');
        const passwordInput = document.querySelector('input[type="password"], input[placeholder*="密码"]');
        const loginButton = document.querySelector('.login-button, button[type="submit"]');
        const rememberCheckbox = document.querySelector('input[type="checkbox"]');
        
        // 测试输入框焦点
        if (usernameInput) {
            usernameInput.focus();
            const isFocused = document.activeElement === usernameInput;
            this.recordTest('用户名输入框焦点', isFocused, '输入框可以获得焦点');
            
            // 测试输入功能
            usernameInput.value = 'testuser';
            const hasValue = usernameInput.value === 'testuser';
            this.recordTest('用户名输入功能', hasValue, '可以正常输入文本');
        }
        
        if (passwordInput) {
            passwordInput.focus();
            const isFocused = document.activeElement === passwordInput;
            this.recordTest('密码输入框焦点', isFocused, '密码框可以获得焦点');
            
            // 测试密码输入
            passwordInput.value = 'testpass';
            const hasValue = passwordInput.value === 'testpass';
            this.recordTest('密码输入功能', hasValue, '可以正常输入密码');
        }
        
        // 测试复选框
        if (rememberCheckbox) {
            const initialChecked = rememberCheckbox.checked;
            rememberCheckbox.click();
            const changedState = rememberCheckbox.checked !== initialChecked;
            this.recordTest('复选框交互', changedState, '复选框状态可以切换');
        }
        
        // 测试按钮点击
        if (loginButton) {
            const isClickable = !loginButton.disabled;
            this.recordTest('登录按钮可点击', isClickable, '按钮处于可点击状态');
        }
    }

    // 测试键盘导航
    async testKeyboardNavigation() {
        console.log('\n⌨️ 开始测试键盘导航...');
        
        const focusableElements = document.querySelectorAll(
            'input, button, a, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length > 0) {
            // 测试Tab键导航
            focusableElements[0].focus();
            let currentIndex = 0;
            
            for (let i = 0; i < Math.min(focusableElements.length, 5); i++) {
                const currentElement = document.activeElement;
                const expectedElement = focusableElements[currentIndex];
                
                const isCorrectFocus = currentElement === expectedElement;
                this.recordTest(
                    `Tab导航步骤${i + 1}`,
                    isCorrectFocus,
                    `焦点在${currentElement.tagName.toLowerCase()}`
                );
                
                // 模拟Tab键
                const tabEvent = new KeyboardEvent('keydown', {
                    key: 'Tab',
                    code: 'Tab',
                    keyCode: 9,
                    bubbles: true
                });
                document.dispatchEvent(tabEvent);
                
                currentIndex = (currentIndex + 1) % focusableElements.length;
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        }
    }

    // 测试CSS样式和动画
    async testVisualEffects() {
        console.log('\n🎨 开始测试视觉效果...');
        
        const loginBox = document.querySelector('.login-box');
        if (loginBox) {
            const styles = window.getComputedStyle(loginBox);
            
            // 测试基本样式
            const hasBackground = styles.backgroundColor !== 'rgba(0, 0, 0, 0)';
            this.recordTest('登录盒子背景', hasBackground, '背景色已设置');
            
            const hasBorderRadius = parseFloat(styles.borderRadius) > 0;
            this.recordTest('圆角样式', hasBorderRadius, '边框圆角已应用');
            
            const hasShadow = styles.boxShadow !== 'none';
            this.recordTest('阴影效果', hasShadow, '盒子阴影已应用');
        }
        
        // 测试动画元素
        const animatedElements = document.querySelectorAll('[class*="animate"], [class*="animation"]');
        this.recordTest('动画元素存在', animatedElements.length > 0, `发现${animatedElements.length}个动画元素`);
        
        // 测试背景装饰
        const backgroundElements = document.querySelectorAll('.bg-decoration, .simple-bg');
        this.recordTest('背景装饰', backgroundElements.length > 0, '背景装饰元素存在');
    }

    // 测试性能指标
    async testPerformance() {
        console.log('\n⚡ 开始测试性能指标...');
        
        // 测试页面加载时间
        const navigationTiming = performance.getEntriesByType('navigation')[0];
        if (navigationTiming) {
            const loadTime = navigationTiming.loadEventEnd - navigationTiming.fetchStart;
            this.recordTest('页面加载时间', loadTime < 2000, `${loadTime.toFixed(0)}ms`);
            
            const domContentLoaded = navigationTiming.domContentLoadedEventEnd - navigationTiming.fetchStart;
            this.recordTest('DOM加载时间', domContentLoaded < 1000, `${domContentLoaded.toFixed(0)}ms`);
        }
        
        // 测试内存使用
        if (performance.memory) {
            const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
            this.recordTest('内存使用', memoryUsage < 50, `${memoryUsage.toFixed(1)}MB`);
        }
        
        // 测试FPS (简单检测)
        let frameCount = 0;
        const startTime = performance.now();
        
        const countFrames = () => {
            frameCount++;
            if (performance.now() - startTime < 1000) {
                requestAnimationFrame(countFrames);
            } else {
                this.recordTest('动画帧率', frameCount > 50, `${frameCount}fps`);
            }
        };
        requestAnimationFrame(countFrames);
    }

    // 运行所有测试
    async runAllTests() {
        console.log('🚀 开始登录页面全面测试...\n');
        
        try {
            await this.testPageElements();
            await this.testResponsiveLayout();
            await this.testFormInteractions();
            await this.testKeyboardNavigation();
            await this.testVisualEffects();
            await this.testPerformance();
            
            this.generateReport();
        } catch (error) {
            console.error('测试过程中发生错误:', error);
        }
    }

    // 生成测试报告
    generateReport() {
        console.log('\n📊 测试报告生成中...\n');
        
        const successRate = ((this.passedTests / this.totalTests) * 100).toFixed(1);
        
        console.log('='.repeat(60));
        console.log('🧪 登录页面自动化测试报告');
        console.log('='.repeat(60));
        console.log(`📈 总测试数: ${this.totalTests}`);
        console.log(`✅ 通过测试: ${this.passedTests}`);
        console.log(`❌ 失败测试: ${this.failedTests}`);
        console.log(`📊 成功率: ${successRate}%`);
        console.log('='.repeat(60));
        
        // 详细结果
        console.log('\n📋 详细测试结果:');
        this.testResults.forEach((result, index) => {
            console.log(`${index + 1}. ${result.passed ? '✅' : '❌'} ${result.name}: ${result.details}`);
        });
        
        // 评级
        let grade = 'F';
        if (successRate >= 95) grade = 'A+';
        else if (successRate >= 90) grade = 'A';
        else if (successRate >= 85) grade = 'B+';
        else if (successRate >= 80) grade = 'B';
        else if (successRate >= 70) grade = 'C';
        else if (successRate >= 60) grade = 'D';
        
        console.log(`\n🏆 综合评级: ${grade}`);
        console.log(`\n测试完成时间: ${new Date().toLocaleString()}`);
    }
}

// 导出测试类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LoginPageTester;
}

// 浏览器环境下自动运行
if (typeof window !== 'undefined') {
    window.LoginPageTester = LoginPageTester;
    
    // 页面加载完成后自动运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                const tester = new LoginPageTester();
                tester.runAllTests();
            }, 1000);
        });
    } else {
        setTimeout(() => {
            const tester = new LoginPageTester();
            tester.runAllTests();
        }, 1000);
    }
}
