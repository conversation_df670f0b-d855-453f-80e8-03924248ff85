<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .debug-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="debug-title">🔍 管理后台导航调试页面</h1>
        
        <div class="debug-item">
            <strong>当前URL:</strong> <span id="currentUrl"></span>
        </div>
        
        <div class="debug-item">
            <strong>预览模式状态:</strong> <span id="previewMode"></span>
        </div>
        
        <div class="debug-item">
            <strong>LocalStorage 预览模式:</strong> <span id="localStoragePreview"></span>
        </div>
        
        <div class="debug-item">
            <button class="btn" onclick="enablePreviewMode()">启用预览模式</button>
            <button class="btn" onclick="disablePreviewMode()">禁用预览模式</button>
            <button class="btn" onclick="clearStorage()">清除存储</button>
            <button class="btn" onclick="goToAdmin()">进入管理后台</button>
        </div>
    </div>

    <div class="debug-container">
        <h2 class="debug-title">📋 系统检查</h2>
        <div id="systemChecks"></div>
    </div>

    <div class="debug-container">
        <h2 class="debug-title">🛠️ 快速操作</h2>
        <div class="debug-item">
            <button class="btn" onclick="testPreviewMode()">测试预览模式</button>
            <button class="btn" onclick="checkUserStore()">检查用户存储</button>
            <button class="btn" onclick="checkRoutes()">检查路由配置</button>
        </div>
    </div>

    <div class="debug-container">
        <h2 class="debug-title">📊 调试日志</h2>
        <div id="debugLogs"></div>
    </div>

    <script>
        // 更新页面信息
        function updatePageInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            
            const urlParams = new URLSearchParams(window.location.search);
            const previewFromUrl = urlParams.get('preview') === 'true';
            const previewFromStorage = localStorage.getItem('preview-mode') === 'true';
            
            document.getElementById('previewMode').textContent = previewFromUrl ? '✅ 已启用 (URL)' : '❌ 未启用';
            document.getElementById('localStoragePreview').textContent = previewFromStorage ? '✅ 已启用' : '❌ 未启用';
        }

        // 启用预览模式
        function enablePreviewMode() {
            localStorage.setItem('preview-mode', 'true');
            addLog('✅ 预览模式已启用', 'success');
            updatePageInfo();
        }

        // 禁用预览模式
        function disablePreviewMode() {
            localStorage.removeItem('preview-mode');
            addLog('❌ 预览模式已禁用', 'warning');
            updatePageInfo();
        }

        // 清除存储
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            addLog('🗑️ 所有存储已清除', 'warning');
            updatePageInfo();
        }

        // 进入管理后台
        function goToAdmin() {
            window.location.href = '/?preview=true';
        }

        // 测试预览模式
        function testPreviewMode() {
            enablePreviewMode();
            setTimeout(() => {
                window.location.href = '/?preview=true';
            }, 500);
        }

        // 检查用户存储
        function checkUserStore() {
            const userInfo = localStorage.getItem('user-info');
            const token = localStorage.getItem('token');
            
            addLog('用户信息: ' + (userInfo || '无'), userInfo ? 'success' : 'error');
            addLog('Token: ' + (token || '无'), token ? 'success' : 'error');
        }

        // 检查路由配置
        function checkRoutes() {
            addLog('正在检查路由配置...', 'warning');
            // 这里可以添加更多路由检查逻辑
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('debugLogs');
            const logItem = document.createElement('div');
            logItem.className = `debug-item ${type}`;
            logItem.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            logsContainer.appendChild(logItem);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        // 系统检查
        function performSystemChecks() {
            const checksContainer = document.getElementById('systemChecks');
            const checks = [
                {
                    name: '浏览器支持',
                    test: () => typeof localStorage !== 'undefined' && typeof URLSearchParams !== 'undefined',
                    message: '浏览器支持现代Web API'
                },
                {
                    name: '本地存储',
                    test: () => {
                        try {
                            localStorage.setItem('test', 'test');
                            localStorage.removeItem('test');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    },
                    message: '本地存储功能正常'
                },
                {
                    name: '预览模式配置',
                    test: () => {
                        const urlParams = new URLSearchParams(window.location.search);
                        return urlParams.get('preview') === 'true' || localStorage.getItem('preview-mode') === 'true';
                    },
                    message: '预览模式已配置'
                }
            ];

            checks.forEach(check => {
                const result = check.test();
                const checkItem = document.createElement('div');
                checkItem.className = `debug-item ${result ? 'success' : 'error'}`;
                checkItem.innerHTML = `<strong>${check.name}:</strong> ${result ? '✅' : '❌'} ${check.message}`;
                checksContainer.appendChild(checkItem);
            });
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            updatePageInfo();
            performSystemChecks();
            addLog('🚀 调试页面已加载', 'success');
        });

        // 定期更新信息
        setInterval(updatePageInfo, 5000);
    </script>
</body>
</html>
