# 简单的PowerShell HTTP服务器
param(
    [int]$Port = 8080
)

Add-Type -AssemblyName System.Net.HttpListener
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")
$listener.Start()

Write-Host "服务器已启动: http://localhost:$Port" -ForegroundColor Green
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow

try {
    while ($listener.IsListening) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        $localPath = $request.Url.LocalPath
        if ($localPath -eq '/') {
            $localPath = '/preview.html'
        }
        
        $filePath = Join-Path (Get-Location) $localPath.TrimStart('/')
        
        if (Test-Path $filePath) {
            $content = Get-Content $filePath -Raw -Encoding UTF8
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($content)
            
            $response.ContentLength64 = $buffer.Length
            if ($filePath.EndsWith('.html')) {
                $response.ContentType = 'text/html; charset=utf-8'
            } elseif ($filePath.EndsWith('.css')) {
                $response.ContentType = 'text/css'
            } elseif ($filePath.EndsWith('.js')) {
                $response.ContentType = 'application/javascript'
            } else {
                $response.ContentType = 'text/plain'
            }
            
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
            Write-Host "已提供文件: $localPath" -ForegroundColor Cyan
        } else {
            $response.StatusCode = 404
            $errorContent = "404 - 文件未找到: $localPath"
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($errorContent)
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
            Write-Host "404 错误: $localPath" -ForegroundColor Red
        }
        
        $response.Close()
    }
} finally {
    $listener.Stop()
    Write-Host "服务器已停止" -ForegroundColor Red
}