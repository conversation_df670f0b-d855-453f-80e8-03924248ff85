<?php

require_once 'vendor/autoload.php';

use App\Models\WechatGroup;
use App\Models\GroupAccessLog;
use App\Models\DomainPool;
use App\Services\IPLocationService;
use App\Services\BrowserDetectionService;
use App\Services\GroupAccessValidationService;

/**
 * 增强功能测试脚本
 * 测试城市定位、营销功能、防封系统等新增功能
 */
class EnhancedFeaturesTest
{
    private $results = [];

    public function runAllTests()
    {
        echo "=== FFJQ项目增强功能测试 ===\n\n";

        $this->testCityLocationService();
        $this->testMarketingFeatures();
        $this->testAntiBlockSystem();
        $this->testBrowserDetection();
        $this->testGroupAccessValidation();
        $this->testDomainPoolManagement();

        $this->printResults();
    }

    /**
     * 测试城市定位服务
     */
    private function testCityLocationService()
    {
        echo "1. 测试城市定位服务...\n";
        
        try {
            $ipLocationService = new IPLocationService();
            
            // 测试IP定位
            $testIPs = ['*******', '***************', '*********'];
            foreach ($testIPs as $ip) {
                $city = $ipLocationService->getCity($ip);
                echo "   IP {$ip} -> 城市: {$city}\n";
            }
            
            // 测试城市格式化
            $testCities = ['北京市', '上海市', '广州市'];
            foreach ($testCities as $city) {
                $formatted = $ipLocationService->formatCityName($city);
                echo "   城市格式化: {$city} -> {$formatted}\n";
            }
            
            $this->results['city_location'] = '✓ 通过';
            
        } catch (Exception $e) {
            $this->results['city_location'] = '✗ 失败: ' . $e->getMessage();
        }
        
        echo "\n";
    }

    /**
     * 测试营销功能
     */
    private function testMarketingFeatures()
    {
        echo "2. 测试营销功能...\n";
        
        try {
            // 创建测试群组
            $group = new WechatGroup([
                'title' => 'xxx交流群',
                'auto_city_replace' => 1,
                'avatar_library' => 'qq',
                'display_type' => 1,
                'show_virtual_activity' => 1,
            ]);
            
            // 测试城市替换
            $testCities = ['北京', '上海', '广州'];
            foreach ($testCities as $city) {
                $replacedTitle = $group->getCityReplacedTitle($city);
                echo "   标题替换: {$group->title} + {$city} -> {$replacedTitle}\n";
            }
            
            // 测试虚拟成员生成
            $virtualMembers = $group->generateVirtualMembers(5);
            echo "   生成虚拟成员: " . count($virtualMembers) . "个\n";
            
            // 测试FAQ格式化
            $group->faq_content = "问题1----答案1\n问题2----答案2";
            $formattedFaq = $group->getFormattedFaqAttribute();
            echo "   FAQ格式化: " . count($formattedFaq) . "个问题\n";
            
            $this->results['marketing_features'] = '✓ 通过';
            
        } catch (Exception $e) {
            $this->results['marketing_features'] = '✗ 失败: ' . $e->getMessage();
        }
        
        echo "\n";
    }

    /**
     * 测试防封系统
     */
    private function testAntiBlockSystem()
    {
        echo "3. 测试防封系统...\n";
        
        try {
            // 测试域名池管理
            $domain = new DomainPool([
                'domain' => 'test.example.com',
                'status' => DomainPool::STATUS_NORMAL,
                'health_score' => 85,
            ]);
            
            echo "   域名状态: {$domain->status_name}\n";
            echo "   健康分数: {$domain->health_score}\n";
            echo "   是否可用: " . ($domain->isAvailable() ? '是' : '否') . "\n";
            
            // 测试域名健康检查
            $healthData = [
                'accessible' => true,
                'response_time' => 500,
                'ssl_valid' => true,
                'wechat_accessible' => true,
            ];
            echo "   健康检查模拟: 可访问=" . ($healthData['accessible'] ? '是' : '否') . "\n";
            
            $this->results['anti_block_system'] = '✓ 通过';
            
        } catch (Exception $e) {
            $this->results['anti_block_system'] = '✗ 失败: ' . $e->getMessage();
        }
        
        echo "\n";
    }

    /**
     * 测试浏览器检测
     */
    private function testBrowserDetection()
    {
        echo "4. 测试浏览器检测...\n";
        
        try {
            $browserService = new BrowserDetectionService();
            
            // 模拟不同的User-Agent
            $testUserAgents = [
                'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Mobile Safari/537.36 MicroMessenger/7.0.15.1680',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
            ];
            
            foreach ($testUserAgents as $ua) {
                // 这里需要模拟request，实际测试中会有request对象
                echo "   User-Agent检测: " . substr($ua, 0, 50) . "...\n";
            }
            
            // 测试浏览器引导页面生成
            $guidePage = $browserService->generateBrowserGuidePage('https://example.com', '测试群组');
            echo "   浏览器引导页面: " . (strlen($guidePage) > 0 ? '生成成功' : '生成失败') . "\n";
            
            $this->results['browser_detection'] = '✓ 通过';
            
        } catch (Exception $e) {
            $this->results['browser_detection'] = '✗ 失败: ' . $e->getMessage();
        }
        
        echo "\n";
    }

    /**
     * 测试群组访问验证
     */
    private function testGroupAccessValidation()
    {
        echo "5. 测试群组访问验证...\n";
        
        try {
            $validationService = new GroupAccessValidationService();
            
            // 测试访问令牌生成和验证
            $groupId = 1;
            $timestamp = time();
            $token = md5($groupId . $timestamp . 'test_key');
            
            echo "   访问令牌生成: {$token}\n";
            
            // 测试访问统计
            $mockStats = [
                'total_visits' => 100,
                'unique_visitors' => 80,
                'success_rate' => 85.5,
                'top_cities' => ['北京' => 20, '上海' => 15, '广州' => 10],
            ];
            
            echo "   访问统计模拟: 总访问{$mockStats['total_visits']}次, 成功率{$mockStats['success_rate']}%\n";
            
            $this->results['group_access_validation'] = '✓ 通过';
            
        } catch (Exception $e) {
            $this->results['group_access_validation'] = '✗ 失败: ' . $e->getMessage();
        }
        
        echo "\n";
    }

    /**
     * 测试域名池管理
     */
    private function testDomainPoolManagement()
    {
        echo "6. 测试域名池管理...\n";
        
        try {
            // 测试域名统计
            $stats = [
                'total' => 10,
                'normal' => 8,
                'abnormal' => 1,
                'blocked' => 1,
                'avg_health_score' => 75.5,
            ];
            
            echo "   域名池统计: 总计{$stats['total']}个, 正常{$stats['normal']}个\n";
            echo "   平均健康分数: {$stats['avg_health_score']}\n";
            
            // 测试自动维护
            $maintenanceResults = [
                'checked' => 5,
                'blocked' => 1,
                'restored' => 0,
                'errors' => [],
            ];
            
            echo "   自动维护: 检查{$maintenanceResults['checked']}个, 封禁{$maintenanceResults['blocked']}个\n";
            
            $this->results['domain_pool_management'] = '✓ 通过';
            
        } catch (Exception $e) {
            $this->results['domain_pool_management'] = '✗ 失败: ' . $e->getMessage();
        }
        
        echo "\n";
    }

    /**
     * 打印测试结果
     */
    private function printResults()
    {
        echo "=== 测试结果汇总 ===\n";
        foreach ($this->results as $test => $result) {
            echo sprintf("%-30s %s\n", $test, $result);
        }
        
        $passed = count(array_filter($this->results, function($result) {
            return strpos($result, '✓') === 0;
        }));
        
        $total = count($this->results);
        
        echo "\n总计: {$passed}/{$total} 项测试通过\n";
        
        if ($passed === $total) {
            echo "🎉 所有增强功能测试通过！\n";
        } else {
            echo "⚠️  部分功能需要进一步检查\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new EnhancedFeaturesTest();
    $test->runAllTests();
} else {
    echo "请在命令行环境下运行此测试脚本\n";
}