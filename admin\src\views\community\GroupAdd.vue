<template>
  <div class="group-add-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">创建群组</h1>
          <p class="page-subtitle">创建新的微信群组并配置完整的营销功能</p>
        </div>
        <div class="header-actions">
          <el-button @click="handleCancel">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button type="success" @click="handlePreview" :disabled="!form.title">
            <el-icon><View /></el-icon>
            预览效果
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            <el-icon><Check /></el-icon>
            创建群组
          </el-button>
        </div>
      </div>
    </div>

    <!-- 创建表单 -->
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="default"
      >
        <!-- 基础信息配置 -->
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>基础信息</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="群组名称" prop="title">
                <el-input 
                  v-model="form.title" 
                  placeholder="请输入群组名称，支持xxx占位符"
                  maxlength="200"
                  show-word-limit
                />
                <div class="form-tip">
                  💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="群组价格" prop="price">
                <el-input-number
                  v-model="form.price"
                  :min="0"
                  :precision="2"
                  placeholder="0.00"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="付款方式" prop="payment_methods">
            <el-checkbox-group v-model="form.payment_methods">
              <el-checkbox value="wechat" border>
                <el-icon><ChatDotRound /></el-icon>
                微信支付
              </el-checkbox>
              <el-checkbox value="alipay" border>
                <el-icon><Money /></el-icon>
                支付宝
              </el-checkbox>
              <el-checkbox value="epay" border>
                <el-icon><CreditCard /></el-icon>
                易支付
              </el-checkbox>
            </el-checkbox-group>
            <div class="form-tip">
              选择支持的付款方式，用户可通过选中的方式进行付费
            </div>
          </el-form-item>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="群组类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择群组类型" style="width: 100%">
                  <el-option label="普通群" value="normal" />
                  <el-option label="VIP群" value="vip" />
                  <el-option label="分销群" value="distribution" />
                  <el-option label="测试群" value="test" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="群组状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio value="active">启用</el-radio>
                  <el-radio value="inactive">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="群组描述">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="3"
              placeholder="请输入群组描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="付费后内容类型">
            <el-radio-group v-model="form.paid_content_type">
              <el-radio value="qr_code">入群二维码</el-radio>
              <el-radio value="image">图片资源</el-radio>
              <el-radio value="link">下载链接</el-radio>
              <el-radio value="document">文档资料</el-radio>
              <el-radio value="video">视频链接</el-radio>
            </el-radio-group>
            <div class="form-tip">选择用户付费成功后要展示的内容类型</div>
          </el-form-item>

          <!-- 入群二维码配置 -->
          <template v-if="form.paid_content_type === 'qr_code'">
            <el-form-item label="入群二维码">
              <el-upload
                class="qr-uploader"
                :action="uploadUrl"
                :show-file-list="false"
                :on-success="handleQrSuccess"
                :before-upload="beforeQrUpload"
              >
                <img v-if="form.qr_code" :src="form.qr_code" class="qr-image" />
                <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="form-tip">上传微信群二维码，用户付费后可扫码入群</div>
            </el-form-item>
          </template>

          <!-- 图片资源配置 -->
          <template v-if="form.paid_content_type === 'image'">
            <el-form-item label="图片资源">
              <el-upload
                class="image-uploader"
                :action="uploadUrl"
                :show-file-list="false"
                :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload"
                multiple
              >
                <div v-if="form.paid_images && form.paid_images.length" class="image-list">
                  <div v-for="(img, index) in form.paid_images" :key="index" class="image-item">
                    <img :src="img" />
                    <el-button size="small" type="danger" @click="removePaidImage(index)">删除</el-button>
                  </div>
                </div>
                <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="form-tip">上传付费后展示的图片资源</div>
            </el-form-item>
          </template>

          <!-- 下载链接配置 -->
          <template v-if="form.paid_content_type === 'link'">
            <el-form-item label="下载链接">
              <el-input v-model="form.paid_link" placeholder="请输入下载链接" />
              <div class="form-tip">用户付费后可访问的下载链接</div>
            </el-form-item>
            <el-form-item label="链接描述">
              <el-input v-model="form.paid_link_desc" placeholder="如：专业资料包下载" />
            </el-form-item>
          </template>

          <!-- 文档资料配置 -->
          <template v-if="form.paid_content_type === 'document'">
            <el-form-item label="文档内容">
              <div class="rich-editor-container">
                <RichTextEditor
                  v-model="form.paid_document_content"
                  :height="200"
                  placeholder="输入付费后展示的文档内容"
                  :max-length="5000"
                />
              </div>
            </el-form-item>
          </template>

          <!-- 视频链接配置 -->
          <template v-if="form.paid_content_type === 'video'">
            <el-form-item label="视频链接">
              <el-input v-model="form.paid_video_url" placeholder="请输入视频链接" />
              <div class="form-tip">支持各大视频平台链接</div>
            </el-form-item>
            <el-form-item label="视频标题">
              <el-input v-model="form.paid_video_title" placeholder="如：专业培训视频" />
            </el-form-item>
            <el-form-item label="视频描述">
              <el-input v-model="form.paid_video_desc" type="textarea" :rows="3" placeholder="视频内容描述" />
            </el-form-item>
          </template>
        </el-card>

        <!-- 城市定位配置 -->
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Location /></el-icon>
              <span>城市定位配置</span>
            </div>
          </template>

          <el-form-item label="启用城市定位">
            <el-switch 
              v-model="form.auto_city_replace" 
              :active-value="1" 
              :inactive-value="0"
              @change="handleCityToggle"
            />
            <span class="form-tip">启用后，系统会根据用户IP自动替换标题中的城市信息</span>
          </el-form-item>

          <template v-if="form.auto_city_replace">
            <el-form-item label="城市插入策略">
              <el-select v-model="form.city_insert_strategy" style="width: 100%">
                <el-option label="智能判断（推荐）" value="auto" />
                <el-option label="前缀模式（北京+标题）" value="prefix" />
                <el-option label="后缀模式（标题+北京）" value="suffix" />
                <el-option label="自然插入（智能融入）" value="natural" />
                <el-option label="不插入" value="none" />
              </el-select>
            </el-form-item>
            <el-form-item label="城市定位测试">
              <el-row :gutter="12">
                <el-col :span="8">
                  <el-input v-model="testCity" placeholder="输入测试城市" />
                </el-col>
                <el-col :span="8">
                  <el-button @click="testCityReplacement">测试替换效果</el-button>
                </el-col>
                <el-col :span="8">
                  <span v-if="testResult" class="test-result">{{ testResult }}</span>
                </el-col>
              </el-row>
            </el-form-item>
          </template>
        </el-card>

        <!-- 营销展示配置 -->
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Promotion /></el-icon>
              <span>营销展示配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="阅读数显示">
                <el-input v-model="form.read_count_display" placeholder="如：10万+" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="点赞数">
                <el-input-number v-model="form.like_count" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="想看数">
                <el-input-number v-model="form.want_see_count" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="入群按钮文案">
                <el-input v-model="form.button_title" placeholder="如：立即加入群聊" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="头像库选择">
                <el-select v-model="form.avatar_library" style="width: 100%">
                  <el-option label="QQ风格（扩列交友）" value="qq" />
                  <el-option label="综合随机" value="za" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="展示类型">
                <el-radio-group v-model="form.display_type">
                  <el-radio :value="1">文字+图片</el-radio>
                  <el-radio :value="2">纯图片</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="微信访问控制">
                <el-radio-group v-model="form.wx_accessible">
                  <el-radio :value="1">微信能打开</el-radio>
                  <el-radio :value="2">微信内不能打开</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 内容配置 -->
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>内容配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="群简介标题">
                <el-input v-model="form.group_intro_title" placeholder="如：群简介" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="FAQ标题">
                <el-input v-model="form.faq_title" placeholder="如：常见问题" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="群简介内容">
            <div class="rich-editor-container">
              <div class="editor-actions">
                <el-button size="small" type="primary" @click="insertTemplate('intro')">
                  <el-icon><Star /></el-icon>
                  插入模板
                </el-button>
                <el-button size="small" @click="insertImage('intro')">
                  <el-icon><Picture /></el-icon>
                  插入图片
                </el-button>
                <el-button size="small" @click="form.group_intro_content = ''" type="danger" plain>
                  <el-icon><Delete /></el-icon>
                  清空内容
                </el-button>
              </div>

              <div class="rich-text-editor-wrapper">
                <RichTextEditor
                  v-model="form.group_intro_content"
                  :height="200"
                  placeholder="详细介绍群组的价值和特色，支持富文本格式。可以添加粗体、斜体、列表、链接、图片等内容。"
                  :max-length="2000"
                />
              </div>

              <div class="editor-help">
                <el-icon><InfoFilled /></el-icon>
                <span>富文本编辑器支持：<strong>粗体</strong>、<em>斜体</em>、列表、链接、图片等格式</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="常见问题">
            <div class="rich-editor-container">
              <div class="editor-actions">
                <el-button size="small" type="primary" @click="insertTemplate('faq')">
                  <el-icon><Star /></el-icon>
                  插入FAQ模板
                </el-button>
                <el-button size="small" @click="form.faq_content = ''" type="danger" plain>
                  <el-icon><Delete /></el-icon>
                  清空内容
                </el-button>
              </div>

              <div class="rich-text-editor-wrapper">
                <RichTextEditor
                  v-model="form.faq_content"
                  :height="180"
                  placeholder="输入常见问题和答案，支持富文本格式。建议格式：Q: 问题内容 A: 答案内容"
                  :max-length="3000"
                />
              </div>

              <div class="editor-help">
                <el-icon><InfoFilled /></el-icon>
                <span>建议格式：<strong>Q: 问题内容</strong> <br> <strong>A: 答案内容</strong>，每个问答占一行</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="群友评论">
            <div class="rich-editor-container">
              <div class="editor-actions">
                <el-button size="small" type="primary" @click="insertTemplate('reviews')">
                  <el-icon><Star /></el-icon>
                  插入评价模板
                </el-button>
                <el-button size="small" @click="form.member_reviews = ''" type="danger" plain>
                  <el-icon><Delete /></el-icon>
                  清空内容
                </el-button>
              </div>

              <div class="rich-text-editor-wrapper">
                <RichTextEditor
                  v-model="form.member_reviews"
                  :height="160"
                  placeholder="输入群友评价内容，支持富文本格式。建议格式：用户名：评价内容 ⭐⭐⭐⭐⭐"
                  :max-length="2000"
                />
              </div>

              <div class="editor-help">
                <el-icon><InfoFilled /></el-icon>
                <span>建议格式：<strong>用户名：评价内容 ⭐⭐⭐⭐⭐</strong>，每个评价占一行</span>
              </div>
            </div>
          </el-form-item>
        </el-card>

        <!-- 虚拟数据配置 -->
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>虚拟数据配置</span>
            </div>
          </template>

          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="虚拟成员数">
                <el-input-number v-model="form.virtual_members" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="虚拟订单数">
                <el-input-number v-model="form.virtual_orders" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="虚拟收入">
                <el-input-number v-model="form.virtual_income" :min="0" :precision="2" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="今日浏览量">
                <el-input-number v-model="form.today_views" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="显示虚拟活动">
                <el-switch v-model="form.show_virtual_activity" :active-value="1" :inactive-value="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示成员头像">
                <el-switch v-model="form.show_member_avatars" :active-value="1" :inactive-value="0" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示群友评论">
                <el-switch v-model="form.show_member_reviews" :active-value="1" :inactive-value="0" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="生成虚拟成员">
            <el-row :gutter="12">
              <el-col :span="8">
                <el-input-number v-model="virtualMemberCount" :min="1" :max="50" />
              </el-col>
              <el-col :span="8">
                <el-button @click="generateVirtualMembers">生成成员数据</el-button>
              </el-col>
              <el-col :span="8">
                <span class="form-tip">将自动生成指定数量的虚拟成员</span>
              </el-col>
            </el-row>
          </el-form-item>
        </el-card>

        <!-- 客服配置 -->
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Service /></el-icon>
              <span>客服配置</span>
            </div>
          </template>

          <el-form-item label="显示客服信息">
            <el-radio-group v-model="form.show_customer_service">
              <el-radio :value="1">不显示</el-radio>
              <el-radio :value="2">显示</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="form.show_customer_service === 2">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客服标题">
                  <el-input v-model="form.customer_service_title" placeholder="如：VIP专属客服" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服描述">
                  <el-input v-model="form.customer_service_desc" placeholder="如：有问题请联系客服" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客服头像">
                  <el-upload
                    class="avatar-uploader"
                    :action="uploadUrl"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload"
                  >
                    <img v-if="form.customer_service_avatar" :src="form.customer_service_avatar" class="avatar" />
                    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服二维码">
                  <el-upload
                    class="qr-uploader"
                    :action="uploadUrl"
                    :show-file-list="false"
                    :on-success="handleServiceQrSuccess"
                    :before-upload="beforeQrUpload"
                  >
                    <img v-if="form.customer_service_qr" :src="form.customer_service_qr" class="qr-image" />
                    <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <el-form-item label="广告二维码">
            <el-upload
              class="qr-uploader"
              :action="uploadUrl"
              :show-file-list="false"
              :on-success="handleAdQrSuccess"
              :before-upload="beforeQrUpload"
            >
              <img v-if="form.ad_qr_code" :src="form.ad_qr_code" class="qr-image" />
              <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
            </el-upload>
            <div class="form-tip">可选：用于在群组页面显示广告</div>
          </el-form-item>
        </el-card>

        <!-- 快速配置 -->
        <el-card class="config-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>快速配置</span>
            </div>
          </template>

          <el-form-item label="应用营销模板">
            <el-row :gutter="12">
              <el-col :span="8">
                <el-select v-model="selectedTemplate" placeholder="选择营销模板">
                  <el-option 
                    v-for="template in marketingTemplates" 
                    :key="template.id"
                    :label="template.name" 
                    :value="template.id" 
                  />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-button @click="applyTemplate">应用模板</el-button>
              </el-col>
              <el-col :span="8">
                <el-button @click="testCityReplacement" :disabled="!form.title">测试城市替换</el-button>
              </el-col>
            </el-row>
            <div v-if="testResult" class="test-result">
              <span>测试结果：{{ testResult }}</span>
            </div>
          </el-form-item>
        </el-card>
      </el-form>
    </div>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreview" title="群组预览" width="60%">
      <div class="preview-content" v-if="previewData">
        <div class="preview-header">
          <h3>{{ previewData.title }}</h3>
          <div class="preview-stats">
            <span>阅读 {{ previewData.read_count_display || '0' }}</span>
            <span>点赞 {{ previewData.like_count || 0 }}</span>
            <span>想看 {{ previewData.want_see_count || 0 }}</span>
          </div>
          <div class="preview-price">¥{{ previewData.price || form.price }}</div>
        </div>
        <div class="preview-intro" v-if="previewData.group_intro_content">
          <h4>{{ previewData.group_intro_title || '群简介' }}</h4>
          <p>{{ previewData.group_intro_content }}</p>
        </div>
        <div class="preview-members" v-if="previewData.virtual_members && previewData.virtual_members.length">
          <h4>群成员 ({{ previewData.virtual_members.length }}人)</h4>
          <div class="member-list">
            <div 
              v-for="member in previewData.virtual_members.slice(0, 8)" 
              :key="member.nickname"
              class="member-item"
            >
              <img :src="member.avatar" :alt="member.nickname" />
              <span>{{ member.nickname }}</span>
            </div>
          </div>
        </div>
        <div class="preview-button">
          <el-button type="primary" size="large">
            {{ previewData.button_title || '立即加入群聊' }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  ArrowLeft, Check, Plus, Location, InfoFilled,
  Promotion, Document, DataAnalysis, Service, Setting, View,
  Star, Picture, Delete, ChatDotRound, Money, CreditCard
} from '@element-plus/icons-vue'
import { createGroup } from '@/api/community'
import RichTextEditor from '@/components/RichTextEditor.vue'

const router = useRouter()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const showPreview = ref(false)
const previewData = ref(null)
const selectedTemplate = ref('')
const marketingTemplates = ref([])
const testResult = ref('')
const testCity = ref('北京')
const virtualMemberCount = ref(13)
const uploadUrl = ref('/api/upload/image')

// 表单数据（整合完整的营销配置）
const form = reactive({
  // 基础信息
  title: '',
  price: 0,
  payment_methods: ['wechat', 'alipay'], // 默认支持微信和支付宝
  type: 'normal',
  status: 'active',
  description: '',

  // 付费后内容配置
  paid_content_type: 'qr_code', // qr_code, image, link, document, video
  qr_code: '', // 入群二维码
  paid_images: [], // 付费图片资源
  paid_link: '', // 下载链接
  paid_link_desc: '', // 链接描述
  paid_document_content: '', // 文档内容
  paid_video_url: '', // 视频链接
  paid_video_title: '', // 视频标题
  paid_video_desc: '', // 视频描述

  // 城市定位配置
  auto_city_replace: 0,
  city_insert_strategy: 'auto',

  // 营销展示配置
  read_count_display: '10万+',
  like_count: 888,
  want_see_count: 666,
  button_title: '立即加入群聊',
  avatar_library: 'qq',
  display_type: 1,
  wx_accessible: 1,

  // 内容配置
  group_intro_title: '群简介',
  group_intro_content: '',
  faq_title: '常见问题',
  faq_content: '',
  member_reviews: '',

  // 虚拟数据配置
  virtual_members: 100,
  virtual_orders: 50,
  virtual_income: 5000.00,
  today_views: 1200,
  show_virtual_activity: 1,
  show_member_avatars: 1,
  show_member_reviews: 1,

  // 客服配置
  show_customer_service: 1,
  customer_service_title: '',
  customer_service_desc: '',
  customer_service_avatar: '',
  customer_service_qr: '',
  ad_qr_code: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入群组价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择群组类型', trigger: 'change' }
  ]
}

// 方法
const handleCityToggle = (value) => {
  if (value) {
    // 启用城市定位时，检查标题是否包含xxx占位符
    if (!form.title.includes('xxx')) {
      ElMessage.info('建议在群组名称中使用"xxx"作为城市占位符，如："xxx交流群"')
    }
  }
}

const testCityReplacement = () => {
  if (!form.title) {
    ElMessage.warning('请先输入群组名称')
    return
  }
  
  let result = form.title
  
  if (form.auto_city_replace) {
    switch (form.city_insert_strategy) {
      case 'prefix':
        result = testCity.value + form.title.replace(/^xxx/, '')
        break
      case 'suffix':
        result = form.title.replace(/xxx/, '') + '(' + testCity.value + '版)'
        break
      case 'natural':
        result = form.title.replace(/xxx/g, testCity.value)
        break
      case 'auto':
        if (form.title.includes('xxx')) {
          result = form.title.replace(/xxx/g, testCity.value)
        } else {
          result = testCity.value + form.title
        }
        break
      default:
        result = form.title.replace(/xxx/g, testCity.value)
    }
  }
  
  testResult.value = result
  ElMessage.success('城市替换测试完成')
}

const applyTemplate = () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请选择营销模板')
    return
  }
  const template = marketingTemplates.value.find(t => t.id === selectedTemplate.value)
  if (template && template.config) {
    // 应用模板配置，但保留已填写的基础信息
    Object.assign(form, template.config)
    ElMessage.success(`已应用${template.name}模板配置`)
  }
}

const generateVirtualMembers = () => {
  const nicknames = [
    '最美的太阳花', '孤海的浪漫', '薰衣草', '木槿，花', '森林小巷少女与狐@',
    '冬日暖阳', '午後の夏天', '嘴角的美人痣。', '朽梦挽歌', '心淡然',
    '青春不散场', '时光不老我们不散', '岁月如歌', '梦想起航', '阳光少年'
  ]
  
  const members = Array.from({ length: virtualMemberCount.value }, (_, i) => ({
    nickname: nicknames[i % nicknames.length] + (i > nicknames.length - 1 ? i : ''),
    avatar: `/face/${form.avatar_library}/${(i % 41) + 1}.${form.avatar_library === 'qq' ? 'jpg' : 'jpeg'}`,
    join_time: new Date().toLocaleString()
  }))
  
  ElMessage.success(`成功生成 ${virtualMemberCount.value} 个虚拟成员`)
  return members
}

const handlePreview = () => {
  // 生成预览数据
  previewData.value = {
    title: testResult.value || form.title,
    price: form.price,
    read_count_display: form.read_count_display,
    like_count: form.like_count,
    want_see_count: form.want_see_count,
    button_title: form.button_title,
    group_intro_title: form.group_intro_title,
    group_intro_content: form.group_intro_content,
    virtual_members: generateVirtualMembers()
  }
  showPreview.value = true
}

const handleCancel = () => {
  router.go(-1)
}

// 上传相关方法
const handleQrSuccess = (response) => {
  form.qr_code = response.data.url
  ElMessage.success('二维码上传成功')
}

const handleAvatarSuccess = (response) => {
  form.customer_service_avatar = response.data.url
  ElMessage.success('客服头像上传成功')
}

const handleServiceQrSuccess = (response) => {
  form.customer_service_qr = response.data.url
  ElMessage.success('客服二维码上传成功')
}

const handleAdQrSuccess = (response) => {
  form.ad_qr_code = response.data.url
  ElMessage.success('广告二维码上传成功')
}

// 付费内容相关处理方法
const handleImageSuccess = (response) => {
  if (!form.paid_images) {
    form.paid_images = []
  }
  form.paid_images.push(response.data.url)
  ElMessage.success('图片上传成功')
}

const removePaidImage = (index) => {
  form.paid_images.splice(index, 1)
}

const beforeQrUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const beforeAvatarUpload = beforeQrUpload

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    // 准备提交数据，包含所有营销配置字段
    const submitData = {
      // 基础信息
      title: form.title,
      price: form.price,
      description: form.description,
      status: form.status === 'active' ? 1 : 0,

      // 付费后内容配置
      paid_content_type: form.paid_content_type,
      qr_code: form.qr_code, // 入群二维码
      paid_images: form.paid_images,
      paid_link: form.paid_link,
      paid_link_desc: form.paid_link_desc,
      paid_document_content: form.paid_document_content,
      paid_video_url: form.paid_video_url,
      paid_video_title: form.paid_video_title,
      paid_video_desc: form.paid_video_desc,
      
      // 城市定位配置
      auto_city_replace: form.auto_city_replace,
      city_insert_strategy: form.city_insert_strategy,
      
      // 营销展示配置
      read_count_display: form.read_count_display,
      like_count: form.like_count,
      want_see_count: form.want_see_count,
      button_title: form.button_title,
      avatar_library: form.avatar_library,
      display_type: form.display_type,
      wx_accessible: form.wx_accessible,
      
      // 内容配置
      group_intro_title: form.group_intro_title,
      group_intro_content: form.group_intro_content,
      faq_title: form.faq_title,
      faq_content: form.faq_content,
      member_reviews: form.member_reviews,
      
      // 虚拟数据配置
      virtual_members: form.virtual_members,
      virtual_orders: form.virtual_orders,
      virtual_income: form.virtual_income,
      today_views: form.today_views,
      show_virtual_activity: form.show_virtual_activity,
      show_member_avatars: form.show_member_avatars,
      show_member_reviews: form.show_member_reviews,
      
      // 客服配置
      show_customer_service: form.show_customer_service,
      customer_service_title: form.customer_service_title,
      customer_service_desc: form.customer_service_desc,
      customer_service_avatar: form.customer_service_avatar,
      customer_service_qr: form.customer_service_qr,
      ad_qr_code: form.ad_qr_code
    }

    // 调用真正的API
    const response = await createGroup(submitData)
    
    if (response.code === 200) {
      ElMessage.success('群组创建成功！所有营销配置已保存')
      router.push('/community/groups')
    } else {
      ElMessage.error(response.message || '创建失败')
    }

  } catch (error) {
    console.error('创建群组失败:', error)
    ElMessage.error('创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 获取营销模板
const fetchMarketingTemplates = async () => {
  try {
    // 模拟营销模板数据
    marketingTemplates.value = [
      {
        id: 1,
        name: '商务交流模板',
        config: {
          read_count_display: '5万+',
          like_count: 1200,
          want_see_count: 800,
          button_title: '立即加入商务群',
          group_intro_title: '商务交流群简介',
          group_intro_content: '专业的商务交流平台，汇聚各行业精英',
          virtual_members: 150,
          virtual_orders: 80
        }
      },
      {
        id: 2,
        name: '社交娱乐模板',
        config: {
          read_count_display: '10万+',
          like_count: 2000,
          want_see_count: 1500,
          button_title: '快来聊天吧',
          group_intro_title: '欢乐聊天群',
          group_intro_content: '轻松愉快的聊天环境，结识更多朋友',
          virtual_members: 200,
          virtual_orders: 120
        }
      },
      {
        id: 3,
        name: '学习教育模板',
        config: {
          read_count_display: '8万+',
          like_count: 1500,
          want_see_count: 1000,
          button_title: '加入学习群',
          group_intro_title: '学习交流群',
          group_intro_content: '专业的学习交流平台，共同进步成长',
          virtual_members: 180,
          virtual_orders: 90
        }
      }
    ]
  } catch (error) {
    console.error('获取营销模板失败:', error)
  }
}

// 富文本编辑器相关方法
const insertTemplate = (type) => {
  let template = ''

  switch (type) {
    case 'intro':
      template = `<h3>🎯 群组特色</h3>
<p>• <strong>专业交流</strong>：汇聚行业精英，分享最新资讯</p>
<p>• <strong>资源共享</strong>：独家资料、工具、经验分享</p>
<p>• <strong>人脉拓展</strong>：结识志同道合的朋友</p>
<p>• <strong>持续成长</strong>：定期活动、培训、讲座</p>

<h3>💎 加入收获</h3>
<p>✅ 获得行业内幕消息和趋势分析</p>
<p>✅ 学习成功案例和实战经验</p>
<p>✅ 建立有价值的商业人脉</p>
<p>✅ 参与线上线下活动交流</p>`
      form.group_intro_content = template
      break

    case 'faq':
      template = `<div><strong>Q: 这个群主要讨论什么内容？</strong></div>
<div>A: 我们主要分享行业资讯、经验交流、资源共享，以及定期组织线上线下活动。</div>
<br>
<div><strong>Q: 群里会有广告吗？</strong></div>
<div>A: 我们严格管理群内容，禁止无关广告，只允许有价值的内容分享。</div>
<br>
<div><strong>Q: 如何参与群内活动？</strong></div>
<div>A: 群内会定期发布活动通知，大家可以根据兴趣自由参与。</div>
<br>
<div><strong>Q: 有什么群规需要遵守？</strong></div>
<div>A: 请保持友善交流，分享有价值内容，禁止发布广告和无关信息。</div>`
      form.faq_content = template
      break

    case 'reviews':
      template = `<div><strong>张先生</strong>：群里的资源真的很棒，学到了很多实用的技巧！⭐⭐⭐⭐⭐</div>
<br>
<div><strong>李女士</strong>：群主很用心，经常分享有价值的内容，强烈推荐！⭐⭐⭐⭐⭐</div>
<br>
<div><strong>王总</strong>：通过这个群认识了很多同行朋友，合作机会很多。⭐⭐⭐⭐⭐</div>
<br>
<div><strong>陈经理</strong>：群内氛围很好，大家都很乐于分享，收获满满。⭐⭐⭐⭐⭐</div>`
      form.member_reviews = template
      break
  }

  ElMessage.success('模板内容已插入')
}

const insertImage = (type) => {
  ElMessage.info('图片上传功能开发中，敬请期待')
}

onMounted(() => {
  // 加载营销模板
  fetchMarketingTemplates()
})
</script>

<style lang="scss" scoped>
.group-add-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
}

.page-subtitle {
  color: #909399;
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-card {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.config-card :deep(.el-card__header) {
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
  padding: 16px 20px;
}

.config-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.avatar-uploader,
.qr-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409eff;
    }
  }
}

.avatar-uploader-icon,
.qr-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.avatar,
.qr-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
}

// 付费内容配置样式
.image-uploader {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 12px;

    .image-item {
      position: relative;
      width: 120px;
      height: 120px;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .el-button {
        position: absolute;
        top: 4px;
        right: 4px;
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }

  .image-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: .3s;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
  }
}

.test-result {
  color: #67c23a;
  font-weight: 500;
  padding: 4px 8px;
  background: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
  margin-top: 8px;
}

/* 预览样式 */
.preview-content {
  padding: 20px;
}

.preview-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.preview-header h3 {
  font-size: 20px;
  color: #303133;
  margin: 0 0 12px 0;
}

.preview-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 12px 0;
  color: #909399;
  font-size: 14px;
}

.preview-price {
  font-size: 24px;
  color: #f56c6c;
  font-weight: 600;
}

.preview-intro {
  margin: 20px 0;
}

.preview-intro h4 {
  color: #303133;
  margin: 0 0 8px 0;
}

.preview-intro p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.preview-members {
  margin: 20px 0;
}

.preview-members h4 {
  color: #303133;
  margin: 0 0 12px 0;
}

.member-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.member-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 60px;
}

.member-item img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-bottom: 4px;
}

.member-item span {
  font-size: 12px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.preview-button {
  text-align: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-switch) {
  .el-switch__label {
    color: #606266;
    font-weight: 500;
  }

  .el-switch__label.is-active {
    color: #409eff;
  }
}

// 富文本编辑器样式
.rich-editor-container {
  .editor-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  .rich-text-editor-wrapper {
    margin-bottom: 8px;

    :deep(.rich-text-editor) {
      border-radius: 6px;

      .editor-toolbar {
        background: #fafafa;
        border-bottom: 1px solid #e4e7ed;
        padding: 8px 12px;

        .el-button-group {
          margin-right: 8px;
        }

        .toolbar-divider {
          width: 1px;
          height: 20px;
          background: #e4e7ed;
          margin: 0 8px;
          display: inline-block;
        }
      }

      .editor-content {
        padding: 12px;
        min-height: 120px;
        line-height: 1.6;

        &:focus {
          outline: none;
        }

        p {
          margin: 0 0 8px 0;

          &:last-child {
            margin-bottom: 0;
          }
        }

        h3 {
          margin: 16px 0 8px 0;
          color: #303133;
          font-size: 16px;

          &:first-child {
            margin-top: 0;
          }
        }

        strong {
          color: #409eff;
          font-weight: 600;
        }

        ul, ol {
          margin: 8px 0;
          padding-left: 20px;
        }

        li {
          margin: 4px 0;
        }
      }

      .editor-footer {
        padding: 8px 12px;
        border-top: 1px solid #e4e7ed;
        background: #f5f7fa;
        display: flex;
        justify-content: flex-end;

        .word-count {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  .editor-help {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #909399;
    font-size: 13px;
    margin-top: 8px;

    .el-icon {
      color: #409eff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .group-add-container {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .config-card {
    margin-bottom: 16px;
  }

  :deep(.el-col) {
    margin-bottom: 16px;
  }
}
</style>