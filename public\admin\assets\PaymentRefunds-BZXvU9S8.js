import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                     *//* empty css                       */import{b3 as a,bc as l,bd as t,aH as r,aW as u,aV as s,bl as d,as as o,T as n,b1 as p,aM as i,b6 as c,b7 as m,U as _,b8 as f,Y as v,bi as b,bS as g,bj as y,bk as h,a4 as k,bM as w,bN as j,au as V,Q as z}from"./element-plus-DcSKpKA8.js";import{P as C}from"./PageLayout-OFR6SHfu.js";import{f as U,h as x,p as Y}from"./payment-D7px_L1O.js";import{r as D,L as M,e as R,k as P,l as O,E as S,z as q,t as B,D as E,u as F,A as L,y as Q,B as A}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const G={class:"payment-refunds"},H={class:"filter-section"},I={class:"table-section"},N={class:"card-header"},T={class:"header-actions"},W={class:"amount"},$={class:"pagination-wrapper"},J={key:0,class:"refund-detail"},K={class:"amount"},X={class:"refund-reason"},Z={key:0,class:"refund-remark"},ee=e({__name:"PaymentRefunds",setup(e){const ee=D(!1),ae=D(!1),le=D(!1),te=D(!1),re=D(!1),ue=D("approve"),se=D(null),de=M({refund_no:"",order_no:"",status:"",date_range:[]}),oe=M({remark:""}),ne=D([]),pe=M({current:1,size:20,total:0}),ie=async()=>{ee.value=!0;try{const e={page:pe.current,size:pe.size,...de},a=await U(e);ne.value=a.data.list,pe.total=a.data.total}catch(e){ne.value=[{id:1,refund_no:"RF202401010001",order_no:"ORD202401010001",amount:99,reason:"商品质量问题",status:"pending",payment_method:"alipay",applicant:"张三",created_at:"2024-01-01 10:00:00",processed_at:null,remark:""},{id:2,refund_no:"RF202401010002",order_no:"ORD202401010002",amount:199,reason:"不喜欢商品",status:"completed",payment_method:"wechat",applicant:"李四",created_at:"2024-01-01 11:00:00",processed_at:"2024-01-01 12:00:00",remark:"已处理完成"}],pe.total=2}finally{ee.value=!1}},ce=()=>{pe.current=1,ie()},me=()=>{Object.assign(de,{refund_no:"",order_no:"",status:"",date_range:[]}),ce()},_e=e=>{pe.size=e,ie()},fe=e=>{pe.current=e,ie()},ve=async()=>{if(oe.remark.trim()){le.value=!0;try{await Y(se.value.id,{action:ue.value,remark:oe.remark}),z.success(`退款${"approve"===ue.value?"通过":"拒绝"}成功`),re.value=!1,ie()}catch(e){z.error("处理失败："+e.message)}finally{le.value=!1}}else z.warning("请输入处理备注")},be=async()=>{ae.value=!0;try{await x(de),z.success("导出成功")}catch(e){z.error("导出失败："+e.message)}finally{ae.value=!1}},ge=e=>({pending:"warning",processing:"primary",completed:"success",rejected:"danger"}[e]||"info"),ye=e=>({pending:"待处理",processing:"处理中",completed:"已完成",rejected:"已拒绝"}[e]||"未知"),he=e=>({alipay:"支付宝",wechat:"微信支付",easypay:"易支付",bank:"银行卡"}[e]||"未知");return R(()=>{ie()}),(e,z)=>{const U=r,x=t,Y=s,D=u,M=d,R=n,ie=o,ke=l,we=a,je=m,Ve=f,ze=c,Ce=h,Ue=j,xe=w,Ye=V,De=y;return O(),P("div",G,[S(C,{title:"退款管理",subtitle:"管理支付退款申请和处理"},{default:q(()=>[B("div",H,[S(we,{class:"filter-card"},{default:q(()=>[S(ke,{model:de,inline:""},{default:q(()=>[S(x,{label:"退款单号"},{default:q(()=>[S(U,{modelValue:de.refund_no,"onUpdate:modelValue":z[0]||(z[0]=e=>de.refund_no=e),placeholder:"请输入退款单号",clearable:""},null,8,["modelValue"])]),_:1}),S(x,{label:"订单号"},{default:q(()=>[S(U,{modelValue:de.order_no,"onUpdate:modelValue":z[1]||(z[1]=e=>de.order_no=e),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])]),_:1}),S(x,{label:"退款状态"},{default:q(()=>[S(D,{modelValue:de.status,"onUpdate:modelValue":z[2]||(z[2]=e=>de.status=e),placeholder:"请选择状态",clearable:""},{default:q(()=>[S(Y,{label:"待处理",value:"pending"}),S(Y,{label:"处理中",value:"processing"}),S(Y,{label:"已完成",value:"completed"}),S(Y,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"])]),_:1}),S(x,{label:"申请时间"},{default:q(()=>[S(M,{modelValue:de.date_range,"onUpdate:modelValue":z[3]||(z[3]=e=>de.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),S(x,null,{default:q(()=>[S(ie,{type:"primary",onClick:ce,loading:ee.value},{default:q(()=>[S(R,null,{default:q(()=>[S(F(p))]),_:1}),z[10]||(z[10]=E(" 搜索 ",-1))]),_:1,__:[10]},8,["loading"]),S(ie,{onClick:me},{default:q(()=>[S(R,null,{default:q(()=>[S(F(i))]),_:1}),z[11]||(z[11]=E(" 重置 ",-1))]),_:1,__:[11]})]),_:1})]),_:1},8,["model"])]),_:1})]),B("div",I,[S(we,{class:"table-card"},{header:q(()=>[B("div",N,[z[13]||(z[13]=B("h3",null,"退款列表",-1)),B("div",T,[S(ie,{onClick:be,loading:ae.value},{default:q(()=>[S(R,null,{default:q(()=>[S(F(k))]),_:1}),z[12]||(z[12]=E(" 导出 ",-1))]),_:1,__:[12]},8,["loading"])])])]),default:q(()=>[L((O(),Q(ze,{data:ne.value,stripe:""},{default:q(()=>[S(je,{prop:"refund_no",label:"退款单号",width:"180"}),S(je,{prop:"order_no",label:"订单号",width:"180"}),S(je,{prop:"amount",label:"退款金额",width:"120"},{default:q(({row:e})=>[B("span",W,"¥"+_(e.amount),1)]),_:1}),S(je,{prop:"reason",label:"退款原因",width:"200","show-overflow-tooltip":""}),S(je,{prop:"status",label:"状态",width:"100"},{default:q(({row:e})=>[S(Ve,{type:ge(e.status)},{default:q(()=>[E(_(ye(e.status)),1)]),_:2},1032,["type"])]),_:1}),S(je,{prop:"payment_method",label:"支付方式",width:"120"},{default:q(({row:e})=>[S(Ve,{size:"small"},{default:q(()=>[E(_(he(e.payment_method)),1)]),_:2},1024)]),_:1}),S(je,{prop:"applicant",label:"申请人",width:"120"}),S(je,{prop:"created_at",label:"申请时间",width:"160"}),S(je,{label:"操作",width:"200",fixed:"right"},{default:q(({row:e})=>[S(ie,{size:"small",onClick:a=>{return l=e,se.value=l,void(te.value=!0);var l}},{default:q(()=>[S(R,null,{default:q(()=>[S(F(v))]),_:1}),z[14]||(z[14]=E(" 查看 ",-1))]),_:2,__:[14]},1032,["onClick"]),"pending"===e.status?(O(),Q(ie,{key:0,size:"small",type:"success",onClick:a=>{return l=e,se.value=l,ue.value="approve",oe.remark="",void(re.value=!0);var l}},{default:q(()=>[S(R,null,{default:q(()=>[S(F(b))]),_:1}),z[15]||(z[15]=E(" 通过 ",-1))]),_:2,__:[15]},1032,["onClick"])):A("",!0),"pending"===e.status?(O(),Q(ie,{key:1,size:"small",type:"danger",onClick:a=>{return l=e,se.value=l,ue.value="reject",oe.remark="",void(re.value=!0);var l}},{default:q(()=>[S(R,null,{default:q(()=>[S(F(g))]),_:1}),z[16]||(z[16]=E(" 拒绝 ",-1))]),_:2,__:[16]},1032,["onClick"])):A("",!0)]),_:1})]),_:1},8,["data"])),[[De,ee.value]]),B("div",$,[S(Ce,{"current-page":pe.current,"onUpdate:currentPage":z[4]||(z[4]=e=>pe.current=e),"page-size":pe.size,"onUpdate:pageSize":z[5]||(z[5]=e=>pe.size=e),total:pe.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_e,onCurrentChange:fe},null,8,["current-page","page-size","total"])])]),_:1})])]),_:1}),S(Ye,{modelValue:te.value,"onUpdate:modelValue":z[6]||(z[6]=e=>te.value=e),title:"退款详情",width:"800px"},{default:q(()=>[se.value?(O(),P("div",J,[S(xe,{column:2,border:""},{default:q(()=>[S(Ue,{label:"退款单号"},{default:q(()=>[E(_(se.value.refund_no),1)]),_:1}),S(Ue,{label:"订单号"},{default:q(()=>[E(_(se.value.order_no),1)]),_:1}),S(Ue,{label:"退款金额"},{default:q(()=>[B("span",K,"¥"+_(se.value.amount),1)]),_:1}),S(Ue,{label:"支付方式"},{default:q(()=>[E(_(he(se.value.payment_method)),1)]),_:1}),S(Ue,{label:"退款状态"},{default:q(()=>[S(Ve,{type:ge(se.value.status)},{default:q(()=>[E(_(ye(se.value.status)),1)]),_:1},8,["type"])]),_:1}),S(Ue,{label:"申请人"},{default:q(()=>[E(_(se.value.applicant),1)]),_:1}),S(Ue,{label:"申请时间"},{default:q(()=>[E(_(se.value.created_at),1)]),_:1}),S(Ue,{label:"处理时间"},{default:q(()=>[E(_(se.value.processed_at||"未处理"),1)]),_:1})]),_:1}),B("div",X,[z[17]||(z[17]=B("h4",null,"退款原因",-1)),B("p",null,_(se.value.reason),1)]),se.value.remark?(O(),P("div",Z,[z[18]||(z[18]=B("h4",null,"处理备注",-1)),B("p",null,_(se.value.remark),1)])):A("",!0)])):A("",!0)]),_:1},8,["modelValue"]),S(Ye,{modelValue:re.value,"onUpdate:modelValue":z[9]||(z[9]=e=>re.value=e),title:"approve"===ue.value?"通过退款":"拒绝退款",width:"500px"},{footer:q(()=>[S(ie,{onClick:z[8]||(z[8]=e=>re.value=!1)},{default:q(()=>z[19]||(z[19]=[E("取消",-1)])),_:1,__:[19]}),S(ie,{type:"approve"===ue.value?"success":"danger",onClick:ve,loading:le.value},{default:q(()=>[E(" 确认"+_("approve"===ue.value?"通过":"拒绝"),1)]),_:1},8,["type","loading"])]),default:q(()=>[S(ke,{model:oe,"label-width":"80px"},{default:q(()=>[S(x,{label:"处理备注",required:""},{default:q(()=>[S(U,{modelValue:oe.remark,"onUpdate:modelValue":z[7]||(z[7]=e=>oe.remark=e),type:"textarea",rows:4,placeholder:"approve"===ue.value?"请输入通过原因":"请输入拒绝原因"},null,8,["modelValue","placeholder"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-8d6eff59"]]);export{ee as default};
