<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑群组' : '创建群组'"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="unified-group-dialog"
  >
    <!-- 编辑模式：保留原有表单 -->
    <el-form
      v-if="isEdit"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="group-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="群组名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入群组名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="群组分类" prop="category">
            <el-select v-model="formData.category" placeholder="请选择分类" style="width: 100%">
              <el-option label="创业交流" value="startup" />
              <el-option label="投资理财" value="finance" />
              <el-option label="科技互联网" value="tech" />
              <el-option label="教育培训" value="education" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入群价格" prop="price">
            <el-input-number
              v-model="formData.price"
              :min="0"
              :max="9999"
              :precision="2"
              style="width: 100%"
              placeholder="0.00"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大成员数" prop="max_members">
            <el-input-number
              v-model="formData.max_members"
              :min="1"
              :max="500"
              style="width: 100%"
              placeholder="500"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="群组描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入群组描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="群组头像">
            <div class="avatar-upload">
              <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                accept="image/*"
              >
                <img v-if="formData.avatar" :src="formData.avatar" class="avatar" alt="群组头像" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：200x200px</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="群二维码">
            <div class="qrcode-upload">
              <el-upload
                class="qrcode-uploader"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleQRCodeSuccess"
                :before-upload="beforeQRCodeUpload"
                accept="image/*"
              >
                <img v-if="formData.qr_code" :src="formData.qr_code" class="qrcode" alt="群二维码" />
                <el-icon v-else class="qrcode-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：400x400px</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="群组状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="1">活跃</el-radio>
              <el-radio :label="0">暂停</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否推荐" prop="is_recommended">
            <el-switch
              v-model="formData.is_recommended"
              active-text="推荐"
              inactive-text="不推荐"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="群组标签">
        <el-tag
          v-for="tag in formData.tags"
          :key="tag"
          closable
          @close="removeTag(tag)"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ tag }}
        </el-tag>
        <el-input
          v-if="inputVisible"
          ref="inputRef"
          v-model="inputValue"
          size="small"
          style="width: 100px;"
          @keyup.enter="handleInputConfirm"
          @blur="handleInputConfirm"
        />
        <el-button v-else size="small" @click="showInput">+ 添加标签</el-button>
      </el-form-item>

      <el-form-item label="群组公告">
        <rich-text-editor
          v-model="formData.announcement"
          :height="200"
          placeholder="请输入群组公告"
        />
      </el-form-item>
    </el-form>

    <!-- 创建模式：使用统一组件 -->
    <GroupCreateForm
      v-else
      mode="dialog"
      user-role="admin"
      :default-values="adminDefaults"
      :show-preview="true"
      :show-templates="true"
      @success="handleCreateSuccess"
      @cancel="handleCreateCancel"
    />

    <template v-if="isEdit" #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          更新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { createGroup, updateGroup } from '@/api/community'
import RichTextEditor from '@/components/RichTextEditor.vue'
import GroupCreateForm from '@/components/GroupCreateForm.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  groupData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const userStore = useUserStore()
const formRef = ref(null)
const inputRef = ref(null)
const loading = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 是否为编辑模式
const isEdit = computed(() => props.groupData && props.groupData.id)

// 上传配置
const uploadUrl = computed(() => import.meta.env.VITE_API_BASE_URL + '/upload/image')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

// 管理员创建群组的默认配置
const adminDefaults = reactive({
  type: 'normal',
  auto_city_replace: 0,
  read_count_display: '10万+',
  like_count: 888,
  want_see_count: 666,
  button_title: '立即加入群聊',
  avatar_library: 'qq',
  status: 'active'
})

// 表单数据
const formData = reactive({
  name: '',
  category: '',
  description: '',
  price: 0,
  max_members: 500,
  avatar: '',
  qr_code: '',
  status: 1,
  is_recommended: false,
  tags: [],
  announcement: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '群组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择群组分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入入群价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  max_members: [
    { required: true, message: '请输入最大成员数', trigger: 'blur' },
    { type: 'number', min: 1, max: 500, message: '成员数在 1 到 500 之间', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 监听群组数据变化
watch(() => props.groupData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, {
      name: newData.name || '',
      category: newData.category || '',
      description: newData.description || '',
      price: newData.price || 0,
      max_members: newData.max_members || 500,
      avatar: newData.avatar || '',
      qr_code: newData.qr_code || '',
      status: newData.status !== undefined ? newData.status : 1,
      is_recommended: newData.is_recommended || false,
      tags: newData.tags || [],
      announcement: newData.announcement || ''
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      name: '',
      category: '',
      description: '',
      price: 0,
      max_members: 500,
      avatar: '',
      qr_code: '',
      status: 1,
      is_recommended: false,
      tags: [],
      announcement: ''
    })
  }
}, { immediate: true, deep: true })

// 头像上传成功
const handleAvatarSuccess = (response) => {
  if (response.success) {
    formData.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 二维码上传成功
const handleQRCodeSuccess = (response) => {
  if (response.success) {
    formData.qr_code = response.data.url
    ElMessage.success('二维码上传成功')
  } else {
    ElMessage.error('二维码上传失败')
  }
}

// 二维码上传前验证
const beforeQRCodeUpload = (file) => {
  return beforeAvatarUpload(file)
}

// 显示标签输入框
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 确认添加标签
const handleInputConfirm = () => {
  if (inputValue.value && !formData.tags.includes(inputValue.value)) {
    formData.tags.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 移除标签
const removeTag = (tag) => {
  const index = formData.tags.indexOf(tag)
  if (index > -1) {
    formData.tags.splice(index, 1)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const submitData = { ...formData }
    
    if (isEdit.value) {
      await updateGroup(props.groupData.id, submitData)
      ElMessage.success('群组更新成功')
    } else {
      await createGroup(submitData)
      ElMessage.success('群组创建成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理创建成功（统一组件）
const handleCreateSuccess = (groupData) => {
  emit('success', groupData)
  dialogVisible.value = false
}

// 处理创建取消（统一组件）
const handleCreateCancel = () => {
  dialogVisible.value = false
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.group-form {
  .avatar-upload,
  .qrcode-upload {
    text-align: center;
  }

  .avatar-uploader,
  .qrcode-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .avatar-uploader :deep(.el-upload) {
    width: 120px;
    height: 120px;
  }

  .qrcode-uploader :deep(.el-upload) {
    width: 150px;
    height: 150px;
  }

  .avatar,
  .qrcode {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .avatar-uploader-icon,
  .qrcode-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .upload-tip {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .group-form {
    .avatar-uploader :deep(.el-upload) {
      width: 100px;
      height: 100px;
    }

    .qrcode-uploader :deep(.el-upload) {
      width: 120px;
      height: 120px;
    }
  }
}
</style>