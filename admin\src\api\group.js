import request from '@/utils/request'

/**
 * 群组管理API
 */

// 创建群组
export function createGroup(data) {
  return request({
    url: '/api/admin/groups',
    method: 'post',
    data
  })
}

// 获取群组列表
export function getGroupList(params) {
  return request({
    url: '/api/admin/groups',
    method: 'get',
    params
  })
}

// 获取群组详情
export function getGroupDetail(id) {
  return request({
    url: `/api/admin/groups/${id}`,
    method: 'get'
  })
}

// 更新群组
export function updateGroup(id, data) {
  return request({
    url: `/api/admin/groups/${id}`,
    method: 'put',
    data
  })
}

// 删除群组
export function deleteGroup(id) {
  return request({
    url: `/api/admin/groups/${id}`,
    method: 'delete'
  })
}

// 批量删除群组
export function batchDeleteGroups(ids) {
  return request({
    url: '/api/admin/groups/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 发布/下架群组
export function toggleGroupStatus(id, status) {
  return request({
    url: `/api/admin/groups/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 获取群组统计数据
export function getGroupStats(id) {
  return request({
    url: `/api/admin/groups/${id}/stats`,
    method: 'get'
  })
}

// 获取群组成员列表
export function getGroupMembers(id, params) {
  return request({
    url: `/api/admin/groups/${id}/members`,
    method: 'get',
    params
  })
}

// 移除群组成员
export function removeGroupMember(groupId, userId) {
  return request({
    url: `/api/admin/groups/${groupId}/members/${userId}`,
    method: 'delete'
  })
}

// 获取群组订单列表
export function getGroupOrders(id, params) {
  return request({
    url: `/api/admin/groups/${id}/orders`,
    method: 'get',
    params
  })
}

// 测试支付配置
export function testPaymentConfig(methods) {
  return request({
    url: '/api/admin/payment/test',
    method: 'post',
    data: { methods }
  })
}

// 获取域名池列表
export function getDomainPools() {
  return request({
    url: '/api/admin/domain-pools',
    method: 'get'
  })
}

// 获取群组模板列表
export function getGroupTemplates() {
  return request({
    url: '/api/admin/group-templates',
    method: 'get'
  })
}

// 应用群组模板
export function applyGroupTemplate(templateId) {
  return request({
    url: `/api/admin/group-templates/${templateId}/apply`,
    method: 'post'
  })
}

// 保存为模板
export function saveAsTemplate(data) {
  return request({
    url: '/api/admin/group-templates',
    method: 'post',
    data
  })
}

// 预览群组
export function previewGroup(data) {
  return request({
    url: '/api/admin/groups/preview',
    method: 'post',
    data
  })
}

// 生成推广海报
export function generatePoster(groupId, options = {}) {
  return request({
    url: `/api/admin/groups/${groupId}/poster`,
    method: 'post',
    data: options
  })
}

// 获取城市列表
export function getCityList() {
  return request({
    url: '/api/common/cities',
    method: 'get'
  })
}

// 测试城市替换
export function testCityReplacement(title, city, strategy) {
  return request({
    url: '/api/admin/groups/test-city-replacement',
    method: 'post',
    data: { title, city, strategy }
  })
}

// 上传文件
export function uploadFile(file, type = 'image') {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)
  
  return request({
    url: '/api/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取群组分析数据
export function getGroupAnalytics(id, params) {
  return request({
    url: `/api/admin/groups/${id}/analytics`,
    method: 'get',
    params
  })
}

// 导出群组数据
export function exportGroupData(id, format = 'excel') {
  return request({
    url: `/api/admin/groups/${id}/export`,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

// 复制群组
export function duplicateGroup(id) {
  return request({
    url: `/api/admin/groups/${id}/duplicate`,
    method: 'post'
  })
}

// 获取推荐设置
export function getRecommendedSettings(category) {
  return request({
    url: '/api/admin/groups/recommended-settings',
    method: 'get',
    params: { category }
  })
}