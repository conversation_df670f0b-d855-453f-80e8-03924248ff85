<template>
  <PageLayout
    :title="groupInfo.name || '群组详情'"
    subtitle="查看群组详细信息"
    icon="View"
    :loading="loading"
  >
    <template #actions>
      <el-button @click="$router.go(-1)">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <el-button type="primary" @click="handleEdit">
        <el-icon><Edit /></el-icon>
        编辑群组
      </el-button>
    </template>

    <div class="group-detail" v-if="!loading">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>群组名称：</label>
              <span>{{ groupInfo.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>群组类型：</label>
              <el-tag :type="getTypeColor(groupInfo.type)">
                {{ getTypeName(groupInfo.type) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>群组状态：</label>
              <el-tag :type="groupInfo.status === 'active' ? 'success' : 'danger'">
                {{ groupInfo.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>群主：</label>
              <span>{{ groupInfo.ownerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>当前人数：</label>
              <span>{{ groupInfo.memberCount }} / {{ groupInfo.maxMembers }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>入群费用：</label>
              <span>{{ groupInfo.joinFee }}元</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(groupInfo.createdAt) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>最后活跃：</label>
              <span>{{ formatDate(groupInfo.lastActiveAt) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>群组标签：</label>
              <el-tag 
                v-for="tag in groupInfo.tags" 
                :key="tag" 
                size="small" 
                style="margin-right: 8px;"
              >
                {{ tag }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <div class="info-item" v-if="groupInfo.description">
          <label>群组描述：</label>
          <p>{{ groupInfo.description }}</p>
        </div>

        <div class="info-item" v-if="groupInfo.rules">
          <label>入群规则：</label>
          <p>{{ groupInfo.rules }}</p>
        </div>
      </el-card>

      <!-- 统计信息 -->
      <el-card class="stats-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>统计信息</h3>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ groupInfo.stats.totalMessages }}</div>
              <div class="stat-label">总消息数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ groupInfo.stats.activeMembers }}</div>
              <div class="stat-label">活跃成员</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ groupInfo.stats.totalRevenue }}</div>
              <div class="stat-label">总收入（元）</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ groupInfo.stats.conversionRate }}%</div>
              <div class="stat-label">转化率</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </PageLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit, View } from '@element-plus/icons-vue'
import PageLayout from '@/components/layout/PageLayout.vue'

const router = useRouter()
const route = useRoute()
const loading = ref(true)

// 群组信息
const groupInfo = reactive({
  id: '',
  name: '',
  type: '',
  status: '',
  ownerName: '',
  memberCount: 0,
  maxMembers: 0,
  joinFee: 0,
  description: '',
  rules: '',
  tags: [],
  createdAt: '',
  lastActiveAt: '',
  stats: {
    totalMessages: 0,
    activeMembers: 0,
    totalRevenue: 0,
    conversionRate: 0
  }
})

// 获取类型名称
const getTypeName = (type) => {
  const typeMap = {
    normal: '普通群',
    vip: 'VIP群',
    distribution: '分销群',
    test: '测试群'
  }
  return typeMap[type] || '未知'
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    normal: '',
    vip: 'warning',
    distribution: 'success',
    test: 'info'
  }
  return colorMap[type] || ''
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 编辑群组
const handleEdit = () => {
  router.push(`/community/groups/edit/${route.params.id}`)
}

// 加载群组详情
const loadGroupDetail = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    Object.assign(groupInfo, {
      id: route.params.id,
      name: '测试群组',
      type: 'normal',
      status: 'active',
      ownerName: '张三',
      memberCount: 85,
      maxMembers: 100,
      joinFee: 9.9,
      description: '这是一个测试群组，用于演示群组功能。',
      rules: '1. 禁止发广告\n2. 保持友善交流\n3. 遵守群规',
      tags: ['热门', '活跃'],
      createdAt: '2024-01-15T10:30:00Z',
      lastActiveAt: '2024-08-02T14:20:00Z',
      stats: {
        totalMessages: 1250,
        activeMembers: 68,
        totalRevenue: 841.5,
        conversionRate: 12.5
      }
    })
  } catch (error) {
    ElMessage.error('加载群组详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadGroupDetail()
})
</script>

<style lang="scss" scoped>
.group-detail {
  .info-card,
  .stats-card {
    margin-bottom: 24px;
    
    .card-header {
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }
  }
  
  .info-item {
    margin-bottom: 16px;
    
    label {
      font-weight: 500;
      color: #666;
      margin-right: 8px;
    }
    
    span {
      color: #333;
    }
    
    p {
      margin: 8px 0 0 0;
      color: #333;
      line-height: 1.6;
    }
  }
  
  .stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}
</style>
