<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 短链接模型
 * 管理防红系统中的短链接
 */
class ShortLink extends Model
{
    use HasFactory;

    protected $table = 'short_links';

    protected $fillable = [
        'short_code',
        'original_url',
        'domain',
        'user_id',
        'link_type',
        'click_count',
        'last_click_time',
        'expires_at',
        'status',
        'meta_data',
    ];

    protected $casts = [
        'last_click_time' => 'datetime',
        'expires_at' => 'datetime',
        'meta_data' => 'json',
    ];

    // 状态常量
    const STATUS_ACTIVE = 1;   // 激活
    const STATUS_DISABLED = 0; // 禁用

    // 链接类型常量
    const TYPE_INVITE = 'invite';     // 邀请链接
    const TYPE_GROUP = 'group';       // 群组链接
    const TYPE_PAYMENT = 'payment';   // 支付链接
    const TYPE_GENERAL = 'general';   // 通用链接

    /**
     * 创建用户关联
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 域名关联
     */
    public function domainPool()
    {
        return $this->belongsTo(DomainPool::class, 'domain', 'domain');
    }

    /**
     * 访问日志关联
     */
    public function accessLogs()
    {
        return $this->hasMany(LinkAccessLog::class, 'short_link_id');
    }

    /**
     * 获取完整的短链接URL
     */
    public function getFullUrlAttribute(): string
    {
        return "https://{$this->domain}/s/{$this->short_code}";
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute(): string
    {
        return $this->status === self::STATUS_ACTIVE ? '激活' : '禁用';
    }

    /**
     * 获取类型名称
     */
    public function getTypeNameAttribute(): string
    {
        $types = [
            self::TYPE_INVITE => '邀请链接',
            self::TYPE_GROUP => '群组链接',
            self::TYPE_PAYMENT => '支付链接',
            self::TYPE_GENERAL => '通用链接',
        ];
        
        return $types[$this->link_type] ?? '未知类型';
    }

    /**
     * 是否有效
     */
    public function isValid(): bool
    {
        // 检查状态
        if ($this->status !== self::STATUS_ACTIVE) {
            return false;
        }

        // 检查过期时间
        if ($this->expires_at && $this->expires_at < now()) {
            return false;
        }

        // 检查域名是否可用
        $domain = DomainPool::where('domain', $this->domain)->first();
        if (!$domain || !$domain->isAvailable()) {
            return false;
        }

        return true;
    }

    /**
     * 是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at < now();
    }

    /**
     * 增加点击次数
     */
    public function incrementClickCount(): void
    {
        $this->increment('click_count');
        $this->update(['last_click_time' => now()]);
    }

    /**
     * 记录访问日志
     */
    public function recordAccess(array $data = []): void
    {
        $this->accessLogs()->create([
            'visitor_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'referer' => request()->header('referer'),
            'platform' => $this->detectPlatform(),
            'device_type' => $this->detectDeviceType(),
            'location' => $data['location'] ?? null,
            'is_valid' => $this->isValid() ? 1 : 0,
            'access_time' => now(),
        ]);
    }

    /**
     * 检测访问平台
     */
    private function detectPlatform(): string
    {
        $userAgent = request()->userAgent();
        
        if (str_contains($userAgent, 'MicroMessenger')) {
            return 'wechat';
        }
        
        if (str_contains($userAgent, 'QQ/')) {
            return 'qq';
        }
        
        if (str_contains($userAgent, 'DingTalk')) {
            return 'dingtalk';
        }
        
        if (str_contains($userAgent, 'Mobile')) {
            return 'mobile';
        }
        
        return 'browser';
    }

    /**
     * 检测设备类型
     */
    private function detectDeviceType(): string
    {
        $userAgent = request()->userAgent();
        
        if (str_contains($userAgent, 'Mobile')) {
            return 'mobile';
        }
        
        if (str_contains($userAgent, 'Tablet')) {
            return 'tablet';
        }
        
        return 'desktop';
    }

    /**
     * 生成短链接代码
     */
    public static function generateShortCode(int $length = 8): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        
        do {
            $shortCode = '';
            for ($i = 0; $i < $length; $i++) {
                $shortCode .= $characters[rand(0, $charactersLength - 1)];
            }
        } while (self::where('short_code', $shortCode)->exists());
        
        return $shortCode;
    }

    /**
     * 创建短链接
     */
    public static function createShortLink(string $originalUrl, array $options = []): self
    {
        // 获取最佳域名
        $domain = DomainPool::getBestAvailableDomain();
        if (!$domain) {
            throw new \Exception('没有可用的域名');
        }

        // 生成短链接代码
        $shortCode = self::generateShortCode();

        // 创建短链接
        $shortLink = self::create([
            'short_code' => $shortCode,
            'original_url' => $originalUrl,
            'domain' => $domain->domain,
            'user_id' => $options['user_id'] ?? null,
            'link_type' => $options['link_type'] ?? self::TYPE_GENERAL,
            'expires_at' => $options['expires_at'] ?? null,
            'meta_data' => $options['meta_data'] ?? null,
            'status' => self::STATUS_ACTIVE,
        ]);

        // 增加域名使用次数
        $domain->incrementUseCount();

        return $shortLink;
    }

    /**
     * 获取今日点击统计
     */
    public function getTodayClicksAttribute(): int
    {
        return $this->accessLogs()
            ->whereDate('access_time', today())
            ->count();
    }

    /**
     * 获取转换域名后的短链接
     */
    public function switchDomain(?string $newDomain = null): self
    {
        if (!$newDomain) {
            $domain = DomainPool::getBestAvailableDomain();
            if (!$domain) {
                throw new \Exception('没有可用的域名');
            }
            $newDomain = $domain->domain;
        }

        $this->update(['domain' => $newDomain]);
        
        return $this;
    }

    /**
     * 批量切换域名
     */
    public static function batchSwitchDomain(string $oldDomain, string $newDomain): int
    {
        return self::where('domain', $oldDomain)
            ->where('status', self::STATUS_ACTIVE)
            ->update(['domain' => $newDomain]);
    }
} 