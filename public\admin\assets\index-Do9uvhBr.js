import{_ as e}from"./index-D2bI4m-v.js";/* empty css                      *//* empty css               *//* empty css                  */import{bk as a}from"./element-plus-DcSKpKA8.js";import{c as t,k as l,l as i,E as p}from"./vue-vendor-DGsK9sC4.js";const o={class:"pagination-container"},s=e({__name:"index",props:{total:{type:Number,required:!0,default:0},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:()=>[10,20,30,50]},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},small:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],setup(e,{emit:s}){const n=e,r=s,u=t({get:()=>n.page,set(e){r("update:page",e)}}),d=t({get:()=>n.limit,set(e){r("update:limit",e)}}),g=e=>{r("pagination",{page:u.value,limit:e})},m=e=>{r("pagination",{page:e,limit:d.value})};return(t,s)=>{const n=a;return i(),l("div",o,[p(n,{"current-page":u.value,"onUpdate:currentPage":s[0]||(s[0]=e=>u.value=e),"page-size":d.value,"onUpdate:pageSize":s[1]||(s[1]=e=>d.value=e),"page-sizes":e.pageSizes,total:e.total,layout:e.layout,background:e.background,small:e.small,disabled:e.disabled,onSizeChange:g,onCurrentChange:m},null,8,["current-page","page-size","page-sizes","total","layout","background","small","disabled"])])}}},[["__scopeId","data-v-961e4b3f"]]);export{s as P};
