import{_ as t}from"./index-D2bI4m-v.js";/* empty css                */import{a_ as e,aO as a,bX as s,p as r,T as d,U as n,o as l,b3 as o}from"./element-plus-DcSKpKA8.js";/* empty css                                                                 */import{c as i,y as u,l as p,z as c,t as f,E as v,C as _,k as y,B as m}from"./vue-vendor-DGsK9sC4.js";const S={class:"stat-content"},g={class:"stat-info"},b={class:"stat-title"},x={class:"stat-value"},j=t({__name:"StatCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},icon:{type:String,required:!0},color:{type:String,default:"#409EFF"},prefix:{type:String,default:""},suffix:{type:String,default:""},trend:{type:Number,default:void 0}},setup(t){const j=t,C=i(()=>j.trend>0?"trend-up":j.trend<0?"trend-down":"trend-neutral"),k=i(()=>j.trend>0?e:j.trend<0?a:s);return(e,a)=>{const s=d,i=o;return p(),u(i,{class:"stat-card",style:r({borderTop:`3px solid ${t.color}`})},{default:c(()=>{return[f("div",S,[f("div",{class:"stat-icon",style:r({backgroundColor:t.color})},[v(s,{size:24},{default:c(()=>[(p(),u(_(t.icon)))]),_:1})],4),f("div",g,[f("div",b,n(t.title),1),f("div",x,n(t.prefix)+n((e=t.value,"number"==typeof e?e.toLocaleString():e))+n(t.suffix),1),void 0!==t.trend?(p(),y("div",{key:0,class:l(["stat-trend",C.value])},[v(s,{size:12},{default:c(()=>[(p(),u(_(k.value)))]),_:1}),f("span",null,n(Math.abs(t.trend))+"%",1)],2)):m("",!0)])])];var e}),_:1},8,["style"])}}},[["__scopeId","data-v-5f4105ca"]]);export{j as S};
