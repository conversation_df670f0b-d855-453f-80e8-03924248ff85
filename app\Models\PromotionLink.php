<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PromotionLink extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'promotion_code',
        'target_url',
        'protected_url',
        'target_group_id',
        'landing_page_id',
        'remark',
        'clicks',
        'conversions',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the user that owns the promotion link.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
} 