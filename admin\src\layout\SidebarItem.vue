<template>
  <div v-if="!item.meta?.hidden">
    <!-- 有子菜单的情况 -->
    <el-sub-menu
      v-if="hasChildren"
      :index="resolvePath"
      :popper-class="'modern-submenu-popper'"
    >
      <template #title>
        <el-icon v-if="item.meta?.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span class="menu-title">{{ item.meta?.title }}</span>
      </template>
      
      <sidebar-item
        v-for="child in visibleChildren"
        :key="child.path"
        :item="child"
        :base-path="resolvePath"
      />
    </el-sub-menu>
    
    <!-- 单个菜单项 -->
    <el-menu-item
      v-else
      :index="resolvePath"
      :class="{ 'is-active': isActive }"
    >
      <el-icon v-if="item.meta?.icon">
        <component :is="item.meta.icon" />
      </el-icon>
      <template #title>
        <span class="menu-title">{{ item.meta?.title }}</span>
        <el-badge
          v-if="item.meta?.badge"
          :value="item.meta.badge"
          :type="item.meta.badgeType || 'danger'"
          class="menu-badge"
        />
      </template>
    </el-menu-item>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { isExternal } from '@/utils/validate'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

const route = useRoute()

// 计算属性
const hasChildren = computed(() => {
  return item.value.children && item.value.children.length > 0
})

const visibleChildren = computed(() => {
  if (!props.item.children) return []
  return props.item.children.filter(child => !child.meta?.hidden)
})

const resolvePath = computed(() => {
  if (isExternal(props.item.path)) {
    return props.item.path
  }
  
  if (props.basePath) {
    return `${props.basePath}/${props.item.path}`.replace(/\/+/g, '/')
  }
  
  return props.item.path
})

const isActive = computed(() => {
  return route.path === resolvePath.value
})

// 响应式引用
const item = computed(() => props.item)
</script>

<style lang="scss" scoped>
.menu-title {
  font-weight: 500;
  transition: all 0.3s ease;
}

.menu-badge {
  margin-left: auto;
}

// 子菜单样式
:deep(.el-sub-menu) {
  .el-sub-menu__title {
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.5s;
    }
    
    &:hover::before {
      left: 100%;
    }
  }
  
  .el-menu {
    background: rgba(0, 0, 0, 0.1);
    
    .el-menu-item {
      padding-left: 60px !important;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 40px;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transition: all 0.3s ease;
      }
      
      &:hover::before,
      &.is-active::before {
        background: #ffffff;
        transform: translateY(-50%) scale(1.2);
      }
      
      &.is-active {
        background: rgba(255, 255, 255, 0.1);
        
        &::after {
          content: '';
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: #ffffff;
          border-radius: 2px;
        }
      }
    }
  }
}

// 菜单项激活状态
:deep(.el-menu-item.is-active) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #3b82f6 0%, #8b5cf6 100%);
    border-radius: 0 2px 2px 0;
  }
}

// 外部链接样式
.external-link {
  .menu-title::after {
    content: '↗';
    margin-left: 4px;
    font-size: 12px;
    opacity: 0.6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .menu-title {
    font-size: 14px;
  }
}
</style>

<style lang="scss">
// 全局子菜单弹出层样式
.modern-submenu-popper {
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
  
  .el-menu {
    background: transparent !important;
    border: none !important;
    
    .el-menu-item {
      color: rgba(255, 255, 255, 0.75) !important;
      transition: all 0.3s ease !important;
      border-radius: 6px !important;
      margin: 4px 8px !important;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        transform: translateX(4px) !important;
      }
      
      &.is-active {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
      }
    }
  }
}
</style>