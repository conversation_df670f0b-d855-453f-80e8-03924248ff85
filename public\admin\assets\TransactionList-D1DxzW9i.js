import{c as e,_ as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                             *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               *//* empty css                *//* empty css                     *//* empty css                       *//* empty css                  */import{r as l,L as t,$ as d,e as o,k as s,l as i,E as n,z as u,J as r,u as p,a2 as c,F as m,Y as f,D as _,A as v,y as b,t as y,G as w,B as g}from"./vue-vendor-DGsK9sC4.js";import{k as h}from"./finance-Cskg9amr.js";import{P as j}from"./index-Do9uvhBr.js";import{bc as k,bd as x,aH as V,aW as U,aV as C,bl as P,as as L,b3 as D,b2 as Y,b4 as R,b6 as q,b7 as B,b8 as F,U as M,o as T,bj as z,bM as A,bN as E,au as G}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";/* empty css                      */const I={class:"app-container"},K=a({__name:"TransactionList",setup(a){const K=l(!0),O=l(!1),Z=l([]),$=l(0),H=l([]),J={recharge:"充值",withdraw:"提现",commission:"佣金",order_payment:"订单支付",refund:"退款",system_adjust:"系统调账"},N={pending:"处理中",completed:"已完成",failed:"已失败",cancelled:"已取消"},Q=t({queryParams:{page:1,limit:10,keyword:void 0,type:void 0,status:void 0,start_date:void 0,end_date:void 0},dialog:{visible:!1,data:null}}),{queryParams:S,dialog:W}=d(Q),X=e=>e?parseFloat(e).toFixed(2):"0.00",ee=e=>({recharge:"success",withdraw:"warning",commission:"primary",order_payment:"info",refund:"danger",system_adjust:""}[e]||"info"),ae=e=>({pending:"warning",completed:"success",failed:"danger",cancelled:"info"}[e]||"info");async function le(){K.value=!0;try{H.value&&2===H.value.length?(S.value.start_date=H.value[0],S.value.end_date=H.value[1]):(S.value.start_date=void 0,S.value.end_date=void 0);const e=await h(S.value);Z.value=e.data.data,$.value=e.data.total}finally{K.value=!1}}function te(){S.value.page=1,le()}function de(){H.value=[],S.value={page:1,limit:10,keyword:void 0,type:void 0,status:void 0,start_date:void 0,end_date:void 0},te()}async function oe(){O.value=!0;try{await function(a,l={},t="export.xlsx"){return new Promise((d,o)=>{e({url:a,method:"get",params:l,responseType:"blob"}).then(e=>{const a=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),l=window.URL.createObjectURL(a),o=document.createElement("a");o.href=l,o.download=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(l),d(e)}).catch(e=>{o(e)})})}("/finance/transactions/export",S.value,`transactions_${Date.now()}.xlsx`)}finally{O.value=!1}}return o(()=>{le()}),(e,a)=>{const l=V,t=x,d=C,o=U,h=P,Q=L,se=k,ie=D,ne=Y,ue=R,re=B,pe=F,ce=q,me=E,fe=A,_e=G,ve=z;return i(),s("div",I,[n(ie,{class:"filter-card"},{default:u(()=>[n(se,{inline:!0,model:p(S),onSubmit:r(te,["prevent"])},{default:u(()=>[n(t,{label:"关键字"},{default:u(()=>[n(l,{modelValue:p(S).keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>p(S).keyword=e),placeholder:"订单号/用户昵称",clearable:"",onKeyup:c(te,["enter"])},null,8,["modelValue"])]),_:1}),n(t,{label:"交易类型"},{default:u(()=>[n(o,{modelValue:p(S).type,"onUpdate:modelValue":a[1]||(a[1]=e=>p(S).type=e),placeholder:"全部类型",clearable:""},{default:u(()=>[(i(),s(m,null,f(J,(e,a)=>n(d,{key:a,label:e,value:a},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),n(t,{label:"交易状态"},{default:u(()=>[n(o,{modelValue:p(S).status,"onUpdate:modelValue":a[2]||(a[2]=e=>p(S).status=e),placeholder:"全部状态",clearable:""},{default:u(()=>[(i(),s(m,null,f(N,(e,a)=>n(d,{key:a,label:e,value:a},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),n(t,{label:"创建时间"},{default:u(()=>[n(h,{modelValue:H.value,"onUpdate:modelValue":a[3]||(a[3]=e=>H.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(t,null,{default:u(()=>[n(Q,{type:"primary",icon:"el-icon-search",onClick:te},{default:u(()=>a[8]||(a[8]=[_("查询",-1)])),_:1,__:[8]}),n(Q,{icon:"el-icon-refresh",onClick:de},{default:u(()=>a[9]||(a[9]=[_("重置",-1)])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])]),_:1}),n(ue,{gutter:10,class:"mb8"},{default:u(()=>[n(ne,{span:1.5},{default:u(()=>[n(Q,{type:"success",plain:"",icon:"el-icon-download",onClick:oe,loading:O.value},{default:u(()=>a[10]||(a[10]=[_("导出",-1)])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1}),n(ie,null,{default:u(()=>[v((i(),b(ce,{data:Z.value},{default:u(()=>[n(re,{label:"交易号",prop:"id",width:"100"}),n(re,{label:"用户",prop:"user.nickname",width:"150"}),n(re,{label:"交易类型",align:"center",width:"120"},{default:u(e=>[n(pe,{type:ee(e.row.type)},{default:u(()=>[_(M(J[e.row.type]||"未知"),1)]),_:2},1032,["type"])]),_:1}),n(re,{label:"金额",width:"150"},{default:u(e=>[y("span",{class:T(e.row.amount>0?"text-green":"text-red")},M(e.row.amount>0?"+":"")+" ¥"+M(X(e.row.amount)),3)]),_:1}),n(re,{label:"状态",align:"center",width:"100"},{default:u(e=>[n(pe,{type:ae(e.row.status)},{default:u(()=>[_(M(N[e.row.status]||"未知"),1)]),_:2},1032,["type"])]),_:1}),n(re,{label:"描述",prop:"description","show-overflow-tooltip":""}),n(re,{label:"创建时间",prop:"created_at",width:"160"}),n(re,{label:"操作",width:"100",fixed:"right"},{default:u(e=>[n(Q,{link:"",type:"primary",onClick:a=>{return l=e.row,W.value.data=l,void(W.value.visible=!0);var l}},{default:u(()=>a[11]||(a[11]=[_("详情",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ve,K.value]]),v(n(j,{total:$.value,page:p(S).page,"onUpdate:page":a[4]||(a[4]=e=>p(S).page=e),limit:p(S).limit,"onUpdate:limit":a[5]||(a[5]=e=>p(S).limit=e),onPagination:le},null,8,["total","page","limit"]),[[w,$.value>0]])]),_:1}),n(_e,{title:"交易详情",modelValue:p(W).visible,"onUpdate:modelValue":a[7]||(a[7]=e=>p(W).visible=e),width:"600px"},{footer:u(()=>[n(Q,{onClick:a[6]||(a[6]=e=>p(W).visible=!1)},{default:u(()=>a[12]||(a[12]=[_("关闭",-1)])),_:1,__:[12]})]),default:u(()=>[p(W).data?(i(),b(fe,{key:0,column:2,border:""},{default:u(()=>[n(me,{label:"交易号"},{default:u(()=>[_(M(p(W).data.id),1)]),_:1}),n(me,{label:"用户"},{default:u(()=>[_(M(p(W).data.user?.nickname)+" (ID: "+M(p(W).data.user_id)+")",1)]),_:1}),n(me,{label:"交易类型"},{default:u(()=>[n(pe,{type:ee(p(W).data.type)},{default:u(()=>[_(M(J[p(W).data.type]||"未知"),1)]),_:1},8,["type"])]),_:1}),n(me,{label:"交易状态"},{default:u(()=>[n(pe,{type:ae(p(W).data.status)},{default:u(()=>[_(M(N[p(W).data.status]||"未知"),1)]),_:1},8,["type"])]),_:1}),n(me,{label:"交易金额"},{default:u(()=>[y("span",{class:T(p(W).data.amount>0?"text-green":"text-red")},M(p(W).data.amount>0?"+":"")+" ¥"+M(X(p(W).data.amount)),3)]),_:1}),n(me,{label:"关联订单号"},{default:u(()=>[_(M(p(W).data.order_id||"无"),1)]),_:1}),n(me,{label:"创建时间"},{default:u(()=>[_(M(p(W).data.created_at),1)]),_:1}),n(me,{label:"更新时间"},{default:u(()=>[_(M(p(W).data.updated_at),1)]),_:1}),n(me,{label:"描述",span:2},{default:u(()=>[_(M(p(W).data.description),1)]),_:1})]),_:1})):g("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-4412ac9a"]]);export{K as default};
