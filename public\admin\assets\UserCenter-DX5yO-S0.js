import{_ as a}from"./index-D2bI4m-v.js";/* empty css                     *//* empty css                       *//* empty css                        *//* empty css                 *//* empty css                *//* empty css               *//* empty css               */import{aN as e,U as l,b8 as s,T as t,ac as n,as as o,b2 as r,b4 as u,a7 as i,bw as d,b$ as c,af as p,bE as m,aQ as v,b3 as f,bm as _,o as y,bo as b,bv as g,bc as h,bd as C,aH as k,au as V,Q as j}from"./element-plus-DcSKpKA8.js";import{S as w}from"./StatCard-WpSR56Tk.js";import{L as x}from"./LineChart-Ba008-uu.js";import{A as F}from"./AvatarUpload-QrJM1H43.js";import{g as U,a as z,b as A,c as E,d as q,u as S}from"./user-mKGRZpRV.js";import{r as L,L as P,e as D,k as Z,l as B,t as H,E as I,y as N,B as Q,z as R,D as T,u as $,F as J,Y as K}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";/* empty css                                                                 */import"./chart-Bup65vvO.js";/* empty css                  *//* empty css                    */import"./chunk-KZPPZA2C-BZQYgWVq.js";const M={class:"user-center"},O={class:"page-header"},W={class:"user-info"},X={class:"user-details"},Y={class:"user-tags"},G={class:"header-actions"},aa={class:"card-header"},ea={class:"recent-orders"},la={key:0,class:"empty-state"},sa={key:1},ta={class:"order-info"},na={class:"order-title"},oa={class:"order-meta"},ra={class:"order-no"},ua={class:"order-time"},ia={class:"order-amount"},da={class:"order-status"},ca={class:"card-header"},pa={class:"points-history"},ma={key:0,class:"empty-state"},va={key:1},fa={class:"points-info"},_a={class:"points-title"},ya={class:"points-time"},ba={class:"card-header"},ga={class:"analysis-summary"},ha={class:"summary-item"},Ca={class:"value"},ka={class:"summary-item"},Va={class:"value"},ja={class:"summary-item"},wa={class:"value"},xa={class:"summary-item"},Fa={class:"value"},Ua=a({__name:"UserCenter",setup(a){const Ua=L(!1),za=L(!1),Aa=L("month"),Ea=L({}),qa=L({}),Sa=L([]),La=L([]),Pa=L({}),Da=P({name:"",email:"",phone:"",avatar:"",bio:""}),Za={name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},Ba=L(),Ha=L({labels:[],datasets:[{label:"消费金额",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}),Ia={responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},Na=async()=>{try{const a=await U();Ea.value=a.data,Object.assign(Da,{name:Ea.value.name||"",email:Ea.value.email||"",phone:Ea.value.phone||"",avatar:Ea.value.avatar||"",bio:Ea.value.bio||""})}catch(a){j.error("加载用户信息失败")}},Qa=async()=>{try{const a=await q({period:Aa.value});Pa.value=a.data.summary,Ha.value={labels:a.data.chart.labels,datasets:[{label:"消费金额",data:a.data.chart.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4}]}}catch(a){j.error("加载消费分析失败")}},Ra=async()=>{try{await Ba.value.validate(),za.value=!0,await S(Da),j.success("资料更新成功"),Ua.value=!1,Na()}catch(a){!1!==a&&j.error("更新失败")}finally{za.value=!1}},Ta=a=>{Ea.value.avatar=a.url,j.success("头像更新成功!")},$a=a=>{console.error("头像上传失败:",a),j.error("头像上传失败，请重试")},Ja=()=>{j.info("订单页面开发中...")},Ka=()=>{j.info("积分页面开发中...")},Ma=()=>{j.info("优惠券页面开发中...")},Oa=()=>{j.info("安全设置页面开发中...")},Wa=()=>{j.info("意见反馈页面开发中...")},Xa=()=>{j.info("帮助中心页面开发中...")},Ya=a=>new Date(a).toLocaleString("zh-CN"),Ga=a=>({pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[a]||"未知状态");return D(()=>{Na(),(async()=>{try{const a=await z();qa.value=a.data}catch(a){j.error("加载统计数据失败")}})(),(async()=>{try{const a=await A({limit:5});Sa.value=a.data}catch(a){j.error("加载订单数据失败")}})(),(async()=>{try{const a=await E({limit:5});La.value=a.data}catch(a){j.error("加载积分记录失败")}})(),Qa()}),(a,j)=>{const U=e,z=s,A=t,E=o,q=r,S=u,L=f,P=_,D=g,Na=b,ae=C,ee=k,le=h,se=V;return B(),Z("div",M,[H("div",O,[H("div",W,[I(U,{size:80,src:Ea.value.avatar},null,8,["src"]),H("div",X,[H("h2",null,l(Ea.value.name||Ea.value.username),1),H("p",null,l(Ea.value.email),1),H("div",Y,[I(z,{type:"primary"},{default:R(()=>{return[T(l((a=Ea.value.role,{admin:"管理员",substation:"分站管理员",agent:"代理商",distributor:"分销员",group_owner:"群主",user:"普通用户"}[a]||"未知角色")),1)];var a}),_:1}),Ea.value.vip_level?(B(),N(z,{key:0,type:"warning"},{default:R(()=>[T("VIP"+l(Ea.value.vip_level),1)]),_:1})):Q("",!0),I(z,{type:1===Ea.value.status?"success":"danger"},{default:R(()=>[T(l(1===Ea.value.status?"正常":"禁用"),1)]),_:1},8,["type"])])])]),H("div",G,[I(E,{type:"primary",onClick:j[0]||(j[0]=a=>Ua.value=!0)},{default:R(()=>[I(A,null,{default:R(()=>[I($(n))]),_:1}),j[9]||(j[9]=T(" 编辑资料 ",-1))]),_:1,__:[9]})])]),I(S,{gutter:20,class:"stats-row"},{default:R(()=>[I(q,{span:6},{default:R(()=>[I(w,{title:"我的订单",value:qa.value.total_orders||0,icon:"Tickets",color:"#409EFF"},null,8,["value"])]),_:1}),I(q,{span:6},{default:R(()=>[I(w,{title:"消费金额",value:qa.value.total_spent||0,icon:"Money",color:"#67C23A",prefix:"¥"},null,8,["value"])]),_:1}),I(q,{span:6},{default:R(()=>[I(w,{title:"积分余额",value:qa.value.points||0,icon:"Star",color:"#E6A23C"},null,8,["value"])]),_:1}),I(q,{span:6},{default:R(()=>[I(w,{title:"优惠券",value:qa.value.coupons||0,icon:"Discount",color:"#F56C6C",suffix:"张"},null,8,["value"])]),_:1})]),_:1}),I(L,{class:"quick-functions"},{header:R(()=>j[10]||(j[10]=[H("span",null,"快捷功能",-1)])),default:R(()=>[I(S,{gutter:15},{default:R(()=>[I(q,{span:4},{default:R(()=>[H("div",{class:"function-item",onClick:Ja},[I(A,{class:"function-icon"},{default:R(()=>[I($(i))]),_:1}),j[11]||(j[11]=H("span",null,"我的订单",-1))])]),_:1}),I(q,{span:4},{default:R(()=>[H("div",{class:"function-item",onClick:Ka},[I(A,{class:"function-icon"},{default:R(()=>[I($(d))]),_:1}),j[12]||(j[12]=H("span",null,"积分管理",-1))])]),_:1}),I(q,{span:4},{default:R(()=>[H("div",{class:"function-item",onClick:Ma},[I(A,{class:"function-icon"},{default:R(()=>[I($(c))]),_:1}),j[13]||(j[13]=H("span",null,"优惠券",-1))])]),_:1}),I(q,{span:4},{default:R(()=>[H("div",{class:"function-item",onClick:Oa},[I(A,{class:"function-icon"},{default:R(()=>[I($(p))]),_:1}),j[14]||(j[14]=H("span",null,"安全设置",-1))])]),_:1}),I(q,{span:4},{default:R(()=>[H("div",{class:"function-item",onClick:Wa},[I(A,{class:"function-icon"},{default:R(()=>[I($(m))]),_:1}),j[15]||(j[15]=H("span",null,"意见反馈",-1))])]),_:1}),I(q,{span:4},{default:R(()=>[H("div",{class:"function-item",onClick:Xa},[I(A,{class:"function-icon"},{default:R(()=>[I($(v))]),_:1}),j[16]||(j[16]=H("span",null,"帮助中心",-1))])]),_:1})]),_:1})]),_:1}),I(S,{gutter:20,class:"content-row"},{default:R(()=>[I(q,{span:12},{default:R(()=>[I(L,null,{header:R(()=>[H("div",aa,[j[18]||(j[18]=H("span",null,"最近订单",-1)),I(E,{size:"small",onClick:Ja},{default:R(()=>j[17]||(j[17]=[T("查看全部",-1)])),_:1,__:[17]})])]),default:R(()=>[H("div",ea,[0===Sa.value.length?(B(),Z("div",la,[I(P,{description:"暂无订单"})])):(B(),Z("div",sa,[(B(!0),Z(J,null,K(Sa.value,a=>{return B(),Z("div",{key:a.id,class:"order-item"},[H("div",ta,[H("div",na,l(a.wechat_group?.title||"订单"),1),H("div",oa,[H("span",ra,"订单号: "+l(a.order_no),1),H("span",ua,l(Ya(a.created_at)),1)])]),H("div",ia,"¥"+l(a.amount),1),H("div",da,[I(z,{type:(e=a.status,{pending:"warning",paid:"success",cancelled:"danger",refunded:"info"}[e]||"info")},{default:R(()=>[T(l(Ga(a.status)),1)]),_:2},1032,["type"])])]);var e}),128))]))])]),_:1})]),_:1}),I(q,{span:12},{default:R(()=>[I(L,null,{header:R(()=>[H("div",ca,[j[20]||(j[20]=H("span",null,"积分记录",-1)),I(E,{size:"small",onClick:Ka},{default:R(()=>j[19]||(j[19]=[T("查看全部",-1)])),_:1,__:[19]})])]),default:R(()=>[H("div",pa,[0===La.value.length?(B(),Z("div",ma,[I(P,{description:"暂无积分记录"})])):(B(),Z("div",va,[(B(!0),Z(J,null,K(La.value,a=>(B(),Z("div",{key:a.id,class:"points-item"},[H("div",fa,[H("div",_a,l(a.description),1),H("div",ya,l(Ya(a.created_at)),1)]),H("div",{class:y(["points-change","add"===a.type?"positive":"negative"])},l("add"===a.type?"+":"-")+l(a.points),3)]))),128))]))])]),_:1})]),_:1})]),_:1}),I(L,{class:"consumption-analysis"},{header:R(()=>[H("div",ba,[j[24]||(j[24]=H("span",null,"消费分析",-1)),I(Na,{modelValue:Aa.value,"onUpdate:modelValue":j[1]||(j[1]=a=>Aa.value=a),size:"small",onChange:Qa},{default:R(()=>[I(D,{label:"month"},{default:R(()=>j[21]||(j[21]=[T("本月",-1)])),_:1,__:[21]}),I(D,{label:"quarter"},{default:R(()=>j[22]||(j[22]=[T("本季度",-1)])),_:1,__:[22]}),I(D,{label:"year"},{default:R(()=>j[23]||(j[23]=[T("本年",-1)])),_:1,__:[23]})]),_:1},8,["modelValue"])])]),default:R(()=>[I(S,{gutter:20},{default:R(()=>[I(q,{span:16},{default:R(()=>[I(x,{data:Ha.value,options:Ia,height:"300px"},null,8,["data"])]),_:1}),I(q,{span:8},{default:R(()=>[H("div",ga,[H("div",ha,[j[25]||(j[25]=H("span",{class:"label"},"总消费:",-1)),H("span",Ca,"¥"+l(Pa.value.total_amount||0),1)]),H("div",ka,[j[26]||(j[26]=H("span",{class:"label"},"订单数:",-1)),H("span",Va,l(Pa.value.total_orders||0),1)]),H("div",ja,[j[27]||(j[27]=H("span",{class:"label"},"平均客单价:",-1)),H("span",wa,"¥"+l(Pa.value.avg_amount||0),1)]),H("div",xa,[j[28]||(j[28]=H("span",{class:"label"},"最高消费:",-1)),H("span",Fa,"¥"+l(Pa.value.max_amount||0),1)])])]),_:1})]),_:1})]),_:1}),I(se,{modelValue:Ua.value,"onUpdate:modelValue":j[8]||(j[8]=a=>Ua.value=a),title:"编辑个人资料",width:"600px"},{footer:R(()=>[I(E,{onClick:j[7]||(j[7]=a=>Ua.value=!1)},{default:R(()=>j[29]||(j[29]=[T("取消",-1)])),_:1,__:[29]}),I(E,{type:"primary",onClick:Ra,loading:za.value},{default:R(()=>j[30]||(j[30]=[T(" 保存 ",-1)])),_:1,__:[30]},8,["loading"])]),default:R(()=>[I(le,{model:Da,rules:Za,ref_key:"profileFormRef",ref:Ba,"label-width":"100px"},{default:R(()=>[I(ae,{label:"头像"},{default:R(()=>[I(F,{modelValue:Da.avatar,"onUpdate:modelValue":j[2]||(j[2]=a=>Da.avatar=a),size:100,"max-size":2,"enable-preview":!0,onSuccess:Ta,onError:$a},null,8,["modelValue"])]),_:1}),I(ae,{label:"姓名",prop:"name"},{default:R(()=>[I(ee,{modelValue:Da.name,"onUpdate:modelValue":j[3]||(j[3]=a=>Da.name=a),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),I(ae,{label:"邮箱",prop:"email"},{default:R(()=>[I(ee,{modelValue:Da.email,"onUpdate:modelValue":j[4]||(j[4]=a=>Da.email=a),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),I(ae,{label:"手机号",prop:"phone"},{default:R(()=>[I(ee,{modelValue:Da.phone,"onUpdate:modelValue":j[5]||(j[5]=a=>Da.phone=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),I(ae,{label:"个人简介"},{default:R(()=>[I(ee,{modelValue:Da.bio,"onUpdate:modelValue":j[6]||(j[6]=a=>Da.bio=a),type:"textarea",rows:3,placeholder:"请输入个人简介"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-a9862009"]]);export{Ua as default};
