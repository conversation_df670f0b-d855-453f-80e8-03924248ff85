<template>
  <div class="modern-group-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">社群管理</h1>
          <p class="page-subtitle">管理和监控您的社群运营状况</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" @click="handleCreate" class="create-btn">
            <el-icon><Plus /></el-icon>
            创建群组
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="stats-card primary">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Comment /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.total_groups }}</div>
                <div class="stats-label">总群组数</div>
              </div>
            </div>
            <div class="stats-trend positive">
              <el-icon><CaretTop /></el-icon>
              <span>+15.8%</span>
            </div>
            <div class="stats-bg-icon">
              <el-icon><Comment /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card success">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.active_groups }}</div>
                <div class="stats-label">活跃群组</div>
              </div>
            </div>
            <div class="stats-trend positive">
              <el-icon><CaretTop /></el-icon>
              <span>+8.2%</span>
            </div>
            <div class="stats-bg-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card warning">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ stats.total_members }}</div>
                <div class="stats-label">总成员数</div>
              </div>
            </div>
            <div class="stats-trend positive">
              <el-icon><CaretTop /></el-icon>
              <span>+12.1%</span>
            </div>
            <div class="stats-bg-icon">
              <el-icon><User /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card danger">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ (stats.total_revenue || 0).toFixed(0) }}</div>
                <div class="stats-label">总收入</div>
              </div>
            </div>
            <div class="stats-trend positive">
              <el-icon><CaretTop /></el-icon>
              <span>+18.5%</span>
            </div>
            <div class="stats-bg-icon">
              <el-icon><Money /></el-icon>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和操作区域 -->
    <div class="filter-section">
      <el-card class="filter-card" shadow="never">
        <div class="filter-content">
          <div class="filter-left">
            <div class="search-group">
              <el-input
                v-model="listQuery.keyword"
                placeholder="搜索群组名称、群主..."
                class="search-input"
                @keyup.enter="handleFilter"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="filter-group">
              <el-select
                v-model="listQuery.status"
                placeholder="群组状态"
                clearable
                class="filter-select"
              >
                <el-option label="全部状态" value="" />
                <el-option label="活跃" value="active" />
                <el-option label="暂停" value="paused" />
                <el-option label="已满" value="full" />
                <el-option label="待审核" value="pending" />
              </el-select>
              <el-select
                v-model="listQuery.category"
                placeholder="群组分类"
                clearable
                class="filter-select"
              >
                <el-option label="全部分类" value="" />
                <el-option label="创业交流" value="startup" />
                <el-option label="投资理财" value="finance" />
                <el-option label="科技互联网" value="tech" />
                <el-option label="教育培训" value="education" />
                <el-option label="其他" value="other" />
              </el-select>
            </div>
          </div>
          <div class="filter-right">
            <el-button type="primary" @click="handleFilter" class="action-btn">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button type="success" @click="handleAutoRules" class="action-btn">
              <el-icon><Setting /></el-icon>
              自动化规则
            </el-button>
            <el-button type="warning" @click="handleExport" class="action-btn">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button v-if="multipleSelection.length > 0" type="info" @click="handleBatchOperation" class="action-btn">
              批量操作 ({{ multipleSelection.length }})
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="table-header">
            <div class="header-left">
              <h3>群组列表</h3>
              <span class="total-count">共 {{ total }} 个群组</span>
            </div>
            <div class="header-right">
              <el-button-group>
                <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                  表格视图
                </el-button>
                <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
                  卡片视图
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-table
            v-loading="listLoading"
            :data="list"
            element-loading-text="加载中..."
            class="modern-table"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="群组信息" width="280" fixed="left">
              <template #default="{ row }">
                <div class="group-info">
                  <div class="group-avatar">
                    <el-avatar :src="row.avatar" :alt="row.name" size="large">
                      {{ row.name.charAt(0) }}
                    </el-avatar>
                    <div class="status-dot" :class="row.status"></div>
                  </div>
                  <div class="group-details">
                    <div class="group-name">{{ row.name }}</div>
                    <div class="group-desc">{{ row.description }}</div>
                    <div class="group-meta">
                      <span class="group-id">ID: {{ row.id }}</span>
                      <span class="group-category">{{ getCategoryText(row.category) }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="群主信息" width="150">
              <template #default="{ row }">
                <div class="owner-info">
                  <div class="owner-name">{{ row.owner?.name || row.owner_name }}</div>
                  <el-tag size="small" type="info" class="owner-role">
                    {{ row.owner?.role || row.owner_role || '群主' }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="价格" width="100">
              <template #default="{ row }">
                <div class="price-info">
                  <span class="price-value">¥{{ (row.price || 0).toFixed(2) }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="成员统计" width="180">
              <template #default="{ row }">
                <div class="member-stats">
                  <div class="member-count">
                    <span class="current">{{ row.memberCount || row.current_members || 0 }}</span>
                    <span class="separator">/</span>
                    <span class="max">{{ row.maxMembers || row.max_members || 500 }}</span>
                  </div>
                  <el-progress
                    :percentage="((row.memberCount || row.current_members || 0) / (row.maxMembers || row.max_members || 500) * 100)"
                    :stroke-width="6"
                    :show-text="false"
                    :color="getProgressColor((row.memberCount || row.current_members || 0) / (row.maxMembers || row.max_members || 500))"
                    class="member-progress"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="健康度" width="120">
              <template #default="{ row }">
                <div class="health-score">
                  <div class="score-circle" :class="getHealthScoreClass(row.health_score)">
                    {{ row.health_score || 'N/A' }}
                  </div>
                  <div class="score-label">{{ getHealthScoreLabel(row.health_score) }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)" class="status-tag">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" width="160">
              <template #default="{ row }">
                <div class="time-info">
                  <div class="date">{{ formatDate(row.created_at) }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="handleEdit(row)" class="action-btn-small">
                    编辑
                  </el-button>
                  <el-button type="success" size="small" @click="handleMembers(row)" class="action-btn-small">
                    成员
                  </el-button>
                  <el-button type="info" size="small" @click="handleAnalytics(row)" class="action-btn-small">
                    分析
                  </el-button>
                  <el-dropdown @command="handleCommand" class="action-dropdown">
                    <el-button type="warning" size="small" class="action-btn-small">
                      更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="`qrcode-${row.id}`">
                          二维码
                        </el-dropdown-item>
                        <el-dropdown-item :command="`content-${row.id}`">
                          <el-icon><Document /></el-icon>
                          内容管理
                        </el-dropdown-item>
                        <el-dropdown-item :command="`clone-${row.id}`">
                          克隆群组
                        </el-dropdown-item>
                        <el-dropdown-item :command="`pause-${row.id}`" v-if="row.status === 'active'">
                          暂停群组
                        </el-dropdown-item>
                        <el-dropdown-item :command="`resume-${row.id}`" v-if="row.status === 'paused'">
                          恢复群组
                        </el-dropdown-item>
                        <el-dropdown-item :command="`delete-${row.id}`" divided>
                          <el-icon><Delete /></el-icon>
                          删除群组
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <el-row :gutter="24">
            <el-col :span="8" v-for="item in list" :key="item.id">
              <div class="group-card" @click="handleEdit(item)">
                <div class="card-header">
                  <div class="group-avatar">
                    <el-avatar :src="item.avatar" :alt="item.name" size="large">
                      {{ item.name.charAt(0) }}
                    </el-avatar>
                    <div class="status-indicator" :class="item.status"></div>
                  </div>
                  <div class="card-actions">
                    <el-dropdown @command="handleCommand" trigger="click">
                      <el-button text class="more-btn">
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="`edit-${item.id}`">编辑</el-dropdown-item>
                          <el-dropdown-item :command="`members-${item.id}`">成员管理</el-dropdown-item>
                          <el-dropdown-item :command="`analytics-${item.id}`">数据分析</el-dropdown-item>
                          <el-dropdown-item :command="`delete-${item.id}`" divided>删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
                <div class="card-content">
                  <h4 class="group-title">{{ item.name }}</h4>
                  <p class="group-description">{{ item.description }}</p>
                  <div class="group-stats">
                    <div class="stat-item">
                      <span class="label">成员</span>
                      <span class="value">{{ item.memberCount || item.current_members || 0 }}/{{ item.maxMembers || item.max_members || 500 }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="label">价格</span>
                      <span class="value price">¥{{ (item.price || 0).toFixed(2) }}</span>
                    </div>
                  </div>
                  <div class="group-tags">
                    <el-tag :type="getCategoryTagType(item.category)" size="small">
                      {{ getCategoryText(item.category) }}
                    </el-tag>
                    <el-tag :type="getStatusTagType(item.status)" size="small">
                      {{ getStatusText(item.status) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="listQuery.page"
            v-model:page-size="listQuery.limit"
            :page-sizes="[12, 24, 36, 48]"
            :total="total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="modern-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 对话框组件 -->
    <GroupDialog
      v-model="dialogVisible"
      :group-data="currentGroup"
      @success="handleDialogSuccess"
    />

    <GroupMemberManager
      v-model="memberDialogVisible"
      :group-data="currentGroup"
      @success="handleMemberSuccess"
    />

    <GroupAnalyticsSimple
      v-model:visible="analyticsDrawerVisible"
      :group-id="currentGroupId"
      :group-data="currentGroup"
    />

    <QRCodeDialog
      v-model="qrcodeDialogVisible"
      :group-data="currentGroup"
    />

    <GroupContentManager
      v-model="contentDialogVisible"
      :group-data="currentGroup"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Comment, ChatDotRound, User, Money, CaretTop, Search, Plus, Download, 
  ArrowDown, Setting, Document, Delete, MoreFilled
} from '@element-plus/icons-vue'
import GroupDialog from './components/GroupDialog.vue'
import GroupMemberManager from './components/GroupMemberManager.vue'
import GroupAnalyticsSimple from './components/GroupAnalyticsSimple.vue'
import QRCodeDialog from './components/QRCodeDialog.vue'
import GroupContentManager from './components/GroupContentManager.vue'
import { getGroupList, deleteGroup, updateGroupStatus, exportGroups, getGroupStats } from '@/api/community'
import { mockCommunityEnhancedAPI } from '@/api/mock/community-enhanced'
import { formatDate } from '@/utils/format'

// 响应式数据
const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const dialogVisible = ref(false)
const memberDialogVisible = ref(false)
const analyticsDrawerVisible = ref(false)
const qrcodeDialogVisible = ref(false)
const contentDialogVisible = ref(false)
const currentGroup = ref({})
const currentGroupId = ref(null)
const multipleSelection = ref([])
const viewMode = ref('table') // 'table' 或 'card'

// 统计数据
const stats = ref({
  total_groups: 0,
  active_groups: 0,
  total_members: 0,
  total_revenue: 0
})

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  status: '',
  category: ''
})

// 获取群组列表
const getList = async () => {
  listLoading.value = true
  try {
    const response = await getGroupList(listQuery)
    const { data } = response

    if (data && data.list) {
      list.value = data.list
      total.value = data.total || 0
    } else {
      list.value = []
      total.value = 0
      ElMessage.warning('暂无数据')
    }
  } catch (error) {
    console.error('获取群组列表失败:', error)
    ElMessage.error('获取群组列表失败')
    list.value = []
    total.value = 0
  } finally {
    listLoading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const response = await getGroupStats()
    const { data } = response

    if (data) {
      stats.value = data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 筛选
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 创建群组
const handleCreate = () => {
  currentGroup.value = {}
  dialogVisible.value = true
}

// 编辑群组
const handleEdit = (row) => {
  currentGroup.value = { ...row }
  dialogVisible.value = true
}

// 成员管理
const handleMembers = (row) => {
  currentGroup.value = { ...row }
  memberDialogVisible.value = true
}

// 数据分析
const handleAnalytics = (row) => {
  currentGroupId.value = row.id
  analyticsDrawerVisible.value = true
}

// 自动化规则
const handleAutoRules = () => {
  ElMessage.info('正在跳转到自动化规则配置页面...')
}

// 二维码
const handleQRCode = (row) => {
  currentGroup.value = { ...row }
  qrcodeDialogVisible.value = true
}

// 内容管理
const handleContentManage = (row) => {
  currentGroup.value = { ...row }
  contentDialogVisible.value = true
}

// 克隆群组
const handleClone = async (groupId) => {
  try {
    await ElMessageBox.confirm('确定要克隆该群组吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    ElMessage.success('群组克隆成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('克隆失败')
    }
  }
}

// 更新群组状态
const handleUpdateStatus = async (groupId, status) => {
  try {
    const action = status === 'paused' ? '暂停' : '恢复'
    await ElMessageBox.confirm(`确定要${action}该群组吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await updateGroupStatus(groupId, status)
    ElMessage.success(`${action}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 删除群组
const handleDelete = async (groupId) => {
  try {
    await ElMessageBox.confirm('确定要删除该群组吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await deleteGroup(groupId)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  const [action, groupId] = command.split('-')
  const id = parseInt(groupId)
  const group = list.value.find(g => g.id === id)
  
  switch (action) {
    case 'edit':
      handleEdit(group)
      break
    case 'members':
      handleMembers(group)
      break
    case 'analytics':
      handleAnalytics(group)
      break
    case 'qrcode':
      currentGroup.value = { ...group }
      qrcodeDialogVisible.value = true
      break
    case 'content':
      currentGroup.value = { ...group }
      contentDialogVisible.value = true
      break
    case 'clone':
      handleClone(id)
      break
    case 'pause':
      handleUpdateStatus(id, 'paused')
      break
    case 'resume':
      handleUpdateStatus(id, 'active')
      break
    case 'delete':
      handleDelete(id)
      break
  }
}

// 导出数据
const handleExport = async () => {
  try {
    await exportGroups(listQuery)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 批量操作
const handleBatchOperation = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要操作的群组')
    return
  }
  
  ElMessageBox.confirm('请选择批量操作类型', '批量操作', {
    distinguishCancelAndClose: true,
    confirmButtonText: '暂停群组',
    cancelButtonText: '恢复群组'
  }).then(() => {
    batchUpdateStatus('paused')
  }).catch((action) => {
    if (action === 'cancel') {
      batchUpdateStatus('active')
    }
  })
}

// 批量更新状态
const batchUpdateStatus = async (status) => {
  try {
    const groupIds = multipleSelection.value.map(group => group.id)
    ElMessage.success('批量操作成功')
    getList()
  } catch (error) {
    ElMessage.error('批量操作失败')
  }
}

// 选择变化
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 分页
const handleSizeChange = (val) => {
  listQuery.limit = val
  getList()
}

const handleCurrentChange = (val) => {
  listQuery.page = val
  getList()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  getList()
  getStats()
}

const handleMemberSuccess = () => {
  getList()
}

// 工具函数
const getCategoryTagType = (category) => {
  const types = {
    startup: 'success',
    finance: 'warning',
    tech: 'primary',
    education: 'info',
    other: ''
  }
  return types[category] || ''
}

const getCategoryText = (category) => {
  const texts = {
    startup: '创业交流',
    finance: '投资理财',
    tech: '科技互联网',
    education: '教育培训',
    other: '其他'
  }
  return texts[category] || '未知'
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    paused: 'warning',
    full: 'info',
    pending: 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    paused: '暂停',
    full: '已满',
    pending: '待审核'
  }
  return texts[status] || '未知'
}

const getProgressColor = (percentage) => {
  if (percentage < 0.5) return '#67c23a'
  if (percentage < 0.8) return '#e6a23c'
  return '#f56c6c'
}

const getHealthScoreClass = (score) => {
  if (!score || score < 40) return 'poor'
  if (score < 60) return 'fair'
  if (score < 80) return 'good'
  return 'excellent'
}

const getHealthScoreLabel = (score) => {
  if (!score || score < 40) return '较差'
  if (score < 60) return '一般'
  if (score < 80) return '良好'
  return '优秀'
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.modern-group-list {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

// 页面头部
.page-header {
  margin-bottom: 32px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    
    .header-left {
      .page-title {
        font-size: 32px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      .page-subtitle {
        font-size: 16px;
        color: #64748b;
        margin: 0;
      }
    }
    
    .header-actions {
      .create-btn {
        height: 48px;
        padding: 0 24px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        border: none;
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.6);
        }
      }
    }
  }
}

// 统计卡片区域
.stats-section {
  margin-bottom: 32px;

  .stats-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 24px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    }

    .stats-content {
      display: flex;
      align-items: center;
      position: relative;
      z-index: 2;

      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        color: white;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      }

      .stats-info {
        flex: 1;

        .stats-value {
          font-size: 28px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }
    }

    .stats-trend {
      position: absolute;
      top: 20px;
      right: 20px;
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: 600;
      z-index: 2;

      &.positive {
        color: #10b981;
      }

      .el-icon {
        margin-right: 2px;
      }
    }

    .stats-bg-icon {
      position: absolute;
      top: -10px;
      right: -10px;
      font-size: 120px;
      opacity: 0.05;
      z-index: 1;
    }

    &.primary .stats-icon {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    &.success .stats-icon {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    &.warning .stats-icon {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    &.danger .stats-icon {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
  }
}

// 筛选区域
.filter-section {
  margin-bottom: 24px;

  .filter-card {
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);

    .filter-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      .filter-left {
        display: flex;
        align-items: center;
        gap: 16px;
        flex: 1;

        .search-group {
          .search-input {
            width: 320px;

            :deep(.el-input__wrapper) {
              border-radius: 12px;
              border: 1px solid #e2e8f0;
              background: rgba(248, 250, 252, 0.8);
              transition: all 0.3s ease;

              &:hover {
                border-color: #3b82f6;
                background: rgba(255, 255, 255, 0.9);
              }

              &.is-focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }
        }

        .filter-group {
          display: flex;
          gap: 12px;

          .filter-select {
            width: 140px;

            :deep(.el-select__wrapper) {
              border-radius: 12px;
              border: 1px solid #e2e8f0;
              background: rgba(248, 250, 252, 0.8);
              transition: all 0.3s ease;

              &:hover {
                border-color: #3b82f6;
                background: rgba(255, 255, 255, 0.9);
              }

              &.is-focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }
        }
      }

      .filter-right {
        display: flex;
        gap: 12px;

        .action-btn {
          height: 40px;
          padding: 0 16px;
          border-radius: 10px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

// 表格区域
.table-section {
  .table-card {
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        h3 {
          margin: 0;
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
        }

        .total-count {
          color: #64748b;
          font-size: 14px;
          background: #f1f5f9;
          padding: 4px 12px;
          border-radius: 20px;
        }
      }

      .header-right {
        :deep(.el-button-group) {
          .el-button {
            border-radius: 8px;
            font-weight: 500;

            &:first-child {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }

            &:last-child {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
          }
        }
      }
    }

    // 表格视图样式
    .table-view {
      .modern-table {
        :deep(.el-table__header) {
          background: #f8fafc;

          th {
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #374151;
          }
        }

        :deep(.el-table__body) {
          tr {
            transition: all 0.3s ease;

            &:hover {
              background: #f8fafc;
            }

            td {
              border-bottom: 1px solid #f1f5f9;
              padding: 16px 12px;
            }
          }
        }

        .group-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .group-avatar {
            position: relative;

            .status-dot {
              position: absolute;
              bottom: -2px;
              right: -2px;
              width: 12px;
              height: 12px;
              border-radius: 50%;
              border: 2px solid white;

              &.active {
                background: #10b981;
              }

              &.paused {
                background: #f59e0b;
              }

              &.full {
                background: #6b7280;
              }

              &.pending {
                background: #ef4444;
              }
            }
          }

          .group-details {
            .group-name {
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 4px;
              font-size: 15px;
            }

            .group-desc {
              font-size: 13px;
              color: #64748b;
              margin-bottom: 6px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 180px;
            }

            .group-meta {
              display: flex;
              gap: 12px;

              .group-id {
                font-size: 12px;
                color: #9ca3af;
                background: #f3f4f6;
                padding: 2px 6px;
                border-radius: 4px;
              }

              .group-category {
                font-size: 12px;
                color: #3b82f6;
                background: rgba(59, 130, 246, 0.1);
                padding: 2px 6px;
                border-radius: 4px;
              }
            }
          }
        }

        .owner-info {
          .owner-name {
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 4px;
          }

          .owner-role {
            font-size: 12px;
          }
        }

        .price-info {
          .price-value {
            color: #ef4444;
            font-weight: 700;
            font-size: 16px;
          }
        }

        .member-stats {
          .member-count {
            font-size: 13px;
            color: #64748b;
            margin-bottom: 6px;
            text-align: center;

            .current {
              font-weight: 600;
              color: #1e293b;
            }

            .separator {
              margin: 0 4px;
              color: #9ca3af;
            }

            .max {
              color: #9ca3af;
            }
          }

          .member-progress {
            :deep(.el-progress-bar__outer) {
              border-radius: 3px;
            }

            :deep(.el-progress-bar__inner) {
              border-radius: 3px;
            }
          }
        }

        .health-score {
          text-align: center;

          .score-circle {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            margin: 0 auto 4px auto;
            color: white;

            &.excellent {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            }

            &.good {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            }

            &.fair {
              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            }

            &.poor {
              background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            }
          }

          .score-label {
            font-size: 12px;
            color: #64748b;
          }
        }

        .status-tag {
          font-weight: 500;
        }

        .time-info {
          .date {
            color: #64748b;
            font-size: 13px;
          }
        }

        .action-buttons {
          display: flex;
          gap: 6px;
          align-items: center;

          .action-btn-small {
            height: 32px;
            padding: 0 12px;
            font-size: 12px;
            border-radius: 6px;
            font-weight: 500;
          }

          .action-dropdown {
            :deep(.el-button) {
              height: 32px;
              padding: 0 12px;
              font-size: 12px;
              border-radius: 6px;
            }
          }
        }
      }
    }

    // 卡片视图样式
    .card-view {
      .group-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: 24px;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .group-avatar {
            position: relative;

            .status-indicator {
              position: absolute;
              bottom: -2px;
              right: -2px;
              width: 12px;
              height: 12px;
              border-radius: 50%;
              border: 2px solid white;

              &.active {
                background: #10b981;
              }

              &.paused {
                background: #f59e0b;
              }

              &.full {
                background: #6b7280;
              }

              &.pending {
                background: #ef4444;
              }
            }
          }

          .card-actions {
            .more-btn {
              color: #64748b;
              
              &:hover {
                color: #3b82f6;
              }
            }
          }
        }

        .card-content {
          .group-title {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 8px 0;
          }

          .group-description {
            color: #64748b;
            font-size: 14px;
            margin: 0 0 16px 0;
            line-height: 1.5;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .group-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .stat-item {
              text-align: center;

              .label {
                display: block;
                font-size: 12px;
                color: #64748b;
                margin-bottom: 4px;
              }

              .value {
                font-weight: 600;
                color: #1e293b;

                &.price {
                  color: #ef4444;
                }
              }
            }
          }

          .group-tags {
            display: flex;
            gap: 8px;
          }
        }
      }
    }

    // 分页样式
    .pagination-wrapper {
      padding: 24px 0;
      text-align: center;
      border-top: 1px solid #f1f5f9;

      .modern-pagination {
        :deep(.el-pagination) {
          .el-pager li {
            border-radius: 8px;
            margin: 0 2px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
            }

            &.is-active {
              background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
              color: white;
            }
          }

          .btn-prev,
          .btn-next {
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-section {
    .el-col {
      margin-bottom: 16px;
    }
  }

  .filter-section {
    .filter-content {
      flex-direction: column;
      gap: 16px;

      .filter-left,
      .filter-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .modern-group-list {
    padding: 16px;
  }

  .page-header {
    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }

  .stats-section {
    .el-col {
      span: 12;
    }
  }

  .table-section {
    .table-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
    }
  }

  .card-view {
    .el-col {
      span: 24;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card,
.filter-card,
.table-card,
.group-card {
  animation: fadeInUp 0.6s ease-out;
}

// 加载状态
.el-loading-mask {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}
</style>
