<?php

// 检查防红系统相关的数据库表
echo "=== 检查防红系统数据库表 ===\n";

// 需要检查的表
$tables = [
    'domain_pools',
    'short_links', 
    'link_access_logs',
    'domain_check_logs',
    'system_settings'
];

// 检查迁移文件是否存在
$migrationDir = 'database/migrations';
if (is_dir($migrationDir)) {
    echo "✓ 迁移目录存在: {$migrationDir}\n";
    
    $migrationFiles = glob($migrationDir . '/*.php');
    echo "找到 " . count($migrationFiles) . " 个迁移文件\n";
    
    foreach ($tables as $table) {
        $found = false;
        foreach ($migrationFiles as $file) {
            if (strpos($file, $table) !== false) {
                echo "✓ 找到表迁移: {$table} -> " . basename($file) . "\n";
                $found = true;
                break;
            }
        }
        if (!$found) {
            echo "✗ 缺少表迁移: {$table}\n";
        }
    }
} else {
    echo "✗ 迁移目录不存在: {$migrationDir}\n";
}

echo "\n测试完成！\n";