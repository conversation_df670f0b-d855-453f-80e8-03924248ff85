<template>
  <div class="substation-finance">
    <div class="page-header">
      <h2>分站财务管理</h2>
      <p>管理分站的财务数据、佣金结算和收入统计</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatCard
          title="总收入"
          :value="stats.overview?.total_revenue || 0"
          icon="Money"
          color="#67C23A"
          suffix="元"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="总佣金"
          :value="stats.overview?.total_commission || 0"
          icon="Coin"
          color="#E6A23C"
          suffix="元"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="净利润"
          :value="stats.overview?.net_profit || 0"
          icon="TrendCharts"
          color="#409EFF"
          suffix="元"
        />
      </el-col>
      <el-col :span="6">
        <StatCard
          title="活跃用户"
          :value="stats.overview?.active_users || 0"
          icon="User"
          color="#F56C6C"
          suffix="人"
        />
      </el-col>
    </el-row>

    <!-- 功能操作区 -->
    <div class="action-bar">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>时间筛选</span>
            </template>
            <el-radio-group v-model="selectedPeriod" @change="handlePeriodChange">
              <el-radio-button label="today">今日</el-radio-button>
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
              <el-radio-button label="quarter">本季度</el-radio-button>
              <el-radio-button label="year">本年</el-radio-button>
            </el-radio-group>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>快捷操作</span>
            </template>
            <el-button type="primary" @click="generateReport">
              <el-icon><Document /></el-icon>
              生成报表
            </el-button>
            <el-button type="success" @click="batchSettle">
              <el-icon><Money /></el-icon>
              批量结算
            </el-button>
            <el-button type="info" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>收入趋势</span>
          </template>
          <LineChart
            :data="revenueChartData"
            :options="chartOptions"
            height="300px"
          />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>佣金分布</span>
          </template>
          <DoughnutChart
            :data="commissionChartData"
            :options="doughnutOptions"
            height="300px"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 顶级代理商排行 -->
    <el-card class="top-agents-card">
      <template #header>
        <span>顶级代理商排行</span>
      </template>
      <el-table :data="topAgents" stripe>
        <el-table-column prop="agent.agent_name" label="代理商名称" />
        <el-table-column prop="agent.agent_code" label="代理商编码" />
        <el-table-column prop="total_commission" label="总佣金" sortable>
          <template #default="{ row }">
            <span class="amount">¥{{ row.total_commission }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_count" label="订单数量" sortable />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button size="small" @click="viewAgentDetail(row.agent.id)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 结算记录 -->
    <el-card class="settlement-records">
      <template #header>
        <span>结算记录</span>
      </template>
      <el-table :data="settlementRecords.data" stripe v-loading="loading">
        <el-table-column prop="agent.agent_name" label="代理商" />
        <el-table-column prop="order.order_no" label="订单号" />
        <el-table-column prop="commission_amount" label="佣金金额">
          <template #default="{ row }">
            <span class="amount">¥{{ row.commission_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="settled_at" label="结算时间">
          <template #default="{ row }">
            {{ formatDate(row.settled_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="settlementRecords.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 生成报表对话框 -->
    <el-dialog v-model="reportDialogVisible" title="生成财务报表" width="500px">
      <el-form :model="reportForm" label-width="100px">
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="reportForm.start_date"
            type="date"
            placeholder="选择开始日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="reportForm.end_date"
            type="date"
            placeholder="选择结束日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="报表格式">
          <el-select v-model="reportForm.format" style="width: 100%">
            <el-option label="在线查看" value="array" />
            <el-option label="Excel文件" value="excel" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="reportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGenerateReport" :loading="reportLoading">
          生成报表
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Money, Download, User, Coin, TrendCharts } from '@element-plus/icons-vue'
import StatCard from '@/components/StatCard.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import { 
  getSubstationFinanceStats, 
  getSubstationSettlementRecords, 
  generateSubstationFinanceReport 
} from '@/api/substation'

// 响应式数据
const loading = ref(false)
const selectedPeriod = ref('month')
const currentPage = ref(1)
const pageSize = ref(20)
const reportDialogVisible = ref(false)
const reportLoading = ref(false)

const stats = ref({})
const topAgents = ref([])
const settlementRecords = ref({ data: [], total: 0 })

const reportForm = reactive({
  start_date: '',
  end_date: '',
  format: 'array'
})

// 图表数据
const revenueChartData = computed(() => {
  const trends = stats.value.trends || []
  return {
    labels: trends.map(item => item.date),
    datasets: [
      {
        label: '收入',
        data: trends.map(item => item.revenue),
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4
      },
      {
        label: '利润',
        data: trends.map(item => item.profit),
        borderColor: '#67C23A',
        backgroundColor: 'rgba(103, 194, 58, 0.1)',
        tension: 0.4
      }
    ]
  }
})

const commissionChartData = computed(() => {
  const commission = stats.value.commission || {}
  const commissionByLevel = commission.commission_by_level || []
  
  return {
    labels: commissionByLevel.map(item => item.commission_level),
    datasets: [{
      data: commissionByLevel.map(item => item.total_commission),
      backgroundColor: [
        '#409EFF',
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399'
      ]
    }]
  }
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

// 方法
const loadFinanceStats = async () => {
  try {
    loading.value = true
    const response = await getSubstationFinanceStats({ period: selectedPeriod.value })
    stats.value = response.data
    topAgents.value = response.data.top_agents || []
  } catch (error) {
    ElMessage.error('加载财务统计失败')
  } finally {
    loading.value = false
  }
}

const loadSettlementRecords = async () => {
  try {
    const response = await getSubstationSettlementRecords({
      page: currentPage.value,
      limit: pageSize.value
    })
    settlementRecords.value = response.data
  } catch (error) {
    ElMessage.error('加载结算记录失败')
  }
}

const handlePeriodChange = () => {
  loadFinanceStats()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadSettlementRecords()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadSettlementRecords()
}

const generateReport = () => {
  reportDialogVisible.value = true
  // 设置默认日期为本月
  const now = new Date()
  reportForm.start_date = new Date(now.getFullYear(), now.getMonth(), 1)
  reportForm.end_date = new Date(now.getFullYear(), now.getMonth() + 1, 0)
}

const confirmGenerateReport = async () => {
  try {
    reportLoading.value = true
    const response = await generateSubstationFinanceReport({
      start_date: formatDate(reportForm.start_date),
      end_date: formatDate(reportForm.end_date),
      format: reportForm.format
    })
    
    if (reportForm.format === 'excel') {
      // 处理文件下载
      ElMessage.success('报表生成成功，正在下载...')
    } else {
      // 显示报表数据
      ElMessage.success('报表生成成功')
    }
    
    reportDialogVisible.value = false
  } catch (error) {
    ElMessage.error('生成报表失败')
  } finally {
    reportLoading.value = false
  }
}

const batchSettle = async () => {
  try {
    await ElMessageBox.confirm('确定要进行批量结算吗？', '确认操作', {
      type: 'warning'
    })
    
    // 这里应该先选择要结算的记录
    ElMessage.info('请先选择要结算的记录')
  } catch (error) {
    // 用户取消操作
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const viewAgentDetail = (agentId) => {
  // 跳转到代理商详情页
  ElMessage.info(`查看代理商 ${agentId} 详情`)
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  const types = {
    'pending': 'warning',
    'confirmed': 'success',
    'cancelled': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '待结算',
    'confirmed': '已结算',
    'cancelled': '已取消'
  }
  return texts[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadFinanceStats()
  loadSettlementRecords()
})
</script>

<style scoped>
.substation-finance {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.top-agents-card,
.settlement-records {
  margin-bottom: 20px;
}

.amount {
  color: #67C23A;
  font-weight: bold;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>