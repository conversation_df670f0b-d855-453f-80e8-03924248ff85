import{r as e,_ as s}from"./index-D2bI4m-v.js";/* empty css                    *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               *//* empty css                *//* empty css                    */import{T as t,b1 as a,as as r,a4 as l,aZ as u,U as o,b2 as n,b3 as c,b4 as m,b5 as i,b6 as d,b7 as p,b8 as h,b9 as v,Q as f}from"./element-plus-DcSKpKA8.js";import{r as y,c as _,k as g,l as b,t as w,B as $,E as k,y as R,z as x,D as j,u as E}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const T=new class{constructor(){this.results=[],this.errors=[]}async checkAllRoutes(){console.log("🔍 开始路由检测..."),this.results=[],this.errors=[];const e=this.getAllRoutes();for(const s of e)await this.checkRoute(s);return{results:this.results,errors:this.errors,summary:this.generateSummary()}}getAllRoutes(){const s=[],t=(e,a="")=>{e.forEach(e=>{if(e.path&&!e.meta?.hidden){const t=this.resolvePath(a,e.path);s.push({path:t,name:e.name,component:e.component,meta:e.meta,parent:a})}if(e.children){const s=this.resolvePath(a,e.path);t(e.children,s)}})};return t(e.options.routes),s}async checkRoute(e){const s={path:e.path,name:e.name,status:"unknown",error:null,loadTime:0,componentExists:!1,hasErrors:!1};try{const a=Date.now();if(e.component)if("function"==typeof e.component)try{await e.component();s.componentExists=!0,s.status="success"}catch(t){s.componentExists=!1,s.status="component_error",s.error=t.message,this.errors.push({path:e.path,type:"component_load_error",error:t.message})}else s.componentExists=!0,s.status="success";else s.status="no_component";s.loadTime=Date.now()-a}catch(t){s.status="error",s.error=t.message,this.errors.push({path:e.path,type:"route_error",error:t.message})}this.results.push(s),console.log(`✓ 检测路由: ${e.path} - ${s.status}`)}resolvePath(e,s){return s.startsWith("/")?s:e?`${e}/${s}`.replace(/\/+/g,"/"):`/${s}`}generateSummary(){const e=this.results.length,s=this.results.filter(e=>"success"===e.status).length;return{total:e,success:s,errors:this.results.filter(e=>"success"!==e.status).length,successRate:e>0?(s/e*100).toFixed(2):0,avgLoadTime:this.results.reduce((e,s)=>e+s.loadTime,0)/e}}generateReport(){const e=this.generateSummary();let s="# 路由检测报告\n\n";s+="## 📊 检测摘要\n\n",s+=`- **总路由数**: ${e.total}\n`,s+=`- **成功加载**: ${e.success}\n`,s+=`- **加载失败**: ${e.errors}\n`,s+=`- **成功率**: ${e.successRate}%\n`,s+=`- **平均加载时间**: ${e.avgLoadTime.toFixed(2)}ms\n\n`;const t=this.results.filter(e=>"success"===e.status);t.length>0&&(s+=`## ✅ 成功加载的路由 (${t.length})\n\n`,t.forEach(e=>{s+=`- \`${e.path}\` - ${e.name||"未命名"}\n`}),s+="\n");const a=this.results.filter(e=>"success"!==e.status);return a.length>0&&(s+=`## ❌ 加载失败的路由 (${a.length})\n\n`,a.forEach(e=>{s+=`- \`${e.path}\` - ${e.status}\n`,e.error&&(s+=`  - 错误: ${e.error}\n`)}),s+="\n"),this.errors.length>0&&(s+="## 🔍 错误详情\n\n",this.errors.forEach(e=>{s+=`### ${e.path}\n`,s+=`- **类型**: ${e.type}\n`,s+=`- **错误**: ${e.error}\n\n`})),s}},L={class:"route-checker"},U={class:"checker-controls"},z={key:0,class:"progress-section"},S={class:"progress-text"},A={key:1,class:"results-summary"},D={class:"summary-item"},I={class:"summary-number"},C={class:"summary-item"},O={class:"summary-number"},P={class:"summary-item"},V={class:"summary-number"},B={class:"summary-item"},F={class:"summary-number"},q={key:2,class:"results-detail"},M={key:0,class:"error-text"},Q={key:1,class:"success-text"},W={class:"error-text"},Z=s({__name:"RouteChecker",setup(e){const s=y(!1),Z=y(0),G=y(""),H=y(null),J=y("all"),K=_(()=>H.value?H.value.results.filter(e=>"success"!==e.status):[]),N=async()=>{s.value=!0,Z.value=0,G.value="";try{const e=setInterval(()=>{Z.value<90&&(Z.value+=10*Math.random())},200),s=await T.checkAllRoutes();clearInterval(e),Z.value=100,H.value=s,f.success(`检测完成！成功: ${s.summary.success}, 失败: ${s.summary.errors}`)}catch(e){f.error("检测过程中发生错误: "+e.message)}finally{s.value=!1}},X=()=>{if(!H.value)return;const e=T.generateReport(),s=new Blob([e],{type:"text/markdown"}),t=URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download=`route-check-report-${(new Date).toISOString().slice(0,10)}.md`,a.click(),URL.revokeObjectURL(t),f.success("报告已下载")},Y=e=>{switch(e){case"success":return"success";case"component_error":case"error":return"danger";case"no_component":return"warning";default:return"info"}},ee=e=>{switch(e){case"success":return"成功";case"component_error":return"组件错误";case"no_component":return"无组件";case"error":return"错误";default:return"未知"}};return(e,f)=>{const y=t,_=r,T=u,se=c,te=n,ae=m,re=p,le=h,ue=d,oe=i,ne=v;return b(),g("div",L,[f[7]||(f[7]=w("div",{class:"page-header"},[w("h1",null,"🔍 路由检测工具"),w("p",null,"自动化检测所有路由的可访问性和组件加载状态")],-1)),w("div",U,[k(_,{type:"primary",onClick:N,loading:s.value,size:"large"},{default:x(()=>[k(y,null,{default:x(()=>[k(E(a))]),_:1}),f[1]||(f[1]=j(" 开始检测 ",-1))]),_:1,__:[1]},8,["loading"]),H.value?(b(),R(_,{key:0,onClick:X,size:"large"},{default:x(()=>[k(y,null,{default:x(()=>[k(E(l))]),_:1}),f[2]||(f[2]=j(" 下载报告 ",-1))]),_:1,__:[2]})):$("",!0)]),s.value?(b(),g("div",z,[k(T,{percentage:Z.value,status:100===Z.value?"success":""},null,8,["percentage","status"]),w("p",S,"正在检测路由... "+o(G.value),1)])):$("",!0),H.value&&!s.value?(b(),g("div",A,[k(ae,{gutter:20},{default:x(()=>[k(te,{span:6},{default:x(()=>[k(se,{class:"summary-card"},{default:x(()=>[w("div",D,[w("div",I,o(H.value.summary.total),1),f[3]||(f[3]=w("div",{class:"summary-label"},"总路由数",-1))])]),_:1})]),_:1}),k(te,{span:6},{default:x(()=>[k(se,{class:"summary-card success"},{default:x(()=>[w("div",C,[w("div",O,o(H.value.summary.success),1),f[4]||(f[4]=w("div",{class:"summary-label"},"成功加载",-1))])]),_:1})]),_:1}),k(te,{span:6},{default:x(()=>[k(se,{class:"summary-card error"},{default:x(()=>[w("div",P,[w("div",V,o(H.value.summary.errors),1),f[5]||(f[5]=w("div",{class:"summary-label"},"加载失败",-1))])]),_:1})]),_:1}),k(te,{span:6},{default:x(()=>[k(se,{class:"summary-card"},{default:x(()=>[w("div",B,[w("div",F,o(H.value.summary.successRate)+"%",1),f[6]||(f[6]=w("div",{class:"summary-label"},"成功率",-1))])]),_:1})]),_:1})]),_:1})])):$("",!0),H.value&&!s.value?(b(),g("div",q,[k(ne,{modelValue:J.value,"onUpdate:modelValue":f[0]||(f[0]=e=>J.value=e)},{default:x(()=>[k(oe,{label:"所有路由",name:"all"},{default:x(()=>[k(ue,{data:H.value.results,style:{width:"100%"}},{default:x(()=>[k(re,{prop:"path",label:"路由路径",width:"300"}),k(re,{prop:"name",label:"路由名称",width:"200"}),k(re,{label:"状态",width:"120"},{default:x(e=>[k(le,{type:Y(e.row.status),size:"small"},{default:x(()=>[j(o(ee(e.row.status)),1)]),_:2},1032,["type"])]),_:1}),k(re,{prop:"loadTime",label:"加载时间(ms)",width:"120"}),k(re,{label:"错误信息"},{default:x(e=>[e.row.error?(b(),g("span",M,o(e.row.error),1)):(b(),g("span",Q,"无"))]),_:1})]),_:1},8,["data"])]),_:1}),k(oe,{label:"失败路由",name:"failed"},{default:x(()=>[k(ue,{data:K.value,style:{width:"100%"}},{default:x(()=>[k(re,{prop:"path",label:"路由路径",width:"300"}),k(re,{prop:"name",label:"路由名称",width:"200"}),k(re,{label:"状态",width:"120"},{default:x(e=>[k(le,{type:"danger",size:"small"},{default:x(()=>[j(o(ee(e.row.status)),1)]),_:2},1024)]),_:1}),k(re,{label:"错误信息"},{default:x(e=>[w("span",W,o(e.row.error),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])])):$("",!0)])}}},[["__scopeId","data-v-d24fb311"]]);export{Z as default};
