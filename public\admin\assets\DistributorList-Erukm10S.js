import{d as e,_ as l}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                        *//* empty css                    *//* empty css                  *//* empty css               *//* empty css               *//* empty css                *//* empty css                     *//* empty css                  */import{r as a,L as t,$ as i,e as o,k as s,l as n,E as u,A as d,z as r,J as c,u as p,a2 as m,F as _,Y as v,y as b,D as f,t as g,G as y,B as w}from"./vue-vendor-DGsK9sC4.js";import{P as h}from"./index-Do9uvhBr.js";import{Q as k,bc as V,bd as C,aH as j,aW as U,aV as $,as as x,b3 as q,b2 as P,b4 as D,bj as F,U as T,b6 as z,b7 as I,b8 as B,bf as L,au as R,R as S}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";/* empty css                      */const A={class:"app-container"},E={class:"stat-content"},G={class:"stat-number"},H={class:"stat-content"},J={class:"stat-number"},K={class:"stat-content"},N={class:"stat-number"},Q={class:"stat-content"},W={class:"stat-number"},Y={class:"dialog-footer"},M=l({__name:"DistributorList",setup(l){const M=a(!0),O=a(!0),X=a([]),Z=a(!0),ee=a(!0),le=a(0),ae=a([]),te=a([]),ie=a(null),oe={1:"初级分销员",2:"中级分销员",3:"高级分销员",4:"金牌分销员"},se=t({stats:{},queryParams:{page:1,limit:10,keyword:void 0,distribution_group_id:void 0,level:void 0},dialog:{visible:!1,title:"",type:"add"},form:{},rules:{name:[{required:!0,message:"用户名不能为空",trigger:"blur"}],email:[{required:!0,message:"邮箱不能为空",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}],distribution_group_id:[{required:!0,message:"必须选择一个分销组",trigger:"change"}],level:[{required:!0,message:"必须选择一个等级",trigger:"change"}]}}),{stats:ne,queryParams:ue,dialog:de,form:re,rules:ce}=i(se),pe=e=>e?parseFloat(e).toFixed(2):"0.00",me=e=>({1:"info",2:"success",3:"warning",4:"danger"}[e]||"info");async function _e(){M.value=!0;try{console.log("加载分销员列表..."),await new Promise(e=>setTimeout(e,500)),ae.value=[{id:1,name:"张三",email:"<EMAIL>",level:1,status:"active",distribution_group:{name:"华东区"},children_count:15,total_commission:8650.5,created_at:"2024-01-15 10:30:00"},{id:2,name:"李四",email:"<EMAIL>",level:2,status:"active",distribution_group:{name:"华南区"},children_count:23,total_commission:12450.8,created_at:"2024-01-10 14:20:00"},{id:3,name:"王五",email:"<EMAIL>",level:3,status:"inactive",distribution_group:null,children_count:8,total_commission:5230.2,created_at:"2024-01-08 09:15:00"}],le.value=3,console.log("分销员列表加载完成")}catch(e){console.error("加载分销员列表失败:",e),k.error("加载分销员列表失败")}finally{M.value=!1}}async function ve(){O.value=!0;try{console.log("加载统计数据..."),await new Promise(e=>setTimeout(e,300)),ne.value={total_distributors:156,new_distributors_today:8,total_commission:285650.5,total_children:89},console.log("统计数据加载完成")}catch(e){console.error("加载统计数据失败:",e),k.error("加载统计数据失败")}finally{O.value=!1}}function be(){ue.value.page=1,_e()}function fe(){ue.value={page:1,limit:10,keyword:void 0,distribution_group_id:void 0,level:void 0},be()}function ge(e){X.value=e,Z.value=1!==e.length,ee.value=!e.length}function ye(){re.value={name:"",email:"",password:"",status:"active",level:1,distribution_group_id:null},ie.value&&ie.value.resetFields()}function we(){ye(),de.value={visible:!0,title:"新增分销员",type:"add"}}function he(e){const l=e||X.value[0];re.value={id:l.id,name:l.name,distribution_group_id:l.distribution_group_id},de.value={visible:!0,title:`分配分组 - ${l.name}`,type:"assign_group"}}function ke(e){const l=e||X.value[0];re.value={id:l.id,name:l.name,level:l.level},de.value={visible:!0,title:`等级变更 - ${l.name}`,type:"upgrade"}}async function Ve(l){const a=l?[l.id]:X.value.map(e=>e.id);var t;await S.confirm(`是否确认删除ID为"${a.join(",")}"的分销员?`,"警告",{type:"warning"}),await(t={ids:a},e.delete(`/admin/distributors/${t}`)),k.success("删除成功"),_e(),ve()}async function Ce(l){const a="active"===l.status?"启用":"禁用";try{await S.confirm(`确认要"${a}"分销员"${l.name}"吗?`,"警告",{type:"warning"}),await((l,a)=>e.put(`/admin/distributors/${l}/status`,a))(l.id,{status:l.status}),k.success(a+"成功")}catch{l.status="active"===l.status?"inactive":"active"}}function je(){de.value.visible=!1,ye()}async function Ue(){await ie.value.validate();try{switch(de.value.type){case"add":await(l=>e.post("/admin/distributors",l))(re.value),k.success("新增成功");break;case"edit":await((l,a)=>e.put(`/admin/distributors/${l}`,a))(re.value.id,re.value),k.success("修改成功");break;case"assign_group":await((l,a)=>e.put(`/admin/distributors/${l}/group`,a))(re.value.id,{distribution_group_id:re.value.distribution_group_id}),k.success("分配成功");break;case"upgrade":await((l,a)=>e.put(`/admin/distributors/${l}/level`,a))(re.value.id,{level:re.value.level}),k.success("等级变更成功")}de.value.visible=!1,_e(),ve()}catch(l){}}return o(()=>{_e(),ve(),async function(){try{console.log("加载分销组选项..."),await new Promise(e=>setTimeout(e,200)),te.value=[{id:1,name:"华东区"},{id:2,name:"华南区"},{id:3,name:"华北区"},{id:4,name:"西南区"}],console.log("分销组选项加载完成")}catch(e){console.error("加载分销组选项失败:",e),k.error("加载分销组选项失败")}}()}),(e,l)=>{const a=j,t=C,i=$,o=U,k=x,S=V,ee=q,se=P,ve=D,$e=I,xe=B,qe=L,Pe=z,De=R,Fe=F;return n(),s("div",A,[u(ee,{class:"filter-card"},{default:r(()=>[u(S,{inline:!0,model:p(ue),onSubmit:c(be,["prevent"])},{default:r(()=>[u(t,{label:"名称/邮箱"},{default:r(()=>[u(a,{modelValue:p(ue).keyword,"onUpdate:modelValue":l[0]||(l[0]=e=>p(ue).keyword=e),placeholder:"分销员名称或邮箱",clearable:"",onKeyup:m(be,["enter"])},null,8,["modelValue"])]),_:1}),u(t,{label:"分销组"},{default:r(()=>[u(o,{modelValue:p(ue).distribution_group_id,"onUpdate:modelValue":l[1]||(l[1]=e=>p(ue).distribution_group_id=e),placeholder:"全部分销组",clearable:""},{default:r(()=>[(n(!0),s(_,null,v(te.value,e=>(n(),b(i,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(t,{label:"分销等级"},{default:r(()=>[u(o,{modelValue:p(ue).level,"onUpdate:modelValue":l[2]||(l[2]=e=>p(ue).level=e),placeholder:"全部分销等级",clearable:""},{default:r(()=>[(n(),s(_,null,v(oe,(e,l)=>u(i,{key:l,label:e,value:l},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),u(t,null,{default:r(()=>[u(k,{type:"primary",icon:"el-icon-search",onClick:be},{default:r(()=>l[14]||(l[14]=[f("查询",-1)])),_:1,__:[14]}),u(k,{icon:"el-icon-refresh",onClick:fe},{default:r(()=>l[15]||(l[15]=[f("重置",-1)])),_:1,__:[15]})]),_:1})]),_:1},8,["model"])]),_:1}),u(ve,{gutter:10,class:"mb8"},{default:r(()=>[u(se,{span:1.5},{default:r(()=>[u(k,{type:"primary",plain:"",icon:"el-icon-plus",onClick:we},{default:r(()=>l[16]||(l[16]=[f("新增",-1)])),_:1,__:[16]})]),_:1}),u(se,{span:1.5},{default:r(()=>[u(k,{type:"info",plain:"",icon:"el-icon-s-operation",disabled:Z.value,onClick:l[3]||(l[3]=e=>he(X.value[0]))},{default:r(()=>l[17]||(l[17]=[f("分配分组",-1)])),_:1,__:[17]},8,["disabled"])]),_:1}),u(se,{span:1.5},{default:r(()=>[u(k,{type:"warning",plain:"",icon:"el-icon-top",disabled:Z.value,onClick:l[4]||(l[4]=e=>ke(X.value[0]))},{default:r(()=>l[18]||(l[18]=[f("等级变更",-1)])),_:1,__:[18]},8,["disabled"])]),_:1})]),_:1}),d((n(),b(ve,{gutter:20,class:"stats-container"},{default:r(()=>[u(se,{span:6},{default:r(()=>[u(ee,{shadow:"hover"},{default:r(()=>[g("div",E,[l[19]||(l[19]=g("div",{class:"stat-label"},"总分销员",-1)),g("div",G,T(p(ne).total_distributors),1)])]),_:1})]),_:1}),u(se,{span:6},{default:r(()=>[u(ee,{shadow:"hover"},{default:r(()=>[g("div",H,[l[20]||(l[20]=g("div",{class:"stat-label"},"今日新增",-1)),g("div",J,T(p(ne).new_distributors_today),1)])]),_:1})]),_:1}),u(se,{span:6},{default:r(()=>[u(ee,{shadow:"hover"},{default:r(()=>[g("div",K,[l[21]||(l[21]=g("div",{class:"stat-label"},"累计佣金",-1)),g("div",N,"¥"+T(pe(p(ne).total_commission)),1)])]),_:1})]),_:1}),u(se,{span:6},{default:r(()=>[u(ee,{shadow:"hover"},{default:r(()=>[g("div",Q,[l[22]||(l[22]=g("div",{class:"stat-label"},"总下级数",-1)),g("div",W,T(p(ne).total_children),1)])]),_:1})]),_:1})]),_:1})),[[Fe,O.value]]),u(ee,null,{default:r(()=>[d((n(),b(Pe,{data:ae.value,onSelectionChange:ge},{default:r(()=>[u($e,{type:"selection",width:"55",align:"center"}),u($e,{label:"分销员信息",width:"220"},{default:r(({row:e})=>[g("div",null,T(e.name)+" (ID: "+T(e.id)+")",1),g("div",null,T(e.email),1)]),_:1}),u($e,{label:"分销等级",align:"center"},{default:r(({row:e})=>[u(xe,{type:me(e.level)},{default:r(()=>[f(T(oe[e.level]||"未知"),1)]),_:2},1032,["type"])]),_:1}),u($e,{label:"分销组",align:"center"},{default:r(({row:e})=>[g("span",null,T(e.distribution_group?e.distribution_group.name:"未分配"),1)]),_:1}),u($e,{label:"团队",align:"center"},{default:r(({row:e})=>[g("div",null,"下级数: "+T(e.children_count||0),1)]),_:1}),u($e,{label:"业绩",align:"center"},{default:r(({row:e})=>[g("div",null,"累计佣金: ¥"+T(pe(e.total_commission)),1)]),_:1}),u($e,{label:"状态",align:"center"},{default:r(({row:e})=>[u(qe,{modelValue:e.status,"onUpdate:modelValue":l=>e.status=l,"active-value":"active","inactive-value":"inactive",onChange:l=>Ce(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),u($e,{label:"注册时间",prop:"created_at",width:"160"}),u($e,{label:"操作",width:"220",fixed:"right"},{default:r(({row:e})=>[u(k,{link:"",type:"primary",icon:"el-icon-edit",onClick:l=>function(e){ye();const l=e||X.value[0];re.value={...l,password:""},de.value={visible:!0,title:`编辑分销员 - ${l.name}`,type:"edit"}}(e)},{default:r(()=>l[23]||(l[23]=[f("编辑",-1)])),_:2,__:[23]},1032,["onClick"]),u(k,{link:"",type:"info",icon:"el-icon-s-operation",onClick:l=>he(e)},{default:r(()=>l[24]||(l[24]=[f("分配",-1)])),_:2,__:[24]},1032,["onClick"]),u(k,{link:"",type:"warning",icon:"el-icon-top",onClick:l=>ke(e)},{default:r(()=>l[25]||(l[25]=[f("等级",-1)])),_:2,__:[25]},1032,["onClick"]),u(k,{link:"",type:"danger",icon:"el-icon-delete",onClick:l=>Ve(e)},{default:r(()=>l[26]||(l[26]=[f("删除",-1)])),_:2,__:[26]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Fe,M.value]]),d(u(h,{total:le.value,page:p(ue).page,"onUpdate:page":l[5]||(l[5]=e=>p(ue).page=e),limit:p(ue).limit,"onUpdate:limit":l[6]||(l[6]=e=>p(ue).limit=e),onPagination:_e},null,8,["total","page","limit"]),[[y,le.value>0]])]),_:1}),u(De,{title:p(de).title,modelValue:p(de).visible,"onUpdate:modelValue":l[13]||(l[13]=e=>p(de).visible=e),width:"600px","append-to-body":""},{footer:r(()=>[g("div",Y,[u(k,{onClick:je},{default:r(()=>l[27]||(l[27]=[f("取 消",-1)])),_:1,__:[27]}),u(k,{type:"primary",onClick:Ue},{default:r(()=>l[28]||(l[28]=[f("确 定",-1)])),_:1,__:[28]})])]),default:r(()=>[u(S,{ref_key:"formRef",ref:ie,model:p(re),rules:p(ce),"label-width":"100px"},{default:r(()=>["add"===p(de).type||"edit"===p(de).type?(n(),s(_,{key:0},[u(t,{label:"用户名",prop:"name"},{default:r(()=>[u(a,{modelValue:p(re).name,"onUpdate:modelValue":l[7]||(l[7]=e=>p(re).name=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),u(t,{label:"邮箱",prop:"email"},{default:r(()=>[u(a,{modelValue:p(re).email,"onUpdate:modelValue":l[8]||(l[8]=e=>p(re).email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),"add"===p(de).type?(n(),b(t,{key:0,label:"密码",prop:"password"},{default:r(()=>[u(a,{modelValue:p(re).password,"onUpdate:modelValue":l[9]||(l[9]=e=>p(re).password=e),type:"password",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1})):w("",!0),"edit"===p(de).type?(n(),b(t,{key:1,label:"重置密码"},{default:r(()=>[u(a,{modelValue:p(re).password,"onUpdate:modelValue":l[10]||(l[10]=e=>p(re).password=e),type:"password",placeholder:"留空则不修改密码"},null,8,["modelValue"])]),_:1})):w("",!0)],64)):w("",!0),"assign_group"===p(de).type?(n(),s(_,{key:1},[u(t,{label:"分销商"},{default:r(()=>[g("span",null,T(p(re).name),1)]),_:1}),u(t,{label:"分销组",prop:"distribution_group_id"},{default:r(()=>[u(o,{modelValue:p(re).distribution_group_id,"onUpdate:modelValue":l[11]||(l[11]=e=>p(re).distribution_group_id=e),placeholder:"请选择分销组",style:{width:"100%"}},{default:r(()=>[(n(!0),s(_,null,v(te.value,e=>(n(),b(i,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})],64)):w("",!0),"upgrade"===p(de).type?(n(),s(_,{key:2},[u(t,{label:"分销商"},{default:r(()=>[g("span",null,T(p(re).name),1)]),_:1}),u(t,{label:"当前等级"},{default:r(()=>[u(xe,{type:me(p(re).level)},{default:r(()=>[f(T(oe[p(re).level]||"未知"),1)]),_:1},8,["type"])]),_:1}),u(t,{label:"新等级",prop:"level"},{default:r(()=>[u(o,{modelValue:p(re).level,"onUpdate:modelValue":l[12]||(l[12]=e=>p(re).level=e),placeholder:"请选择新等级",style:{width:"100%"}},{default:r(()=>[(n(),s(_,null,v(oe,(e,l)=>u(i,{key:l,label:e,value:Number(l)},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})],64)):w("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}},[["__scopeId","data-v-6c76374e"]]);export{M as default};
