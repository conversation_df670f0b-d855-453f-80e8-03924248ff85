<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台预览测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        
        .title {
            font-size: 2.5rem;
            color: #1f2937;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .status {
            display: inline-flex;
            align-items: center;
            background: #dcfce7;
            color: #166534;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            background: #22c55e;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-link {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            text-decoration: none;
            padding: 20px;
            border-radius: 16px;
            transition: all 0.3s ease;
            display: block;
        }
        
        .test-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }
        
        .test-link h3 {
            font-size: 1.3rem;
            margin-bottom: 8px;
        }
        
        .test-link p {
            opacity: 0.9;
            font-size: 0.95rem;
        }
        
        .info-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            color: #92400e;
            text-align: left;
        }
        
        .info-box h4 {
            margin-bottom: 10px;
            color: #92400e;
        }
        
        .debug-info {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 0.9rem;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="title">🚀 管理后台预览测试</h1>
        
        <div class="status">
            <div class="status-dot"></div>
            预览服务器运行中
        </div>
        
        <div class="test-links">
            <a href="http://localhost:4173/admin/?preview=true" class="test-link" target="_blank">
                <h3>🎭 预览模式入口</h3>
                <p>带预览参数的管理后台</p>
            </a>
            
            <a href="http://localhost:4173/admin/" class="test-link" target="_blank">
                <h3>🔐 正常模式入口</h3>
                <p>需要登录的管理后台</p>
            </a>
            
            <a href="http://localhost:4173/admin/#/dashboard" class="test-link" target="_blank">
                <h3>📊 直接访问仪表板</h3>
                <p>跳过首页直接到仪表板</p>
            </a>
            
            <a href="http://localhost:4173/admin/#/community/groups" class="test-link" target="_blank">
                <h3>👥 社群管理页面</h3>
                <p>测试社群管理功能</p>
            </a>
        </div>
        
        <div class="info-box">
            <h4>💡 测试说明</h4>
            <p>
                如果页面显示空白或出现错误，请检查浏览器控制台的错误信息。
                预览模式应该能够无需登录直接访问所有功能。
            </p>
        </div>
        
        <div class="debug-info">
            <strong>调试信息:</strong><br>
            当前时间: <span id="current-time"></span><br>
            预览模式状态: <span id="preview-status"></span><br>
            本地存储: <span id="local-storage"></span>
        </div>
    </div>

    <script>
        // 更新调试信息
        function updateDebugInfo() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
            document.getElementById('preview-status').textContent = localStorage.getItem('preview-mode') || '未设置';
            document.getElementById('local-storage').textContent = Object.keys(localStorage).length + ' 项';
        }
        
        // 初始化
        updateDebugInfo();
        setInterval(updateDebugInfo, 1000);
        
        // 设置预览模式
        localStorage.setItem('preview-mode', 'true');
        
        // 测试链接点击事件
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function(e) {
                const url = this.href;
                console.log('正在测试链接:', url);
                
                // 如果是预览模式链接，确保设置了预览标识
                if (url.includes('preview=true')) {
                    localStorage.setItem('preview-mode', 'true');
                }
            });
        });
        
        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch('http://localhost:4173/admin/');
                if (response.ok) {
                    console.log('✅ 预览服务器运行正常');
                } else {
                    console.warn('⚠️ 预览服务器响应异常:', response.status);
                }
            } catch (error) {
                console.error('❌ 无法连接到预览服务器:', error);
            }
        }
        
        checkServerStatus();
    </script>
</body>
</html>
