<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨鑫流量变现 - 登录页面美化效果展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
        }

        .header p {
            font-size: 1.25rem;
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto;
        }

        .showcase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .showcase-item {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .showcase-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .showcase-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .showcase-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 24px;
            color: white;
        }

        .admin-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        }

        .user-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .distributor-icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .showcase-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .showcase-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 24px;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 0.875rem;
            color: #4b5563;
        }

        .feature-list li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(16, 185, 129, 0.1);
            border-radius: 50%;
            font-size: 12px;
        }

        .preview-button {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .preview-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .preview-button svg {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }

        .improvements-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 40px;
        }

        .improvements-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 24px;
            text-align: center;
        }

        .improvements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .improvement-item {
            padding: 24px;
            background: #f8fafc;
            border-radius: 16px;
            border: 1px solid #e5e7eb;
        }

        .improvement-item h4 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .improvement-item h4::before {
            content: '';
            width: 8px;
            height: 8px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 50%;
            margin-right: 12px;
        }

        .improvement-item p {
            color: #6b7280;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .tech-stack {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
        }

        .tech-stack h3 {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .tech-stack p {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 32px;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
        }

        .tech-tag {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .footer {
            text-align: center;
            padding: 40px 0;
            color: #6b7280;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .showcase-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .improvements-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .container {
                padding: 20px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>晨鑫流量变现</h1>
            <p>登录页面美化效果展示 - 现代化设计与用户体验优化</p>
        </div>

        <!-- 登录页面展示 -->
        <div class="showcase-grid">
            <!-- 管理员登录 -->
            <div class="showcase-item">
                <div class="showcase-header">
                    <div class="showcase-icon admin-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H5V21H19V9Z"/>
                        </svg>
                    </div>
                    <div>
                        <div class="showcase-title">管理员登录</div>
                        <div class="showcase-subtitle">Vue 3 + Element Plus</div>
                    </div>
                </div>
                
                <ul class="feature-list">
                    <li>动态渐变背景与浮动装饰</li>
                    <li>现代化表单设计</li>
                    <li>实时状态指示器</li>
                    <li>功能特色展示区域</li>
                    <li>响应式设计适配</li>
                    <li>光效与粒子动画</li>
                </ul>
                
                <button class="preview-button" onclick="openPreview('admin')">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                    </svg>
                    预览效果
                </button>
            </div>

            <!-- 用户登录 -->
            <div class="showcase-item">
                <div class="showcase-header">
                    <div class="showcase-icon user-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>
                    <div>
                        <div class="showcase-title">用户登录</div>
                        <div class="showcase-subtitle">Nuxt 3 + Tailwind CSS</div>
                    </div>
                </div>
                
                <ul class="feature-list">
                    <li>毛玻璃效果登录卡片</li>
                    <li>流畅的交互动画</li>
                    <li>社交登录集成</li>
                    <li>表单验证与错误处理</li>
                    <li>移动端优化</li>
                    <li>现代化图标设计</li>
                </ul>
                
                <button class="preview-button" onclick="openPreview('user')">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                    </svg>
                    预览效果
                </button>
            </div>

            <!-- 分销员登录 -->
            <div class="showcase-item">
                <div class="showcase-header">
                    <div class="showcase-icon distributor-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
                            <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2 1l-3 4v2h2l2.54-3.4L16.5 18H20zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm2 16v-7H9V9c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v6h1.5v7h4z"/>
                        </svg>
                    </div>
                    <div>
                        <div class="showcase-title">分销员登录</div>
                        <div class="showcase-subtitle">Laravel Blade + 原生JS</div>
                    </div>
                </div>
                
                <ul class="feature-list">
                    <li>统一的视觉设计语言</li>
                    <li>增强的错误提示</li>
                    <li>加载状态优化</li>
                    <li>表单验证改进</li>
                    <li>背景装饰效果</li>
                    <li>用户体验提升</li>
                </ul>
                
                <button class="preview-button" onclick="openPreview('distributor')">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                    </svg>
                    预览效果
                </button>
            </div>
        </div>

        <!-- 改进说明 -->
        <div class="improvements-section">
            <h3 class="improvements-title">美化改进要点</h3>
            <div class="improvements-grid">
                <div class="improvement-item">
                    <h4>视觉设计</h4>
                    <p>采用现代化的渐变背景、毛玻璃效果和动态装饰元素，提升视觉层次感和品牌形象。</p>
                </div>
                
                <div class="improvement-item">
                    <h4>交互体验</h4>
                    <p>优化表单交互、添加悬停效果、改进加载状态，让用户操作更加流畅自然。</p>
                </div>
                
                <div class="improvement-item">
                    <h4>响应式设计</h4>
                    <p>确保在各种设备上都有良好的显示效果，适配移动端和桌面端的不同需求。</p>
                </div>
                
                <div class="improvement-item">
                    <h4>品牌统一</h4>
                    <p>统一使用晨鑫流量变现品牌标识，保持三个登录页面的设计语言一致性。</p>
                </div>
                
                <div class="improvement-item">
                    <h4>动画效果</h4>
                    <p>添加浮动装饰、光效、粒子等动画效果，增强页面的动态感和现代感。</p>
                </div>
                
                <div class="improvement-item">
                    <h4>用户反馈</h4>
                    <p>改进错误提示、加载状态和成功反馈，让用户清楚了解操作状态。</p>
                </div>
            </div>
        </div>

        <!-- 技术栈 -->
        <div class="tech-stack">
            <h3>技术栈</h3>
            <p>使用现代化的前端技术栈，确保最佳的性能和用户体验</p>
            <div class="tech-tags">
                <span class="tech-tag">Vue 3</span>
                <span class="tech-tag">Nuxt 3</span>
                <span class="tech-tag">Element Plus</span>
                <span class="tech-tag">Tailwind CSS</span>
                <span class="tech-tag">Laravel Blade</span>
                <span class="tech-tag">CSS3 动画</span>
                <span class="tech-tag">响应式设计</span>
                <span class="tech-tag">现代化 UI</span>
            </div>
        </div>

        <!-- 底部 -->
        <div class="footer">
            <p>© 2024 晨鑫流量变现. 智能社群营销与多级分销平台</p>
        </div>
    </div>

    <script>
        function openPreview(type) {
            let url = '';
            switch(type) {
                case 'admin':
                    url = '/admin/#/login';
                    break;
                case 'user':
                    url = '/login';
                    break;
                case 'distributor':
                    url = '/distributor/login';
                    break;
            }
            
            if (url) {
                // 在新窗口中打开预览
                window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            } else {
                alert('预览功能需要在实际项目环境中运行');
            }
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.showcase-item, .improvement-item');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });

            items.forEach(item => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'all 0.6s ease';
                observer.observe(item);
            });
        });
    </script>
</body>
</html>