const fs = require('fs');
const path = require('path');

function analyzeScssBrackets(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let bracketStack = [];
    let errors = [];
    let openBrackets = 0;
    let closeBrackets = 0;
    
    console.log('🔍 SCSS括号匹配分析');
    console.log('='.repeat(50));
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNum = i + 1;
        
        // 跳过注释行
        if (line.trim().startsWith('//') || line.trim().startsWith('/*')) {
            continue;
        }
        
        // 计算括号
        const openCount = (line.match(/\{/g) || []).length;
        const closeCount = (line.match(/\}/g) || []).length;
        
        openBrackets += openCount;
        closeBrackets += closeCount;
        
        // 记录每个开括号
        for (let j = 0; j < openCount; j++) {
            bracketStack.push({
                line: lineNum,
                content: line.trim(),
                type: 'open'
            });
        }
        
        // 处理每个闭括号
        for (let j = 0; j < closeCount; j++) {
            if (bracketStack.length === 0) {
                errors.push({
                    line: lineNum,
                    type: 'extra_close',
                    content: line.trim(),
                    message: `多余的闭括号 '}' 在第${lineNum}行`
                });
            } else {
                bracketStack.pop();
            }
        }
        
        // 显示有括号的行
        if (openCount > 0 || closeCount > 0) {
            const balance = openBrackets - closeBrackets;
            console.log(`${lineNum.toString().padStart(4)}: ${balance.toString().padStart(3)} | ${line.trim()}`);
        }
    }
    
    console.log('\n📊 统计结果:');
    console.log(`总开括号: ${openBrackets}`);
    console.log(`总闭括号: ${closeBrackets}`);
    console.log(`平衡差值: ${openBrackets - closeBrackets}`);
    
    if (errors.length > 0) {
        console.log('\n❌ 发现错误:');
        errors.forEach(error => {
            console.log(`  第${error.line}行: ${error.message}`);
            console.log(`    内容: ${error.content}`);
        });
    }
    
    if (bracketStack.length > 0) {
        console.log('\n⚠️  未匹配的开括号:');
        bracketStack.forEach(bracket => {
            console.log(`  第${bracket.line}行: ${bracket.content}`);
        });
    }
    
    // 重点检查第959行附近
    console.log('\n🎯 第959行附近分析:');
    for (let i = 955; i <= 965; i++) {
        if (i <= lines.length) {
            const line = lines[i - 1];
            const openCount = (line.match(/\{/g) || []).length;
            const closeCount = (line.match(/\}/g) || []).length;
            console.log(`${i.toString().padStart(4)}: +${openCount} -${closeCount} | ${line}`);
        }
    }
    
    return {
        openBrackets,
        closeBrackets,
        errors,
        unmatched: bracketStack
    };
}

// 分析Login.vue文件
const filePath = path.join(__dirname, 'src', 'views', 'Login.vue');
try {
    const result = analyzeScssBrackets(filePath);
    
    if (result.openBrackets === result.closeBrackets && result.errors.length === 0) {
        console.log('\n✅ SCSS括号匹配正确！');
    } else {
        console.log('\n❌ SCSS括号匹配有问题，需要修复！');
    }
} catch (error) {
    console.error('分析失败:', error.message);
}
