import{_ as s}from"./index-D2bI4m-v.js";/* empty css                        *//* empty css                    *//* empty css                *//* empty css               *//* empty css                 *//* empty css                  *//* empty css                *//* empty css               */import{s as e}from"./system-CZ_rLt3R.js";import{C as a,r as t}from"./chart-Bup65vvO.js";import{as as l,b2 as c,o as i,U as r,b4 as n,b3 as o,bf as u,aI as d,bR as h,b8 as m,b6 as p,b7 as g,bY as f}from"./element-plus-DcSKpKA8.js";import{k as v,l as y,t as _,B as b,E as k,z as w,D as C,F as S,Y as R,y as D}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";a.register(...t);const T={class:"system-monitor"},M={class:"page-header"},H={class:"header-actions"},j={class:"status-overview"},I={class:"status-icon"},A={class:"status-info"},x={class:"status-info"},L={class:"status-info"},P={class:"status-info"},q={class:"performance-metrics"},Q={class:"metrics-grid"},$={class:"metric-item"},U={class:"metric-value"},V={class:"metric-item"},z={class:"metric-value"},O={class:"metric-item"},Y={class:"metric-value"},B={class:"metric-item"},E={class:"metric-value"},F={class:"metrics-grid"},J={class:"metric-item"},N={class:"metric-value"},Z={class:"metric-item"},G={class:"metric-value"},K={class:"metric-item"},W={class:"metric-value"},X={class:"metric-item"},ss={class:"metric-value"},es={class:"charts-section"},as={slot:"header"},ts={class:"chart-container"},ls={ref:"performanceChart",width:"800",height:"300"},cs={key:0,class:"alerts-section"},is={slot:"header"},rs={class:"alerts-list"},ns={key:1,class:"suggestions-section"},os={class:"suggestions-list"},us={class:"suggestion-header"},ds={class:"suggestion-category"},hs={class:"suggestion-content"},ms={key:0,class:"suggestion-details"},ps={key:2,class:"slow-queries-section"},gs={"slot-scope":"scope"},fs={"slot-scope":"scope"},vs={class:"sql-code"},ys={"slot-scope":"scope"},_s={"slot-scope":"scope"},bs={class:"system-info-section"},ks={class:"config-list"},ws={class:"config-item"},Cs={class:"config-value"},Ss={class:"config-item"},Rs={class:"config-value"},Ds={class:"config-item"},Ts={class:"config-value"},Ms={class:"config-item"},Hs={class:"config-value"},js={class:"config-item"},Is={class:"config-value"},As={key:0,class:"check-results"},xs={class:"check-item"},Ls={class:"check-item"},Ps={class:"check-item"},qs={class:"check-item"};const Qs=s({name:"SystemMonitor",data:()=>({loading:!1,checkLoading:!1,autoRefresh:!1,refreshInterval:null,systemHealth:{},performanceMetrics:{},alerts:[],suggestions:[],slowQueries:[],systemConfig:{},checkResults:null,performanceChart:null,chartData:{labels:[],datasets:[{label:"CPU使用率",data:[],borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)"},{label:"内存使用率",data:[],borderColor:"rgb(255, 99, 132)",backgroundColor:"rgba(255, 99, 132, 0.2)"}]}}),mounted(){this.loadAllData(),this.initChart()},beforeDestroy(){this.refreshInterval&&clearInterval(this.refreshInterval),this.performanceChart&&this.performanceChart.destroy()},methods:{async loadAllData(){this.loading=!0;try{await Promise.all([this.loadSystemHealth(),this.loadPerformanceMetrics(),this.loadAlerts(),this.loadSuggestions(),this.loadSlowQueries(),this.loadSystemConfig()])}catch(s){console.error("加载监控数据失败:",s),this.$message.error("加载监控数据失败")}finally{this.loading=!1}},async loadSystemHealth(){const s=await e.getHealthStatus();this.systemHealth=s.data},async loadPerformanceMetrics(){const s=await e.getPerformanceMetrics();this.performanceMetrics=s.data},async loadAlerts(){const s=await e.getAlerts();this.alerts=s.data},async loadSuggestions(){const s=await e.getOptimizationSuggestions();this.suggestions=s.data},async loadSlowQueries(){const s=await e.getSlowQueries();this.slowQueries=s.data},async loadSystemConfig(){const s=await e.getSystemConfiguration();this.systemConfig=s.data},async loadRealTimeMetrics(){const s=await e.getRealTimeMetrics();this.updateChart(s.data)},async refreshData(){await this.loadAllData(),this.$message.success("数据已刷新")},async runSystemCheck(){this.checkLoading=!0;try{const s=await e.runSystemCheck();this.checkResults=s.data,this.$message.success("系统检查完成")}catch(s){console.error("系统检查失败:",s),this.$message.error("系统检查失败")}finally{this.checkLoading=!1}},toggleAutoRefresh(s){s?this.refreshInterval=setInterval(()=>{this.loadRealTimeMetrics()},3e4):this.refreshInterval&&(clearInterval(this.refreshInterval),this.refreshInterval=null)},initChart(){const s=this.$refs.performanceChart.getContext("2d");this.performanceChart=new a(s,{type:"line",data:this.chartData,options:{responsive:!0,plugins:{title:{display:!0,text:"系统性能实时监控"}},scales:{y:{beginAtZero:!0,max:100}}}})},updateChart(s){const e=(new Date).toLocaleTimeString();this.chartData.labels.push(e),this.chartData.datasets[0].data.push(s.cpu_usage),this.chartData.datasets[1].data.push(s.memory_usage),this.chartData.labels.length>20&&(this.chartData.labels.shift(),this.chartData.datasets[0].data.shift(),this.chartData.datasets[1].data.shift()),this.performanceChart.update()},getStatusClass(s){switch(s){case"healthy":return"status-healthy";case"warning":return"status-warning";case"unhealthy":return"status-error";default:return"status-unknown"}},getStatusIcon(s){switch(s){case"healthy":return"el-icon-check";case"warning":return"el-icon-warning";case"unhealthy":return"el-icon-close";default:return"el-icon-question"}},getStatusText(s){switch(s){case"healthy":return"正常";case"warning":return"警告";case"unhealthy":return"异常";default:return"未知"}},getAlertType(s){switch(s){case"critical":return"error";case"warning":return"warning";default:return"info"}},getPriorityType(s){switch(s){case"high":return"danger";case"medium":return"warning";default:return"info"}},getStatusType(s){switch(s){case"ok":case"healthy":return"success";case"warning":return"warning";case"error":case"unhealthy":return"danger";default:return"info"}},formatTime:s=>new Date(s).toLocaleString()}},[["render",function(s,e,a,t,Qs,$s){const Us=l,Vs=c,zs=n,Os=o,Ys=u,Bs=d,Es=h,Fs=m,Js=g,Ns=f,Zs=p;return y(),v("div",T,[_("div",M,[e[3]||(e[3]=_("h1",null,"系统监控",-1)),_("div",H,[k(Us,{onClick:$s.refreshData,loading:Qs.loading,type:"primary"},{default:w(()=>e[1]||(e[1]=[_("i",{class:"el-icon-refresh"},null,-1),C(" 刷新数据 ",-1)])),_:1,__:[1]},8,["onClick","loading"]),k(Us,{onClick:$s.runSystemCheck,loading:Qs.checkLoading,type:"success"},{default:w(()=>e[2]||(e[2]=[_("i",{class:"el-icon-check"},null,-1),C(" 运行检查 ",-1)])),_:1,__:[2]},8,["onClick","loading"])])]),_("div",j,[k(zs,{gutter:20},{default:w(()=>[k(Vs,{span:6},{default:w(()=>[_("div",{class:i(["status-card",$s.getStatusClass(Qs.systemHealth.overall_status)])},[_("div",I,[_("i",{class:i($s.getStatusIcon(Qs.systemHealth.overall_status))},null,2)]),_("div",A,[e[4]||(e[4]=_("h3",null,"系统整体状态",-1)),_("p",null,r($s.getStatusText(Qs.systemHealth.overall_status)),1)])],2)]),_:1}),k(Vs,{span:6},{default:w(()=>[_("div",{class:i(["status-card",$s.getStatusClass(Qs.systemHealth.database?.status)])},[e[6]||(e[6]=_("div",{class:"status-icon"},[_("i",{class:"el-icon-database"})],-1)),_("div",x,[e[5]||(e[5]=_("h3",null,"数据库",-1)),_("p",null,r(Qs.systemHealth.database?.connection_time||0)+"ms",1)])],2)]),_:1}),k(Vs,{span:6},{default:w(()=>[_("div",{class:i(["status-card",$s.getStatusClass(Qs.systemHealth.cache?.status)])},[e[8]||(e[8]=_("div",{class:"status-icon"},[_("i",{class:"el-icon-cpu"})],-1)),_("div",L,[e[7]||(e[7]=_("h3",null,"缓存系统",-1)),_("p",null,r(Qs.systemHealth.cache?.hit_rate||0)+"% 命中率",1)])],2)]),_:1}),k(Vs,{span:6},{default:w(()=>[_("div",{class:i(["status-card",$s.getStatusClass(Qs.systemHealth.storage?.status)])},[e[10]||(e[10]=_("div",{class:"status-icon"},[_("i",{class:"el-icon-folder"})],-1)),_("div",P,[e[9]||(e[9]=_("h3",null,"存储空间",-1)),_("p",null,r(Qs.systemHealth.storage?.usage_percentage||0)+"% 已使用",1)])],2)]),_:1})]),_:1})]),_("div",q,[k(zs,{gutter:20},{default:w(()=>[k(Vs,{span:12},{default:w(()=>[k(Os,null,{default:w(()=>[e[15]||(e[15]=_("div",{slot:"header"},[_("span",null,"数据库性能")],-1)),_("div",Q,[_("div",$,[e[11]||(e[11]=_("span",{class:"metric-label"},"总查询数",-1)),_("span",U,r(Qs.performanceMetrics.database?.total_queries||0),1)]),_("div",V,[e[12]||(e[12]=_("span",{class:"metric-label"},"平均响应时间",-1)),_("span",z,r(Qs.performanceMetrics.database?.avg_time||0)+"ms",1)]),_("div",O,[e[13]||(e[13]=_("span",{class:"metric-label"},"慢查询数",-1)),_("span",Y,r(Qs.performanceMetrics.database?.slow_queries||0),1)]),_("div",B,[e[14]||(e[14]=_("span",{class:"metric-label"},"总耗时",-1)),_("span",E,r(Qs.performanceMetrics.database?.total_time||0)+"ms",1)])])]),_:1,__:[15]})]),_:1}),k(Vs,{span:12},{default:w(()=>[k(Os,null,{default:w(()=>[e[20]||(e[20]=_("div",{slot:"header"},[_("span",null,"缓存性能")],-1)),_("div",F,[_("div",J,[e[16]||(e[16]=_("span",{class:"metric-label"},"命中率",-1)),_("span",N,r(Qs.performanceMetrics.cache?.hit_rate||0)+"%",1)]),_("div",Z,[e[17]||(e[17]=_("span",{class:"metric-label"},"未命中率",-1)),_("span",G,r(Qs.performanceMetrics.cache?.miss_rate||0)+"%",1)]),_("div",K,[e[18]||(e[18]=_("span",{class:"metric-label"},"内存使用",-1)),_("span",W,r(Qs.performanceMetrics.cache?.memory_usage||0),1)]),_("div",X,[e[19]||(e[19]=_("span",{class:"metric-label"},"驱逐率",-1)),_("span",ss,r(Qs.performanceMetrics.cache?.eviction_rate||0)+"%",1)])])]),_:1,__:[20]})]),_:1})]),_:1})]),_("div",es,[k(zs,{gutter:20},{default:w(()=>[k(Vs,{span:24},{default:w(()=>[k(Os,null,{default:w(()=>[_("div",as,[e[21]||(e[21]=_("span",null,"实时性能监控",-1)),k(Ys,{modelValue:Qs.autoRefresh,"onUpdate:modelValue":e[0]||(e[0]=s=>Qs.autoRefresh=s),"active-text":"自动刷新","inactive-text":"手动刷新",onChange:$s.toggleAutoRefresh},null,8,["modelValue","onChange"])]),_("div",ts,[_("canvas",ls,null,512)])]),_:1})]),_:1})]),_:1})]),Qs.alerts.length>0?(y(),v("div",cs,[k(Os,null,{default:w(()=>[_("div",is,[e[22]||(e[22]=_("span",null,"系统告警",-1)),k(Bs,{value:Qs.alerts.length,class:"alert-badge"},null,8,["value"])]),_("div",rs,[(y(!0),v(S,null,R(Qs.alerts,s=>(y(),D(Es,{key:s.timestamp,title:s.message,type:$s.getAlertType(s.type),description:`组件: ${s.component} | 时间: ${$s.formatTime(s.timestamp)}`,"show-icon":"",class:"alert-item"},null,8,["title","type","description"]))),128))])]),_:1})])):b("",!0),Qs.suggestions.length>0?(y(),v("div",ns,[k(Os,null,{default:w(()=>[e[23]||(e[23]=_("div",{slot:"header"},[_("span",null,"优化建议")],-1)),_("div",os,[(y(!0),v(S,null,R(Qs.suggestions,s=>(y(),v("div",{key:s.category,class:"suggestion-item"},[_("div",us,[k(Fs,{type:$s.getPriorityType(s.priority)},{default:w(()=>[C(r(s.priority),1)]),_:2},1032,["type"]),_("span",ds,r(s.category),1)]),_("div",hs,[_("p",null,r(s.suggestion),1),s.details?(y(),v("div",ms,[_("pre",null,r(JSON.stringify(s.details,null,2)),1)])):b("",!0)])]))),128))])]),_:1,__:[23]})])):b("",!0),Qs.slowQueries.length>0?(y(),v("div",ps,[k(Os,null,{default:w(()=>[e[24]||(e[24]=_("div",{slot:"header"},[_("span",null,"慢查询监控")],-1)),k(Zs,{data:Qs.slowQueries,stripe:""},{default:w(()=>[k(Js,{prop:"time",label:"查询时间",width:"100"},{default:w(()=>[_("template",gs,[C(r(s.scope.row.time)+"ms ",1)])]),_:1}),k(Js,{prop:"sql",label:"SQL语句","min-width":"400"},{default:w(()=>[_("template",fs,[_("code",vs,r(s.scope.row.sql),1)])]),_:1}),k(Js,{prop:"url",label:"请求URL",width:"200"},{default:w(()=>[_("template",ys,[k(Ns,{href:s.scope.row.url,target:"_blank"},{default:w(()=>[C(r(s.scope.row.url),1)]),_:1},8,["href"])])]),_:1}),k(Js,{prop:"timestamp",label:"时间",width:"150"},{default:w(()=>[_("template",_s,[C(r($s.formatTime(s.scope.row.timestamp)),1)])]),_:1})]),_:1},8,["data"])]),_:1,__:[24]})])):b("",!0),_("div",bs,[k(zs,{gutter:20},{default:w(()=>[k(Vs,{span:12},{default:w(()=>[k(Os,null,{default:w(()=>[e[30]||(e[30]=_("div",{slot:"header"},[_("span",null,"系统配置")],-1)),_("div",ks,[_("div",ws,[e[25]||(e[25]=_("span",{class:"config-label"},"PHP版本",-1)),_("span",Cs,r(Qs.systemConfig.php_version),1)]),_("div",Ss,[e[26]||(e[26]=_("span",{class:"config-label"},"Laravel版本",-1)),_("span",Rs,r(Qs.systemConfig.laravel_version),1)]),_("div",Ds,[e[27]||(e[27]=_("span",{class:"config-label"},"数据库驱动",-1)),_("span",Ts,r(Qs.systemConfig.database?.driver),1)]),_("div",Ms,[e[28]||(e[28]=_("span",{class:"config-label"},"缓存驱动",-1)),_("span",Hs,r(Qs.systemConfig.cache?.driver),1)]),_("div",js,[e[29]||(e[29]=_("span",{class:"config-label"},"环境",-1)),_("span",Is,r(Qs.systemConfig.environment),1)])])]),_:1,__:[30]})]),_:1}),k(Vs,{span:12},{default:w(()=>[k(Os,null,{default:w(()=>[e[35]||(e[35]=_("div",{slot:"header"},[_("span",null,"系统检查结果")],-1)),Qs.checkResults?(y(),v("div",As,[_("div",xs,[e[31]||(e[31]=_("span",{class:"check-label"},"整体状态",-1)),k(Fs,{type:$s.getStatusType(Qs.checkResults.overall_status)},{default:w(()=>[C(r(Qs.checkResults.overall_status),1)]),_:1},8,["type"])]),_("div",Ls,[e[32]||(e[32]=_("span",{class:"check-label"},"数据库",-1)),k(Fs,{type:$s.getStatusType(Qs.checkResults.checks?.database?.status)},{default:w(()=>[C(r(Qs.checkResults.checks?.database?.status),1)]),_:1},8,["type"])]),_("div",Ps,[e[33]||(e[33]=_("span",{class:"check-label"},"缓存",-1)),k(Fs,{type:$s.getStatusType(Qs.checkResults.checks?.cache?.status)},{default:w(()=>[C(r(Qs.checkResults.checks?.cache?.status),1)]),_:1},8,["type"])]),_("div",qs,[e[34]||(e[34]=_("span",{class:"check-label"},"存储",-1)),k(Fs,{type:$s.getStatusType(Qs.checkResults.checks?.storage?.status)},{default:w(()=>[C(r(Qs.checkResults.checks?.storage?.status),1)]),_:1},8,["type"])])])):b("",!0)]),_:1,__:[35]})]),_:1})]),_:1})])])}],["__scopeId","data-v-73708e1e"]]);export{Qs as default};
