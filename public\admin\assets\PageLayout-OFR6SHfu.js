import{_ as e}from"./index-D2bI4m-v.js";import{aE as t,o as a,U as s,aG as l,p as i,as as o}from"./element-plus-DcSKpKA8.js";import{c as n,k as r,l as c,B as d,t as p,E as y,z as u,F as v,Y as g,y as m,D as f,m as k}from"./vue-vendor-DGsK9sC4.js";const h={key:0,class:"page-header"},b={class:"page-header-content"},_={key:0,class:"breadcrumb-section"},x={class:"title-section"},S={class:"title-content"},$={key:0,class:"title-icon"},C={class:"title-text"},j={class:"page-title"},B={key:0,class:"page-subtitle"},F={key:0,class:"page-actions"},T={key:1,class:"stats-section"},H={class:"stats-grid"},A={class:"stat-content"},D={class:"stat-value"},E={class:"stat-label"},O={class:"change-indicator"},P={key:0,class:"loading-container"},q={class:"loading-spinner"},w={class:"loading-text"},z={key:1,class:"error-container"},G={class:"error-content"},I={class:"error-title"},L={class:"error-message"},U={key:0,class:"error-actions"},Y={key:2,class:"empty-container"},J={class:"empty-content"},K={class:"empty-title"},M={class:"empty-message"},N={key:0,class:"empty-actions"},Q={key:3,class:"content-wrapper"},R={key:1,class:"page-footer"},V={key:2,class:"fab-container"},W=e({__name:"PageLayout",props:{title:{type:String,required:!0},subtitle:{type:String,default:""},icon:{type:String,default:""},breadcrumb:{type:Array,default:()=>[]},stats:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},loadingText:{type:String,default:"加载中..."},error:{type:Object,default:null},empty:{type:Object,default:null},hideHeader:{type:Boolean,default:!1},hideFooter:{type:Boolean,default:!1},fullScreen:{type:Boolean,default:!1},contentClass:{type:String,default:""}},setup(e){const W=e,X=n(()=>[W.contentClass,{"has-header":!W.hideHeader,"has-footer":!W.hideFooter,"is-loading":W.loading,"has-error":W.error,"is-empty":W.empty}]);return(n,W)=>{const Z=t,ee=l,te=o;return c(),r("div",{class:a(["page-layout",{"full-screen":e.fullScreen}])},[e.hideHeader?d("",!0):(c(),r("div",h,[p("div",b,[e.breadcrumb&&e.breadcrumb.length?(c(),r("div",_,[y(ee,{separator:"/"},{default:u(()=>[(c(!0),r(v,null,g(e.breadcrumb,(e,t)=>(c(),m(Z,{key:t,to:e.path},{default:u(()=>[e.icon?(c(),r("i",{key:0,class:a(e.icon)},null,2)):d("",!0),f(" "+s(e.title),1)]),_:2},1032,["to"]))),128))]),_:1})])):d("",!0),p("div",x,[p("div",S,[e.icon?(c(),r("div",$,[p("i",{class:a([e.icon,"page-icon"])},null,2)])):d("",!0),p("div",C,[p("h1",j,s(e.title),1),e.subtitle?(c(),r("p",B,s(e.subtitle),1)):d("",!0)])]),n.$slots.actions?(c(),r("div",F,[k(n.$slots,"actions",{},void 0,!0)])):d("",!0)]),e.stats&&e.stats.length?(c(),r("div",T,[p("div",H,[(c(!0),r(v,null,g(e.stats,(e,t)=>(c(),r("div",{key:t,class:"stat-card animate-fade-in-up",style:i({animationDelay:100*t+"ms"})},[p("div",{class:a(["stat-icon",e.color])},[p("i",{class:a(e.icon)},null,2)],2),p("div",A,[p("div",D,s(e.value),1),p("div",E,s(e.label),1),e.change?(c(),r("div",{key:0,class:a(["stat-change",e.changeType])},[p("span",O,s("increase"===e.changeType?"↑":"↓"),1),f(" "+s(e.change),1)],2)):d("",!0)])],4))),128))])])):d("",!0)])])),p("div",{class:a(["page-content",X.value])},[e.loading?(c(),r("div",P,[p("div",q,[W[0]||(W[0]=p("div",{class:"spinner"},null,-1)),p("p",w,s(e.loadingText||"加载中..."),1)])])):e.error?(c(),r("div",z,[p("div",G,[W[1]||(W[1]=p("div",{class:"error-icon"}," ⚠️ ",-1)),p("h3",I,s(e.error.title||"出现错误"),1),p("p",L,s(e.error.message||"请稍后重试"),1),e.error.actions?(c(),r("div",U,[(c(!0),r(v,null,g(e.error.actions,e=>(c(),m(te,{key:e.key,type:e.type||"primary",onClick:e.handler},{default:u(()=>[f(s(e.text),1)]),_:2},1032,["type","onClick"]))),128))])):d("",!0)])])):e.empty?(c(),r("div",Y,[p("div",J,[W[2]||(W[2]=p("div",{class:"empty-icon"}," 📦 ",-1)),p("h3",K,s(e.empty.title||"暂无数据"),1),p("p",M,s(e.empty.message||"暂时没有相关数据"),1),e.empty.actions?(c(),r("div",N,[(c(!0),r(v,null,g(e.empty.actions,e=>(c(),m(te,{key:e.key,type:e.type||"primary",onClick:e.handler},{default:u(()=>[f(s(e.text),1)]),_:2},1032,["type","onClick"]))),128))])):d("",!0)])])):(c(),r("div",Q,[k(n.$slots,"default",{},void 0,!0)]))],2),n.$slots.footer&&!e.hideFooter?(c(),r("div",R,[k(n.$slots,"footer",{},void 0,!0)])):d("",!0),n.$slots.fab?(c(),r("div",V,[k(n.$slots,"fab",{},void 0,!0)])):d("",!0)],2)}}},[["__scopeId","data-v-63636fb0"]]);export{W as P};
