{"permissions": {"allow": ["Bash(cd \"C:\\Users\\<USER>\\Desktop\\ffjq\")", "Bash(php -l app/Http/Controllers/Api/AdvancedGroupController.php)", "Bash(php -l app/Http/Controllers/Api/AnalyticsController.php)", "Bash(php -l app/Http/Controllers/Api/AntiBlockController.php)", "Bash(php -l app/Http/Controllers/Api/AuthController.php)", "Bash(php -l app/Http/Controllers/Api/CommissionLogController.php)", "Bash(php artisan migrate:status)", "Bash(ls -la)", "Bash(find \"C:\\Users\\<USER>\\Desktop\\ffjq\\付费进群源码包\" -name \"*.php\" -type f)", "Bash(rm:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(curl:*)", "Bash(php:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(.小白一键部署.sh)", "Bash(file:*)", "Bash(:)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(composer check-platform-reqs:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(composer install:*)", "Bash(grep:*)", "<PERSON><PERSON>(composer:*)", "Bash(yum install:*)", "Bash(apt-get:*)", "Bash(apt-get install:*)", "Bash(dnf install:*)", "Bash(systemctl:*)", "Bash(service:*)", "<PERSON><PERSON>(journalctl:*)", "<PERSON><PERSON>(tasklist:*)"], "deny": []}}