import{_ as e}from"./index-D2bI4m-v.js";/* empty css                     *//* empty css                       *//* empty css                 *//* empty css               *//* empty css                  *//* empty css               */import{ag as a,r as l,L as r,y as s,l as o,z as u,t,E as d,D as m,u as p}from"./vue-vendor-DGsK9sC4.js";import{bc as i,b4 as n,b2 as g,bd as f,aH as _,aW as c,aV as b,bo as V,bp as w,as as v,T as h,aS as y,bi as j,Q as P}from"./element-plus-DcSKpKA8.js";import{P as U}from"./PageLayout-OFR6SHfu.js";import"./utils-4VKArNEK.js";const q={class:"user-add-form"},k=e({__name:"UserAdd",setup(e){const k=a(),x=l(),E=l(!1),z=l(!1),C=r({username:"",email:"",phone:"",role:"user",password:"",confirmPassword:"",status:"active",remark:""}),H={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],role:[{required:!0,message:"请选择用户角色",trigger:"change"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,a,l)=>{a!==C.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}]},I=async()=>{try{await x.value.validate(),z.value=!0,await new Promise(e=>setTimeout(e,1e3)),P.success("用户创建成功"),k.push("/user/list")}catch(e){console.error("表单验证失败:",e)}finally{z.value=!1}};return(e,a)=>{const l=h,r=v,P=_,k=f,L=g,T=n,$=b,A=c,B=w,D=V,Q=i;return o(),s(U,{title:"添加用户",subtitle:"创建新的系统用户",icon:"Plus",loading:E.value},{actions:u(()=>[d(r,{onClick:a[0]||(a[0]=a=>e.$router.go(-1))},{default:u(()=>[d(l,null,{default:u(()=>[d(p(y))]),_:1}),a[9]||(a[9]=m(" 返回 ",-1))]),_:1,__:[9]}),d(r,{type:"primary",onClick:I,loading:z.value},{default:u(()=>[d(l,null,{default:u(()=>[d(p(j))]),_:1}),a[10]||(a[10]=m(" 保存用户 ",-1))]),_:1,__:[10]},8,["loading"])]),default:u(()=>[t("div",q,[d(Q,{ref_key:"formRef",ref:x,model:C,rules:H,"label-width":"120px",size:"large"},{default:u(()=>[d(T,{gutter:24},{default:u(()=>[d(L,{span:12},{default:u(()=>[d(k,{label:"用户名",prop:"username"},{default:u(()=>[d(P,{modelValue:C.username,"onUpdate:modelValue":a[1]||(a[1]=e=>C.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1})]),_:1}),d(L,{span:12},{default:u(()=>[d(k,{label:"邮箱",prop:"email"},{default:u(()=>[d(P,{modelValue:C.email,"onUpdate:modelValue":a[2]||(a[2]=e=>C.email=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(T,{gutter:24},{default:u(()=>[d(L,{span:12},{default:u(()=>[d(k,{label:"手机号",prop:"phone"},{default:u(()=>[d(P,{modelValue:C.phone,"onUpdate:modelValue":a[3]||(a[3]=e=>C.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),d(L,{span:12},{default:u(()=>[d(k,{label:"用户角色",prop:"role"},{default:u(()=>[d(A,{modelValue:C.role,"onUpdate:modelValue":a[4]||(a[4]=e=>C.role=e),placeholder:"请选择用户角色"},{default:u(()=>[d($,{label:"管理员",value:"admin"}),d($,{label:"普通用户",value:"user"}),d($,{label:"分销员",value:"distributor"}),d($,{label:"群主",value:"group_owner"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(T,{gutter:24},{default:u(()=>[d(L,{span:12},{default:u(()=>[d(k,{label:"密码",prop:"password"},{default:u(()=>[d(P,{modelValue:C.password,"onUpdate:modelValue":a[5]||(a[5]=e=>C.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),d(L,{span:12},{default:u(()=>[d(k,{label:"确认密码",prop:"confirmPassword"},{default:u(()=>[d(P,{modelValue:C.confirmPassword,"onUpdate:modelValue":a[6]||(a[6]=e=>C.confirmPassword=e),type:"password",placeholder:"请确认密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(k,{label:"用户状态",prop:"status"},{default:u(()=>[d(D,{modelValue:C.status,"onUpdate:modelValue":a[7]||(a[7]=e=>C.status=e)},{default:u(()=>[d(B,{label:"active"},{default:u(()=>a[11]||(a[11]=[m("启用",-1)])),_:1,__:[11]}),d(B,{label:"inactive"},{default:u(()=>a[12]||(a[12]=[m("禁用",-1)])),_:1,__:[12]})]),_:1},8,["modelValue"])]),_:1}),d(k,{label:"备注"},{default:u(()=>[d(P,{modelValue:C.remark,"onUpdate:modelValue":a[8]||(a[8]=e=>C.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["loading"])}}},[["__scopeId","data-v-38decc6f"]]);export{k as default};
