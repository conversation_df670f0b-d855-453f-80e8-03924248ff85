import{_ as e,c as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                 *//* empty css                             *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                     *//* empty css                       *//* empty css                        *//* empty css               *//* empty css                */import{r as l,L as t,e as s,k as d,l as n,E as o,z as u,t as r,u as i,D as c,A as p,y as m,B as v,G as _}from"./vue-vendor-DGsK9sC4.js";import{b as f}from"./export-1WkTdiMF.js";import{a as g,c as b}from"./format-3eU4VJ9V.js";import{Q as h,b2 as y,b3 as w,U as k,b4 as x,bc as V,bd as j,aH as C,aW as U,aV as $,be as z,bl as R,as as D,b6 as L,b7 as B,b8 as S,bj as I,bk as O,bM as E,bN as F,au as G,bR as M,R as Q}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";const T={class:"app-container"},W={class:"stats-content"},q={class:"stats-data"},A={class:"stats-number"},H={class:"stats-amount"},N={class:"stats-content"},P={class:"stats-data"},J={class:"stats-number"},K={class:"stats-amount"},X={class:"stats-content"},Y={class:"stats-data"},Z={class:"stats-number"},ee={class:"stats-amount"},ae={class:"stats-content"},le={class:"stats-data"},te={class:"stats-number"},se={class:"stats-amount"},de={class:"user-info"},ne={class:"user-avatar"},oe=["src"],ue={class:"user-details"},re={class:"user-name"},ie={class:"user-id"},ce={class:"amount-info"},pe={class:"withdraw-amount"},me={class:"fee-amount"},ve={class:"actual-amount"},_e={class:"method-info"},fe={class:"account-info"},ge={class:"account-name"},be={class:"account-number"},he={class:"time-info"},ye={class:"apply-time"},we={key:0,class:"process-time"},ke={style:{"margin-top":"20px","text-align":"center"}},xe={key:0,class:"detail-container"},Ve={key:0,style:{"margin-top":"20px"}},je={key:1,style:{"margin-top":"20px"}},Ce={class:"dialog-footer"},Ue={key:0,style:{"margin-bottom":"20px"}},$e={class:"dialog-footer"},ze={style:{"margin-bottom":"20px"}},Re={class:"dialog-footer"},De=e({__name:"WithdrawManage",setup(e){const De=l([]),Le=l(0),Be=l(!0),Se=l(!1),Ie=l(!1),Oe=l(!1),Ee=l([]),Fe=l(!1),Ge=l(!1),Me=l(!1),Qe=l(null),Te=l("approve"),We=l("approve"),qe=l([]),Ae=t({page:1,limit:15,user_name:"",status:"",method:"",min_amount:null,max_amount:null,date_range:[]}),He=t({admin_remark:""}),Ne=t({admin_remark:""}),Pe=l({pending_count:0,pending_amount:0,approved_count:0,approved_amount:0,completed_count:0,completed_amount:0,total_count:0,total_amount:0});s(()=>{Je(),Ke()});const Je=async()=>{Be.value=!0;try{const e={...Ae};e.date_range&&2===e.date_range.length&&(e.start_date=e.date_range[0],e.end_date=e.date_range[1],delete e.date_range);const l=await a({url:"/withdraw-records",method:"get",params:e});De.value=l.data.data,Le.value=l.data.total}catch(e){h.error("获取数据失败")}finally{Be.value=!1}},Ke=async()=>{try{const e=await a({url:"/withdraw-records/stats",method:"get"});Pe.value=e.data}catch(e){console.error("获取统计数据失败")}},Xe=()=>{Ae.page=1,Je()},Ye=()=>{Object.assign(Ae,{page:1,limit:15,user_name:"",status:"",method:"",min_amount:null,max_amount:null,date_range:[]}),Je()},Ze=()=>{Ke(),h.success("统计数据已刷新")},ea=e=>{Ee.value=e},aa=()=>{const e=Ee.value.filter(e=>"pending"===e.status);0!==e.length?(We.value="approve",qe.value=e,Ne.admin_remark="",Me.value=!0):h.warning("请选择待审核的提现申请")},la=()=>{const e=Ee.value.filter(e=>"pending"===e.status);0!==e.length?(We.value="reject",qe.value=e,Ne.admin_remark="",Me.value=!0):h.warning("请选择待审核的提现申请")},ta=async()=>{Oe.value=!0;try{const e="approve"===We.value?"batch-approve":"batch-reject",l="approve"===We.value?"批准":"拒绝";await a({url:`/withdraw-records/${e}`,method:"post",data:{ids:qe.value.map(e=>e.id),admin_remark:Ne.admin_remark}}),h.success(`批量${l}成功`),Me.value=!1,Je(),Ke()}catch(e){h.error(`批量${"approve"===We.value?"批准":"拒绝"}失败`)}finally{Oe.value=!1}},sa=async()=>{Se.value=!0;try{const e={...Ae,format:"excel",fields:["id","user_name","amount","fee","method","account_name","account_number","status","created_at"]},a=await f(e),l=window.URL.createObjectURL(new Blob([a.data])),t=document.createElement("a");t.href=l,t.download=`提现记录_${(new Date).toLocaleDateString()}.xlsx`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(l),h.success("导出成功")}catch(e){h.error("导出失败")}finally{Se.value=!1}},da=e=>{Qe.value=e,Te.value="approve",He.admin_remark="",Ge.value=!0,Fe.value=!1},na=e=>{Qe.value=e,Te.value="reject",He.admin_remark="",Ge.value=!0,Fe.value=!1},oa=async()=>{Ie.value=!0;try{const e="approve"===Te.value?"approve":"reject",l="approve"===Te.value?"批准":"拒绝";await a({url:`/withdraw-records/${Qe.value.id}/${e}`,method:"post",data:{admin_remark:He.admin_remark}}),h.success(`${l}成功`),Ge.value=!1,Je(),Ke()}catch(e){h.error(("approve"===Te.value?"批准":"拒绝")+"失败")}finally{Ie.value=!1}},ua=e=>({pending:"待审核",approved:"已批准",rejected:"已拒绝",processing:"处理中",completed:"已完成",failed:"失败"}[e]||"未知"),ra=e=>({pending:"warning",approved:"success",rejected:"danger",processing:"primary",completed:"success",failed:"danger"}[e]||"info"),ia=e=>({alipay:"支付宝",wechat:"微信",bank_card:"银行卡",balance:"余额"}[e]||"未知");return(e,l)=>{const t=w,s=y,f=x,ca=C,pa=j,ma=$,va=U,_a=z,fa=R,ga=D,ba=V,ha=B,ya=S,wa=L,ka=O,xa=F,Va=E,ja=G,Ca=M,Ua=I;return n(),d("div",T,[o(f,{gutter:20,style:{"margin-bottom":"20px"}},{default:u(()=>[o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",W,[l[19]||(l[19]=r("div",{class:"stats-icon pending-icon"},[r("i",{class:"el-icon-time"})],-1)),r("div",q,[r("div",A,k(Pe.value.pending_count),1),l[18]||(l[18]=r("div",{class:"stats-label"},"待审核",-1)),r("div",H,"¥"+k(i(g)(Pe.value.pending_amount)),1)])])]),_:1})]),_:1}),o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",N,[l[21]||(l[21]=r("div",{class:"stats-icon approved-icon"},[r("i",{class:"el-icon-check"})],-1)),r("div",P,[r("div",J,k(Pe.value.approved_count),1),l[20]||(l[20]=r("div",{class:"stats-label"},"已批准",-1)),r("div",K,"¥"+k(i(g)(Pe.value.approved_amount)),1)])])]),_:1})]),_:1}),o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",X,[l[23]||(l[23]=r("div",{class:"stats-icon completed-icon"},[r("i",{class:"el-icon-success"})],-1)),r("div",Y,[r("div",Z,k(Pe.value.completed_count),1),l[22]||(l[22]=r("div",{class:"stats-label"},"已完成",-1)),r("div",ee,"¥"+k(i(g)(Pe.value.completed_amount)),1)])])]),_:1})]),_:1}),o(s,{span:6},{default:u(()=>[o(t,{class:"stats-card"},{default:u(()=>[r("div",ae,[l[25]||(l[25]=r("div",{class:"stats-icon total-icon"},[r("i",{class:"el-icon-wallet"})],-1)),r("div",le,[r("div",te,k(Pe.value.total_count),1),l[24]||(l[24]=r("div",{class:"stats-label"},"总提现",-1)),r("div",se,"¥"+k(i(g)(Pe.value.total_amount)),1)])])]),_:1})]),_:1})]),_:1}),o(t,{style:{"margin-bottom":"20px"}},{default:u(()=>[o(ba,{inline:!0,model:Ae,"label-width":"80px"},{default:u(()=>[o(pa,{label:"用户名"},{default:u(()=>[o(ca,{modelValue:Ae.user_name,"onUpdate:modelValue":l[0]||(l[0]=e=>Ae.user_name=e),placeholder:"请输入用户名",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(pa,{label:"状态"},{default:u(()=>[o(va,{modelValue:Ae.status,"onUpdate:modelValue":l[1]||(l[1]=e=>Ae.status=e),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:u(()=>[o(ma,{label:"全部",value:""}),o(ma,{label:"待审核",value:"pending"}),o(ma,{label:"已批准",value:"approved"}),o(ma,{label:"已拒绝",value:"rejected"}),o(ma,{label:"处理中",value:"processing"}),o(ma,{label:"已完成",value:"completed"}),o(ma,{label:"失败",value:"failed"})]),_:1},8,["modelValue"])]),_:1}),o(pa,{label:"提现方式"},{default:u(()=>[o(va,{modelValue:Ae.method,"onUpdate:modelValue":l[2]||(l[2]=e=>Ae.method=e),placeholder:"请选择方式",clearable:"",style:{width:"150px"}},{default:u(()=>[o(ma,{label:"全部",value:""}),o(ma,{label:"支付宝",value:"alipay"}),o(ma,{label:"微信",value:"wechat"}),o(ma,{label:"银行卡",value:"bank_card"}),o(ma,{label:"余额",value:"balance"})]),_:1},8,["modelValue"])]),_:1}),o(pa,{label:"金额范围"},{default:u(()=>[o(_a,{modelValue:Ae.min_amount,"onUpdate:modelValue":l[3]||(l[3]=e=>Ae.min_amount=e),min:0,precision:2,placeholder:"最小金额",style:{width:"120px"}},null,8,["modelValue"]),l[26]||(l[26]=r("span",{style:{margin:"0 10px"}},"-",-1)),o(_a,{modelValue:Ae.max_amount,"onUpdate:modelValue":l[4]||(l[4]=e=>Ae.max_amount=e),min:0,precision:2,placeholder:"最大金额",style:{width:"120px"}},null,8,["modelValue"])]),_:1,__:[26]}),o(pa,{label:"时间范围"},{default:u(()=>[o(fa,{modelValue:Ae.date_range,"onUpdate:modelValue":l[5]||(l[5]=e=>Ae.date_range=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"250px"}},null,8,["modelValue"])]),_:1}),o(pa,null,{default:u(()=>[o(ga,{type:"primary",icon:"Search",onClick:Xe},{default:u(()=>l[27]||(l[27]=[c("搜索",-1)])),_:1,__:[27]}),o(ga,{icon:"Refresh",onClick:Ye},{default:u(()=>l[28]||(l[28]=[c("重置",-1)])),_:1,__:[28]})]),_:1})]),_:1},8,["model"])]),_:1}),o(t,{style:{"margin-bottom":"20px"}},{default:u(()=>[o(f,null,{default:u(()=>[o(s,{span:12},{default:u(()=>[o(ga,{type:"success",icon:"Check",disabled:!Ee.value.length,onClick:aa},{default:u(()=>l[29]||(l[29]=[c(" 批量批准 ",-1)])),_:1,__:[29]},8,["disabled"]),o(ga,{type:"danger",icon:"Close",disabled:!Ee.value.length,onClick:la},{default:u(()=>l[30]||(l[30]=[c(" 批量拒绝 ",-1)])),_:1,__:[30]},8,["disabled"]),o(ga,{type:"warning",icon:"Download",onClick:sa,loading:Se.value},{default:u(()=>l[31]||(l[31]=[c(" 导出数据 ",-1)])),_:1,__:[31]},8,["loading"])]),_:1}),o(s,{span:12,style:{"text-align":"right"}},{default:u(()=>[o(ga,{type:"primary",icon:"Refresh",onClick:Ze},{default:u(()=>l[32]||(l[32]=[c(" 刷新统计 ",-1)])),_:1,__:[32]})]),_:1})]),_:1})]),_:1}),o(t,null,{default:u(()=>[p((n(),m(wa,{data:De.value,border:"",fit:"","highlight-current-row":"",style:{width:"100%"},onSelectionChange:ea},{default:u(()=>[o(ha,{type:"selection",width:"55",align:"center"}),o(ha,{label:"ID",prop:"id",align:"center",width:"80"}),o(ha,{label:"用户信息",width:"180",align:"center"},{default:u(({row:e})=>[r("div",de,[r("div",ne,[r("img",{src:e.user?.avatar||"/default-avatar.png",alt:""},null,8,oe)]),r("div",ue,[r("div",re,k(e.user?.name||"未知用户"),1),r("div",ie,"ID: "+k(e.user?.id),1)])])]),_:1}),o(ha,{label:"提现金额",width:"150",align:"center"},{default:u(({row:e})=>[r("div",ce,[r("div",pe,"¥"+k(i(g)(e.amount)),1),r("div",me,"手续费: ¥"+k(i(g)(e.fee||0)),1),r("div",ve,"实际: ¥"+k(i(g)(e.amount-(e.fee||0))),1)])]),_:1}),o(ha,{label:"提现方式",width:"120",align:"center"},{default:u(({row:e})=>{return[r("div",_e,[o(ya,{type:(a=e.method,{alipay:"primary",wechat:"success",bank_card:"warning",balance:"info"}[a]||"info")},{default:u(()=>[c(k(ia(e.method)),1)]),_:2},1032,["type"])])];var a}),_:1}),o(ha,{label:"账户信息",width:"200",align:"center"},{default:u(({row:e})=>{return[r("div",fe,[r("div",ge,k(e.account_name),1),r("div",be,k((a=e.account_number,a?a.length<=4?a:a.replace(/(\d{4})\d*(\d{4})/,"$1****$2"):"-")),1)])];var a}),_:1}),o(ha,{label:"状态",width:"100",align:"center"},{default:u(({row:e})=>[o(ya,{type:ra(e.status)},{default:u(()=>[c(k(ua(e.status)),1)]),_:2},1032,["type"])]),_:1}),o(ha,{label:"时间信息",width:"180",align:"center"},{default:u(({row:e})=>[r("div",he,[r("div",ye,"申请: "+k(i(b)(e.created_at)),1),e.processed_at?(n(),d("div",we," 处理: "+k(i(b)(e.processed_at)),1)):v("",!0)])]),_:1}),o(ha,{label:"操作",width:"200",align:"center",fixed:"right"},{default:u(({row:e})=>[o(ga,{type:"text",size:"small",onClick:a=>(e=>{Qe.value=e,Fe.value=!0})(e)},{default:u(()=>l[33]||(l[33]=[c(" 详情 ",-1)])),_:2,__:[33]},1032,["onClick"]),"pending"===e.status?(n(),m(ga,{key:0,type:"text",size:"small",onClick:a=>da(e)},{default:u(()=>l[34]||(l[34]=[c(" 批准 ",-1)])),_:2,__:[34]},1032,["onClick"])):v("",!0),"pending"===e.status?(n(),m(ga,{key:1,type:"text",size:"small",onClick:a=>na(e)},{default:u(()=>l[35]||(l[35]=[c(" 拒绝 ",-1)])),_:2,__:[35]},1032,["onClick"])):v("",!0),"approved"===e.status?(n(),m(ga,{key:2,type:"text",size:"small",onClick:l=>(e=>{Q.confirm("确定要处理这笔提现申请吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await a({url:`/withdraw-records/${e.id}/process`,method:"post"}),h.success("处理成功"),Je(),Ke()}catch(l){h.error("处理失败")}})})(e)},{default:u(()=>l[36]||(l[36]=[c(" 处理 ",-1)])),_:2,__:[36]},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"])),[[Ua,Be.value]]),r("div",ke,[p(o(ka,{"current-page":Ae.page,"onUpdate:currentPage":l[6]||(l[6]=e=>Ae.page=e),"page-size":Ae.limit,"onUpdate:pageSize":l[7]||(l[7]=e=>Ae.limit=e),"page-sizes":[15,30,50,100],total:Le.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Je,onCurrentChange:Je},null,8,["current-page","page-size","total"]),[[_,Le.value>0]])])]),_:1}),o(ja,{modelValue:Fe.value,"onUpdate:modelValue":l[11]||(l[11]=e=>Fe.value=e),title:"提现详情",width:"700px"},{footer:u(()=>[r("div",Ce,[o(ga,{onClick:l[8]||(l[8]=e=>Fe.value=!1)},{default:u(()=>l[39]||(l[39]=[c("关闭",-1)])),_:1,__:[39]}),Qe.value&&"pending"===Qe.value.status?(n(),m(ga,{key:0,type:"success",onClick:l[9]||(l[9]=e=>da(Qe.value))},{default:u(()=>l[40]||(l[40]=[c(" 批准 ",-1)])),_:1,__:[40]})):v("",!0),Qe.value&&"pending"===Qe.value.status?(n(),m(ga,{key:1,type:"danger",onClick:l[10]||(l[10]=e=>na(Qe.value))},{default:u(()=>l[41]||(l[41]=[c(" 拒绝 ",-1)])),_:1,__:[41]})):v("",!0)])]),default:u(()=>[Qe.value?(n(),d("div",xe,[o(Va,{column:2,border:""},{default:u(()=>[o(xa,{label:"用户"},{default:u(()=>[c(k(Qe.value.user?.name||"未知用户"),1)]),_:1}),o(xa,{label:"用户ID"},{default:u(()=>[c(k(Qe.value.user?.id),1)]),_:1}),o(xa,{label:"状态"},{default:u(()=>[o(ya,{type:ra(Qe.value.status)},{default:u(()=>[c(k(ua(Qe.value.status)),1)]),_:1},8,["type"])]),_:1}),o(xa,{label:"提现金额"},{default:u(()=>[c("¥"+k(i(g)(Qe.value.amount)),1)]),_:1}),o(xa,{label:"手续费"},{default:u(()=>[c("¥"+k(i(g)(Qe.value.fee||0)),1)]),_:1}),o(xa,{label:"实际到账"},{default:u(()=>[c("¥"+k(i(g)(Qe.value.amount-(Qe.value.fee||0))),1)]),_:1}),o(xa,{label:"提现方式"},{default:u(()=>[c(k(ia(Qe.value.method)),1)]),_:1}),o(xa,{label:"账户姓名"},{default:u(()=>[c(k(Qe.value.account_name),1)]),_:1}),o(xa,{label:"账户号码"},{default:u(()=>[c(k(Qe.value.account_number),1)]),_:1}),o(xa,{label:"申请时间"},{default:u(()=>[c(k(i(b)(Qe.value.created_at)),1)]),_:1}),o(xa,{label:"处理时间"},{default:u(()=>[c(k(Qe.value.processed_at?i(b)(Qe.value.processed_at):"-"),1)]),_:1}),o(xa,{label:"处理人"},{default:u(()=>[c(k(Qe.value.processed_by||"-"),1)]),_:1})]),_:1}),Qe.value.remark?(n(),d("div",Ve,[l[37]||(l[37]=r("h4",null,"申请备注",-1)),o(t,null,{default:u(()=>[r("p",null,k(Qe.value.remark),1)]),_:1})])):v("",!0),Qe.value.admin_remark?(n(),d("div",je,[l[38]||(l[38]=r("h4",null,"管理员备注",-1)),o(t,null,{default:u(()=>[r("p",null,k(Qe.value.admin_remark),1)]),_:1})])):v("",!0)])):v("",!0)]),_:1},8,["modelValue"]),o(ja,{modelValue:Ge.value,"onUpdate:modelValue":l[14]||(l[14]=e=>Ge.value=e),title:"approve"===Te.value?"批准提现":"拒绝提现",width:"500px"},{footer:u(()=>[r("div",$e,[o(ga,{onClick:l[13]||(l[13]=e=>Ge.value=!1)},{default:u(()=>l[42]||(l[42]=[c("取消",-1)])),_:1,__:[42]}),o(ga,{type:"approve"===Te.value?"success":"danger",onClick:oa,loading:Ie.value},{default:u(()=>[c(k("approve"===Te.value?"批准":"拒绝"),1)]),_:1},8,["type","loading"])])]),default:u(()=>[Qe.value?(n(),d("div",Ue,[o(Ca,{title:("approve"===Te.value?"批准":"拒绝")+"提现申请",description:`用户: ${Qe.value.user?.name} | 金额: ¥${i(g)(Qe.value.amount)}`,type:"approve"===Te.value?"success":"warning","show-icon":"",closable:!1},null,8,["title","description","type"])])):v("",!0),o(ba,{ref:"reviewFormRef",model:He,"label-width":"100px"},{default:u(()=>[o(pa,{label:"管理员备注",prop:"admin_remark"},{default:u(()=>[o(ca,{modelValue:He.admin_remark,"onUpdate:modelValue":l[12]||(l[12]=e=>He.admin_remark=e),type:"textarea",rows:4,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),o(ja,{modelValue:Me.value,"onUpdate:modelValue":l[17]||(l[17]=e=>Me.value=e),title:`批量${"approve"===We.value?"批准":"拒绝"}提现`,width:"500px"},{footer:u(()=>[r("div",Re,[o(ga,{onClick:l[16]||(l[16]=e=>Me.value=!1)},{default:u(()=>l[43]||(l[43]=[c("取消",-1)])),_:1,__:[43]}),o(ga,{type:"approve"===We.value?"success":"danger",onClick:ta,loading:Oe.value},{default:u(()=>[c(" 确认"+k("approve"===We.value?"批准":"拒绝"),1)]),_:1},8,["type","loading"])])]),default:u(()=>[r("div",ze,[o(Ca,{title:`即将${"approve"===We.value?"批准":"拒绝"}${qe.value.length}条提现申请`,description:`总金额: ¥${i(g)(qe.value.reduce((e,a)=>e+a.amount,0))}`,type:"approve"===We.value?"success":"warning","show-icon":"",closable:!1},null,8,["title","description","type"])]),o(ba,{ref:"batchReviewFormRef",model:Ne,"label-width":"100px"},{default:u(()=>[o(pa,{label:"管理员备注",prop:"admin_remark"},{default:u(()=>[o(ca,{modelValue:Ne.admin_remark,"onUpdate:modelValue":l[15]||(l[15]=e=>Ne.admin_remark=e),type:"textarea",rows:4,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-07782019"]]);export{De as default};
