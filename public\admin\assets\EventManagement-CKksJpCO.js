import{_ as e}from"./index-D2bI4m-v.js";/* empty css                 *//* empty css               *//* empty css                *//* empty css               *//* empty css                    */import{bc as a,bd as t,aH as l,bo as s,bv as n,bl as i,b4 as o,b2 as u,be as d,bj as m,as as r,au as p,Q as c,b9 as _,b5 as f,b3 as v,b8 as g,U as b,T as y,bH as h,ao as V,ai as j,aY as w,av as x,aO as k,ax as D,ay as U,bF as T,ab as C,bt as O,bm as Z,aa as Y,R as $}from"./element-plus-DcSKpKA8.js";import{P as E}from"./PageLayout-OFR6SHfu.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                       *//* empty css                       *//* empty css                        */import{r as H,c as S,L as q,d as z,y as F,l as J,z as L,A as N,E as B,D as I,t as M,e as P,k as R,B as A,F as G,Y as Q,u as K}from"./vue-vendor-DGsK9sC4.js";import{f as W}from"./format-3eU4VJ9V.js";import"./utils-4VKArNEK.js";const X={class:"dialog-footer"},ee=e({__name:"EventEditDialog",props:{modelValue:Boolean,eventData:{type:Object,default:null}},emits:["update:modelValue","success"],setup(e,{emit:_}){const f=e,v=_,g=H(null),b=H(!1),y=H(!1),h=S(()=>!!f.eventData),V=q({name:"",type:"online",time_range:[],fee:0,max_participants:0,cover_image:"",description:""}),j={name:[{required:!0,message:"请输入活动名称",trigger:"blur"}],type:[{required:!0,message:"请选择活动类型",trigger:"change"}],time_range:[{required:!0,message:"请选择活动时间",trigger:"change"}]};z(()=>f.eventData,e=>{if(e){const a=JSON.parse(JSON.stringify(e));a.time_range=[a.start_time,a.end_time],Object.assign(V,a)}else Object.assign(V,{name:"",type:"online",time_range:[],fee:0,max_participants:0,cover_image:"",description:""})});const w=()=>{g.value.resetFields(),Object.assign(V,{name:"",type:"online",time_range:[],fee:0,max_participants:0,cover_image:"",description:""})},x=()=>{g.value.validate(e=>{if(e){y.value=!0;const e={...V,start_time:V.time_range[0],end_time:V.time_range[1]};delete e.time_range,setTimeout(()=>{y.value=!1,c.success(h.value?"活动更新成功":"活动创建成功"),v("success",e),v("update:modelValue",!1)},500)}})};return(c,_)=>{const f=l,v=t,k=n,D=s,U=i,T=d,C=u,O=o,Z=a,Y=r,$=p,E=m;return J(),F($,{"model-value":e.modelValue,title:h.value?"编辑活动":"创建新活动",width:"700px","onUpdate:modelValue":_[8]||(_[8]=e=>c.$emit("update:modelValue",e)),onClose:w},{footer:L(()=>[M("span",X,[B(Y,{onClick:_[7]||(_[7]=e=>c.$emit("update:modelValue",!1))},{default:L(()=>_[12]||(_[12]=[I("取消",-1)])),_:1,__:[12]}),B(Y,{type:"primary",onClick:x,loading:y.value},{default:L(()=>_[13]||(_[13]=[I(" 确定 ",-1)])),_:1,__:[13]},8,["loading"])])]),default:L(()=>[N((J(),F(Z,{ref_key:"formRef",ref:g,model:V,rules:j,"label-width":"100px"},{default:L(()=>[B(v,{label:"活动名称",prop:"name"},{default:L(()=>[B(f,{modelValue:V.name,"onUpdate:modelValue":_[0]||(_[0]=e=>V.name=e),placeholder:"请输入活动名称"},null,8,["modelValue"])]),_:1}),B(v,{label:"活动类型",prop:"type"},{default:L(()=>[B(D,{modelValue:V.type,"onUpdate:modelValue":_[1]||(_[1]=e=>V.type=e)},{default:L(()=>[B(k,{label:"online"},{default:L(()=>_[9]||(_[9]=[I("线上活动",-1)])),_:1,__:[9]}),B(k,{label:"offline"},{default:L(()=>_[10]||(_[10]=[I("线下活动",-1)])),_:1,__:[10]}),B(k,{label:"check-in"},{default:L(()=>_[11]||(_[11]=[I("打卡挑战",-1)])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),B(v,{label:"活动时间",prop:"time_range"},{default:L(()=>[B(U,{modelValue:V.time_range,"onUpdate:modelValue":_[2]||(_[2]=e=>V.time_range=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"100%"},"value-format":"YYYY-MM-DDTHH:mm:ssZ"},null,8,["modelValue"])]),_:1}),B(O,{gutter:20},{default:L(()=>[B(C,{span:12},{default:L(()=>[B(v,{label:"参与费用",prop:"fee"},{default:L(()=>[B(T,{modelValue:V.fee,"onUpdate:modelValue":_[3]||(_[3]=e=>V.fee=e),precision:2,min:0,placeholder:"0表示免费",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),B(C,{span:12},{default:L(()=>[B(v,{label:"人数限制",prop:"max_participants"},{default:L(()=>[B(T,{modelValue:V.max_participants,"onUpdate:modelValue":_[4]||(_[4]=e=>V.max_participants=e),min:0,placeholder:"0表示不限制",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),B(v,{label:"活动封面",prop:"cover_image"},{default:L(()=>[B(f,{modelValue:V.cover_image,"onUpdate:modelValue":_[5]||(_[5]=e=>V.cover_image=e),placeholder:"请输入封面图片URL"},null,8,["modelValue"])]),_:1}),B(v,{label:"活动详情",prop:"description"},{default:L(()=>[B(f,{type:"textarea",rows:4,modelValue:V.description,"onUpdate:modelValue":_[6]||(_[6]=e=>V.description=e),placeholder:"请输入详细的活动介绍..."},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[E,b.value]])]),_:1},8,["model-value","title"])}}},[["__scopeId","data-v-f63857e5"]]),ae={class:"app-container"},te={class:"page-header"},le={class:"toolbar-container"},se={class:"event-list-container"},ne={class:"event-image-wrapper"},ie=["src"],oe={class:"event-info"},ue={class:"event-title"},de={class:"event-time"},me={class:"event-stats"},re={class:"event-actions"},pe=e({__name:"EventManagement",setup(e){const a=H(!1),t=H(!1),l=H("all"),s=H([]),n=H(null),i=S(()=>{const e=new Date;return"all"===l.value?s.value:s.value.filter(a=>{const t=new Date(a.start_time),s=new Date(a.end_time);return"ongoing"===l.value?t<=e&&s>=e:"upcoming"===l.value?t>e:"finished"===l.value&&s<e})}),d=e=>{const a=new Date,t=new Date(e.start_time),l=new Date(e.end_time);return l<a?{text:"已结束",type:"info"}:t<=a&&l>=a?{text:"进行中",type:"success"}:t>a?{text:"未开始",type:"warning"}:{text:"未知",type:"info"}},m=()=>{a.value=!0,setTimeout(()=>{s.value=[{id:1,name:"AI技术应用线上分享会",type:"online",start_time:"2024-06-10T19:00:00Z",end_time:"2024-06-10T21:00:00Z",participants_count:88,max_participants:200,fee:0,cover_image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500",status:"ongoing"},{id:2,name:"产品经理线下交流沙龙",type:"offline",start_time:"2024-06-15T14:00:00Z",end_time:"2024-06-15T17:00:00Z",participants_count:45,max_participants:50,fee:99,cover_image:"https://images.unsplash.com/photo-1556761175-5973dc0f32e7?w=500",status:"upcoming"},{id:3,name:"30天健身打卡挑战",type:"check-in",start_time:"2024-05-01T00:00:00Z",end_time:"2024-05-30T23:59:59Z",participants_count:152,max_participants:200,fee:19.9,cover_image:"https://images.unsplash.com/photo-1541534401786-20772b48a48c?w=500",status:"finished"}],a.value=!1},500)},p=()=>{},q=()=>{n.value=null,t.value=!0},z=e=>{const{action:a,event:t}=e;"poster"===a?c.success(`正在为活动 "${t.name}" 生成推广海报...`):"copy"===a?(c.success(`活动 "${t.name}" 已复制`),m()):"delete"===a&&$.confirm(`确定要删除活动 "${t.name}" 吗？`,"警告",{type:"warning"}).then(()=>{s.value=s.value.filter(e=>e.id!==t.id),c.success("活动删除成功")}).catch(()=>{})},N=()=>{m()};return P(()=>{m()}),(e,a)=>{const s=y,m=r,$=f,H=_,S=g,P=w,X=U,pe=D,ce=x,_e=v,fe=u,ve=o,ge=Z;return J(),R("div",ae,[B(E,null,{header:L(()=>[M("div",te,[M("h1",null,[B(s,null,{default:L(()=>[B(K(Y))]),_:1}),a[2]||(a[2]=I(" 活动与营销管理 ",-1))]),a[3]||(a[3]=M("p",null,"创建、管理并推广您的社群活动，提升用户参与度和活跃度。",-1))])]),default:L(()=>[M("div",le,[B(m,{type:"primary",icon:"Plus",onClick:q},{default:L(()=>a[4]||(a[4]=[I(" 创建新活动 ",-1)])),_:1,__:[4]}),B(H,{modelValue:l.value,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),onTabClick:p,class:"event-tabs"},{default:L(()=>[B($,{label:"全部活动",name:"all"}),B($,{label:"进行中",name:"ongoing"}),B($,{label:"未开始",name:"upcoming"}),B($,{label:"已结束",name:"finished"})]),_:1},8,["modelValue"])]),M("div",se,[B(ve,{gutter:20},{default:L(()=>[(J(!0),R(G,null,Q(i.value,e=>(J(),F(fe,{span:8,key:e.id,class:"event-col"},{default:L(()=>[B(_e,{class:"event-card",shadow:"hover"},{default:L(()=>[M("div",ne,[M("img",{src:e.cover_image,class:"event-image"},null,8,ie),B(S,{class:"event-status-tag",type:d(e).type},{default:L(()=>[I(b(d(e).text),1)]),_:2},1032,["type"])]),M("div",oe,[M("h3",ue,b(e.name),1),M("p",de,[B(s,null,{default:L(()=>[B(K(h))]),_:1}),I(" "+b(K(W)(e.start_time))+" - "+b(K(W)(e.end_time)),1)]),M("div",me,[M("span",null,[B(s,null,{default:L(()=>[B(K(V))]),_:1}),I(" "+b(e.participants_count)+" / "+b(e.max_participants)+" 人",1)]),M("span",null,[B(s,null,{default:L(()=>[B(K(j))]),_:1}),I(" ¥"+b(e.fee),1)])])]),M("div",re,[B(P,null,{default:L(()=>[B(m,{type:"primary",size:"small",onClick:a=>(e=>{n.value=JSON.parse(JSON.stringify(e)),t.value=!0})(e)},{default:L(()=>a[5]||(a[5]=[I("编辑",-1)])),_:2,__:[5]},1032,["onClick"]),B(m,{type:"success",size:"small",onClick:a=>(e=>{c.info(`查看活动 "${e.name}" 的数据分析...`)})(e)},{default:L(()=>a[6]||(a[6]=[I("数据",-1)])),_:2,__:[6]},1032,["onClick"])]),_:2},1024),B(ce,{onCommand:z},{dropdown:L(()=>[B(pe,null,{default:L(()=>[B(X,{command:{action:"poster",event:e}},{default:L(()=>[B(s,null,{default:L(()=>[B(K(T))]),_:1}),a[8]||(a[8]=I("生成海报 ",-1))]),_:2,__:[8]},1032,["command"]),B(X,{command:{action:"copy",event:e}},{default:L(()=>[B(s,null,{default:L(()=>[B(K(C))]),_:1}),a[9]||(a[9]=I("复制活动 ",-1))]),_:2,__:[9]},1032,["command"]),B(X,{command:{action:"delete",event:e},divided:""},{default:L(()=>[B(s,null,{default:L(()=>[B(K(O))]),_:1}),a[10]||(a[10]=I("删除活动 ",-1))]),_:2,__:[10]},1032,["command"])]),_:2},1024)]),default:L(()=>[B(m,{size:"small"},{default:L(()=>[a[7]||(a[7]=I(" 更多",-1)),B(s,{class:"el-icon--right"},{default:L(()=>[B(K(k))]),_:1})]),_:1,__:[7]})]),_:2},1024)])]),_:2},1024)]),_:2},1024))),128))]),_:1}),0===i.value.length?(J(),F(ge,{key:0,description:"暂无活动"})):A("",!0)])]),_:1}),B(ee,{modelValue:t.value,"onUpdate:modelValue":a[1]||(a[1]=e=>t.value=e),"event-data":n.value,onSuccess:N},null,8,["modelValue","event-data"])])}}},[["__scopeId","data-v-2fe96726"]]);export{pe as default};
