import{_ as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                     *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                      *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                       *//* empty css                        *//* empty css               */import{b2 as e,b4 as l,b3 as t,bo as o,bv as s,as as n,T as r,am as u,ai as d,a4 as i,b6 as p,b7 as _,U as c,b8 as m,bj as f,bk as v,bc as b,bd as g,bl as h,aW as y,aV as C,au as w,Q as j,R as k}from"./element-plus-DcSKpKA8.js";import{S as x}from"./StatCard-WpSR56Tk.js";import{L as V}from"./LineChart-Ba008-uu.js";import{D as F}from"./DoughnutChart-JEDVUFw0.js";import{a as z,b as A,e as U}from"./substation-Bk2UQYn7.js";import{r as D,L as E,c as S,e as L,k as P,l as M,t as R,E as Z,z as q,D as T,u as W,A as Y,y as B}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";/* empty css                                                                 */import"./chart-Bup65vvO.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const G={class:"substation-finance"},H={class:"action-bar"},I={class:"amount"},K={class:"amount"},N={class:"pagination"},Q=a({__name:"SubstationFinance",setup(a){const Q=D(!1),X=D("month"),$=D(1),J=D(20),O=D(!1),aa=D(!1),ea=D({}),la=D([]),ta=D({data:[],total:0}),oa=E({start_date:"",end_date:"",format:"array"}),sa=S(()=>{const a=ea.value.trends||[];return{labels:a.map(a=>a.date),datasets:[{label:"收入",data:a.map(a=>a.revenue),borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4},{label:"利润",data:a.map(a=>a.profit),borderColor:"#67C23A",backgroundColor:"rgba(103, 194, 58, 0.1)",tension:.4}]}}),na=S(()=>{const a=(ea.value.commission||{}).commission_by_level||[];return{labels:a.map(a=>a.commission_level),datasets:[{data:a.map(a=>a.total_commission),backgroundColor:["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399"]}]}}),ra={responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0}}},ua={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}},da=async()=>{try{Q.value=!0;const a=await z({period:X.value});ea.value=a.data,la.value=a.data.top_agents||[]}catch(a){j.error("加载财务统计失败")}finally{Q.value=!1}},ia=async()=>{try{const a=await A({page:$.value,limit:J.value});ta.value=a.data}catch(a){j.error("加载结算记录失败")}},pa=()=>{da()},_a=a=>{J.value=a,ia()},ca=a=>{$.value=a,ia()},ma=()=>{O.value=!0;const a=new Date;oa.start_date=new Date(a.getFullYear(),a.getMonth(),1),oa.end_date=new Date(a.getFullYear(),a.getMonth()+1,0)},fa=async()=>{try{aa.value=!0;await U({start_date:ga(oa.start_date),end_date:ga(oa.end_date),format:oa.format});"excel"===oa.format?j.success("报表生成成功，正在下载..."):j.success("报表生成成功"),O.value=!1}catch(a){j.error("生成报表失败")}finally{aa.value=!1}},va=async()=>{try{await k.confirm("确定要进行批量结算吗？","确认操作",{type:"warning"}),j.info("请先选择要结算的记录")}catch(a){}},ba=()=>{j.info("导出功能开发中...")},ga=a=>a?new Date(a).toLocaleString("zh-CN"):"",ha=a=>({pending:"待结算",confirmed:"已结算",cancelled:"已取消"}[a]||"未知");return L(()=>{da(),ia()}),(a,k)=>{const z=e,A=l,U=s,D=o,E=t,S=r,L=n,da=_,ia=p,ya=m,Ca=v,wa=h,ja=g,ka=C,xa=y,Va=b,Fa=w,za=f;return M(),P("div",G,[k[25]||(k[25]=R("div",{class:"page-header"},[R("h2",null,"分站财务管理"),R("p",null,"管理分站的财务数据、佣金结算和收入统计")],-1)),Z(A,{gutter:20,class:"stats-row"},{default:q(()=>[Z(z,{span:6},{default:q(()=>[Z(x,{title:"总收入",value:ea.value.overview?.total_revenue||0,icon:"Money",color:"#67C23A",suffix:"元"},null,8,["value"])]),_:1}),Z(z,{span:6},{default:q(()=>[Z(x,{title:"总佣金",value:ea.value.overview?.total_commission||0,icon:"Coin",color:"#E6A23C",suffix:"元"},null,8,["value"])]),_:1}),Z(z,{span:6},{default:q(()=>[Z(x,{title:"净利润",value:ea.value.overview?.net_profit||0,icon:"TrendCharts",color:"#409EFF",suffix:"元"},null,8,["value"])]),_:1}),Z(z,{span:6},{default:q(()=>[Z(x,{title:"活跃用户",value:ea.value.overview?.active_users||0,icon:"User",color:"#F56C6C",suffix:"人"},null,8,["value"])]),_:1})]),_:1}),R("div",H,[Z(A,{gutter:20},{default:q(()=>[Z(z,{span:12},{default:q(()=>[Z(E,null,{header:q(()=>k[8]||(k[8]=[R("span",null,"时间筛选",-1)])),default:q(()=>[Z(D,{modelValue:X.value,"onUpdate:modelValue":k[0]||(k[0]=a=>X.value=a),onChange:pa},{default:q(()=>[Z(U,{label:"today"},{default:q(()=>k[9]||(k[9]=[T("今日",-1)])),_:1,__:[9]}),Z(U,{label:"week"},{default:q(()=>k[10]||(k[10]=[T("本周",-1)])),_:1,__:[10]}),Z(U,{label:"month"},{default:q(()=>k[11]||(k[11]=[T("本月",-1)])),_:1,__:[11]}),Z(U,{label:"quarter"},{default:q(()=>k[12]||(k[12]=[T("本季度",-1)])),_:1,__:[12]}),Z(U,{label:"year"},{default:q(()=>k[13]||(k[13]=[T("本年",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),Z(z,{span:12},{default:q(()=>[Z(E,null,{header:q(()=>k[14]||(k[14]=[R("span",null,"快捷操作",-1)])),default:q(()=>[Z(L,{type:"primary",onClick:ma},{default:q(()=>[Z(S,null,{default:q(()=>[Z(W(u))]),_:1}),k[15]||(k[15]=T(" 生成报表 ",-1))]),_:1,__:[15]}),Z(L,{type:"success",onClick:va},{default:q(()=>[Z(S,null,{default:q(()=>[Z(W(d))]),_:1}),k[16]||(k[16]=T(" 批量结算 ",-1))]),_:1,__:[16]}),Z(L,{type:"info",onClick:ba},{default:q(()=>[Z(S,null,{default:q(()=>[Z(W(i))]),_:1}),k[17]||(k[17]=T(" 导出数据 ",-1))]),_:1,__:[17]})]),_:1})]),_:1})]),_:1})]),Z(A,{gutter:20,class:"charts-row"},{default:q(()=>[Z(z,{span:12},{default:q(()=>[Z(E,null,{header:q(()=>k[18]||(k[18]=[R("span",null,"收入趋势",-1)])),default:q(()=>[Z(V,{data:sa.value,options:ra,height:"300px"},null,8,["data"])]),_:1})]),_:1}),Z(z,{span:12},{default:q(()=>[Z(E,null,{header:q(()=>k[19]||(k[19]=[R("span",null,"佣金分布",-1)])),default:q(()=>[Z(F,{data:na.value,options:ua,height:"300px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),Z(E,{class:"top-agents-card"},{header:q(()=>k[20]||(k[20]=[R("span",null,"顶级代理商排行",-1)])),default:q(()=>[Z(ia,{data:la.value,stripe:""},{default:q(()=>[Z(da,{prop:"agent.agent_name",label:"代理商名称"}),Z(da,{prop:"agent.agent_code",label:"代理商编码"}),Z(da,{prop:"total_commission",label:"总佣金",sortable:""},{default:q(({row:a})=>[R("span",I,"¥"+c(a.total_commission),1)]),_:1}),Z(da,{prop:"order_count",label:"订单数量",sortable:""}),Z(da,{label:"操作"},{default:q(({row:a})=>[Z(L,{size:"small",onClick:e=>{return l=a.agent.id,void j.info(`查看代理商 ${l} 详情`);var l}},{default:q(()=>k[21]||(k[21]=[T(" 查看详情 ",-1)])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),Z(E,{class:"settlement-records"},{header:q(()=>k[22]||(k[22]=[R("span",null,"结算记录",-1)])),default:q(()=>[Y((M(),B(ia,{data:ta.value.data,stripe:""},{default:q(()=>[Z(da,{prop:"agent.agent_name",label:"代理商"}),Z(da,{prop:"order.order_no",label:"订单号"}),Z(da,{prop:"commission_amount",label:"佣金金额"},{default:q(({row:a})=>[R("span",K,"¥"+c(a.commission_amount),1)]),_:1}),Z(da,{prop:"settled_at",label:"结算时间"},{default:q(({row:a})=>[T(c(ga(a.settled_at)),1)]),_:1}),Z(da,{prop:"status",label:"状态"},{default:q(({row:a})=>{return[Z(ya,{type:(e=a.status,{pending:"warning",confirmed:"success",cancelled:"danger"}[e]||"info")},{default:q(()=>[T(c(ha(a.status)),1)]),_:2},1032,["type"])];var e}),_:1})]),_:1},8,["data"])),[[za,Q.value]]),R("div",N,[Z(Ca,{"current-page":$.value,"onUpdate:currentPage":k[1]||(k[1]=a=>$.value=a),"page-size":J.value,"onUpdate:pageSize":k[2]||(k[2]=a=>J.value=a),total:ta.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_a,onCurrentChange:ca},null,8,["current-page","page-size","total"])])]),_:1}),Z(Fa,{modelValue:O.value,"onUpdate:modelValue":k[7]||(k[7]=a=>O.value=a),title:"生成财务报表",width:"500px"},{footer:q(()=>[Z(L,{onClick:k[6]||(k[6]=a=>O.value=!1)},{default:q(()=>k[23]||(k[23]=[T("取消",-1)])),_:1,__:[23]}),Z(L,{type:"primary",onClick:fa,loading:aa.value},{default:q(()=>k[24]||(k[24]=[T(" 生成报表 ",-1)])),_:1,__:[24]},8,["loading"])]),default:q(()=>[Z(Va,{model:oa,"label-width":"100px"},{default:q(()=>[Z(ja,{label:"开始日期"},{default:q(()=>[Z(wa,{modelValue:oa.start_date,"onUpdate:modelValue":k[3]||(k[3]=a=>oa.start_date=a),type:"date",placeholder:"选择开始日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),Z(ja,{label:"结束日期"},{default:q(()=>[Z(wa,{modelValue:oa.end_date,"onUpdate:modelValue":k[4]||(k[4]=a=>oa.end_date=a),type:"date",placeholder:"选择结束日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),Z(ja,{label:"报表格式"},{default:q(()=>[Z(xa,{modelValue:oa.format,"onUpdate:modelValue":k[5]||(k[5]=a=>oa.format=a),style:{width:"100%"}},{default:q(()=>[Z(ka,{label:"在线查看",value:"array"}),Z(ka,{label:"Excel文件",value:"excel"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-ef608016"]]);export{Q as default};
