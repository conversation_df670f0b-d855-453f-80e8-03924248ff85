import{_ as e}from"./index-D2bI4m-v.js";/* empty css               *//* empty css                        *//* empty css                          *//* empty css                    *//* empty css                  *//* empty css                     *//* empty css                */import{r as l,e as a,k as t,l as u,E as d,z as o,J as n,D as i,t as s,A as r,y as m,n as _,L as p,u as c,B as f,F as v,G as y}from"./vue-vendor-DGsK9sC4.js";import{b2 as b,b3 as h,b6 as V,b7 as g,as as w,c7 as k,bj as x,bm as U,U as C,b4 as j,bc as z,bd as I,aH as T,au as R,R as D,Q as S,bl as P,aW as q,aV as A,b8 as F,bk as $,bf as B,cf as N,be as H,aP as L,aA as M,S as O,T as W,a0 as G,W as J,ao as K,af as Z,ae as E,ak as Q,am as X,aq as Y,bg as ee,bh as le}from"./element-plus-DcSKpKA8.js";import{I as ae}from"./ImageUpload-DAXj9MB_.js";import{g as te,u as ue,t as de}from"./system-CZ_rLt3R.js";/* empty css                   *//* empty css                 *//* empty css                *//* empty css                *//* empty css                        *//* empty css               *//* empty css                      *//* empty css                  *//* empty css                       */import{f as oe}from"./format-3eU4VJ9V.js";/* empty css                       */import"./utils-4VKArNEK.js";/* empty css                  *//* empty css                    */const ne={class:"role-settings-container"},ie={class:"card-header"},se={class:"card-header"},re={key:0},me=e({__name:"RoleSettings",setup(e){const p=l([]),c=l([]),f=l(null),v=l([]),y=l(null),P=l(!1),q=l(!1),A=l(!1),F=l(!1),$=l({id:null,name:"",description:""}),B=l(null),N={name:[{required:!0,message:"角色名称不能为空",trigger:"blur"}]},H=e=>{f.value=e,P.value=!0,setTimeout(()=>{1===e.id?v.value=["dashboard","community","community:list","community:rules","community:events","system","system:settings","system:users"]:2===e.id?v.value=["dashboard","community","community:list","community:rules","community:events"]:v.value=[],_(()=>{y.value?.setCheckedKeys(v.value,!1)}),P.value=!1},300)},L=()=>{q.value=!0;const e=[...y.value.getCheckedKeys(),...y.value.getHalfCheckedKeys()];console.log("Saving permissions for role:",f.value.name,e),setTimeout(()=>{S.success("权限保存成功"),q.value=!1},500)},M=()=>{F.value=!1,$.value={id:null,name:"",description:""},A.value=!0},O=()=>{B.value.validate(e=>{if(e){if(F.value){const e=p.value.findIndex(e=>e.id===$.value.id);-1!==e&&(p.value[e]={...$.value})}else p.value.push({...$.value,id:Date.now()});S.success("操作成功"),A.value=!1}})};return a(()=>{p.value=[{id:1,name:"超级管理员",description:"拥有所有权限"},{id:2,name:"社群运营",description:"管理社群相关功能"},{id:3,name:"财务专员",description:"处理财务相关事务"}],c.value=[{id:"dashboard",name:"仪表盘",children:[]},{id:"community",name:"社群管理",children:[{id:"community:list",name:"社群列表"},{id:"community:rules",name:"自动化规则"},{id:"community:events",name:"活动管理"}]},{id:"system",name:"系统管理",children:[{id:"system:settings",name:"系统设置"},{id:"system:users",name:"用户管理"}]}]}),(e,l)=>{const a=w,_=g,W=V,G=h,J=b,K=k,Z=U,E=j,Q=T,X=I,Y=z,ee=R,le=x;return u(),t("div",ne,[d(E,{gutter:20},{default:o(()=>[d(J,{span:8},{default:o(()=>[d(G,{class:"box-card"},{header:o(()=>[s("div",ie,[l[5]||(l[5]=s("span",null,"角色列表",-1)),d(a,{type:"primary",icon:"Plus",onClick:M,size:"small"},{default:o(()=>l[4]||(l[4]=[i("新建角色",-1)])),_:1,__:[4]})])]),default:o(()=>[d(W,{data:p.value,onRowClick:H,"highlight-current-row":""},{default:o(()=>[d(_,{prop:"name",label:"角色名称"}),d(_,{label:"操作",width:"100"},{default:o(({row:e})=>[d(a,{link:"",type:"primary",size:"small",onClick:n(l=>{return a=e,F.value=!0,$.value={...a},void(A.value=!0);var a},["stop"])},{default:o(()=>l[6]||(l[6]=[i("编辑",-1)])),_:2,__:[6]},1032,["onClick"]),d(a,{link:"",type:"danger",size:"small",onClick:n(l=>{return a=e,void D.confirm(`确定要删除角色 "${a.name}" 吗？`,"警告",{type:"warning"}).then(()=>{p.value=p.value.filter(e=>e.id!==a.id),S.success("角色删除成功"),f.value?.id===a.id&&(f.value=null)}).catch(()=>{});var a},["stop"])},{default:o(()=>l[7]||(l[7]=[i("删除",-1)])),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),d(J,{span:16},{default:o(()=>[d(G,{class:"box-card"},{header:o(()=>[s("div",se,[s("span",null,"权限分配 - "+C(f.value?f.value.name:"请选择角色"),1),d(a,{type:"primary",onClick:L,disabled:!f.value,loading:q.value},{default:o(()=>l[8]||(l[8]=[i("保存权限",-1)])),_:1,__:[8]},8,["disabled","loading"])])]),default:o(()=>[f.value?r((u(),t("div",re,[d(K,{ref_key:"permissionTreeRef",ref:y,data:c.value,"show-checkbox":"","node-key":"id","default-expand-all":!0,props:{label:"name",children:"children"},"default-checked-keys":v.value},null,8,["data","default-checked-keys"])])),[[le,P.value]]):(u(),m(Z,{key:1,description:"请先从左侧选择一个角色"}))]),_:1})]),_:1})]),_:1}),d(ee,{modelValue:A.value,"onUpdate:modelValue":l[3]||(l[3]=e=>A.value=e),title:F.value?"编辑角色":"新建角色",width:"400px"},{footer:o(()=>[d(a,{onClick:l[2]||(l[2]=e=>A.value=!1)},{default:o(()=>l[9]||(l[9]=[i("取消",-1)])),_:1,__:[9]}),d(a,{type:"primary",onClick:O},{default:o(()=>l[10]||(l[10]=[i("确定",-1)])),_:1,__:[10]})]),default:o(()=>[d(Y,{model:$.value,ref_key:"roleFormRef",ref:B,rules:N,"label-width":"80px"},{default:o(()=>[d(X,{label:"角色名称",prop:"name"},{default:o(()=>[d(Q,{modelValue:$.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>$.value.name=e),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),d(X,{label:"描述",prop:"description"},{default:o(()=>[d(Q,{type:"textarea",modelValue:$.value.description,"onUpdate:modelValue":l[1]||(l[1]=e=>$.value.description=e),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-2d1958f5"]]),_e={class:"audit-logs-container"},pe={class:"filter-container"},ce={class:"pagination-container"},fe={key:0},ve={key:0},ye={class:"code-block"},be=e({__name:"AuditLogs",setup(e){const n=l(!1),_=l([]),v=p({user:"",dateRange:[],actionType:""}),y=p({page:1,limit:10,total:0}),b=l(!1),k=l(null),U=Array.from({length:50}).map((e,l)=>{const a=[{id:1,name:"Admin"},{id:2,name:"Operator"}],t=["create","update","delete","login"][l%4];return{id:l+1,timestamp:new Date(Date.now()-3600*l*1e3).toISOString(),user:a[l%2],ip_address:`192.168.1.${l+1}`,action_type:t,description:`${a[l%2].name} ${t}了文章 '文章标题${l+1}'`,user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) ...",data_changes:"login"!==t?{before:{status:"draft"},after:{status:"published"}}:null}}),j=()=>{n.value=!0,setTimeout(()=>{y.total=U.length;const e=(y.page-1)*y.limit,l=e+y.limit;_.value=U.slice(e,l),n.value=!1},500)};return a(()=>{j()}),(e,l)=>{const a=T,p=P,U=A,z=q,I=w,D=g,S=F,B=V,N=$,H=h,L=R,M=x;return u(),t("div",_e,[d(H,null,{header:o(()=>l[6]||(l[6]=[s("h3",null,"系统日志",-1)])),default:o(()=>[s("div",pe,[d(a,{modelValue:v.user,"onUpdate:modelValue":l[0]||(l[0]=e=>v.user=e),placeholder:"操作人",style:{width:"180px"},clearable:""},null,8,["modelValue"]),d(p,{modelValue:v.dateRange,"onUpdate:modelValue":l[1]||(l[1]=e=>v.dateRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",style:{width:"380px","margin-left":"10px"}},null,8,["modelValue"]),d(z,{modelValue:v.actionType,"onUpdate:modelValue":l[2]||(l[2]=e=>v.actionType=e),placeholder:"操作类型",style:{width:"150px","margin-left":"10px"},clearable:""},{default:o(()=>[d(U,{label:"创建",value:"create"}),d(U,{label:"更新",value:"update"}),d(U,{label:"删除",value:"delete"}),d(U,{label:"登录",value:"login"})]),_:1},8,["modelValue"]),d(I,{type:"primary",icon:"Search",onClick:j,style:{"margin-left":"10px"}},{default:o(()=>l[7]||(l[7]=[i("搜索",-1)])),_:1,__:[7]})]),r((u(),m(B,{data:_.value,style:{width:"100%","margin-top":"20px"}},{default:o(()=>[d(D,{prop:"timestamp",label:"操作时间",width:"180"},{default:o(({row:e})=>[i(C(c(oe)(e.timestamp)),1)]),_:1}),d(D,{prop:"user.name",label:"操作人",width:"120"}),d(D,{prop:"ip_address",label:"IP地址",width:"150"}),d(D,{label:"操作类型",width:"100"},{default:o(({row:e})=>{return[d(S,{type:(l=e.action_type,{create:"success",update:"primary",delete:"danger",login:"info"}[l]||"default")},{default:o(()=>[i(C(e.action_type.toUpperCase()),1)]),_:2},1032,["type"])];var l}),_:1}),d(D,{prop:"description",label:"操作描述"}),d(D,{label:"操作",width:"100"},{default:o(({row:e})=>[d(I,{link:"",type:"primary",size:"small",onClick:l=>{return a=e,k.value=a,void(b.value=!0);var a}},{default:o(()=>l[8]||(l[8]=[i("详情",-1)])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[M,n.value]]),s("div",ce,[d(N,{"current-page":y.page,"onUpdate:currentPage":l[3]||(l[3]=e=>y.page=e),"page-size":y.limit,"onUpdate:pageSize":l[4]||(l[4]=e=>y.limit=e),total:y.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:j},null,8,["current-page","page-size","total"])])]),_:1}),d(L,{modelValue:b.value,"onUpdate:modelValue":l[5]||(l[5]=e=>b.value=e),title:"日志详情",width:"600px"},{default:o(()=>[k.value?(u(),t("div",fe,[s("p",null,[l[9]||(l[9]=s("strong",null,"操作描述:",-1)),i(" "+C(k.value.description),1)]),s("p",null,[l[10]||(l[10]=s("strong",null,"操作人:",-1)),i(" "+C(k.value.user.name)+" (ID: "+C(k.value.user.id)+")",1)]),s("p",null,[l[11]||(l[11]=s("strong",null,"时间:",-1)),i(" "+C(c(oe)(k.value.timestamp)),1)]),s("p",null,[l[12]||(l[12]=s("strong",null,"IP 地址:",-1)),i(" "+C(k.value.ip_address),1)]),s("p",null,[l[13]||(l[13]=s("strong",null,"User Agent:",-1)),i(" "+C(k.value.user_agent),1)]),k.value.data_changes?(u(),t("div",ve,[l[14]||(l[14]=s("h4",null,"数据变更:",-1)),s("pre",ye,C(JSON.stringify(k.value.data_changes,null,2)),1)])):f("",!0)])):f("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-db8d149e"]]),he={class:"backup-settings-container"},Ve={class:"card-header"},ge=e({__name:"BackupSettings",setup(e){const n=l(!1),_=l(!1),y=l([]),b=p({enabled:!0,frequency:"daily",time:new Date(2023,1,1,2,0,0),retention:7}),k=()=>{n.value=!0,setTimeout(()=>{y.value=[{id:1,timestamp:"2024-06-01T02:00:00Z",filename:"backup-202406010200.zip",size:"128 MB",type:"db"},{id:2,timestamp:"2024-05-31T02:00:00Z",filename:"backup-202405310200.zip",size:"127 MB",type:"db"},{id:3,timestamp:"2024-05-30T15:30:00Z",filename:"manual-backup-202405301530.zip",size:"2.5 GB",type:"files"}],n.value=!1},500)},U=()=>{S.success("自动备份策略已保存")},j=()=>{_.value=!0,S.info("正在创建手动备份，请稍候..."),setTimeout(()=>{_.value=!1,S.success("手动备份创建成功"),k()},3e3)};return a(()=>{k()}),(e,l)=>{const a=B,p=I,k=A,T=q,R=N,P=H,$=w,L=z,M=h,O=g,W=F,G=V,J=x;return u(),t("div",he,[d(M,null,{header:o(()=>l[4]||(l[4]=[s("h3",null,"备份与恢复",-1)])),default:o(()=>[d(M,{shadow:"never",style:{"margin-bottom":"20px"}},{header:o(()=>l[5]||(l[5]=[s("h4",null,"自动备份策略",-1)])),default:o(()=>[d(L,{model:b,"label-width":"120px"},{default:o(()=>[d(p,{label:"启用自动备份"},{default:o(()=>[d(a,{modelValue:b.enabled,"onUpdate:modelValue":l[0]||(l[0]=e=>b.enabled=e)},null,8,["modelValue"])]),_:1}),b.enabled?(u(),t(v,{key:0},[d(p,{label:"备份频率"},{default:o(()=>[d(T,{modelValue:b.frequency,"onUpdate:modelValue":l[1]||(l[1]=e=>b.frequency=e),placeholder:"请选择频率"},{default:o(()=>[d(k,{label:"每天",value:"daily"}),d(k,{label:"每周",value:"weekly"})]),_:1},8,["modelValue"])]),_:1}),d(p,{label:"备份时间"},{default:o(()=>[d(R,{modelValue:b.time,"onUpdate:modelValue":l[2]||(l[2]=e=>b.time=e),format:"HH:mm",placeholder:"选择备份时间"},null,8,["modelValue"])]),_:1}),d(p,{label:"保留备份数量"},{default:o(()=>[d(P,{modelValue:b.retention,"onUpdate:modelValue":l[3]||(l[3]=e=>b.retention=e),min:1,max:30},null,8,["modelValue"]),l[6]||(l[6]=s("span",null," 个",-1))]),_:1,__:[6]})],64)):f("",!0),d(p,null,{default:o(()=>[d($,{type:"primary",onClick:U},{default:o(()=>l[7]||(l[7]=[i("保存策略",-1)])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),_:1}),d(M,{shadow:"never"},{header:o(()=>[s("div",Ve,[l[9]||(l[9]=s("h4",null,"备份历史",-1)),d($,{type:"primary",icon:"Plus",onClick:j,loading:_.value},{default:o(()=>l[8]||(l[8]=[i(" 创建手动备份 ",-1)])),_:1,__:[8]},8,["loading"])])]),default:o(()=>[r((u(),m(G,{data:y.value,style:{width:"100%"}},{default:o(()=>[d(O,{prop:"timestamp",label:"备份时间",width:"180"},{default:o(({row:e})=>[i(C(c(oe)(e.timestamp)),1)]),_:1}),d(O,{prop:"filename",label:"文件名"}),d(O,{prop:"size",label:"文件大小",width:"120"}),d(O,{label:"类型",width:"100"},{default:o(({row:e})=>[d(W,{type:"db"===e.type?"success":"primary"},{default:o(()=>[i(C("db"===e.type?"数据库":"文件"),1)]),_:2},1032,["type"])]),_:1}),d(O,{label:"操作",width:"200",fixed:"right"},{default:o(({row:e})=>[d($,{link:"",type:"primary",icon:"RefreshLeft",size:"small",onClick:l=>{return a=e,void D.confirm(`确定要从备份文件 "${a.filename}" 恢复吗？此操作不可逆，将覆盖现有数据！`,"严重警告",{type:"warning",confirmButtonText:"我确定要恢复"}).then(()=>{S.info("正在执行恢复操作...")}).catch(()=>{});var a}},{default:o(()=>l[10]||(l[10]=[i("恢复",-1)])),_:2,__:[10]},1032,["onClick"]),d($,{link:"",type:"success",icon:"Download",size:"small",onClick:l=>{return a=e,void S.success(`开始下载备份文件: ${a.filename}`);var a}},{default:o(()=>l[11]||(l[11]=[i("下载",-1)])),_:2,__:[11]},1032,["onClick"]),d($,{link:"",type:"danger",icon:"Delete",size:"small",onClick:l=>{return a=e,void D.confirm(`确定要删除备份文件 "${a.filename}" 吗？`,"确认删除",{type:"warning"}).then(()=>{y.value=y.value.filter(e=>e.id!==a.id),S.success("备份文件删除成功")}).catch(()=>{});var a}},{default:o(()=>l[12]||(l[12]=[i("删除",-1)])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[J,n.value]])]),_:1})]),_:1})])}}},[["__scopeId","data-v-b5d48ffa"]]),we={class:"community-settings-container"},ke=e({__name:"CommunitySettings",setup(e){const n=l(!1),r=l(!1),m=p({default_welcome_message:"欢迎 {memberName} 加入 {groupName}！",default_rules:["rule_2"],moderation_enabled:!0,sensitive_words:"广告\n推广\n加V\n私聊",paid_groups_enabled:!0,events_enabled:!0,checkin_enabled:!1}),_=()=>{r.value=!0,setTimeout(()=>{S.success("社群模块设置已保存"),r.value=!1},1e3)};return a(()=>{n.value=!0,setTimeout(()=>{n.value=!1},500)}),(e,l)=>{const a=L,n=T,p=I,c=A,f=q,v=B,y=w,b=z,V=h;return u(),t("div",we,[d(V,null,{header:o(()=>l[7]||(l[7]=[s("h3",null,"社群模块设置",-1)])),default:o(()=>[d(b,{model:m,"label-width":"150px"},{default:o(()=>[d(a,{"content-position":"left"},{default:o(()=>l[8]||(l[8]=[i("默认设置",-1)])),_:1,__:[8]}),d(p,{label:"新成员默认欢迎语"},{default:o(()=>[d(n,{type:"textarea",rows:3,modelValue:m.default_welcome_message,"onUpdate:modelValue":l[0]||(l[0]=e=>m.default_welcome_message=e),placeholder:"可用 {memberName} 指代新成员昵称，{groupName} 指代群组名称"},null,8,["modelValue"])]),_:1}),d(p,{label:"新群组默认规则"},{default:o(()=>[d(f,{modelValue:m.default_rules,"onUpdate:modelValue":l[1]||(l[1]=e=>m.default_rules=e),multiple:"",placeholder:"选择默认应用的自动化规则",style:{width:"100%"}},{default:o(()=>[d(c,{label:"回复“入群”关键词",value:"rule_1"}),d(c,{label:"禁止发送广告链接",value:"rule_2"})]),_:1},8,["modelValue"])]),_:1}),d(a,{"content-position":"left"},{default:o(()=>l[9]||(l[9]=[i("内容审核",-1)])),_:1,__:[9]}),d(p,{label:"新内容先审后发"},{default:o(()=>[d(v,{modelValue:m.moderation_enabled,"onUpdate:modelValue":l[2]||(l[2]=e=>m.moderation_enabled=e)},null,8,["modelValue"]),l[10]||(l[10]=s("div",{class:"form-item-help"},"开启后，所有用户发布的新帖子和评论都需要经过管理员审核才能显示。",-1))]),_:1,__:[10]}),d(p,{label:"敏感词词库"},{default:o(()=>[d(n,{type:"textarea",rows:5,modelValue:m.sensitive_words,"onUpdate:modelValue":l[3]||(l[3]=e=>m.sensitive_words=e),placeholder:"每行一个敏感词"},null,8,["modelValue"]),l[11]||(l[11]=s("div",{class:"form-item-help"},"当用户发布的内容包含这些词汇时，将自动进入审核队列。",-1))]),_:1,__:[11]}),d(a,{"content-position":"left"},{default:o(()=>l[12]||(l[12]=[i("功能开关",-1)])),_:1,__:[12]}),d(p,{label:"允许创建付费群组"},{default:o(()=>[d(v,{modelValue:m.paid_groups_enabled,"onUpdate:modelValue":l[4]||(l[4]=e=>m.paid_groups_enabled=e)},null,8,["modelValue"])]),_:1}),d(p,{label:"开启活动功能"},{default:o(()=>[d(v,{modelValue:m.events_enabled,"onUpdate:modelValue":l[5]||(l[5]=e=>m.events_enabled=e)},null,8,["modelValue"])]),_:1}),d(p,{label:"开启打卡功能"},{default:o(()=>[d(v,{modelValue:m.checkin_enabled,"onUpdate:modelValue":l[6]||(l[6]=e=>m.checkin_enabled=e)},null,8,["modelValue"])]),_:1}),d(p,null,{default:o(()=>[d(y,{type:"primary",onClick:_,loading:r.value},{default:o(()=>l[13]||(l[13]=[i("保存设置",-1)])),_:1,__:[13]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])}}},[["__scopeId","data-v-fff54f81"]]),xe={class:"app-container"},Ue=e({__name:"Settings",setup(e){const n=l("basic"),_=l(),p=l(),V=l(),g=l(),k=l({site_name:"",site_description:"",site_keywords:"",site_logo:"",site_favicon:"",contact_email:"",contact_phone:"",icp_number:""}),x=l({wechat_enabled:!1,wechat_app_id:"",wechat_mch_id:"",wechat_key:"",alipay_enabled:!1,alipay_app_id:"",alipay_private_key:"",alipay_public_key:""}),U=l({email_enabled:!0,sms_enabled:!1,wechat_enabled:!1,system_enabled:!0,user_register:["system"],new_order:["email","system"],withdrawal_request:["email","system"]}),C=l({login_captcha:!0,register_captcha:!0,password_strength:!0,login_attempts:5,session_timeout:120,ip_whitelist:""}),R={site_name:[{required:!0,message:"请输入网站名称",trigger:"blur"}],contact_email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}]},D=e=>{n.value=e},P=async()=>{try{await _.value.validate(),await ue("basic",k.value),S.success("基础设置保存成功")}catch(e){console.error("保存基础设置失败:",e),S.error("保存基础设置失败")}},q=()=>{_.value.resetFields()},A=async()=>{try{await ue("payment",x.value),S.success("支付设置保存成功")}catch(e){console.error("保存支付设置失败:",e),S.error("保存支付设置失败")}},F=async()=>{try{await de(x.value),S.success("支付配置测试通过")}catch(e){console.error("支付配置测试失败:",e),S.error("支付配置测试失败")}},$=async()=>{try{await ue("notification",U.value),S.success("通知设置保存成功")}catch(e){console.error("保存通知设置失败:",e),S.error("保存通知设置失败")}},N=async()=>{try{await ue("security",C.value),S.success("安全设置保存成功")}catch(e){console.error("保存安全设置失败:",e),S.error("保存安全设置失败")}};return a(()=>{(async()=>{try{const{data:e}=await te();k.value={...k.value,...e.basic},x.value={...x.value,...e.payment},U.value={...U.value,...e.notification},C.value={...C.value,...e.security}}catch(e){console.error("获取系统设置失败:",e),S.error("获取系统设置失败")}})()}),(e,l)=>{const a=W,S=O,te=J,ue=M,de=h,oe=b,ne=T,ie=I,se=w,re=z,_e=L,pe=B,ce=le,fe=ee,ve=H,ye=j;return u(),t("div",xe,[d(ye,{gutter:20},{default:o(()=>[d(oe,{span:6},{default:o(()=>[d(de,null,{header:o(()=>l[30]||(l[30]=[s("h3",null,"系统设置",-1)])),default:o(()=>[d(ue,{modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=e=>n.value=e),class:"settings-menu",onSelect:D},{default:o(()=>[d(S,{index:"basic"},{default:o(()=>[d(a,null,{default:o(()=>[d(c(G))]),_:1}),l[31]||(l[31]=s("span",null,"基础设置",-1))]),_:1,__:[31]}),d(te,{index:"access"},{title:o(()=>[d(a,null,{default:o(()=>[d(c(K))]),_:1}),l[32]||(l[32]=s("span",null,"访问控制",-1))]),default:o(()=>[d(S,{index:"roles"},{default:o(()=>l[33]||(l[33]=[i("角色与权限",-1)])),_:1,__:[33]})]),_:1}),d(S,{index:"security"},{default:o(()=>[d(a,null,{default:o(()=>[d(c(Z))]),_:1}),l[34]||(l[34]=s("span",null,"安全设置",-1))]),_:1,__:[34]}),d(te,{index:"integrations"},{title:o(()=>[d(a,null,{default:o(()=>[d(c(E))]),_:1}),l[35]||(l[35]=s("span",null,"集成设置",-1))]),default:o(()=>[d(S,{index:"payment"},{default:o(()=>l[36]||(l[36]=[i("支付设置",-1)])),_:1,__:[36]}),d(S,{index:"notification"},{default:o(()=>l[37]||(l[37]=[i("通知设置",-1)])),_:1,__:[37]}),d(S,{index:"storage"},{default:o(()=>l[38]||(l[38]=[i("存储设置",-1)])),_:1,__:[38]})]),_:1}),d(te,{index:"modules"},{title:o(()=>[d(a,null,{default:o(()=>[d(c(Q))]),_:1}),l[39]||(l[39]=s("span",null,"模块设置",-1))]),default:o(()=>[d(S,{index:"community_settings"},{default:o(()=>l[40]||(l[40]=[i("社群设置",-1)])),_:1,__:[40]})]),_:1}),d(S,{index:"audit_logs"},{default:o(()=>[d(a,null,{default:o(()=>[d(c(X))]),_:1}),l[41]||(l[41]=s("span",null,"系统日志",-1))]),_:1,__:[41]}),d(S,{index:"backup"},{default:o(()=>[d(a,null,{default:o(()=>[d(c(Y))]),_:1}),l[42]||(l[42]=s("span",null,"备份与恢复",-1))]),_:1,__:[42]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(oe,{span:18},{default:o(()=>[r(d(de,null,{header:o(()=>l[43]||(l[43]=[s("h3",null,"基础设置",-1)])),default:o(()=>[d(re,{ref_key:"basicFormRef",ref:_,model:k.value,rules:R,"label-width":"120px"},{default:o(()=>[d(ie,{label:"网站名称",prop:"site_name"},{default:o(()=>[d(ne,{modelValue:k.value.site_name,"onUpdate:modelValue":l[1]||(l[1]=e=>k.value.site_name=e),placeholder:"请输入网站名称"},null,8,["modelValue"])]),_:1}),d(ie,{label:"网站描述",prop:"site_description"},{default:o(()=>[d(ne,{modelValue:k.value.site_description,"onUpdate:modelValue":l[2]||(l[2]=e=>k.value.site_description=e),type:"textarea",rows:3,placeholder:"请输入网站描述"},null,8,["modelValue"])]),_:1}),d(ie,{label:"网站关键词",prop:"site_keywords"},{default:o(()=>[d(ne,{modelValue:k.value.site_keywords,"onUpdate:modelValue":l[3]||(l[3]=e=>k.value.site_keywords=e),placeholder:"请输入网站关键词，用逗号分隔"},null,8,["modelValue"])]),_:1}),d(ie,{label:"网站Logo",prop:"site_logo"},{default:o(()=>[d(ae,{modelValue:k.value.site_logo,"onUpdate:modelValue":l[4]||(l[4]=e=>k.value.site_logo=e),limit:1},null,8,["modelValue"])]),_:1}),d(ie,{label:"网站图标",prop:"site_favicon"},{default:o(()=>[d(ae,{modelValue:k.value.site_favicon,"onUpdate:modelValue":l[5]||(l[5]=e=>k.value.site_favicon=e),limit:1},null,8,["modelValue"])]),_:1}),d(ie,{label:"联系邮箱",prop:"contact_email"},{default:o(()=>[d(ne,{modelValue:k.value.contact_email,"onUpdate:modelValue":l[6]||(l[6]=e=>k.value.contact_email=e),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1}),d(ie,{label:"联系电话",prop:"contact_phone"},{default:o(()=>[d(ne,{modelValue:k.value.contact_phone,"onUpdate:modelValue":l[7]||(l[7]=e=>k.value.contact_phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),d(ie,{label:"备案号",prop:"icp_number"},{default:o(()=>[d(ne,{modelValue:k.value.icp_number,"onUpdate:modelValue":l[8]||(l[8]=e=>k.value.icp_number=e),placeholder:"请输入备案号"},null,8,["modelValue"])]),_:1}),d(ie,null,{default:o(()=>[d(se,{type:"primary",onClick:P},{default:o(()=>l[44]||(l[44]=[i("保存设置",-1)])),_:1,__:[44]}),d(se,{onClick:q},{default:o(()=>l[45]||(l[45]=[i("重置",-1)])),_:1,__:[45]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[y,"basic"===n.value]]),r(d(de,null,{header:o(()=>l[46]||(l[46]=[s("h3",null,"支付设置",-1)])),default:o(()=>[d(re,{ref_key:"paymentFormRef",ref:p,model:x.value,"label-width":"120px"},{default:o(()=>[d(_e,{"content-position":"left"},{default:o(()=>l[47]||(l[47]=[i("微信支付",-1)])),_:1,__:[47]}),d(ie,{label:"启用微信支付"},{default:o(()=>[d(pe,{modelValue:x.value.wechat_enabled,"onUpdate:modelValue":l[9]||(l[9]=e=>x.value.wechat_enabled=e)},null,8,["modelValue"])]),_:1}),x.value.wechat_enabled?(u(),t(v,{key:0},[d(ie,{label:"应用ID",prop:"wechat_app_id"},{default:o(()=>[d(ne,{modelValue:x.value.wechat_app_id,"onUpdate:modelValue":l[10]||(l[10]=e=>x.value.wechat_app_id=e),placeholder:"请输入微信应用ID"},null,8,["modelValue"])]),_:1}),d(ie,{label:"商户号",prop:"wechat_mch_id"},{default:o(()=>[d(ne,{modelValue:x.value.wechat_mch_id,"onUpdate:modelValue":l[11]||(l[11]=e=>x.value.wechat_mch_id=e),placeholder:"请输入微信商户号"},null,8,["modelValue"])]),_:1}),d(ie,{label:"API密钥",prop:"wechat_key"},{default:o(()=>[d(ne,{modelValue:x.value.wechat_key,"onUpdate:modelValue":l[12]||(l[12]=e=>x.value.wechat_key=e),type:"password",placeholder:"请输入微信API密钥","show-password":""},null,8,["modelValue"])]),_:1})],64)):f("",!0),d(_e,{"content-position":"left"},{default:o(()=>l[48]||(l[48]=[i("支付宝支付",-1)])),_:1,__:[48]}),d(ie,{label:"启用支付宝"},{default:o(()=>[d(pe,{modelValue:x.value.alipay_enabled,"onUpdate:modelValue":l[13]||(l[13]=e=>x.value.alipay_enabled=e)},null,8,["modelValue"])]),_:1}),x.value.alipay_enabled?(u(),t(v,{key:1},[d(ie,{label:"应用ID",prop:"alipay_app_id"},{default:o(()=>[d(ne,{modelValue:x.value.alipay_app_id,"onUpdate:modelValue":l[14]||(l[14]=e=>x.value.alipay_app_id=e),placeholder:"请输入支付宝应用ID"},null,8,["modelValue"])]),_:1}),d(ie,{label:"应用私钥",prop:"alipay_private_key"},{default:o(()=>[d(ne,{modelValue:x.value.alipay_private_key,"onUpdate:modelValue":l[15]||(l[15]=e=>x.value.alipay_private_key=e),type:"textarea",rows:4,placeholder:"请输入支付宝应用私钥"},null,8,["modelValue"])]),_:1}),d(ie,{label:"支付宝公钥",prop:"alipay_public_key"},{default:o(()=>[d(ne,{modelValue:x.value.alipay_public_key,"onUpdate:modelValue":l[16]||(l[16]=e=>x.value.alipay_public_key=e),type:"textarea",rows:4,placeholder:"请输入支付宝公钥"},null,8,["modelValue"])]),_:1})],64)):f("",!0),d(ie,null,{default:o(()=>[d(se,{type:"primary",onClick:A},{default:o(()=>l[49]||(l[49]=[i("保存设置",-1)])),_:1,__:[49]}),d(se,{onClick:F},{default:o(()=>l[50]||(l[50]=[i("测试支付",-1)])),_:1,__:[50]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[y,"payment"===n.value]]),r(d(de,null,{header:o(()=>l[51]||(l[51]=[s("h3",null,"通知设置",-1)])),default:o(()=>[d(re,{ref_key:"notificationFormRef",ref:V,model:U.value,"label-width":"120px"},{default:o(()=>[d(ie,{label:"邮件通知"},{default:o(()=>[d(pe,{modelValue:U.value.email_enabled,"onUpdate:modelValue":l[17]||(l[17]=e=>U.value.email_enabled=e)},null,8,["modelValue"])]),_:1}),d(ie,{label:"短信通知"},{default:o(()=>[d(pe,{modelValue:U.value.sms_enabled,"onUpdate:modelValue":l[18]||(l[18]=e=>U.value.sms_enabled=e)},null,8,["modelValue"])]),_:1}),d(ie,{label:"微信通知"},{default:o(()=>[d(pe,{modelValue:U.value.wechat_enabled,"onUpdate:modelValue":l[19]||(l[19]=e=>U.value.wechat_enabled=e)},null,8,["modelValue"])]),_:1}),d(ie,{label:"系统通知"},{default:o(()=>[d(pe,{modelValue:U.value.system_enabled,"onUpdate:modelValue":l[20]||(l[20]=e=>U.value.system_enabled=e)},null,8,["modelValue"])]),_:1}),d(_e,{"content-position":"left"},{default:o(()=>l[52]||(l[52]=[i("通知事件",-1)])),_:1,__:[52]}),d(ie,{label:"新用户注册"},{default:o(()=>[d(fe,{modelValue:U.value.user_register,"onUpdate:modelValue":l[21]||(l[21]=e=>U.value.user_register=e)},{default:o(()=>[d(ce,{label:"email"},{default:o(()=>l[53]||(l[53]=[i("邮件",-1)])),_:1,__:[53]}),d(ce,{label:"sms"},{default:o(()=>l[54]||(l[54]=[i("短信",-1)])),_:1,__:[54]}),d(ce,{label:"system"},{default:o(()=>l[55]||(l[55]=[i("系统",-1)])),_:1,__:[55]})]),_:1},8,["modelValue"])]),_:1}),d(ie,{label:"新订单"},{default:o(()=>[d(fe,{modelValue:U.value.new_order,"onUpdate:modelValue":l[22]||(l[22]=e=>U.value.new_order=e)},{default:o(()=>[d(ce,{label:"email"},{default:o(()=>l[56]||(l[56]=[i("邮件",-1)])),_:1,__:[56]}),d(ce,{label:"sms"},{default:o(()=>l[57]||(l[57]=[i("短信",-1)])),_:1,__:[57]}),d(ce,{label:"system"},{default:o(()=>l[58]||(l[58]=[i("系统",-1)])),_:1,__:[58]})]),_:1},8,["modelValue"])]),_:1}),d(ie,{label:"提现申请"},{default:o(()=>[d(fe,{modelValue:U.value.withdrawal_request,"onUpdate:modelValue":l[23]||(l[23]=e=>U.value.withdrawal_request=e)},{default:o(()=>[d(ce,{label:"email"},{default:o(()=>l[59]||(l[59]=[i("邮件",-1)])),_:1,__:[59]}),d(ce,{label:"sms"},{default:o(()=>l[60]||(l[60]=[i("短信",-1)])),_:1,__:[60]}),d(ce,{label:"system"},{default:o(()=>l[61]||(l[61]=[i("系统",-1)])),_:1,__:[61]})]),_:1},8,["modelValue"])]),_:1}),d(ie,null,{default:o(()=>[d(se,{type:"primary",onClick:$},{default:o(()=>l[62]||(l[62]=[i("保存设置",-1)])),_:1,__:[62]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[y,"notification"===n.value]]),r(d(de,null,{header:o(()=>l[63]||(l[63]=[s("h3",null,"安全设置",-1)])),default:o(()=>[d(re,{ref_key:"securityFormRef",ref:g,model:C.value,"label-width":"120px"},{default:o(()=>[d(ie,{label:"登录验证码"},{default:o(()=>[d(pe,{modelValue:C.value.login_captcha,"onUpdate:modelValue":l[24]||(l[24]=e=>C.value.login_captcha=e)},null,8,["modelValue"])]),_:1}),d(ie,{label:"注册验证码"},{default:o(()=>[d(pe,{modelValue:C.value.register_captcha,"onUpdate:modelValue":l[25]||(l[25]=e=>C.value.register_captcha=e)},null,8,["modelValue"])]),_:1}),d(ie,{label:"密码强度检查"},{default:o(()=>[d(pe,{modelValue:C.value.password_strength,"onUpdate:modelValue":l[26]||(l[26]=e=>C.value.password_strength=e)},null,8,["modelValue"])]),_:1}),d(ie,{label:"登录失败限制"},{default:o(()=>[d(ve,{modelValue:C.value.login_attempts,"onUpdate:modelValue":l[27]||(l[27]=e=>C.value.login_attempts=e),min:3,max:10,placeholder:"登录失败次数限制"},null,8,["modelValue"]),l[64]||(l[64]=s("span",{style:{"margin-left":"10px"}},"次后锁定账户",-1))]),_:1,__:[64]}),d(ie,{label:"会话超时时间"},{default:o(()=>[d(ve,{modelValue:C.value.session_timeout,"onUpdate:modelValue":l[28]||(l[28]=e=>C.value.session_timeout=e),min:30,max:1440,placeholder:"会话超时时间"},null,8,["modelValue"]),l[65]||(l[65]=s("span",{style:{"margin-left":"10px"}},"分钟",-1))]),_:1,__:[65]}),d(ie,{label:"IP白名单"},{default:o(()=>[d(ne,{modelValue:C.value.ip_whitelist,"onUpdate:modelValue":l[29]||(l[29]=e=>C.value.ip_whitelist=e),type:"textarea",rows:3,placeholder:"请输入IP白名单，每行一个IP或IP段"},null,8,["modelValue"])]),_:1}),d(ie,null,{default:o(()=>[d(se,{type:"primary",onClick:N},{default:o(()=>l[66]||(l[66]=[i("保存设置",-1)])),_:1,__:[66]})]),_:1})]),_:1},8,["model"])]),_:1},512),[[y,"security"===n.value]]),"roles"===n.value?(u(),m(me,{key:0})):f("",!0),"audit_logs"===n.value?(u(),m(be,{key:1})):f("",!0),"backup"===n.value?(u(),m(ge,{key:2})):f("",!0),"community_settings"===n.value?(u(),m(ke,{key:3})):f("",!0)]),_:1})]),_:1})])}}},[["__scopeId","data-v-7f59856e"]]);export{Ue as default};
