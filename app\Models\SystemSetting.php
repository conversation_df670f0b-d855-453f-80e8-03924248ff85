<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

/**
 * 系统设置模型
 * 管理系统配置参数
 */
class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'category',
        'key',
        'value',
        'type',
        'description',
        'options',
        'required',
        'updated_by',
    ];

    protected $casts = [
        'options' => 'array',
        'required' => 'boolean',
    ];

    // 设置类型常量
    const TYPE_STRING = 'string';
    const TYPE_TEXT = 'text';
    const TYPE_INTEGER = 'integer';
    const TYPE_DECIMAL = 'decimal';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_EMAIL = 'email';
    const TYPE_URL = 'url';
    const TYPE_JSON = 'json';
    const TYPE_SELECT = 'select';
    const TYPE_MULTISELECT = 'multiselect';

    /**
     * 更新者关联
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 获取格式化的值
     */
    public function getFormattedValueAttribute()
    {
        return match($this->type) {
            self::TYPE_BOOLEAN => filter_var($this->value, FILTER_VALIDATE_BOOLEAN),
            self::TYPE_INTEGER => (int) $this->value,
            self::TYPE_DECIMAL => (float) $this->value,
            self::TYPE_JSON => json_decode($this->value, true),
            self::TYPE_MULTISELECT => is_string($this->value) ? explode(',', $this->value) : $this->value,
            default => $this->value
        };
    }

    /**
     * 获取设置值（静态方法）
     */
    public static function get(string $key, $default = null, string $category = null)
    {
        $cacheKey = $category ? "system_setting_{$category}_{$key}" : "system_setting_{$key}";
        
        return Cache::tags(['system_settings'])->remember($cacheKey, 3600, function () use ($key, $default, $category) {
            $query = self::where('key', $key);
            
            if ($category) {
                $query->where('category', $category);
            }
            
            $setting = $query->first();
            
            return $setting ? $setting->formatted_value : $default;
        });
    }

    /**
     * 设置值（静态方法）
     */
    public static function set(string $key, $value, string $category = 'general', array $options = [])
    {
        $type = $options['type'] ?? self::detectType($value);
        $description = $options['description'] ?? '';
        $required = $options['required'] ?? false;
        
        // 格式化值
        $formattedValue = self::formatValue($value, $type);
        
        $setting = self::updateOrCreate(
            ['category' => $category, 'key' => $key],
            [
                'value' => $formattedValue,
                'type' => $type,
                'description' => $description,
                'options' => $options['options'] ?? null,
                'required' => $required,
                'updated_by' => auth()->id(),
            ]
        );

        // 清除缓存
        $cacheKey = "system_setting_{$category}_{$key}";
        Cache::tags(['system_settings'])->forget($cacheKey);

        return $setting;
    }

    /**
     * 检测值类型
     */
    private static function detectType($value): string
    {
        if (is_bool($value)) {
            return self::TYPE_BOOLEAN;
        }
        
        if (is_int($value)) {
            return self::TYPE_INTEGER;
        }
        
        if (is_float($value)) {
            return self::TYPE_DECIMAL;
        }
        
        if (is_array($value)) {
            return self::TYPE_JSON;
        }
        
        if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return self::TYPE_EMAIL;
        }
        
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return self::TYPE_URL;
        }
        
        return self::TYPE_STRING;
    }

    /**
     * 格式化值
     */
    private static function formatValue($value, string $type): string
    {
        return match($type) {
            self::TYPE_BOOLEAN => $value ? 'true' : 'false',
            self::TYPE_JSON => is_string($value) ? $value : json_encode($value),
            self::TYPE_MULTISELECT => is_array($value) ? implode(',', $value) : $value,
            default => (string) $value
        };
    }

    /**
     * 获取分类下的所有设置
     */
    public static function getByCategory(string $category): array
    {
        $cacheKey = "system_settings_category_{$category}";
        
        return Cache::tags(['system_settings'])->remember($cacheKey, 3600, function () use ($category) {
            $settings = self::where('category', $category)->get();
            
            $result = [];
            foreach ($settings as $setting) {
                $result[$setting->key] = $setting->formatted_value;
            }
            
            return $result;
        });
    }

    /**
     * 获取所有设置
     */
    public static function getAll(): array
    {
        $cacheKey = 'system_settings_all';
        
        return Cache::tags(['system_settings'])->remember($cacheKey, 3600, function () {
            $settings = self::all()->groupBy('category');
            
            $result = [];
            foreach ($settings as $category => $categorySettings) {
                $result[$category] = [];
                foreach ($categorySettings as $setting) {
                    $result[$category][$setting->key] = $setting->formatted_value;
                }
            }
            
            return $result;
        });
    }

    /**
     * 清除设置缓存
     */
    public static function clearCache(?string $category = null, ?string $key = null)
    {
        if ($category && $key) {
            Cache::tags(['system_settings'])->forget("system_setting_{$category}_{$key}");
        } elseif ($category) {
            Cache::tags(['system_settings'])->forget("system_settings_category_{$category}");
        } else {
            Cache::tags(['system_settings'])->flush();
        }
    }

    /**
     * 验证设置值
     */
    public function validateValue($value): bool
    {
        switch ($this->type) {
            case self::TYPE_BOOLEAN:
                return is_bool($value) || in_array($value, ['true', 'false', '1', '0', 1, 0]);
            
            case self::TYPE_INTEGER:
                return is_numeric($value) && is_int($value + 0);
            
            case self::TYPE_DECIMAL:
                return is_numeric($value);
            
            case self::TYPE_EMAIL:
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            
            case self::TYPE_URL:
                return filter_var($value, FILTER_VALIDATE_URL) !== false;
            
            case self::TYPE_JSON:
                if (is_array($value)) {
                    return true;
                }
                json_decode($value);
                return json_last_error() === JSON_ERROR_NONE;
            
            case self::TYPE_SELECT:
                if (!$this->options || !isset($this->options['values'])) {
                    return true;
                }
                return in_array($value, $this->options['values']);
            
            case self::TYPE_MULTISELECT:
                if (!$this->options || !isset($this->options['values'])) {
                    return true;
                }
                $values = is_array($value) ? $value : explode(',', $value);
                return empty(array_diff($values, $this->options['values']));
            
            default:
                return true;
        }
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：必填设置
     */
    public function scopeRequired($query)
    {
        return $query->where('required', true);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 获取设置类型列表
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_STRING => '字符串',
            self::TYPE_TEXT => '文本',
            self::TYPE_INTEGER => '整数',
            self::TYPE_DECIMAL => '小数',
            self::TYPE_BOOLEAN => '布尔值',
            self::TYPE_EMAIL => '邮箱',
            self::TYPE_URL => '网址',
            self::TYPE_JSON => 'JSON',
            self::TYPE_SELECT => '单选',
            self::TYPE_MULTISELECT => '多选',
        ];
    }

    /**
     * 模型事件
     */
    protected static function boot()
    {
        parent::boot();

        // 保存后清除缓存
        static::saved(function ($setting) {
            self::clearCache($setting->category, $setting->key);
        });

        // 删除后清除缓存
        static::deleted(function ($setting) {
            self::clearCache($setting->category, $setting->key);
        });
    }
}