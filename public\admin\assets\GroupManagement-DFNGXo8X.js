import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css               */import{T as a,aU as t,as as l,aM as s,b2 as o,ap as r,U as i,a_ as n,an as u,bE as d,a6 as c,aO as p,b4 as m,b6 as _,b7 as v,b8 as f,aZ as g,av as b,ax as w,ay as h,bj as y,bk as k,aH as V,b1 as C,aW as x,aV as z,b3 as j,bc as U,bd as q,be as D,au as F,ad as T,Q as E,R as A}from"./element-plus-DcSKpKA8.js";import{r as I,L as B,e as P,k as Q,l as $,t as S,E as L,z as M,D as O,u as R,A as G,y as H,B as N}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const W={class:"group-management"},Z={class:"page-header"},J={class:"header-actions"},K={class:"stat-card"},X={class:"stat-icon",style:{"background-color":"#409EFF20",color:"#409EFF"}},Y={class:"stat-content"},ee={class:"stat-value"},ae={class:"stat-trend positive"},te={class:"stat-card"},le={class:"stat-icon",style:{"background-color":"#67C23A20",color:"#67C23A"}},se={class:"stat-content"},oe={class:"stat-value"},re={class:"stat-trend positive"},ie={class:"stat-card"},ne={class:"stat-icon",style:{"background-color":"#E6A23C20",color:"#E6A23C"}},ue={class:"stat-content"},de={class:"stat-value"},ce={class:"stat-trend positive"},pe={class:"stat-card"},me={class:"stat-icon",style:{"background-color":"#F56C6C20",color:"#F56C6C"}},_e={class:"stat-content"},ve={class:"stat-value"},fe={class:"stat-trend negative"},ge={class:"card-header"},be={class:"header-actions"},we={class:"group-info"},he={class:"group-name"},ye={class:"group-desc"},ke={class:"pagination-wrapper"},Ve={class:"dev-notice"},Ce=e({__name:"GroupManagement",setup(e){const Ce=I(!1),xe=I(!1),ze=I(!1),je=I(!1),Ue=I(""),qe=I(""),De=B({total_groups:12,total_members:1580,active_groups:8,conversion_rate:15.8}),Fe=I([]),Te=B({current_page:1,per_page:20,total:0}),Ee=B({name:"",description:"",platform:"",type:"",max_members:200}),Ae={name:[{required:!0,message:"请输入群组名称",trigger:"blur"}],platform:[{required:!0,message:"请选择平台",trigger:"change"}],type:[{required:!0,message:"请选择类型",trigger:"change"}]},Ie=I(),Be=[{id:1,name:"高端客户VIP群",description:"专为高端客户提供的VIP服务群",platform:"wechat",status:"active",member_count:156,daily_messages:89,conversion_count:12,activity_rate:85,created_at:new Date(Date.now()-2592e6)},{id:2,name:"产品交流群",description:"用户产品使用交流和反馈",platform:"qq",status:"active",member_count:234,daily_messages:156,conversion_count:8,activity_rate:72,created_at:new Date(Date.now()-3888e6)},{id:3,name:"新手指导群",description:"新用户入门指导和答疑",platform:"wechat",status:"inactive",member_count:89,daily_messages:23,conversion_count:3,activity_rate:35,created_at:new Date(Date.now()-5184e6)}],Pe=async()=>{try{Ce.value=!0,await new Promise(e=>setTimeout(e,500));let e=[...Be];Ue.value&&(e=e.filter(e=>e.name.includes(Ue.value)||e.description.includes(Ue.value))),qe.value&&(e=e.filter(e=>e.status===qe.value)),Fe.value=e,Te.total=e.length}catch(e){E.error("加载群组列表失败")}finally{Ce.value=!1}},Qe=()=>{Pe(),E.success("数据已刷新")},$e=function(e,a){let t;return function(...l){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e(...l)},a)}}(()=>{Te.current_page=1,Pe()},500),Se=async e=>{try{await A.confirm(`确定要暂停群组"${e.name}"吗？`,"确认暂停",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.status="paused",E.success("群组已暂停")}catch(a){"cancel"!==a&&E.error("暂停失败")}},Le=async e=>{try{e.status="active",E.success("群组已恢复")}catch(a){E.error("恢复失败")}},Me=async e=>{try{await A.confirm(`确定要删除群组"${e.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=Fe.value.findIndex(a=>a.id===e.id);a>-1&&(Fe.value.splice(a,1),De.total_groups--),E.success("群组删除成功")}catch(a){"cancel"!==a&&E.error("删除失败")}},Oe=async()=>{try{await Ie.value.validate(),xe.value=!0,await new Promise(e=>setTimeout(e,1e3));const e={id:Date.now(),...Ee,status:"active",member_count:0,daily_messages:0,conversion_count:0,activity_rate:0,created_at:new Date};Fe.value.unshift(e),De.total_groups++,Object.keys(Ee).forEach(e=>{Ee[e]="max_members"===e?200:""}),ze.value=!1,E.success("群组创建成功")}catch(e){console.error("创建群组失败:",e)}finally{xe.value=!1}},Re=e=>({wechat:"微信",qq:"QQ",dingtalk:"钉钉",work_wechat:"企微"}[e]||"未知"),Ge=e=>({active:"活跃",inactive:"不活跃",paused:"已暂停"}[e]||"未知");return P(()=>{Pe()}),(e,A)=>{const I=a,B=l,P=o,Be=m,He=V,Ne=z,We=x,Ze=v,Je=f,Ke=g,Xe=h,Ye=w,ea=b,aa=_,ta=k,la=j,sa=q,oa=D,ra=U,ia=F,na=y;return $(),Q("div",W,[S("div",Z,[A[16]||(A[16]=S("div",{class:"header-left"},[S("h2",null,"群组管理"),S("p",{class:"page-description"},"管理您负责的群组，监控群组活跃度，优化群组运营")],-1)),S("div",J,[L(B,{type:"primary",onClick:A[0]||(A[0]=e=>ze.value=!0)},{default:M(()=>[L(I,null,{default:M(()=>[L(R(t))]),_:1}),A[14]||(A[14]=O(" 创建群组 ",-1))]),_:1,__:[14]}),L(B,{onClick:Qe},{default:M(()=>[L(I,null,{default:M(()=>[L(R(s))]),_:1}),A[15]||(A[15]=O(" 刷新数据 ",-1))]),_:1,__:[15]})])]),L(Be,{gutter:20,class:"stats-row"},{default:M(()=>[L(P,{span:6},{default:M(()=>[S("div",K,[S("div",X,[L(I,{size:24},{default:M(()=>[L(R(r))]),_:1})]),S("div",Y,[S("div",ee,i(De.total_groups),1),A[18]||(A[18]=S("div",{class:"stat-title"},"管理群组",-1)),S("div",ae,[L(I,null,{default:M(()=>[L(R(n))]),_:1}),A[17]||(A[17]=O(" 12.5% ",-1))])])])]),_:1}),L(P,{span:6},{default:M(()=>[S("div",te,[S("div",le,[L(I,{size:24},{default:M(()=>[L(R(u))]),_:1})]),S("div",se,[S("div",oe,i(De.total_members),1),A[20]||(A[20]=S("div",{class:"stat-title"},"群组成员",-1)),S("div",re,[L(I,null,{default:M(()=>[L(R(n))]),_:1}),A[19]||(A[19]=O(" 8.3% ",-1))])])])]),_:1}),L(P,{span:6},{default:M(()=>[S("div",ie,[S("div",ne,[L(I,{size:24},{default:M(()=>[L(R(d))]),_:1})]),S("div",ue,[S("div",de,i(De.active_groups),1),A[22]||(A[22]=S("div",{class:"stat-title"},"活跃群组",-1)),S("div",ce,[L(I,null,{default:M(()=>[L(R(n))]),_:1}),A[21]||(A[21]=O(" 15.2% ",-1))])])])]),_:1}),L(P,{span:6},{default:M(()=>[S("div",pe,[S("div",me,[L(I,{size:24},{default:M(()=>[L(R(c))]),_:1})]),S("div",_e,[S("div",ve,i(De.conversion_rate)+"%",1),A[24]||(A[24]=S("div",{class:"stat-title"},"转化率",-1)),S("div",fe,[L(I,null,{default:M(()=>[L(R(p))]),_:1}),A[23]||(A[23]=O(" 2.1% ",-1))])])])]),_:1})]),_:1}),L(la,null,{header:M(()=>[S("div",ge,[A[25]||(A[25]=S("span",null,"群组列表",-1)),S("div",be,[L(He,{modelValue:Ue.value,"onUpdate:modelValue":A[1]||(A[1]=e=>Ue.value=e),placeholder:"搜索群组名称",style:{width:"200px","margin-right":"10px"},clearable:"",onInput:R($e)},{prefix:M(()=>[L(I,null,{default:M(()=>[L(R(C))]),_:1})]),_:1},8,["modelValue","onInput"]),L(We,{modelValue:qe.value,"onUpdate:modelValue":A[2]||(A[2]=e=>qe.value=e),placeholder:"群组状态",style:{width:"120px"},onChange:Pe},{default:M(()=>[L(Ne,{label:"全部",value:""}),L(Ne,{label:"活跃",value:"active"}),L(Ne,{label:"不活跃",value:"inactive"}),L(Ne,{label:"已暂停",value:"paused"})]),_:1},8,["modelValue"])])])]),default:M(()=>[G(($(),H(aa,{data:Fe.value,stripe:""},{default:M(()=>[L(Ze,{prop:"name",label:"群组名称",width:"200"},{default:M(({row:e})=>[S("div",we,[S("div",he,i(e.name),1),S("div",ye,i(e.description),1)])]),_:1}),L(Ze,{prop:"platform",label:"平台",width:"100"},{default:M(({row:e})=>{return[L(Je,{type:(a=e.platform,{wechat:"success",qq:"primary",dingtalk:"warning",work_wechat:"info"}[a]||"info"),size:"small"},{default:M(()=>[O(i(Re(e.platform)),1)]),_:2},1032,["type"])];var a}),_:1}),L(Ze,{prop:"member_count",label:"成员数量",width:"100"}),L(Ze,{label:"状态",width:"100"},{default:M(({row:e})=>{return[L(Je,{type:(a=e.status,{active:"success",inactive:"warning",paused:"danger"}[a]||"info"),size:"small"},{default:M(()=>[O(i(Ge(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),L(Ze,{prop:"daily_messages",label:"日消息量",width:"100"}),L(Ze,{prop:"conversion_count",label:"转化数",width:"100"}),L(Ze,{label:"活跃度",width:"120"},{default:M(({row:e})=>{return[L(Ke,{percentage:e.activity_rate,color:(a=e.activity_rate,a>=80?"#67C23A":a>=60?"#E6A23C":a>=40?"#F56C6C":"#909399"),"stroke-width":8},null,8,["percentage","color"])];var a}),_:1}),L(Ze,{label:"创建时间",width:"120"},{default:M(({row:e})=>{return[O(i((a=e.created_at,new Date(a).toLocaleDateString("zh-CN"))),1)];var a}),_:1}),L(Ze,{label:"操作",width:"200",fixed:"right"},{default:M(({row:e})=>[L(B,{size:"small",onClick:a=>{return t=e,E.info(`查看群组"${t.name}"的详细信息`),void(je.value=!0);var t}},{default:M(()=>A[26]||(A[26]=[O(" 详情 ",-1)])),_:2,__:[26]},1032,["onClick"]),L(B,{size:"small",onClick:a=>{return t=e,E.info(`管理群组"${t.name}"`),void(je.value=!0);var t}},{default:M(()=>A[27]||(A[27]=[O(" 管理 ",-1)])),_:2,__:[27]},1032,["onClick"]),L(ea,{onCommand:a=>((e,a)=>{switch(e){case"edit":E.info(`编辑群组"${a.name}"`),je.value=!0;break;case"pause":Se(a);break;case"resume":Le(a);break;case"delete":Me(a)}})(a,e)},{dropdown:M(()=>[L(Ye,null,{default:M(()=>[L(Xe,{command:"edit"},{default:M(()=>A[29]||(A[29]=[O("编辑",-1)])),_:1,__:[29]}),"active"===e.status?($(),H(Xe,{key:0,command:"pause"},{default:M(()=>A[30]||(A[30]=[O("暂停",-1)])),_:1,__:[30]})):N("",!0),"paused"===e.status?($(),H(Xe,{key:1,command:"resume"},{default:M(()=>A[31]||(A[31]=[O("恢复",-1)])),_:1,__:[31]})):N("",!0),L(Xe,{command:"delete",divided:""},{default:M(()=>A[32]||(A[32]=[O("删除",-1)])),_:1,__:[32]})]),_:2},1024)]),default:M(()=>[L(B,{size:"small"},{default:M(()=>[A[28]||(A[28]=O(" 更多",-1)),L(I,{class:"el-icon--right"},{default:M(()=>[L(R(p))]),_:1})]),_:1,__:[28]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[na,Ce.value]]),S("div",ke,[L(ta,{"current-page":Te.current_page,"onUpdate:currentPage":A[3]||(A[3]=e=>Te.current_page=e),"page-size":Te.per_page,"onUpdate:pageSize":A[4]||(A[4]=e=>Te.per_page=e),total:Te.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Pe,onCurrentChange:Pe},null,8,["current-page","page-size","total"])])]),_:1}),L(ia,{modelValue:ze.value,"onUpdate:modelValue":A[11]||(A[11]=e=>ze.value=e),title:"创建群组",width:"600px"},{footer:M(()=>[L(B,{onClick:A[10]||(A[10]=e=>ze.value=!1)},{default:M(()=>A[33]||(A[33]=[O("取消",-1)])),_:1,__:[33]}),L(B,{type:"primary",onClick:Oe,loading:xe.value},{default:M(()=>A[34]||(A[34]=[O(" 创建 ",-1)])),_:1,__:[34]},8,["loading"])]),default:M(()=>[L(ra,{model:Ee,rules:Ae,ref_key:"groupFormRef",ref:Ie,"label-width":"100px"},{default:M(()=>[L(sa,{label:"群组名称",prop:"name"},{default:M(()=>[L(He,{modelValue:Ee.name,"onUpdate:modelValue":A[5]||(A[5]=e=>Ee.name=e),placeholder:"请输入群组名称"},null,8,["modelValue"])]),_:1}),L(sa,{label:"群组描述",prop:"description"},{default:M(()=>[L(He,{modelValue:Ee.description,"onUpdate:modelValue":A[6]||(A[6]=e=>Ee.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述"},null,8,["modelValue"])]),_:1}),L(sa,{label:"所属平台",prop:"platform"},{default:M(()=>[L(We,{modelValue:Ee.platform,"onUpdate:modelValue":A[7]||(A[7]=e=>Ee.platform=e),placeholder:"选择平台"},{default:M(()=>[L(Ne,{label:"微信",value:"wechat"}),L(Ne,{label:"QQ",value:"qq"}),L(Ne,{label:"钉钉",value:"dingtalk"}),L(Ne,{label:"企业微信",value:"work_wechat"})]),_:1},8,["modelValue"])]),_:1}),L(sa,{label:"群组类型",prop:"type"},{default:M(()=>[L(We,{modelValue:Ee.type,"onUpdate:modelValue":A[8]||(A[8]=e=>Ee.type=e),placeholder:"选择类型"},{default:M(()=>[L(Ne,{label:"营销群",value:"marketing"}),L(Ne,{label:"服务群",value:"service"}),L(Ne,{label:"产品群",value:"product"}),L(Ne,{label:"VIP群",value:"vip"})]),_:1},8,["modelValue"])]),_:1}),L(sa,{label:"最大成员数",prop:"max_members"},{default:M(()=>[L(oa,{modelValue:Ee.max_members,"onUpdate:modelValue":A[9]||(A[9]=e=>Ee.max_members=e),min:10,max:500,placeholder:"最大成员数"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),L(ia,{modelValue:je.value,"onUpdate:modelValue":A[13]||(A[13]=e=>je.value=e),title:"功能开发中",width:"400px",center:""},{footer:M(()=>[L(B,{type:"primary",onClick:A[12]||(A[12]=e=>je.value=!1)},{default:M(()=>A[38]||(A[38]=[O("知道了",-1)])),_:1,__:[38]})]),default:M(()=>[S("div",Ve,[L(I,{size:60,color:"#409EFF"},{default:M(()=>[L(R(T))]),_:1}),A[35]||(A[35]=S("h3",null,"功能开发中",-1)),A[36]||(A[36]=S("p",null,"该功能正在紧急开发中，敬请期待！",-1)),A[37]||(A[37]=S("p",null,"预计上线时间：2024年1月",-1))])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-9dec2c32"]]);export{Ce as default};
