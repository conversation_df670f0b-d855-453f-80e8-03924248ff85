<?php

require_once 'vendor/autoload.php';

use App\Services\AntiBlockService;
use App\Models\DomainPool;
use App\Models\ShortLink;
use App\Models\LinkAccessLog;

// 模拟测试防红系统API
echo "=== 防红系统API测试 ===\n";

try {
    // 测试获取统计数据
    $antiBlockService = new AntiBlockService();
    $stats = $antiBlockService->getStats();
    
    echo "统计数据获取成功:\n";
    print_r($stats);
    
    echo "\n测试完成！\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
}