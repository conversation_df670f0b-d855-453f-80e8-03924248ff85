/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityHeatmap: typeof import('./src/components/dashboard/ActivityHeatmap.vue')['default']
    AIContentGenerator: typeof import('./src/components/AIContentGenerator.vue')['default']
    AvatarUpload: typeof import('./src/components/AvatarUpload.vue')['default']
    BarChart: typeof import('./src/components/Charts/BarChart.vue')['default']
    CircularProgress: typeof import('./src/components/UI/CircularProgress.vue')['default']
    ContentTemplateLibrary: typeof import('./src/components/ContentTemplateLibrary.vue')['default']
    CountTo: typeof import('./src/components/CountTo.vue')['default']
    DashboardCard: typeof import('./src/components/dashboard/DashboardCard.vue')['default']
    DataTable: typeof import('./src/components/common/DataTable.vue')['default']
    DoughnutChart: typeof import('./src/components/Charts/DoughnutChart.vue')['default']
    DynamicForm: typeof import('./src/components/common/DynamicForm.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    EnhancedStatCard: typeof import('./src/components/dashboard/EnhancedStatCard.vue')['default']
    ErrorBoundary: typeof import('./src/components/ErrorBoundary.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    FunnelChart: typeof import('./src/components/Charts/FunnelChart.vue')['default']
    GroupCreateForm: typeof import('./src/components/GroupCreateForm.vue')['default']
    GroupCreateSteps: typeof import('./src/components/GroupCreateSteps.vue')['default']
    GuideCard: typeof import('./src/components/UI/GuideCard.vue')['default']
    HelpTip: typeof import('./src/components/UI/HelpTip.vue')['default']
    ImageUpload: typeof import('./src/components/Upload/ImageUpload.vue')['default']
    LineChart: typeof import('./src/components/Charts/LineChart.vue')['default']
    MetricsGrid: typeof import('./src/components/dashboard/MetricsGrid.vue')['default']
    MiniLineChart: typeof import('./src/components/Charts/MiniLineChart.vue')['default']
    ModernDashboard: typeof import('./src/components/dashboard/ModernDashboard.vue')['default']
    ModernLayout: typeof import('./src/components/layout/ModernLayout.vue')['default']
    ModernMenuItem: typeof import('./src/components/layout/ModernMenuItem.vue')['default']
    NotificationCenter: typeof import('./src/components/dashboard/NotificationCenter.vue')['default']
    NotificationDrawer: typeof import('./src/components/NotificationDrawer.vue')['default']
    OrderDetailDialog: typeof import('./src/components/OrderDetailDialog.vue')['default']
    PageLayout: typeof import('./src/components/layout/PageLayout.vue')['default']
    Pagination: typeof import('./src/components/Pagination/index.vue')['default']
    PaymentInfoDialog: typeof import('./src/components/PaymentInfoDialog.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PieChart: typeof import('./src/components/Charts/PieChart.vue')['default']
    PopularGroups: typeof import('./src/components/dashboard/PopularGroups.vue')['default']
    QRCodeGenerator: typeof import('./src/components/QRCodeGenerator.vue')['default']
    QuickActions: typeof import('./src/components/dashboard/QuickActions.vue')['default']
    QuickStats: typeof import('./src/components/dashboard/QuickStats.vue')['default']
    RealtimeChart: typeof import('./src/components/dashboard/RealtimeChart.vue')['default']
    RecentActivities: typeof import('./src/components/dashboard/RecentActivities.vue')['default']
    RecentOrders: typeof import('./src/components/dashboard/RecentOrders.vue')['default']
    RefundDialog: typeof import('./src/components/RefundDialog.vue')['default']
    RichTextEditor: typeof import('./src/components/RichTextEditor.vue')['default']
    RoleSwitcher: typeof import('./src/components/RoleSwitcher.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShortcutHelp: typeof import('./src/components/ShortcutHelp.vue')['default']
    SmartCityReplacement: typeof import('./src/components/SmartCityReplacement.vue')['default']
    StatCard: typeof import('./src/components/dashboard/StatCard.vue')['default']
    SystemStatus: typeof import('./src/components/dashboard/SystemStatus.vue')['default']
    WelcomeBanner: typeof import('./src/components/dashboard/WelcomeBanner.vue')['default']
  }
}
