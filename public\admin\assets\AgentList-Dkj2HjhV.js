import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                       *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                     *//* empty css               */import{b2 as a,b4 as l,bc as t,bd as n,aH as o,aW as s,aV as r,as as u,T as d,b1 as i,aM as p,aU as _,b3 as m,b6 as c,b7 as v,b8 as g,U as f,o as b,av as y,aO as h,ax as V,ay as w,bj as k,bk as j,bo as C,bp as x,be as U,bl as z,au as q,Q as S,R as $}from"./element-plus-DcSKpKA8.js";import{S as D}from"./StatCard-WpSR56Tk.js";import{a as A}from"./agent-SmxfvIrI.js";import{r as F,L as E,e as L,k as B,l as O,t as H,E as I,z as M,D as R,u as W,A as G,y as N,B as P,F as Q,Y as T}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";/* empty css                                                                 */const X={class:"agent-list"},Y={key:0,class:"no-commission"},J={key:1,class:"commission-rate"},K={class:"amount"},Z={key:0,class:"permanent"},ee={key:2,class:"no-expiry"},ae={class:"pagination"},le=e({__name:"AgentList",setup(e){const le=F(!1),te=F(!1),ne=F(1),oe=F(20),se=F(!1),re=F({}),ue=F({data:[],total:0}),de=F([]),ie=E({keyword:"",agent_level:"",agent_type:"",status:""}),pe=E({user_id:"",agent_name:"",agent_level:"platform",agent_type:"individual",commission_rate:10,validity_period:"year",custom_end_date:"",remark:""}),_e={user_id:[{required:!0,message:"请选择关联用户",trigger:"change"}],agent_name:[{required:!0,message:"请输入代理商名称",trigger:"blur"}],agent_level:[{required:!0,message:"请选择代理商等级",trigger:"change"}],agent_type:[{required:!0,message:"请选择代理商类型",trigger:"change"}],commission_rate:[{required:!0,message:"请输入佣金比例",trigger:"blur"}],validity_period:[{required:!0,message:"请选择有效期",trigger:"change"}]},me=F(),ce=async()=>{try{const e=await A.getStats();re.value=e.data}catch(e){S.error("加载统计数据失败")}},ve=async()=>{try{le.value=!0;const e={page:ne.value,limit:oe.value,...ie},a=await A.getList(e);ue.value=a.data}catch(e){S.error("加载代理商列表失败")}finally{le.value=!1}},ge=()=>{ne.value=1,ve()},fe=()=>{Object.keys(ie).forEach(e=>{ie[e]=""}),ge()},be=e=>{oe.value=e,ve()},ye=e=>{ne.value=e,ve()},he=()=>{se.value=!0,Ve()},Ve=async()=>{de.value=[{id:1,name:"张三",username:"zhangsan"},{id:2,name:"李四",username:"lisi"}]},we=async()=>{try{await me.value.validate(),te.value=!0,await A.create(pe),S.success("代理商创建成功"),se.value=!1,ve(),ce()}catch(e){!1!==e&&S.error("创建代理商失败")}finally{te.value=!1}},ke=({action:e,row:a})=>{switch(e){case"edit":S.info(`编辑代理商 ${a.agent_name}`);break;case"renew":S.info(`续费代理商 ${a.agent_name}`);break;case"status":S.info(`管理代理商 ${a.agent_name} 状态`);break;case"delete":je(a)}},je=async e=>{try{await $.confirm(`确定要删除代理商 ${e.agent_name} 吗？`,"确认删除",{type:"warning"}),await A.delete(e.id),S.success("删除成功"),ve(),ce()}catch(a){"cancel"!==a&&S.error("删除失败")}},Ce=e=>({active:"正常",inactive:"未激活",suspended:"暂停",expired:"已过期"}[e]||"未知"),xe=e=>({individual:"个人代理",enterprise:"企业代理",channel:"渠道代理"}[e]||"未知"),Ue=e=>{const a=new Date,l=new Date(e),t=Math.ceil((l-a)/864e5);return t<0?"expired":t<=7?"expiring-soon":"valid"};return L(()=>{ce(),ve()}),(e,$)=>{const A=a,F=l,E=o,L=n,ce=r,ve=s,Ve=d,je=u,ze=t,qe=m,Se=v,$e=g,De=w,Ae=V,Fe=y,Ee=c,Le=j,Be=x,Oe=C,He=U,Ie=z,Me=q,Re=k;return O(),B("div",X,[$[31]||($[31]=H("div",{class:"page-header"},[H("h2",null,"代理商管理"),H("p",null,"管理平台代理商，包括代理商信息、状态管理和绩效分析")],-1)),I(F,{gutter:20,class:"stats-row"},{default:M(()=>[I(A,{span:6},{default:M(()=>[I(D,{title:"总代理商",value:re.value.total_agents||0,icon:"Avatar",color:"#409EFF"},null,8,["value"])]),_:1}),I(A,{span:6},{default:M(()=>[I(D,{title:"活跃代理商",value:re.value.active_agents||0,icon:"Check",color:"#67C23A"},null,8,["value"])]),_:1}),I(A,{span:6},{default:M(()=>[I(D,{title:"平台代理商",value:re.value.platform_agents||0,icon:"Star",color:"#E6A23C"},null,8,["value"])]),_:1}),I(A,{span:6},{default:M(()=>[I(D,{title:"分站代理商",value:re.value.substation_agents||0,icon:"OfficeBuilding",color:"#F56C6C"},null,8,["value"])]),_:1})]),_:1}),I(qe,{class:"search-card"},{default:M(()=>[I(ze,{model:ie,inline:""},{default:M(()=>[I(L,{label:"关键词"},{default:M(()=>[I(E,{modelValue:ie.keyword,"onUpdate:modelValue":$[0]||($[0]=e=>ie.keyword=e),placeholder:"搜索代理商名称或编码",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),I(L,{label:"代理商等级"},{default:M(()=>[I(ve,{modelValue:ie.agent_level,"onUpdate:modelValue":$[1]||($[1]=e=>ie.agent_level=e),placeholder:"选择等级",clearable:""},{default:M(()=>[I(ce,{label:"平台代理商",value:"platform"}),I(ce,{label:"分站代理商",value:"substation"})]),_:1},8,["modelValue"])]),_:1}),I(L,{label:"代理商类型"},{default:M(()=>[I(ve,{modelValue:ie.agent_type,"onUpdate:modelValue":$[2]||($[2]=e=>ie.agent_type=e),placeholder:"选择类型",clearable:""},{default:M(()=>[I(ce,{label:"个人代理",value:"individual"}),I(ce,{label:"企业代理",value:"enterprise"}),I(ce,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),I(L,{label:"状态"},{default:M(()=>[I(ve,{modelValue:ie.status,"onUpdate:modelValue":$[3]||($[3]=e=>ie.status=e),placeholder:"选择状态",clearable:""},{default:M(()=>[I(ce,{label:"正常",value:"active"}),I(ce,{label:"未激活",value:"inactive"}),I(ce,{label:"暂停",value:"suspended"}),I(ce,{label:"已过期",value:"expired"})]),_:1},8,["modelValue"])]),_:1}),I(L,null,{default:M(()=>[I(je,{type:"primary",onClick:ge},{default:M(()=>[I(Ve,null,{default:M(()=>[I(W(i))]),_:1}),$[16]||($[16]=R(" 搜索 ",-1))]),_:1,__:[16]}),I(je,{onClick:fe},{default:M(()=>[I(Ve,null,{default:M(()=>[I(W(p))]),_:1}),$[17]||($[17]=R(" 重置 ",-1))]),_:1,__:[17]}),I(je,{type:"success",onClick:he},{default:M(()=>[I(Ve,null,{default:M(()=>[I(W(_))]),_:1}),$[18]||($[18]=R(" 新增代理商 ",-1))]),_:1,__:[18]})]),_:1})]),_:1},8,["model"])]),_:1}),I(qe,{class:"agents-table"},{header:M(()=>$[19]||($[19]=[H("span",null,"代理商列表",-1)])),default:M(()=>[G((O(),N(Ee,{data:ue.value.data,stripe:""},{default:M(()=>[I(Se,{prop:"agent_code",label:"代理商编码"}),I(Se,{prop:"agent_name",label:"代理商名称"}),I(Se,{prop:"user.username",label:"关联用户"}),I(Se,{prop:"agent_level_label",label:"代理商等级"},{default:M(({row:e})=>{return[I($e,{type:(a=e.agent_level,"platform"===a?"primary":"success")},{default:M(()=>[R(f("platform"===e.agent_level?"平台代理商":"分站代理商"),1)]),_:2},1032,["type"])];var a}),_:1}),I(Se,{prop:"agent_type_label",label:"代理商类型"},{default:M(({row:e})=>{return[I($e,{type:(a=e.agent_type,{individual:"primary",enterprise:"success",channel:"warning"}[a]||"info")},{default:M(()=>[R(f(xe(e.agent_type)),1)]),_:2},1032,["type"])];var a}),_:1}),I(Se,{prop:"commission_display",label:"佣金设置"},{default:M(({row:e})=>[e.no_commission?(O(),B("span",Y,"不抽佣")):(O(),B("span",J,f(e.commission_rate)+"%",1))]),_:1}),I(Se,{prop:"total_commission",label:"总佣金"},{default:M(({row:e})=>[H("span",K,"¥"+f(e.total_commission||0),1)]),_:1}),I(Se,{prop:"status_label",label:"状态"},{default:M(({row:e})=>{return[I($e,{type:(a=e.status,{active:"success",inactive:"info",suspended:"warning",expired:"danger"}[a]||"info")},{default:M(()=>[R(f(Ce(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),I(Se,{prop:"validity_display",label:"有效期"},{default:M(({row:e})=>{return[e.is_permanent?(O(),B("span",Z,"永久有效")):e.end_date?(O(),B("span",{key:1,class:b(Ue(e.end_date))},f((a=e.end_date,a?new Date(a).toLocaleDateString("zh-CN"):"")),3)):(O(),B("span",ee,"未设置"))];var a}),_:1}),I(Se,{label:"操作",width:"200"},{default:M(({row:e})=>[I(je,{size:"small",onClick:a=>{return l=e,void S.info(`查看代理商 ${l.agent_name} 详情`);var l}},{default:M(()=>$[20]||($[20]=[R(" 查看详情 ",-1)])),_:2,__:[20]},1032,["onClick"]),I(Fe,{onCommand:ke,trigger:"click"},{dropdown:M(()=>[I(Ae,null,{default:M(()=>[I(De,{command:{action:"edit",row:e}},{default:M(()=>$[22]||($[22]=[R("编辑",-1)])),_:2,__:[22]},1032,["command"]),e.is_permanent?P("",!0):(O(),N(De,{key:0,command:{action:"renew",row:e}},{default:M(()=>$[23]||($[23]=[R("续费",-1)])),_:2,__:[23]},1032,["command"])),I(De,{command:{action:"status",row:e}},{default:M(()=>$[24]||($[24]=[R("状态管理",-1)])),_:2,__:[24]},1032,["command"]),I(De,{command:{action:"delete",row:e},divided:""},{default:M(()=>$[25]||($[25]=[R("删除",-1)])),_:2,__:[25]},1032,["command"])]),_:2},1024)]),default:M(()=>[I(je,{size:"small",type:"primary"},{default:M(()=>[$[21]||($[21]=R(" 更多操作",-1)),I(Ve,{class:"el-icon--right"},{default:M(()=>[I(W(h))]),_:1})]),_:1,__:[21]})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Re,le.value]]),H("div",ae,[I(Le,{"current-page":ne.value,"onUpdate:currentPage":$[4]||($[4]=e=>ne.value=e),"page-size":oe.value,"onUpdate:pageSize":$[5]||($[5]=e=>oe.value=e),total:ue.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:be,onCurrentChange:ye},null,8,["current-page","page-size","total"])])]),_:1}),I(Me,{modelValue:se.value,"onUpdate:modelValue":$[15]||($[15]=e=>se.value=e),title:"新增代理商",width:"600px"},{footer:M(()=>[I(je,{onClick:$[14]||($[14]=e=>se.value=!1)},{default:M(()=>$[29]||($[29]=[R("取消",-1)])),_:1,__:[29]}),I(je,{type:"primary",onClick:we,loading:te.value},{default:M(()=>$[30]||($[30]=[R(" 确认创建 ",-1)])),_:1,__:[30]},8,["loading"])]),default:M(()=>[I(ze,{model:pe,rules:_e,ref_key:"createFormRef",ref:me,"label-width":"120px"},{default:M(()=>[I(L,{label:"关联用户",prop:"user_id"},{default:M(()=>[I(ve,{modelValue:pe.user_id,"onUpdate:modelValue":$[6]||($[6]=e=>pe.user_id=e),placeholder:"选择用户",filterable:""},{default:M(()=>[(O(!0),B(Q,null,T(de.value,e=>(O(),N(ce,{key:e.id,label:`${e.name} (${e.username})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),I(L,{label:"代理商名称",prop:"agent_name"},{default:M(()=>[I(E,{modelValue:pe.agent_name,"onUpdate:modelValue":$[7]||($[7]=e=>pe.agent_name=e),placeholder:"请输入代理商名称"},null,8,["modelValue"])]),_:1}),I(L,{label:"代理商等级",prop:"agent_level"},{default:M(()=>[I(Oe,{modelValue:pe.agent_level,"onUpdate:modelValue":$[8]||($[8]=e=>pe.agent_level=e)},{default:M(()=>[I(Be,{label:"platform"},{default:M(()=>$[26]||($[26]=[R("平台代理商",-1)])),_:1,__:[26]}),I(Be,{label:"substation"},{default:M(()=>$[27]||($[27]=[R("分站代理商",-1)])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1}),I(L,{label:"代理商类型",prop:"agent_type"},{default:M(()=>[I(ve,{modelValue:pe.agent_type,"onUpdate:modelValue":$[9]||($[9]=e=>pe.agent_type=e),placeholder:"选择类型"},{default:M(()=>[I(ce,{label:"个人代理",value:"individual"}),I(ce,{label:"企业代理",value:"enterprise"}),I(ce,{label:"渠道代理",value:"channel"})]),_:1},8,["modelValue"])]),_:1}),I(L,{label:"佣金比例",prop:"commission_rate"},{default:M(()=>[I(He,{modelValue:pe.commission_rate,"onUpdate:modelValue":$[10]||($[10]=e=>pe.commission_rate=e),min:0,max:100,precision:2,"controls-position":"right"},null,8,["modelValue"]),$[28]||($[28]=H("span",{style:{"margin-left":"10px"}},"%",-1))]),_:1,__:[28]}),I(L,{label:"有效期",prop:"validity_period"},{default:M(()=>[I(ve,{modelValue:pe.validity_period,"onUpdate:modelValue":$[11]||($[11]=e=>pe.validity_period=e),placeholder:"选择有效期"},{default:M(()=>[I(ce,{label:"1周",value:"week"}),I(ce,{label:"1个月",value:"month"}),I(ce,{label:"3个月",value:"quarter"}),I(ce,{label:"6个月",value:"half_year"}),I(ce,{label:"1年",value:"year"}),I(ce,{label:"永久",value:"permanent"}),I(ce,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),"custom"===pe.validity_period?(O(),N(L,{key:0,label:"自定义结束日期"},{default:M(()=>[I(Ie,{modelValue:pe.custom_end_date,"onUpdate:modelValue":$[12]||($[12]=e=>pe.custom_end_date=e),type:"date",placeholder:"选择结束日期"},null,8,["modelValue"])]),_:1})):P("",!0),I(L,{label:"备注"},{default:M(()=>[I(E,{modelValue:pe.remark,"onUpdate:modelValue":$[13]||($[13]=e=>pe.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-ce45ba0f"]]);export{le as default};
