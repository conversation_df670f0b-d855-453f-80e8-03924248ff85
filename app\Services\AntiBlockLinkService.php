<?php

namespace App\Services;

use App\Models\WechatGroup;
use App\Models\DomainPool;
use App\Models\PromotionLink;
use App\Services\AntiBlockService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 防红链接服务
 * 专门处理防红系统中的链接生成和管理
 */
class AntiBlockLinkService
{
    private AntiBlockService $antiBlockService;

    public function __construct(AntiBlockService $antiBlockService)
    {
        $this->antiBlockService = $antiBlockService;
    }

    /**
     * 为群组生成防红推广链接
     */
    public function generatePromotionLink(WechatGroup $group, array $params = []): array
    {
        try {
            // 检查是否启用防红系统
            if (!$this->isAntiBlockEnabled($group)) {
                return $this->generateDefaultLink($group, $params);
            }

            // 获取防红域名
            $antiBlockDomain = $this->getAntiBlockDomain($group);
            
            if (!$antiBlockDomain) {
                Log::warning('无法获取防红域名，使用默认链接', ['group_id' => $group->id]);
                return $this->generateDefaultLink($group, $params);
            }

            // 生成防红链接
            $baseUrl = "https://{$antiBlockDomain}/group/{$group->slug}";
            $fullUrl = $baseUrl . '?' . http_build_query(array_filter($params));

            // 创建推广链接记录
            $promotionLink = $this->createPromotionLinkRecord($group, $fullUrl, $antiBlockDomain);

            // 生成短链接
            $shortUrl = $this->generateShortLink($group, $fullUrl);

            return [
                'success' => true,
                'promotion_url' => $fullUrl,
                'short_url' => $shortUrl,
                'qr_code_url' => $this->generateQRCode($fullUrl),
                'anti_block_enabled' => true,
                'domain' => $antiBlockDomain,
                'link_id' => $promotionLink ? $promotionLink->id : null,
                'expires_at' => $promotionLink ? $promotionLink->expires_at : null
            ];

        } catch (\Exception $e) {
            Log::error('生成防红推广链接失败', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);

            return $this->generateDefaultLink($group, $params);
        }
    }

    /**
     * 生成默认链接（非防红）
     */
    private function generateDefaultLink(WechatGroup $group, array $params = []): array
    {
        $baseUrl = route('group.landing', ['slug' => $group->slug]);
        $fullUrl = $baseUrl . '?' . http_build_query(array_filter($params));

        return [
            'success' => true,
            'promotion_url' => $fullUrl,
            'short_url' => $fullUrl,
            'qr_code_url' => $this->generateQRCode($fullUrl),
            'anti_block_enabled' => false,
            'domain' => parse_url(config('app.url'), PHP_URL_HOST),
            'link_id' => null,
            'expires_at' => null
        ];
    }

    /**
     * 检查群组是否启用防红系统
     */
    private function isAntiBlockEnabled(WechatGroup $group): bool
    {
        $antiBlockConfig = $group->anti_block_config ?? [];
        return ($antiBlockConfig['enabled'] ?? false) === true;
    }

    /**
     * 获取防红域名
     */
    private function getAntiBlockDomain(WechatGroup $group): ?string
    {
        $antiBlockConfig = $group->anti_block_config ?? [];
        $domainPoolId = $antiBlockConfig['domain_pool_id'] ?? null;

        if (!$domainPoolId) {
            return null;
        }

        $domainPool = DomainPool::find($domainPoolId);
        
        if (!$domainPool || $domainPool->status !== 'active') {
            return null;
        }

        // 使用负载均衡策略获取域名
        return $domainPool->getLoadBalancedDomain();
    }

    /**
     * 创建推广链接记录
     */
    private function createPromotionLinkRecord(WechatGroup $group, string $url, string $domain): ?PromotionLink
    {
        try {
            $shortCode = $this->generateShortCode();
            
            return PromotionLink::create([
                'group_id' => $group->id,
                'short_code' => $shortCode,
                'original_url' => $url,
                'title' => "群组推广-{$group->title}",
                'description' => $group->description,
                'click_count' => 0,
                'is_active' => true,
                'expires_at' => now()->addDays(30), // 30天有效期
                'created_by' => $group->creator_id,
                'tracking_params' => [
                    'domain' => $domain,
                    'group_id' => $group->id,
                    'created_at' => now()->toDateTimeString()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('创建推广链接记录失败', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * 生成短链接
     */
    private function generateShortLink(WechatGroup $group, string $originalUrl): string
    {
        try {
            $shortLink = $this->antiBlockService->createShortLink(
                $originalUrl,
                "群组推广-{$group->title}",
                $group->creator_id
            );

            return $shortLink ? $shortLink->full_short_url : $originalUrl;

        } catch (\Exception $e) {
            Log::error('生成短链接失败', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);

            return $originalUrl;
        }
    }

    /**
     * 生成短码
     */
    private function generateShortCode(): string
    {
        do {
            $shortCode = Str::random(8);
        } while (PromotionLink::where('short_code', $shortCode)->exists());

        return $shortCode;
    }

    /**
     * 生成二维码
     */
    private function generateQRCode(string $url): string
    {
        return 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' . urlencode($url);
    }

    /**
     * 检查并切换域名
     */
    public function checkAndSwitchDomain(WechatGroup $group): array
    {
        try {
            if (!$this->isAntiBlockEnabled($group)) {
                return [
                    'success' => false,
                    'message' => '群组未启用防红系统'
                ];
            }

            $antiBlockConfig = $group->anti_block_config ?? [];
            $domainPoolId = $antiBlockConfig['domain_pool_id'] ?? null;

            if (!$domainPoolId) {
                return [
                    'success' => false,
                    'message' => '未配置域名池'
                ];
            }

            // 检查当前域名状态
            $checkResult = $this->antiBlockService->intelligentDomainCheck($domainPoolId);
            
            if (!$checkResult['success']) {
                return [
                    'success' => false,
                    'message' => '域名检查失败：' . $checkResult['message']
                ];
            }

            $healthScore = $checkResult['data']['health_score'] ?? 0;
            
            // 如果健康分数低于阈值，尝试切换域名
            if ($healthScore < 60) {
                $newDomain = $this->switchToHealthyDomain($group);
                
                if ($newDomain) {
                    return [
                        'success' => true,
                        'message' => '域名已切换',
                        'new_domain' => $newDomain,
                        'old_health_score' => $healthScore
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => '无可用的健康域名'
                    ];
                }
            }

            return [
                'success' => true,
                'message' => '当前域名健康',
                'health_score' => $healthScore
            ];

        } catch (\Exception $e) {
            Log::error('检查并切换域名失败', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '操作失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 切换到健康域名
     */
    private function switchToHealthyDomain(WechatGroup $group): ?string
    {
        $antiBlockConfig = $group->anti_block_config ?? [];
        $domainPoolId = $antiBlockConfig['domain_pool_id'] ?? null;

        if (!$domainPoolId) {
            return null;
        }

        $domainPool = DomainPool::find($domainPoolId);
        
        if (!$domainPool) {
            return null;
        }

        // 获取最佳健康域名
        $newDomain = $domainPool->getBestHealthyDomain();
        
        if ($newDomain) {
            // 记录域名切换
            Log::info('域名切换成功', [
                'group_id' => $group->id,
                'domain_pool_id' => $domainPoolId,
                'new_domain' => $newDomain
            ]);

            // 更新域名池使用统计
            $domainPool->increment('switch_count');
        }

        return $newDomain;
    }

    /**
     * 获取群组的推广链接统计
     */
    public function getPromotionStats(WechatGroup $group): array
    {
        try {
            $links = PromotionLink::where('group_id', $group->id)->get();
            
            $totalClicks = $links->sum('click_count');
            $activeLinks = $links->where('is_active', true)->count();
            $expiredLinks = $links->where('expires_at', '<', now())->count();

            return [
                'total_links' => $links->count(),
                'active_links' => $activeLinks,
                'expired_links' => $expiredLinks,
                'total_clicks' => $totalClicks,
                'avg_clicks_per_link' => $links->count() > 0 ? round($totalClicks / $links->count(), 2) : 0,
                'recent_links' => $links->sortByDesc('created_at')->take(5)->values()->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('获取推广链接统计失败', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);

            return [
                'total_links' => 0,
                'active_links' => 0,
                'expired_links' => 0,
                'total_clicks' => 0,
                'avg_clicks_per_link' => 0,
                'recent_links' => []
            ];
        }
    }

    /**
     * 批量更新推广链接状态
     */
    public function batchUpdateLinkStatus(array $linkIds, string $action): array
    {
        try {
            $links = PromotionLink::whereIn('id', $linkIds)->get();
            
            if ($links->isEmpty()) {
                return [
                    'success' => false,
                    'message' => '未找到指定的推广链接'
                ];
            }

            $successCount = 0;
            $failedCount = 0;

            foreach ($links as $link) {
                try {
                    switch ($action) {
                        case 'enable':
                            $link->update(['is_active' => true]);
                            $successCount++;
                            break;
                        case 'disable':
                            $link->update(['is_active' => false]);
                            $successCount++;
                            break;
                        case 'delete':
                            $link->delete();
                            $successCount++;
                            break;
                        case 'extend_expiry':
                            $link->update(['expires_at' => now()->addDays(30)]);
                            $successCount++;
                            break;
                        default:
                            $failedCount++;
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    Log::error('批量更新推广链接失败', [
                        'link_id' => $link->id,
                        'action' => $action,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return [
                'success' => true,
                'message' => "操作完成：成功 {$successCount} 个，失败 {$failedCount} 个",
                'success_count' => $successCount,
                'failed_count' => $failedCount
            ];

        } catch (\Exception $e) {
            Log::error('批量更新推广链接状态失败', [
                'link_ids' => $linkIds,
                'action' => $action,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '批量操作失败：' . $e->getMessage()
            ];
        }
    }
}