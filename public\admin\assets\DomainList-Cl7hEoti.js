import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                        *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                    *//* empty css               *//* empty css               *//* empty css                *//* empty css                     *//* empty css                  */import{r as a,L as l,$ as t,e as i,k as o,l as d,E as n,z as r,J as u,u as s,a2 as p,D as c,A as m,y as _,G as f,t as v}from"./vue-vendor-DGsK9sC4.js";import{b,d as y,c as g,u as w,a as h}from"./anti-block-BQ2PwvXK.js";import{P as k}from"./index-Do9uvhBr.js";import{Q as V,bc as j,bd as C,aW as U,aV as x,aH as q,as as P,b3 as I,b2 as A,b4 as B,b6 as D,b7 as T,b8 as z,U as F,bj as H,bo as L,bp as R,be as S,au as E,R as G}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";/* empty css                      */const J={class:"app-container"},K={class:"dialog-footer"},Q=e({__name:"DomainList",setup(e){const Q=a(!0),W=a([]),$=a(!0),M=a(!0),N=a(0),O=a([]),X=a(null),Y=l({queryParams:{page:1,limit:10,domain:void 0,domain_type:void 0,status:void 0},dialog:{visible:!1,title:""},form:{},rules:{domain:[{required:!0,message:"域名不能为空",trigger:"blur"}],domain_type:[{required:!0,message:"域名类型不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],priority:[{required:!0,message:"优先级不能为空",trigger:"blur"}]}}),{queryParams:Z,dialog:ee,form:ae,rules:le}=t(Y),te=e=>({redirect:"短链接",landing:"中转页",api:"API服务"}[e]||"未知"),ie=e=>({normal:"正常",error:"异常",banned:"封禁",stopped:"停用"}[e]||"未知");async function oe(){Q.value=!0;try{const e=await b(Z.value);O.value=e.data.data,N.value=e.data.total}catch(e){V.error("获取域名列表失败")}finally{Q.value=!1}}function de(){Z.value.page=1,oe()}function ne(){Z.value={page:1,limit:10,domain:void 0,domain_type:void 0,status:void 0},de()}function re(e){W.value=e,$.value=1!==e.length,M.value=!e.length}function ue(){ae.value={domain:void 0,domain_type:"redirect",status:"normal",priority:10,remark:void 0},X.value&&X.value.resetFields()}function se(){ue(),ee.value.visible=!0,ee.value.title="添加域名"}function pe(e){ue();const a=e||W.value[0];ae.value={...a},ee.value.visible=!0,ee.value.title="修改域名"}async function ce(e){const a=e?[e.id]:W.value.map(e=>e.id);await G.confirm("是否确认删除选中的域名?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});try{await y({ids:a}),oe(),V.success("删除成功")}catch(l){V.error("删除失败")}}async function me(){const e=W.value.map(e=>e.id);Q.value=!0;try{await g({ids:e}),V.success("批量检测任务已提交"),oe()}catch(a){V.error("批量检测失败")}finally{Q.value=!1}}function _e(){ee.value.visible=!1,ue()}async function fe(){await X.value.validate();const e=!!ae.value.id;try{e?(await w(ae.value.id,ae.value),V.success("修改成功")):(await h(ae.value),V.success("新增成功")),ee.value.visible=!1,oe()}catch(a){V.error("操作失败")}}return i(()=>{oe()}),(e,a)=>{const l=x,t=U,i=C,b=q,y=P,w=j,h=I,G=A,Y=B,ue=T,ve=z,be=D,ye=R,ge=L,we=S,he=E,ke=H;return d(),o("div",J,[n(h,{class:"filter-card"},{default:r(()=>[n(w,{inline:!0,model:s(Z),onSubmit:u(de,["prevent"])},{default:r(()=>[n(i,{label:"域名类型"},{default:r(()=>[n(t,{modelValue:s(Z).domain_type,"onUpdate:modelValue":a[0]||(a[0]=e=>s(Z).domain_type=e),placeholder:"全部类型",clearable:""},{default:r(()=>[n(l,{label:"短链接域名",value:"redirect"}),n(l,{label:"中转页域名",value:"landing"}),n(l,{label:"API服务域名",value:"api"})]),_:1},8,["modelValue"])]),_:1}),n(i,{label:"域名状态"},{default:r(()=>[n(t,{modelValue:s(Z).status,"onUpdate:modelValue":a[1]||(a[1]=e=>s(Z).status=e),placeholder:"全部状态",clearable:""},{default:r(()=>[n(l,{label:"正常",value:"normal"}),n(l,{label:"异常",value:"error"}),n(l,{label:"封禁",value:"banned"}),n(l,{label:"停用",value:"stopped"})]),_:1},8,["modelValue"])]),_:1}),n(i,{label:"域名搜索"},{default:r(()=>[n(b,{modelValue:s(Z).domain,"onUpdate:modelValue":a[2]||(a[2]=e=>s(Z).domain=e),placeholder:"输入域名关键词",clearable:"",onKeyup:p(de,["enter"])},null,8,["modelValue"])]),_:1}),n(i,null,{default:r(()=>[n(y,{type:"primary",icon:"el-icon-search",onClick:de},{default:r(()=>a[12]||(a[12]=[c("查询",-1)])),_:1,__:[12]}),n(y,{icon:"el-icon-refresh",onClick:ne},{default:r(()=>a[13]||(a[13]=[c("重置",-1)])),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),_:1}),n(Y,{gutter:10,class:"mb8"},{default:r(()=>[n(G,{span:1.5},{default:r(()=>[n(y,{type:"primary",plain:"",icon:"el-icon-plus",onClick:se},{default:r(()=>a[14]||(a[14]=[c("新增",-1)])),_:1,__:[14]})]),_:1}),n(G,{span:1.5},{default:r(()=>[n(y,{type:"success",plain:"",icon:"el-icon-edit",disabled:$.value,onClick:a[3]||(a[3]=e=>pe(W.value[0]))},{default:r(()=>a[15]||(a[15]=[c("修改",-1)])),_:1,__:[15]},8,["disabled"])]),_:1}),n(G,{span:1.5},{default:r(()=>[n(y,{type:"danger",plain:"",icon:"el-icon-delete",disabled:M.value,onClick:ce},{default:r(()=>a[16]||(a[16]=[c("删除",-1)])),_:1,__:[16]},8,["disabled"])]),_:1}),n(G,{span:1.5},{default:r(()=>[n(y,{type:"warning",plain:"",icon:"el-icon-magic-stick",disabled:M.value,onClick:me},{default:r(()=>a[17]||(a[17]=[c("批量检测",-1)])),_:1,__:[17]},8,["disabled"])]),_:1})]),_:1}),n(h,null,{default:r(()=>[m((d(),_(be,{data:O.value,onSelectionChange:re},{default:r(()=>[n(ue,{type:"selection",width:"55",align:"center"}),n(ue,{label:"域名",prop:"domain",width:"250"}),n(ue,{label:"类型",prop:"domain_type",width:"120"},{default:r(e=>{return[n(ve,{type:(a=e.row.domain_type,{redirect:"success",landing:"primary",api:"warning"}[a]||"info")},{default:r(()=>[c(F(te(e.row.domain_type)),1)]),_:2},1032,["type"])];var a}),_:1}),n(ue,{label:"状态",prop:"status",width:"100"},{default:r(e=>{return[n(ve,{type:(a=e.row.status,{normal:"success",error:"danger",banned:"danger",stopped:"info"}[a]||"warning")},{default:r(()=>[c(F(ie(e.row.status)),1)]),_:2},1032,["type"])];var a}),_:1}),n(ue,{label:"优先级",prop:"priority",width:"100"}),n(ue,{label:"备注",prop:"remark","show-overflow-tooltip":""}),n(ue,{label:"创建时间",prop:"created_at",width:"160"}),n(ue,{label:"操作",width:"180","class-name":"small-padding fixed-width"},{default:r(e=>[n(y,{link:"",type:"primary",icon:"el-icon-edit",onClick:a=>pe(e.row)},{default:r(()=>a[18]||(a[18]=[c("修改",-1)])),_:2,__:[18]},1032,["onClick"]),n(y,{link:"",type:"danger",icon:"el-icon-delete",onClick:a=>ce(e.row)},{default:r(()=>a[19]||(a[19]=[c("删除",-1)])),_:2,__:[19]},1032,["onClick"]),n(y,{link:"",type:"warning",icon:"el-icon-magic-stick",onClick:a=>async function(e){Q.value=!0;try{await g({ids:[e.id]}),V.success("检测任务已提交"),oe()}catch(a){V.error("检测失败")}finally{Q.value=!1}}(e.row)},{default:r(()=>a[20]||(a[20]=[c("检测",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ke,Q.value]]),m(n(k,{total:N.value,page:s(Z).page,"onUpdate:page":a[4]||(a[4]=e=>s(Z).page=e),limit:s(Z).limit,"onUpdate:limit":a[5]||(a[5]=e=>s(Z).limit=e),onPagination:oe},null,8,["total","page","limit"]),[[f,N.value>0]])]),_:1}),n(he,{title:s(ee).title,modelValue:s(ee).visible,"onUpdate:modelValue":a[11]||(a[11]=e=>s(ee).visible=e),width:"600px","append-to-body":""},{footer:r(()=>[v("div",K,[n(y,{onClick:_e},{default:r(()=>a[23]||(a[23]=[c("取 消",-1)])),_:1,__:[23]}),n(y,{type:"primary",onClick:fe},{default:r(()=>a[24]||(a[24]=[c("确 定",-1)])),_:1,__:[24]})])]),default:r(()=>[n(w,{ref_key:"domainFormRef",ref:X,model:s(ae),rules:s(le),"label-width":"80px"},{default:r(()=>[n(i,{label:"域名",prop:"domain"},{default:r(()=>[n(b,{modelValue:s(ae).domain,"onUpdate:modelValue":a[6]||(a[6]=e=>s(ae).domain=e),placeholder:"请输入域名，如 domain.com"},null,8,["modelValue"])]),_:1}),n(i,{label:"类型",prop:"domain_type"},{default:r(()=>[n(t,{modelValue:s(ae).domain_type,"onUpdate:modelValue":a[7]||(a[7]=e=>s(ae).domain_type=e),placeholder:"请选择域名类型"},{default:r(()=>[n(l,{label:"短链接域名",value:"redirect"}),n(l,{label:"中转页域名",value:"landing"}),n(l,{label:"API服务域名",value:"api"})]),_:1},8,["modelValue"])]),_:1}),n(i,{label:"状态",prop:"status"},{default:r(()=>[n(ge,{modelValue:s(ae).status,"onUpdate:modelValue":a[8]||(a[8]=e=>s(ae).status=e)},{default:r(()=>[n(ye,{label:"normal"},{default:r(()=>a[21]||(a[21]=[c("正常",-1)])),_:1,__:[21]}),n(ye,{label:"stopped"},{default:r(()=>a[22]||(a[22]=[c("停用",-1)])),_:1,__:[22]})]),_:1},8,["modelValue"])]),_:1}),n(i,{label:"优先级",prop:"priority"},{default:r(()=>[n(we,{modelValue:s(ae).priority,"onUpdate:modelValue":a[9]||(a[9]=e=>s(ae).priority=e),min:0,max:100},null,8,["modelValue"])]),_:1}),n(i,{label:"备注",prop:"remark"},{default:r(()=>[n(b,{modelValue:s(ae).remark,"onUpdate:modelValue":a[10]||(a[10]=e=>s(ae).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}},[["__scopeId","data-v-a469b25d"]]);export{Q as default};
