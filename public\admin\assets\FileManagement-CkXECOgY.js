import{_ as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import{P as e}from"./PageLayout-OFR6SHfu.js";import{b3 as t,b6 as s,b7 as l,b8 as r,U as i,as as n,bj as o,bk as c,Q as d,R as u}from"./element-plus-DcSKpKA8.js";import{r as p,L as m,e as _,k as f,l as g,E as b,z as v,t as y,A as h,y as w,D as z}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const j={class:"page-container"},k={class:"content-wrapper"},C={class:"card-body"},x={class:"pagination-wrapper"},P=a({__name:"FileManagement",setup(a){const P=p(!1),T=p([]),U=m({current:1,size:20,total:0}),B=()=>{L()},D=()=>{d.info("新增功能待实现")},E=a=>{U.size=a,L()},I=a=>{U.current=a,L()},L=async()=>{P.value=!0;try{await new Promise(a=>setTimeout(a,1e3)),T.value=[{id:1,name:"示例数据1",status:"active",created_at:"2024-01-01 12:00:00"},{id:2,name:"示例数据2",status:"inactive",created_at:"2024-01-02 12:00:00"}],U.total=2}catch(a){console.error("加载数据失败:",a),d.error("加载数据失败")}finally{P.value=!1}};return _(()=>{L()}),(a,p)=>{const m=n,_=l,S=r,$=s,q=c,A=t,F=o;return g(),f("div",j,[b(e,{title:"文件管理",subtitle:"管理系统文件和资源",loading:P.value},{actions:v(()=>[b(m,{class:"modern-btn secondary",onClick:B},{default:v(()=>p[2]||(p[2]=[y("i",{class:"el-icon-refresh"},null,-1),z(" 刷新数据 ",-1)])),_:1,__:[2]}),b(m,{class:"modern-btn primary",onClick:D},{default:v(()=>p[3]||(p[3]=[y("i",{class:"el-icon-plus"},null,-1),z(" 新增 ",-1)])),_:1,__:[3]})]),default:v(()=>[y("div",k,[b(A,{class:"modern-card"},{default:v(()=>[p[6]||(p[6]=y("div",{class:"card-header"},[y("h3",null,"文件管理"),y("p",{class:"text-muted"},"管理系统文件和资源")],-1)),y("div",C,[h((g(),w($,{data:T.value,style:{width:"100%"},class:"modern-table"},{default:v(()=>[b(_,{prop:"id",label:"ID",width:"80"}),b(_,{prop:"name",label:"名称","min-width":"150"}),b(_,{prop:"status",label:"状态",width:"100"},{default:v(({row:a})=>[b(S,{type:"active"===a.status?"success":"info",size:"small"},{default:v(()=>[z(i("active"===a.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),b(_,{prop:"created_at",label:"创建时间",width:"180"}),b(_,{label:"操作",width:"200",fixed:"right"},{default:v(({row:a})=>[b(m,{type:"primary",size:"small",onClick:e=>(a=>{d.info(`编辑功能待实现: ${a.name}`)})(a)},{default:v(()=>p[4]||(p[4]=[z(" 编辑 ",-1)])),_:2,__:[4]},1032,["onClick"]),b(m,{type:"danger",size:"small",onClick:e=>(async a=>{try{await u.confirm(`确定要删除 "${a.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),d.success("删除成功"),L()}catch{d.info("已取消删除")}})(a)},{default:v(()=>p[5]||(p[5]=[z(" 删除 ",-1)])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[F,P.value]]),y("div",x,[b(q,{"current-page":U.current,"onUpdate:currentPage":p[0]||(p[0]=a=>U.current=a),"page-size":U.size,"onUpdate:pageSize":p[1]||(p[1]=a=>U.size=a),total:U.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:E,onCurrentChange:I},null,8,["current-page","page-size","total"])])])]),_:1,__:[6]})])]),_:1},8,["loading"])])}}},[["__scopeId","data-v-c273404b"]]);export{P as default};
