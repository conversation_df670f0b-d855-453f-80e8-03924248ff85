<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total-icon">
              <i class="el-icon-medal"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">¥{{ formatNumber(commissionStats.total_amount) }}</div>
              <div class="stats-label">总佣金</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon paid-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">¥{{ formatNumber(commissionStats.paid_amount) }}</div>
              <div class="stats-label">已发放</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">¥{{ formatNumber(commissionStats.pending_amount) }}</div>
              <div class="stats-label">待发放</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon count-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stats-data">
              <div class="stats-number">{{ commissionStats.total_count }}</div>
              <div class="stats-label">总笔数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选区域 -->
    <el-card style="margin-bottom: 20px">
      <el-form :inline="true" :model="listQuery" label-width="80px">
        <el-form-item label="订单号">
          <el-input 
            v-model="listQuery.order_no" 
            placeholder="请输入订单号" 
            clearable 
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="受益人">
          <el-input 
            v-model="listQuery.user_name" 
            placeholder="请输入用户名或昵称" 
            clearable 
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="已结算" :value="1" />
            <el-option label="待结算" :value="2" />
            <el-option label="已取消" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="listQuery.date_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 250px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card style="margin-bottom: 20px">
      <el-row>
        <el-col :span="12">
          <el-button 
            type="success" 
            icon="Check" 
            :disabled="multipleSelection.length === 0"
            @click="handleBatchSettle"
          >
            批量结算
          </el-button>
          <el-button 
            type="warning" 
            icon="Download" 
            @click="handleExportCommissions"
            :loading="exportLoading"
          >
            导出数据
          </el-button>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button 
            type="primary" 
            icon="Plus" 
            @click="handleAdd"
          >
            手动添加佣金
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" prop="id" align="center" width="80" />
        
        <el-table-column label="订单信息" width="200" align="center">
          <template #default="{row}">
            <div class="order-info">
              <div class="order-no">{{ row.order?.order_no || '-' }}</div>
              <div class="order-amount">¥{{ formatNumber(row.order_amount) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="受益人" width="150" align="center">
          <template #default="{row}">
            <div class="user-info">
              <div class="user-avatar">
                <img :src="row.user?.avatar || '/default-avatar.png'" alt="">
              </div>
              <div class="user-details">
                <div class="user-name">{{ row.user?.name || '-' }}</div>
                <div class="user-level">{{ getUserLevel(row.user?.level) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="佣金详情" width="180" align="center">
          <template #default="{row}">
            <div class="commission-info">
              <div class="commission-rate">{{ row.commission_rate }}%</div>
              <div class="commission-amount">¥{{ formatNumber(row.commission_amount) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100" align="center">
          <template #default="{row}">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="备注" prop="remark" min-width="120" show-overflow-tooltip />
        
        <el-table-column label="创建时间" width="160" align="center">
          <template #default="{row}">
            <div class="time-info">
              <div>{{ formatDate(row.created_at) }}</div>
              <div class="time-detail">{{ formatTime(row.created_at) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{row}">
            <el-button 
              type="text" 
              size="small" 
              @click="viewDetail(row)"
            >
              详情
            </el-button>
            <el-button 
              v-if="row.status === 2"
              type="text" 
              size="small" 
              @click="handleSettle(row)"
            >
              结算
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="editCommission(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: center">
        <el-pagination
          v-show="total > 0"
          v-model:current-page="listQuery.page"
          v-model:page-size="listQuery.limit"
          :page-sizes="[15, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchList"
          @current-change="fetchList"
        />
      </div>
    </el-card>

    <!-- 添加佣金对话框 -->
    <el-dialog
      title="添加佣金记录"
      v-model="addDialogVisible"
      width="600px"
    >
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="120px">
        <el-form-item label="订单号" prop="order_id">
          <el-select 
            v-model="addForm.order_id" 
            placeholder="请选择订单"
            filterable
            remote
            :remote-method="remoteSearchOrders"
            style="width: 100%"
          >
            <el-option 
              v-for="order in orderOptions" 
              :key="order.id" 
              :label="order.order_no" 
              :value="order.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="受益用户" prop="user_id">
          <el-select 
            v-model="addForm.user_id" 
            placeholder="请选择用户"
            filterable
            remote
            :remote-method="remoteSearchUsers"
            style="width: 100%"
          >
            <el-option 
              v-for="user in userOptions" 
              :key="user.id" 
              :label="`${user.name} (${user.username})`" 
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="佣金比例" prop="commission_rate">
          <el-input-number 
            v-model="addForm.commission_rate" 
            :min="0" 
            :max="100" 
            :precision="2"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #909399;">%</span>
        </el-form-item>
        
        <el-form-item label="佣金金额" prop="commission_amount">
          <el-input-number 
            v-model="addForm.commission_amount" 
            :min="0" 
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="addForm.remark" 
            type="textarea" 
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdd" :loading="addLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { 
  getCommissionList, 
  getCommissionStats,
  settleCommission,
  batchSettleCommissions,
  addCommission,
  searchOrders,
  searchUsers
} from '@/api/finance';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh, Check, Download, Plus } from '@element-plus/icons-vue';
import { exportCommissions } from '@/api/export';
import { formatDate, formatTime, formatNumber } from '@/utils/format';

const list = ref([]);
const total = ref(0);
const listLoading = ref(true);
const exportLoading = ref(false);
const addLoading = ref(false);
const multipleSelection = ref([]);

const listQuery = reactive({
  page: 1,
  limit: 15,
  order_no: '',
  user_name: '',
  status: '',
  date_range: []
});

const commissionStats = ref({});
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const addForm = reactive({
  order_id: '',
  user_id: '',
  commission_rate: 0,
  commission_amount: 0,
  remark: ''
});

const addFormRules = {
  order_id: [{ required: true, message: '请选择订单', trigger: 'change' }],
  user_id: [{ required: true, message: '请选择用户', trigger: 'change' }],
  commission_rate: [{ required: true, message: '请输入佣金比例', trigger: 'blur' }],
  commission_amount: [{ required: true, message: '请输入佣金金额', trigger: 'blur' }]
};

const orderOptions = ref([]);
const userOptions = ref([]);

onMounted(() => {
  fetchList();
  fetchStats();
});

const fetchList = async () => {
  listLoading.value = true;
  try {
    const params = { ...listQuery };
    if (params.date_range && params.date_range.length === 2) {
      params.start_date = params.date_range[0];
      params.end_date = params.date_range[1];
    }
    delete params.date_range;
    
    const response = await getCommissionList(params);
    list.value = response.data.data;
    total.value = response.data.total;
  } catch (error) {
    ElMessage.error('获取佣金列表失败');
    console.error(error);
  } finally {
    listLoading.value = false;
  }
};

const fetchStats = async () => {
  try {
    const response = await getCommissionStats();
    commissionStats.value = response.data.basic;
  } catch (error) {
    console.error('获取统计数据失败', error);
  }
};

const handleQuery = () => {
  listQuery.page = 1;
  fetchList();
};

const resetQuery = () => {
  Object.assign(listQuery, {
    page: 1,
    limit: 15,
    order_no: '',
    user_name: '',
    status: '',
    date_range: []
  });
  fetchList();
};

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection;
};

const handleSettle = (row) => {
  ElMessageBox.confirm(`确定要结算这笔佣金吗 (ID: ${row.id})?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await settleCommission(row.id);
      ElMessage.success("结算成功");
      fetchList();
      fetchStats();
    } catch (error) {
      ElMessage.error("结算失败");
    }
  });
};

const handleBatchSettle = () => {
  const pendingItems = multipleSelection.value.filter(item => item.status === 2);
  if (pendingItems.length === 0) {
    ElMessage.warning('请选择待结算的佣金记录');
    return;
  }
  const ids = pendingItems.map(item => item.id);
  ElMessageBox.confirm(`确定要结算选中的 ${ids.length} 笔佣金吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await batchSettleCommissions({ ids });
      ElMessage.success("批量结算成功");
      fetchList();
      fetchStats();
    } catch (error) {
      ElMessage.error("批量结算失败");
    }
  });
};

const handleAdd = () => {
  resetAddForm();
  addDialogVisible.value = true;
};

const submitAdd = () => {
  addFormRef.value.validate(async (valid) => {
    if (valid) {
      addLoading.value = true;
      try {
        await addCommission(addForm);
        ElMessage.success("添加成功");
        addDialogVisible.value = false;
        fetchList();
      } catch (error) {
        ElMessage.error("添加失败");
      } finally {
        addLoading.value = false;
      }
    }
  });
};

const resetAddForm = () => {
  if(addFormRef.value) {
    addFormRef.value.resetFields();
  }
  Object.assign(addForm, {
    order_id: '',
    user_id: '',
    commission_rate: 0,
    commission_amount: 0,
    remark: ''
  });
};

const remoteSearchOrders = async (query) => {
  if (query) {
    try {
      const response = await searchOrders({ keyword: query });
      orderOptions.value = response.data;
    } catch (error) {
      console.error('搜索订单失败', error);
    }
  }
};

const remoteSearchUsers = async (query) => {
  if (query) {
    try {
      const response = await searchUsers({ keyword: query });
      userOptions.value = response.data;
    } catch (error) {
      console.error('搜索用户失败', error);
    }
  }
};

const handleExportCommissions = async () => {
  exportLoading.value = true
  try {
    const params = {
      ...listQuery,
      format: 'excel',
      fields: [
        'id', 'order_no', 'order_amount', 'commission_rate', 
        'commission_amount', 'user_name', 'status', 'created_at'
      ]
    }
    
    const response = await exportCommissions(params)
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `佣金明细_${new Date().toLocaleDateString()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const viewDetail = (row) => {
  ElMessage.info(`查看佣金详情：${row.id}`)
}

const editCommission = (row) => {
  ElMessage.info(`编辑佣金：${row.id}`)
}

// 工具函数
const getStatusName = (status) => {
  const statusMap = { 1: '已结算', 2: '待结算', 3: '已取消' };
  return statusMap[status] || '未知';
};

const getStatusTagType = (status) => {
  const typeMap = { 1: 'success', 2: 'warning', 3: 'info' };
  return typeMap[status] || 'info';
};

const getUserLevel = (level) => {
  const levelMap = {
    1: '初级分销商',
    2: '中级分销商',
    3: '高级分销商',
    4: '金牌分销商'
  }
  return levelMap[level] || '普通用户'
}
</script>

<style lang="scss" scoped>
.stats-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  
  .stats-content {
    display: flex;
    align-items: center;
    padding: 20px;
    
    .stats-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      font-size: 20px;
      color: white;
      
      &.total-icon { background: #409eff; }
      &.paid-icon { background: #67c23a; }
      &.pending-icon { background: #e6a23c; }
      &.count-icon { background: #f56c6c; }
    }
    
    .stats-data {
      flex: 1;
      
      .stats-number {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;
      }
      
      .stats-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.order-info {
  text-align: left;
  
  .order-no {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }
  
  .order-amount {
    font-size: 12px;
    color: #409eff;
  }
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 8px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .user-details {
    flex: 1;
    text-align: left;
    
    .user-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 2px;
    }
    
    .user-level {
      font-size: 12px;
      color: #909399;
    }
  }
}

.commission-info {
  text-align: left;
  
  .commission-rate {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }
  
  .commission-amount {
    font-weight: bold;
    color: #67c23a;
  }
}

.time-info {
  text-align: left;
  
  .time-detail {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}
</style> 