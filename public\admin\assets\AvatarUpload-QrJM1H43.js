import{_ as e,g as a}from"./index-D2bI4m-v.js";/* empty css                  *//* empty css                    */import{ao as l,r as t,c as s,k as r,l as o,E as u,z as i,t as c,B as d,u as p,J as n,D as v}from"./vue-vendor-DGsK9sC4.js";import{o as m,aZ as g,T as f,aU as y,V as _,as as h,ac as k,bt as w,bn as x,au as z,R as b,Q as j}from"./element-plus-DcSKpKA8.js";const V={class:"avatar-upload-container"},B=["src"],S={key:1,class:"upload-progress"},C={key:2,class:"avatar-placeholder"},P={key:3,class:"avatar-overlay"},A={class:"overlay-actions"},G={class:"avatar-preview"},J=["src"],M=e({__name:"AvatarUpload",props:{modelValue:{type:String,default:""},size:{type:Number,default:100},maxSize:{type:Number,default:2},enablePreview:{type:Boolean,default:!0}},emits:["update:modelValue","success","error"],setup(e,{emit:M}){l(a=>({c4eae940:`${e.size}px`}));const N=e,T=M,U=t(!1),$=t(0),E=t(!1),I=s({get:()=>N.modelValue,set:e=>T("update:modelValue",e)}),q=s(()=>"/api/upload/avatar"),D=s(()=>({Authorization:`Bearer ${a()}`})),F=e=>{if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(e.type))return j.error("头像只能是 JPG、PNG、GIF 或 WebP 格式!"),!1;return e.size/1024/1024<N.maxSize?(U.value=!0,$.value=0,!0):(j.error(`头像大小不能超过 ${N.maxSize}MB!`),!1)},Q=e=>{$.value=Math.round(e.loaded/e.total*100)},R=e=>{U.value=!1,$.value=0,e.success?(I.value=e.data.url,j.success("头像上传成功!"),T("success",e.data)):(j.error(e.message||"头像上传失败!"),T("error",e))},W=e=>{U.value=!1,$.value=0,console.error("头像上传失败:",e),j.error("头像上传失败!"),T("error",e)},Z=()=>{const e=document.querySelector('.avatar-uploader input[type="file"]');e&&e.click()},H=()=>{b.confirm("确定要删除当前头像吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{I.value="",T("update:modelValue",""),j.success("头像已删除")}).catch(()=>{})},K=()=>{if(I.value){const e=document.createElement("a");e.href=I.value,e.download="avatar.jpg",e.click()}};return(a,l)=>{const t=g,s=f,b=h,j=_,M=x,N=z;return o(),r("div",V,[u(M,{class:"avatar-uploader",action:q.value,headers:D.value,"show-file-list":!1,"on-success":R,"on-error":W,"before-upload":F,"on-progress":Q,accept:"image/jpeg,image/jpg,image/png,image/gif,image/webp"},{default:i(()=>[c("div",{class:m(["avatar-wrapper",{uploading:U.value}])},[I.value&&!U.value?(o(),r("img",{key:0,src:I.value,class:"avatar",alt:"头像"},null,8,B)):U.value?(o(),r("div",S,[u(t,{type:"circle",percentage:$.value,width:Math.min(e.size-20,80),"stroke-width":4},null,8,["percentage","width"]),l[2]||(l[2]=c("div",{class:"progress-text"},"上传中...",-1))])):(o(),r("div",C,[u(s,{class:"avatar-uploader-icon"},{default:i(()=>[u(p(y))]),_:1}),l[3]||(l[3]=c("div",{class:"upload-text"},"点击上传头像",-1))])),I.value&&!U.value?(o(),r("div",P,[c("div",A,[u(j,{content:"更换头像",placement:"top"},{default:i(()=>[u(b,{type:"primary",size:"small",circle:"",onClick:n(Z,["stop"])},{default:i(()=>[u(s,null,{default:i(()=>[u(p(k))]),_:1})]),_:1})]),_:1}),u(j,{content:"删除头像",placement:"top"},{default:i(()=>[u(b,{type:"danger",size:"small",circle:"",onClick:n(H,["stop"])},{default:i(()=>[u(s,null,{default:i(()=>[u(p(w))]),_:1})]),_:1})]),_:1})])])):d("",!0)],2)]),_:1},8,["action","headers"]),u(N,{modelValue:E.value,"onUpdate:modelValue":l[1]||(l[1]=e=>E.value=e),title:"头像预览",width:"400px",center:""},{footer:i(()=>[u(b,{onClick:l[0]||(l[0]=e=>E.value=!1)},{default:i(()=>l[4]||(l[4]=[v("关闭",-1)])),_:1,__:[4]}),u(b,{type:"primary",onClick:K},{default:i(()=>l[5]||(l[5]=[v("下载",-1)])),_:1,__:[5]})]),default:i(()=>[c("div",G,[c("img",{src:I.value,alt:"头像预览"},null,8,J)])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b86ade83"]]);export{M as A};
