import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css               */import{bM as a,bN as l,U as t,bc as s,bd as n,bT as i,bU as o,aW as c,aV as r,be as d,bj as u,as as p,T as _,a4 as v,ab as m,bV as h,bR as f,au as g,Q as b,bQ as w,aU as y,aM as k,b4 as C,b2 as x,af as V,a8 as z,b0 as D,b3 as U,a_ as $,Y as j,an as L,a6 as F,aO as R,ak as A,aQ as E,b6 as S,b7 as q,aH as T,b8 as M,o as B,av as I,ax as O,ay as Y,bk as Q,b1 as N,bl as H,ad as P,aB as G,p as J,R as W}from"./element-plus-DcSKpKA8.js";/* empty css                 *//* empty css                        *//* empty css                        *//* empty css                  *//* empty css                             */import{b as Z}from"./browser-DJkR4j8n.js";import{r as K,L as X,d as ee,k as ae,l as le,E as te,z as se,t as ne,D as ie,A as oe,u as ce,n as re,e as de,y as ue,B as pe}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const _e={class:"qrcode-generator"},ve={class:"qrcode-content"},me={class:"link-info"},he={class:"qrcode-section"},fe={class:"size-text"},ge={class:"qrcode-preview"},be={class:"qrcode-container"},we=["width","height"],ye={class:"qrcode-actions"},ke={class:"usage-tips"},Ce=e({__name:"QRCodeGenerator",props:{visible:{type:Boolean,default:!1},linkData:{type:Object,default:()=>({})}},emits:["update:visible","close"],setup(e,{emit:y}){const k=e,C=y,x=K(!1),V=K(""),z=K(),D=X({size:300,color:{dark:"#000000",light:"#FFFFFF"},errorCorrectionLevel:"M",margin:2});ee(()=>k.visible,e=>{e&&k.linkData.short_url&&re(()=>{U()})});const U=async()=>{if(k.linkData.short_url)try{x.value=!0;const e={width:D.size,height:D.size,color:{dark:D.color.dark,light:D.color.light},errorCorrectionLevel:D.errorCorrectionLevel,margin:D.margin,type:"image/png"};await Z.toCanvas(z.value,k.linkData.short_url,e),V.value=z.value.toDataURL("image/png"),b.success("二维码生成成功")}catch(e){console.error("生成二维码失败:",e),b.error("生成二维码失败")}finally{x.value=!1}else b.warning("推广链接不能为空")},$=()=>{if(V.value)try{const e=document.createElement("a");e.download=`qrcode_${k.linkData.name||"promotion"}_${Date.now()}.png`,e.href=V.value,document.body.appendChild(e),e.click(),document.body.removeChild(e),w.success({title:"下载成功",message:"二维码图片已保存到本地"})}catch(e){console.error("下载失败:",e),b.error("下载失败")}else b.warning("请先生成二维码")},j=async()=>{if(V.value)try{const e=await fetch(V.value),a=await e.blob();if(navigator.clipboard&&window.ClipboardItem){const e=new ClipboardItem({"image/png":a});await navigator.clipboard.write([e]),w.success({title:"复制成功",message:"二维码图片已复制到剪贴板"})}else await navigator.clipboard.writeText(V.value),b.success("二维码数据已复制到剪贴板")}catch(e){console.error("复制失败:",e),b.error("复制失败，请手动保存图片")}else b.warning("请先生成二维码")},L=()=>{if(V.value)try{const e=window.open("","_blank"),a=`\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <title>打印二维码 - ${k.linkData.name}</title>\n        <style>\n          body {\n            margin: 0;\n            padding: 20px;\n            font-family: Arial, sans-serif;\n            text-align: center;\n          }\n          .print-header {\n            margin-bottom: 20px;\n          }\n          .print-header h2 {\n            margin: 0 0 10px 0;\n            color: #333;\n          }\n          .print-header p {\n            margin: 5px 0;\n            color: #666;\n            font-size: 14px;\n          }\n          .qrcode-image {\n            margin: 20px 0;\n          }\n          .qrcode-image img {\n            max-width: 300px;\n            height: auto;\n          }\n          .print-footer {\n            margin-top: 20px;\n            font-size: 12px;\n            color: #999;\n          }\n          @media print {\n            body { margin: 0; }\n            .print-footer { page-break-inside: avoid; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="print-header">\n          <h2>${k.linkData.name||"推广链接二维码"}</h2>\n          <p>推广链接: ${k.linkData.short_url}</p>\n          <p>生成时间: ${(new Date).toLocaleString("zh-CN")}</p>\n        </div>\n        <div class="qrcode-image">\n          <img src="${V.value}" alt="推广链接二维码" />\n        </div>\n        <div class="print-footer">\n          <p>扫描二维码访问推广链接</p>\n          <p>支持微信、支付宝等主流扫码工具</p>\n        </div>\n      </body>\n      </html>\n    `;e.document.write(a),e.document.close(),e.onload=()=>{setTimeout(()=>{e.print(),e.close()},500)},b.success("正在准备打印...")}catch(e){console.error("打印失败:",e),b.error("打印失败")}else b.warning("请先生成二维码")},F=e=>{C("update:visible",e)},R=()=>{C("update:visible",!1),C("close")};return(b,w)=>{const y=l,k=a,C=i,A=n,E=o,S=r,q=c,T=d,M=s,B=_,I=p,O=f,Y=g,Q=u;return le(),ae("div",_e,[te(Y,{"model-value":e.visible,title:"生成推广链接二维码",width:"500px","onUpdate:modelValue":F,onClose:R},{footer:se(()=>[te(I,{onClick:R},{default:se(()=>w[13]||(w[13]=[ie("关闭",-1)])),_:1,__:[13]}),te(I,{type:"primary",onClick:$,disabled:!V.value},{default:se(()=>[te(B,null,{default:se(()=>[te(ce(v))]),_:1}),w[14]||(w[14]=ie(" 下载二维码 ",-1))]),_:1,__:[14]},8,["disabled"])]),default:se(()=>[ne("div",ve,[ne("div",me,[w[5]||(w[5]=ne("h4",null,"推广链接信息",-1)),te(k,{column:1,border:""},{default:se(()=>[te(y,{label:"链接名称"},{default:se(()=>[ie(t(e.linkData.name),1)]),_:1}),te(y,{label:"推广链接"},{default:se(()=>[ie(t(e.linkData.short_url),1)]),_:1}),te(y,{label:"目标URL"},{default:se(()=>[ie(t(e.linkData.target_url),1)]),_:1})]),_:1})]),ne("div",he,[w[6]||(w[6]=ne("h4",null,"二维码设置",-1)),te(M,{model:D,"label-width":"100px"},{default:se(()=>[te(A,{label:"二维码大小"},{default:se(()=>[te(C,{modelValue:D.size,"onUpdate:modelValue":w[0]||(w[0]=e=>D.size=e),min:200,max:500,step:50,"show-stops":"","show-tooltip":"",onChange:U},null,8,["modelValue"]),ne("span",fe,t(D.size)+"px",1)]),_:1}),te(A,{label:"前景色"},{default:se(()=>[te(E,{modelValue:D.color.dark,"onUpdate:modelValue":w[1]||(w[1]=e=>D.color.dark=e),onChange:U},null,8,["modelValue"])]),_:1}),te(A,{label:"背景色"},{default:se(()=>[te(E,{modelValue:D.color.light,"onUpdate:modelValue":w[2]||(w[2]=e=>D.color.light=e),onChange:U},null,8,["modelValue"])]),_:1}),te(A,{label:"容错级别"},{default:se(()=>[te(q,{modelValue:D.errorCorrectionLevel,"onUpdate:modelValue":w[3]||(w[3]=e=>D.errorCorrectionLevel=e),onChange:U},{default:se(()=>[te(S,{label:"低 (L)",value:"L"}),te(S,{label:"中 (M)",value:"M"}),te(S,{label:"高 (Q)",value:"Q"}),te(S,{label:"最高 (H)",value:"H"})]),_:1},8,["modelValue"])]),_:1}),te(A,{label:"边距"},{default:se(()=>[te(T,{modelValue:D.margin,"onUpdate:modelValue":w[4]||(w[4]=e=>D.margin=e),min:0,max:10,onChange:U},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),ne("div",ge,[w[10]||(w[10]=ne("h4",null,"二维码预览",-1)),oe((le(),ae("div",be,[ne("canvas",{ref_key:"qrcodeCanvas",ref:z,width:D.size,height:D.size,class:"qrcode-canvas"},null,8,we)])),[[Q,x.value]]),ne("div",ye,[te(I,{onClick:$,disabled:!V.value},{default:se(()=>[te(B,null,{default:se(()=>[te(ce(v))]),_:1}),w[7]||(w[7]=ie(" 下载二维码 ",-1))]),_:1,__:[7]},8,["disabled"]),te(I,{onClick:j,disabled:!V.value},{default:se(()=>[te(B,null,{default:se(()=>[te(ce(m))]),_:1}),w[8]||(w[8]=ie(" 复制图片 ",-1))]),_:1,__:[8]},8,["disabled"]),te(I,{onClick:L,disabled:!V.value},{default:se(()=>[te(B,null,{default:se(()=>[te(ce(h))]),_:1}),w[9]||(w[9]=ie(" 打印二维码 ",-1))]),_:1,__:[9]},8,["disabled"])])]),ne("div",ke,[w[12]||(w[12]=ne("h4",null,"使用说明",-1)),te(O,{type:"info",closable:!1},{title:se(()=>w[11]||(w[11]=[ne("div",{class:"tips-content"},[ne("p",null,[ne("strong",null,"扫码说明：")]),ne("ul",null,[ne("li",null,"用户扫描此二维码将直接跳转到您的推广链接"),ne("li",null,"系统会自动记录通过二维码产生的访问和转化"),ne("li",null,"建议在宣传材料、名片、海报等场景使用"),ne("li",null,"二维码支持微信、支付宝等主流扫码工具")])],-1)])),_:1})])])]),_:1},8,["model-value"])])}}},[["__scopeId","data-v-29c4cfea"]]),xe={class:"promotion-links"},Ve={class:"page-header"},ze={class:"header-actions"},De={class:"card-header"},Ue={class:"overview-item"},$e={class:"overview-icon",style:{"background-color":"#67C23A20",color:"#67C23A"}},je={class:"overview-content"},Le={class:"overview-value"},Fe={class:"overview-item"},Re={class:"overview-icon",style:{"background-color":"#409EFF20",color:"#409EFF"}},Ae={class:"overview-content"},Ee={class:"overview-value"},Se={class:"overview-item"},qe={class:"overview-icon",style:{"background-color":"#E6A23C20",color:"#E6A23C"}},Te={class:"overview-content"},Me={class:"overview-value"},Be={class:"overview-item"},Ie={class:"overview-icon",style:{"background-color":"#F56C6C20",color:"#F56C6C"}},Oe={class:"overview-content"},Ye={class:"overview-value"},Qe={class:"stat-card"},Ne={class:"stat-icon",style:{"background-color":"#409EFF20",color:"#409EFF"}},He={class:"stat-content"},Pe={class:"stat-value"},Ge={class:"stat-trend positive"},Je={class:"stat-card"},We={class:"stat-icon",style:{"background-color":"#67C23A20",color:"#67C23A"}},Ze={class:"stat-content"},Ke={class:"stat-value"},Xe={class:"stat-trend positive"},ea={class:"stat-card"},aa={class:"stat-icon",style:{"background-color":"#E6A23C20",color:"#E6A23C"}},la={class:"stat-content"},ta={class:"stat-value"},sa={class:"stat-trend positive"},na={class:"stat-card"},ia={class:"stat-icon",style:{"background-color":"#F56C6C20",color:"#F56C6C"}},oa={class:"stat-content"},ca={class:"stat-value"},ra={class:"stat-trend negative"},da={class:"action-icon",style:{color:"#409EFF"}},ua={class:"action-icon",style:{color:"#67C23A"}},pa={class:"action-icon",style:{color:"#E6A23C"}},_a={class:"action-icon",style:{color:"#606266"}},va={class:"card-header"},ma={class:"header-actions"},ha={class:"link-info"},fa={class:"link-name"},ga={class:"link-desc"},ba={class:"link-url-with-status"},wa={class:"link-status-info"},ya={class:"domain-info"},ka={class:"domain-name"},Ca={class:"domain-stats"},xa={class:"success-rate"},Va={key:1,class:"no-expiry"},za={class:"pagination-wrapper"},Da={class:"dev-notice"},Ua={class:"analytics-content"},$a={class:"analytics-card"},ja={class:"card-header"},La={class:"card-value"},Fa={class:"card-trend positive"},Ra={class:"analytics-card"},Aa={class:"card-header"},Ea={class:"card-value"},Sa={class:"card-trend positive"},qa={class:"analytics-card"},Ta={class:"card-header"},Ma={class:"card-value"},Ba={class:"card-trend positive"},Ia={class:"analytics-card"},Oa={class:"card-header"},Ya={class:"card-value"},Qa={class:"card-trend negative"},Na={class:"domain-name"},Ha={class:"protection-stats"},Pa={class:"stat-item"},Ga={class:"stat-value success"},Ja={class:"stat-item"},Wa={class:"stat-value warning"},Za={class:"stat-item"},Ka={class:"stat-value primary"},Xa={class:"stat-item"},el={class:"stat-value success"},al={class:"suggestions"},ll={class:"suggestion-item"},tl={class:"suggestion-item"},sl={class:"suggestion-item"},nl={class:"suggestion-item"},il={class:"suggestion-content"},ol={class:"analytics-footer"},cl={class:"help-content"},rl={class:"help-section"},dl={class:"feature-box"},ul={class:"help-footer"},pl=e({__name:"PromotionLinks",setup(e){const a=K(!1),l=K(!1),i=K(!1),o=K(!1),d=K(!1),h=K(!1),f=K(!1),Z=K(null),ee=K(""),re=K(""),_e=X({total_links:28,total_clicks:15680,total_conversions:1256,conversion_rate:8,protected_links:25,avg_domain_health:88.5,domain_switches_today:3,protection_success_rate:98.2}),ve=K([]),me=X({current_page:1,per_page:20,total:0}),he=K([]),fe=X({total:0,healthy:0,warning:0,critical:0}),ge=X({name:"",description:"",target_url:"",type:"",expires_at:"",custom_suffix:""}),be={name:[{required:!0,message:"请输入链接名称",trigger:"blur"}],target_url:[{required:!0,message:"请输入目标URL",trigger:"blur"},{type:"url",message:"请输入有效的URL",trigger:"blur"}],type:[{required:!0,message:"请选择链接类型",trigger:"change"}]},we=K(),ye=[{id:1,name:"VIP产品推广",description:"高端VIP产品推广链接",short_url:"https://d1.linkhub.pro/vip2024",target_url:"https://example.com/vip-product",type:"product",status:"active",click_count:1580,conversion_count:126,conversion_rate:8,created_at:new Date(Date.now()-1296e6),expires_at:new Date(Date.now()+2592e6),domain_id:1,domain_name:"d1.linkhub.pro",domain_health:95,anti_detection_enabled:!0,last_domain_switch:new Date(Date.now()-432e6),access_success_rate:98.5},{id:2,name:"新年活动推广",description:"2024新年特惠活动",short_url:"https://d2.linkhub.pro/ny2024",target_url:"https://example.com/new-year-sale",type:"activity",status:"active",click_count:2340,conversion_count:187,conversion_rate:8,created_at:new Date(Date.now()-864e6),expires_at:new Date(Date.now()+1296e6),domain_id:2,domain_name:"d2.linkhub.pro",domain_health:88,anti_detection_enabled:!0,last_domain_switch:new Date(Date.now()-1728e5),access_success_rate:96.8},{id:3,name:"用户注册邀请",description:"邀请新用户注册",short_url:"https://d3.linkhub.pro/reg2024",target_url:"https://example.com/register",type:"register",status:"paused",click_count:890,conversion_count:67,conversion_rate:7.5,created_at:new Date(Date.now()-216e7),expires_at:null,domain_id:3,domain_name:"d3.linkhub.pro",domain_health:72,anti_detection_enabled:!1,last_domain_switch:new Date(Date.now()-864e6),access_success_rate:89.2}],ke=async()=>{try{a.value=!0,await new Promise(e=>setTimeout(e,500));let e=[...ye];ee.value&&(e=e.filter(e=>e.name.includes(ee.value)||e.description.includes(ee.value)||e.short_url.includes(ee.value))),re.value&&(e=e.filter(e=>e.status===re.value)),ve.value=e,me.total=e.length}catch(e){b.error("加载推广链接失败")}finally{a.value=!1}},pl=()=>{ke(),b.success("数据已刷新")},_l=function(e,a){let l;return function(...t){clearTimeout(l),l=setTimeout(()=>{clearTimeout(l),e(...t)},a)}}(()=>{me.current_page=1,ke()},500),vl=e=>{Z.value=e,d.value=!0},ml=async e=>{try{await W.confirm(`确定要暂停链接"${e.name}"吗？`,"确认暂停",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.status="paused",b.success("链接已暂停")}catch(a){"cancel"!==a&&b.error("暂停失败")}},hl=async e=>{try{e.status="active",b.success("链接已恢复")}catch(a){b.error("恢复失败")}},fl=async e=>{try{await W.confirm(`确定要删除链接"${e.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=ve.value.findIndex(a=>a.id===e.id);a>-1&&(ve.value.splice(a,1),_e.total_links--),b.success("链接删除成功")}catch(a){"cancel"!==a&&b.error("删除失败")}},gl=async()=>{try{await we.value.validate(),l.value=!0;ge.target_url,ge.custom_suffix,ge.expires_at,ge.name;await new Promise(e=>setTimeout(e,1e3));const e=he.value.length>0?he.value.reduce((e,a)=>a.health_score>e.health_score?a:e):{id:1,domain:"d1.linkhub.pro",health_score:95},a={id:Date.now(),...ge,short_url:`https://${e.domain}/${ge.custom_suffix||Math.random().toString(36).substr(2,8)}`,status:"active",click_count:0,conversion_count:0,conversion_rate:0,created_at:new Date,domain_id:e.id,domain_name:e.domain,domain_health:e.health_score,anti_detection_enabled:!0,last_domain_switch:new Date,access_success_rate:100};ve.value.unshift(a),_e.total_links++,Object.keys(ge).forEach(e=>{ge[e]=""}),i.value=!1,b.success(`推广链接创建成功！已自动选择最佳域名: ${e.domain}`)}catch(e){console.error("创建链接失败:",e),b.error("创建链接失败")}finally{l.value=!1}},bl=()=>{if(0===ve.value.length)return void b.warning("暂无推广链接，请先创建链接");const e=ve.value[0];Z.value=e,d.value=!0,b.success(`正在为"${e.name}"生成二维码`)},wl=async()=>{try{b.info("正在导出推广链接数据...");const e=ve.value.map(e=>({"链接名称":e.name,"链接描述":e.description,"推广链接":e.short_url,"目标URL":e.target_url,"链接类型":El(e.type),"状态":jl(e.status),"点击量":e.click_count,"转化数":e.conversion_count,"转化率":`${e.conversion_rate}%`,"域名":e.domain_name,"域名健康度":`${e.domain_health}%`,"防红保护":e.anti_detection_enabled?"已开启":"未开启","访问成功率":`${e.access_success_rate}%`,"创建时间":Al(e.created_at),"过期时间":e.expires_at?Al(e.expires_at):"永久有效"})),a=Object.keys(e[0]),l=[a.join(","),...e.map(e=>a.map(a=>`"${e[a]}"`).join(","))].join("\n"),t=new Blob(["\ufeff"+l],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a"),n=URL.createObjectURL(t);s.setAttribute("href",n),s.setAttribute("download",`推广链接数据_${(new Date).toISOString().slice(0,10)}.csv`),s.style.visibility="hidden",document.body.appendChild(s),s.click(),document.body.removeChild(s),b.success(`成功导出 ${e.length} 条推广链接数据`)}catch(e){console.error("导出失败:",e),b.error("导出失败，请重试")}},yl=()=>{f.value=!0},kl=()=>{h.value=!0},Cl=()=>{Z.value=null,d.value=!1},xl=()=>{window.open("/docs/promotion-links-anti-detection-guide.md","_blank"),b.success("完整使用说明已在新窗口中打开")},Vl=async e=>{try{await W.confirm(`当前域名 ${e.domain_name} 健康度为 ${e.domain_health}%，是否切换到更健康的域名？`,"域名切换建议",{confirmButtonText:"立即切换",cancelButtonText:"暂不切换",type:"warning"});const a=he.value.find(a=>a.id!==e.domain_id&&a.health_score>e.domain_health)||{id:99,domain:"backup.linkhub.pro",health_score:98},l=ve.value.findIndex(a=>a.id===e.id);l>-1&&(ve.value[l]={...ve.value[l],domain_id:a.id,domain_name:a.domain,domain_health:a.health_score,short_url:ve.value[l].short_url.replace(e.domain_name,a.domain),last_domain_switch:new Date,access_success_rate:100}),b.success(`域名已切换到: ${a.domain}，健康度: ${a.health_score}%`)}catch(a){"cancel"!==a&&b.error("域名切换失败")}},zl=async e=>{try{const a=e.anti_detection_enabled?"关闭":"开启";await W.confirm(`确定要${a}链接"${e.name}"的防红保护吗？`,`${a}防红保护`,{confirmButtonText:"确定",cancelButtonText:"取消",type:e.anti_detection_enabled?"warning":"info"});const l=ve.value.findIndex(a=>a.id===e.id);l>-1&&(ve.value[l].anti_detection_enabled=!e.anti_detection_enabled),b.success(`防红保护已${a}`)}catch(a){"cancel"!==a&&b.error(`${action}防红保护失败`)}},Dl=()=>{const e={};return ve.value.forEach(a=>{e[a.type]||(e[a.type]={type:a.type,typeName:El(a.type),count:0,clicks:0,conversions:0}),e[a.type].count++,e[a.type].clicks+=a.click_count,e[a.type].conversions+=a.conversion_count}),Object.values(e).map(e=>({...e,conversionRate:e.clicks>0?Math.round(e.conversions/e.clicks*100*100)/100:0}))},Ul=()=>{const e={};return ve.value.forEach(a=>{if(e[a.domain_name]||(e[a.domain_name]={domain:a.domain_name,health:a.domain_health,linkCount:0,totalClicks:0,successRate:a.access_success_rate,lastSwitch:null}),e[a.domain_name].linkCount++,e[a.domain_name].totalClicks+=a.click_count,a.last_domain_switch){const l=Rl(a.last_domain_switch);(!e[a.domain_name].lastSwitch||l>e[a.domain_name].lastSwitch)&&(e[a.domain_name].lastSwitch=l)}}),Object.values(e)},$l=async()=>{try{b.info("正在生成分析报告...");const e={"生成时间":(new Date).toLocaleString("zh-CN"),"总体统计":{"总链接数":_e.total_links,"总点击量":_e.total_clicks,"总转化数":_e.total_conversions,"平均转化率":`${_e.conversion_rate}%`,"防红保护覆盖率":`${Math.round(_e.protected_links/_e.total_links*100)}%`,"保护成功率":`${_e.protection_success_rate}%`},"链接类型分析":Dl(),"域名健康分析":Ul()},a=JSON.stringify(e,null,2),l=new Blob([a],{type:"application/json;charset=utf-8;"}),t=document.createElement("a"),s=URL.createObjectURL(l);t.setAttribute("href",s),t.setAttribute("download",`推广链接分析报告_${(new Date).toISOString().slice(0,10)}.json`),t.style.visibility="hidden",document.body.appendChild(t),t.click(),document.body.removeChild(t),b.success("分析报告导出成功")}catch(e){console.error("导出分析报告失败:",e),b.error("导出失败，请重试")}},jl=e=>({active:"活跃",paused:"暂停",expired:"过期"}[e]||"未知"),Ll=e=>e>=10?"high-rate":e>=5?"medium-rate":"low-rate",Fl=e=>{const a=new Date,l=new Date(e),t=Math.ceil((l-a)/864e5);return t<0?"expired":t<=7?"expiring-soon":"normal-expiry"},Rl=e=>new Date(e).toLocaleDateString("zh-CN"),Al=e=>new Date(e).toLocaleString("zh-CN"),El=e=>({product:"产品推广",activity:"活动推广",register:"注册邀请",other:"其他"}[e]||"未知"),Sl=e=>e>=90?"success":e>=80?"primary":e>=70?"warning":"danger";return de(()=>{ke(),(async()=>{try{he.value=[{id:1,domain:"d1.linkhub.pro",health_score:95,status:"active"},{id:2,domain:"d2.linkhub.pro",health_score:88,status:"active"},{id:3,domain:"d3.linkhub.pro",health_score:72,status:"warning"},{id:4,domain:"backup.linkhub.pro",health_score:98,status:"active"}],fe.total=he.value.length,fe.healthy=he.value.filter(e=>e.health_score>=90).length,fe.warning=he.value.filter(e=>e.health_score>=70&&e.health_score<90).length,fe.critical=he.value.filter(e=>e.health_score<70).length}catch(e){console.error("加载域名列表失败:",e)}})()}),(e,W)=>{const K=_,X=p,de=x,he=C,fe=U,ye=T,Al=r,El=c,ql=q,Tl=M,Ml=Y,Bl=O,Il=I,Ol=S,Yl=Q,Ql=n,Nl=H,Hl=s,Pl=g,Gl=G,Jl=u;return le(),ae("div",xe,[ne("div",Ve,[W[23]||(W[23]=ne("div",{class:"header-left"},[ne("h2",null,"推广链接管理"),ne("p",{class:"page-description"},"管理您的推广链接，跟踪推广效果，优化推广策略")],-1)),ne("div",ze,[te(X,{type:"primary",onClick:W[0]||(W[0]=e=>i.value=!0)},{default:se(()=>[te(K,null,{default:se(()=>[te(ce(y))]),_:1}),W[21]||(W[21]=ie(" 创建链接 ",-1))]),_:1,__:[21]}),te(X,{onClick:pl},{default:se(()=>[te(K,null,{default:se(()=>[te(ce(k))]),_:1}),W[22]||(W[22]=ie(" 刷新数据 ",-1))]),_:1,__:[22]})])]),te(fe,{class:"anti-detection-overview",style:{"margin-bottom":"20px"}},{header:se(()=>[ne("div",De,[W[25]||(W[25]=ne("span",null,"🛡️ 防红系统状态",-1)),te(X,{type:"text",onClick:W[1]||(W[1]=a=>e.$router.push("/anti-block/dashboard"))},{default:se(()=>W[24]||(W[24]=[ie(" 查看详情 → ",-1)])),_:1,__:[24]})])]),default:se(()=>[te(he,{gutter:15},{default:se(()=>[te(de,{span:6},{default:se(()=>[ne("div",Ue,[ne("div",$e,[te(K,{size:20},{default:se(()=>[te(ce(V))]),_:1})]),ne("div",je,[ne("div",Le,t(_e.protected_links)+"/"+t(_e.total_links),1),W[26]||(W[26]=ne("div",{class:"overview-label"},"防红保护",-1))])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",Fe,[ne("div",Re,[te(K,{size:20},{default:se(()=>[te(ce(z))]),_:1})]),ne("div",Ae,[ne("div",Ee,t(_e.avg_domain_health)+"%",1),W[27]||(W[27]=ne("div",{class:"overview-label"},"平均域名健康度",-1))])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",Se,[ne("div",qe,[te(K,{size:20},{default:se(()=>[te(ce(k))]),_:1})]),ne("div",Te,[ne("div",Me,t(_e.domain_switches_today),1),W[28]||(W[28]=ne("div",{class:"overview-label"},"今日域名切换",-1))])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",Be,[ne("div",Ie,[te(K,{size:20},{default:se(()=>[te(ce(D))]),_:1})]),ne("div",Oe,[ne("div",Ye,t(_e.protection_success_rate)+"%",1),W[29]||(W[29]=ne("div",{class:"overview-label"},"保护成功率",-1))])])]),_:1})]),_:1})]),_:1}),te(he,{gutter:20,class:"stats-row"},{default:se(()=>[te(de,{span:6},{default:se(()=>[ne("div",Qe,[ne("div",Ne,[te(K,{size:24},{default:se(()=>[te(ce(z))]),_:1})]),ne("div",He,[ne("div",Pe,t(_e.total_links),1),W[31]||(W[31]=ne("div",{class:"stat-title"},"推广链接",-1)),ne("div",Ge,[te(K,null,{default:se(()=>[te(ce($))]),_:1}),W[30]||(W[30]=ie(" 8.5% ",-1))])])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",Je,[ne("div",We,[te(K,{size:24},{default:se(()=>[te(ce(j))]),_:1})]),ne("div",Ze,[ne("div",Ke,t(_e.total_clicks),1),W[33]||(W[33]=ne("div",{class:"stat-title"},"总点击量",-1)),ne("div",Xe,[te(K,null,{default:se(()=>[te(ce($))]),_:1}),W[32]||(W[32]=ie(" 15.2% ",-1))])])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",ea,[ne("div",aa,[te(K,{size:24},{default:se(()=>[te(ce(L))]),_:1})]),ne("div",la,[ne("div",ta,t(_e.total_conversions),1),W[35]||(W[35]=ne("div",{class:"stat-title"},"转化数量",-1)),ne("div",sa,[te(K,null,{default:se(()=>[te(ce($))]),_:1}),W[34]||(W[34]=ie(" 12.3% ",-1))])])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",na,[ne("div",ia,[te(K,{size:24},{default:se(()=>[te(ce(F))]),_:1})]),ne("div",oa,[ne("div",ca,t(_e.conversion_rate)+"%",1),W[37]||(W[37]=ne("div",{class:"stat-title"},"转化率",-1)),ne("div",ra,[te(K,null,{default:se(()=>[te(ce(R))]),_:1}),W[36]||(W[36]=ie(" 1.2% ",-1))])])])]),_:1})]),_:1}),te(fe,{class:"quick-actions-card"},{header:se(()=>W[38]||(W[38]=[ne("span",null,"快捷操作",-1)])),default:se(()=>[te(he,{gutter:20},{default:se(()=>[te(de,{span:6},{default:se(()=>[ne("div",{class:"action-item",onClick:bl},[ne("div",da,[te(K,{size:32},{default:se(()=>[te(ce(A))]),_:1})]),W[39]||(W[39]=ne("span",{class:"action-text"},"生成二维码",-1)),W[40]||(W[40]=ne("span",{class:"action-desc"},"为推广链接生成二维码",-1))])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",{class:"action-item",onClick:wl},[ne("div",ua,[te(K,{size:32},{default:se(()=>[te(ce(v))]),_:1})]),W[41]||(W[41]=ne("span",{class:"action-text"},"批量导出",-1)),W[42]||(W[42]=ne("span",{class:"action-desc"},"导出推广链接数据",-1))])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",{class:"action-item",onClick:yl},[ne("div",pa,[te(K,{size:32},{default:se(()=>[te(ce(F))]),_:1})]),W[43]||(W[43]=ne("span",{class:"action-text"},"数据分析",-1)),W[44]||(W[44]=ne("span",{class:"action-desc"},"查看推广效果分析",-1))])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",{class:"action-item",onClick:kl},[ne("div",_a,[te(K,{size:32},{default:se(()=>[te(ce(E))]),_:1})]),W[45]||(W[45]=ne("span",{class:"action-text"},"使用帮助",-1)),W[46]||(W[46]=ne("span",{class:"action-desc"},"查看使用说明文档",-1))])]),_:1})]),_:1})]),_:1}),te(fe,null,{header:se(()=>[ne("div",va,[W[47]||(W[47]=ne("span",null,"推广链接列表",-1)),ne("div",ma,[te(ye,{modelValue:ee.value,"onUpdate:modelValue":W[2]||(W[2]=e=>ee.value=e),placeholder:"搜索链接名称或URL",style:{width:"200px","margin-right":"10px"},clearable:"",onInput:ce(_l)},{prefix:se(()=>[te(K,null,{default:se(()=>[te(ce(N))]),_:1})]),_:1},8,["modelValue","onInput"]),te(El,{modelValue:re.value,"onUpdate:modelValue":W[3]||(W[3]=e=>re.value=e),placeholder:"链接状态",style:{width:"120px"},onChange:ke},{default:se(()=>[te(Al,{label:"全部",value:""}),te(Al,{label:"活跃",value:"active"}),te(Al,{label:"暂停",value:"paused"}),te(Al,{label:"过期",value:"expired"})]),_:1},8,["modelValue"])])])]),default:se(()=>[oe((le(),ue(Ol,{data:ve.value,stripe:""},{default:se(()=>[te(ql,{type:"selection",width:"55"}),te(ql,{prop:"name",label:"链接名称",width:"200"},{default:se(({row:e})=>[ne("div",ha,[ne("div",fa,t(e.name),1),ne("div",ga,t(e.description),1)])]),_:1}),te(ql,{label:"推广链接",width:"350"},{default:se(({row:e})=>[ne("div",ba,[te(ye,{value:e.short_url,readonly:"",size:"small"},{append:se(()=>[te(X,{onClick:a=>(async e=>{try{if(navigator.clipboard)await navigator.clipboard.writeText(e),w.success({title:"复制成功",message:"推广链接已复制到剪贴板"});else{const a=document.createElement("textarea");a.value=e,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),b.success("链接已复制")}}catch(a){b.error("复制失败")}})(e.short_url),size:"small"},{default:se(()=>[te(K,null,{default:se(()=>[te(ce(m))]),_:1})]),_:2},1032,["onClick"])]),_:2},1032,["value"]),ne("div",wa,[te(Tl,{type:Sl(e.domain_health),size:"mini"},{default:se(()=>[ie(" 域名健康: "+t(e.domain_health)+"% ",1)]),_:2},1032,["type"]),e.anti_detection_enabled?(le(),ue(Tl,{key:0,type:"success",size:"mini"},{default:se(()=>W[48]||(W[48]=[ie(" 防红保护 ",-1)])),_:1,__:[48]})):(le(),ue(Tl,{key:1,type:"warning",size:"mini"},{default:se(()=>W[49]||(W[49]=[ie(" 未保护 ",-1)])),_:1,__:[49]}))])])]),_:1}),te(ql,{label:"状态",width:"100"},{default:se(({row:e})=>{return[te(Tl,{type:(a=e.status,{active:"success",paused:"warning",expired:"danger"}[a]||"info"),size:"small"},{default:se(()=>[ie(t(jl(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),te(ql,{prop:"click_count",label:"点击量",width:"100"}),te(ql,{prop:"conversion_count",label:"转化数",width:"100"}),te(ql,{label:"转化率",width:"100"},{default:se(({row:e})=>[ne("span",{class:B(Ll(e.conversion_rate))},t(e.conversion_rate)+"% ",3)]),_:1}),te(ql,{label:"域名信息",width:"150"},{default:se(({row:e})=>[ne("div",ya,[ne("div",ka,t(e.domain_name),1),ne("div",Ca,[ne("span",xa,"成功率: "+t(e.access_success_rate)+"%",1)])])]),_:1}),te(ql,{label:"创建时间",width:"120"},{default:se(({row:e})=>[ie(t(Rl(e.created_at)),1)]),_:1}),te(ql,{label:"过期时间",width:"120"},{default:se(({row:e})=>[e.expires_at?(le(),ae("span",{key:0,class:B(Fl(e.expires_at))},t(Rl(e.expires_at)),3)):(le(),ae("span",Va,"永久有效"))]),_:1}),te(ql,{label:"操作",width:"250",fixed:"right"},{default:se(({row:e})=>[te(X,{size:"small",onClick:a=>{return l=e,b.info(`查看"${l.name}"的详细统计`),void(o.value=!0);var l}},{default:se(()=>W[50]||(W[50]=[ie(" 统计 ",-1)])),_:2,__:[50]},1032,["onClick"]),te(X,{size:"small",onClick:a=>{return l=e,b.info(`编辑链接"${l.name}"`),void(o.value=!0);var l}},{default:se(()=>W[51]||(W[51]=[ie(" 编辑 ",-1)])),_:2,__:[51]},1032,["onClick"]),e.domain_health<80?(le(),ue(X,{key:0,size:"small",type:"warning",onClick:a=>Vl(e)},{default:se(()=>W[52]||(W[52]=[ie(" 切换域名 ",-1)])),_:2,__:[52]},1032,["onClick"])):pe("",!0),te(Il,{onCommand:a=>((e,a)=>{switch(e){case"qrcode":vl(a);break;case"switch-domain":Vl(a);break;case"toggle-protection":zl(a);break;case"pause":ml(a);break;case"resume":hl(a);break;case"delete":fl(a)}})(a,e)},{dropdown:se(()=>[te(Bl,null,{default:se(()=>[te(Ml,{command:"qrcode"},{default:se(()=>W[54]||(W[54]=[ie("生成二维码",-1)])),_:1,__:[54]}),te(Ml,{command:"switch-domain"},{default:se(()=>W[55]||(W[55]=[ie("切换域名",-1)])),_:1,__:[55]}),te(Ml,{command:"toggle-protection"},{default:se(()=>[ie(t(e.anti_detection_enabled?"关闭防红":"开启防红"),1)]),_:2},1024),"active"===e.status?(le(),ue(Ml,{key:0,command:"pause"},{default:se(()=>W[56]||(W[56]=[ie("暂停",-1)])),_:1,__:[56]})):pe("",!0),"paused"===e.status?(le(),ue(Ml,{key:1,command:"resume"},{default:se(()=>W[57]||(W[57]=[ie("恢复",-1)])),_:1,__:[57]})):pe("",!0),te(Ml,{command:"delete",divided:""},{default:se(()=>W[58]||(W[58]=[ie("删除",-1)])),_:1,__:[58]})]),_:2},1024)]),default:se(()=>[te(X,{size:"small"},{default:se(()=>[W[53]||(W[53]=ie(" 更多",-1)),te(K,{class:"el-icon--right"},{default:se(()=>[te(ce(R))]),_:1})]),_:1,__:[53]})]),_:2},1032,["onCommand"])]),_:1})]),_:1},8,["data"])),[[Jl,a.value]]),ne("div",za,[te(Yl,{"current-page":me.current_page,"onUpdate:currentPage":W[4]||(W[4]=e=>me.current_page=e),"page-size":me.per_page,"onUpdate:pageSize":W[5]||(W[5]=e=>me.per_page=e),total:me.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ke,onCurrentChange:ke},null,8,["current-page","page-size","total"])])]),_:1}),te(Pl,{modelValue:i.value,"onUpdate:modelValue":W[13]||(W[13]=e=>i.value=e),title:"创建推广链接",width:"600px"},{footer:se(()=>[te(X,{onClick:W[12]||(W[12]=e=>i.value=!1)},{default:se(()=>W[59]||(W[59]=[ie("取消",-1)])),_:1,__:[59]}),te(X,{type:"primary",onClick:gl,loading:l.value},{default:se(()=>W[60]||(W[60]=[ie(" 创建 ",-1)])),_:1,__:[60]},8,["loading"])]),default:se(()=>[te(Hl,{model:ge,rules:be,ref_key:"linkFormRef",ref:we,"label-width":"100px"},{default:se(()=>[te(Ql,{label:"链接名称",prop:"name"},{default:se(()=>[te(ye,{modelValue:ge.name,"onUpdate:modelValue":W[6]||(W[6]=e=>ge.name=e),placeholder:"请输入链接名称"},null,8,["modelValue"])]),_:1}),te(Ql,{label:"链接描述",prop:"description"},{default:se(()=>[te(ye,{modelValue:ge.description,"onUpdate:modelValue":W[7]||(W[7]=e=>ge.description=e),type:"textarea",rows:3,placeholder:"请输入链接描述"},null,8,["modelValue"])]),_:1}),te(Ql,{label:"目标URL",prop:"target_url"},{default:se(()=>[te(ye,{modelValue:ge.target_url,"onUpdate:modelValue":W[8]||(W[8]=e=>ge.target_url=e),placeholder:"请输入目标URL"},null,8,["modelValue"])]),_:1}),te(Ql,{label:"链接类型",prop:"type"},{default:se(()=>[te(El,{modelValue:ge.type,"onUpdate:modelValue":W[9]||(W[9]=e=>ge.type=e),placeholder:"选择类型"},{default:se(()=>[te(Al,{label:"产品推广",value:"product"}),te(Al,{label:"活动推广",value:"activity"}),te(Al,{label:"注册邀请",value:"register"}),te(Al,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),te(Ql,{label:"过期时间",prop:"expires_at"},{default:se(()=>[te(Nl,{modelValue:ge.expires_at,"onUpdate:modelValue":W[10]||(W[10]=e=>ge.expires_at=e),type:"datetime",placeholder:"选择过期时间（可选）",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),te(Ql,{label:"自定义后缀"},{default:se(()=>[te(ye,{modelValue:ge.custom_suffix,"onUpdate:modelValue":W[11]||(W[11]=e=>ge.custom_suffix=e),placeholder:"自定义短链接后缀（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),te(Ce,{visible:d.value,"onUpdate:visible":W[14]||(W[14]=e=>d.value=e),"link-data":Z.value||{},onClose:Cl},null,8,["visible","link-data"]),te(Pl,{modelValue:o.value,"onUpdate:modelValue":W[16]||(W[16]=e=>o.value=e),title:"功能开发中",width:"400px",center:""},{footer:se(()=>[te(X,{type:"primary",onClick:W[15]||(W[15]=e=>o.value=!1)},{default:se(()=>W[64]||(W[64]=[ie("知道了",-1)])),_:1,__:[64]})]),default:se(()=>[ne("div",Da,[te(K,{size:60,color:"#409EFF"},{default:se(()=>[te(ce(P))]),_:1}),W[61]||(W[61]=ne("h3",null,"功能开发中",-1)),W[62]||(W[62]=ne("p",null,"该功能正在紧急开发中，敬请期待！",-1)),W[63]||(W[63]=ne("p",null,"预计上线时间：2024年1月",-1))])]),_:1},8,["modelValue"]),te(Pl,{modelValue:f.value,"onUpdate:modelValue":W[18]||(W[18]=e=>f.value=e),title:"推广链接数据分析",width:"90%",top:"3vh"},{footer:se(()=>[ne("div",ol,[te(X,{onClick:$l},{default:se(()=>W[87]||(W[87]=[ie("导出分析报告",-1)])),_:1,__:[87]}),te(X,{type:"primary",onClick:W[17]||(W[17]=e=>f.value=!1)},{default:se(()=>W[88]||(W[88]=[ie("关闭",-1)])),_:1,__:[88]})])]),default:se(()=>[ne("div",Ua,[te(Gl,{height:"75vh"},{default:se(()=>[te(he,{gutter:20,class:"analytics-overview"},{default:se(()=>[te(de,{span:6},{default:se(()=>[ne("div",$a,[ne("div",ja,[W[65]||(W[65]=ne("span",{class:"card-title"},"总链接数",-1)),te(K,{color:"#409EFF"},{default:se(()=>[te(ce(z))]),_:1})]),ne("div",La,t(_e.total_links),1),ne("div",Fa,[te(K,null,{default:se(()=>[te(ce($))]),_:1}),W[66]||(W[66]=ie(" 较上月 +8.5% ",-1))])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",Ra,[ne("div",Aa,[W[67]||(W[67]=ne("span",{class:"card-title"},"总点击量",-1)),te(K,{color:"#67C23A"},{default:se(()=>[te(ce(j))]),_:1})]),ne("div",Ea,t(_e.total_clicks.toLocaleString()),1),ne("div",Sa,[te(K,null,{default:se(()=>[te(ce($))]),_:1}),W[68]||(W[68]=ie(" 较上月 +15.2% ",-1))])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",qa,[ne("div",Ta,[W[69]||(W[69]=ne("span",{class:"card-title"},"总转化数",-1)),te(K,{color:"#E6A23C"},{default:se(()=>[te(ce(L))]),_:1})]),ne("div",Ma,t(_e.total_conversions.toLocaleString()),1),ne("div",Ba,[te(K,null,{default:se(()=>[te(ce($))]),_:1}),W[70]||(W[70]=ie(" 较上月 +12.3% ",-1))])])]),_:1}),te(de,{span:6},{default:se(()=>[ne("div",Ia,[ne("div",Oa,[W[71]||(W[71]=ne("span",{class:"card-title"},"平均转化率",-1)),te(K,{color:"#F56C6C"},{default:se(()=>[te(ce(F))]),_:1})]),ne("div",Ya,t(_e.conversion_rate)+"%",1),ne("div",Qa,[te(K,null,{default:se(()=>[te(ce(R))]),_:1}),W[72]||(W[72]=ie(" 较上月 -1.2% ",-1))])])]),_:1})]),_:1}),te(fe,{class:"analytics-section"},{header:se(()=>W[73]||(W[73]=[ne("span",null,"📊 链接类型分析",-1)])),default:se(()=>[te(Ol,{data:Dl(),stripe:""},{default:se(()=>[te(ql,{prop:"type",label:"链接类型",width:"120"},{default:se(({row:e})=>{return[te(Tl,{type:(a=e.type,{product:"success",activity:"primary",register:"warning",other:"info"}[a]||"info")},{default:se(()=>[ie(t(e.typeName),1)]),_:2},1032,["type"])];var a}),_:1}),te(ql,{prop:"count",label:"链接数量",width:"100"}),te(ql,{prop:"clicks",label:"总点击量",width:"120"},{default:se(({row:e})=>[ie(t(e.clicks.toLocaleString()),1)]),_:1}),te(ql,{prop:"conversions",label:"总转化数",width:"120"},{default:se(({row:e})=>[ie(t(e.conversions.toLocaleString()),1)]),_:1}),te(ql,{prop:"conversionRate",label:"转化率",width:"100"},{default:se(({row:e})=>[ne("span",{class:B(Ll(e.conversionRate))},t(e.conversionRate)+"% ",3)]),_:1}),te(ql,{prop:"avgClicks",label:"平均点击量",width:"120"},{default:se(({row:e})=>[ie(t(Math.round(e.clicks/e.count).toLocaleString()),1)]),_:1})]),_:1},8,["data"])]),_:1}),te(fe,{class:"analytics-section"},{header:se(()=>W[74]||(W[74]=[ne("span",null,"🛡️ 域名健康分析",-1)])),default:se(()=>[te(Ol,{data:Ul(),stripe:""},{default:se(()=>[te(ql,{prop:"domain",label:"域名",width:"180"},{default:se(({row:e})=>[ne("span",Na,t(e.domain),1)]),_:1}),te(ql,{prop:"health",label:"健康度",width:"120"},{default:se(({row:e})=>[te(Tl,{type:Sl(e.health),size:"small"},{default:se(()=>[ie(t(e.health)+"% ",1)]),_:2},1032,["type"])]),_:1}),te(ql,{prop:"linkCount",label:"使用链接数",width:"120"}),te(ql,{prop:"totalClicks",label:"总点击量",width:"120"},{default:se(({row:e})=>[ie(t(e.totalClicks.toLocaleString()),1)]),_:1}),te(ql,{prop:"successRate",label:"访问成功率",width:"120"},{default:se(({row:e})=>[ne("span",{style:J({color:e.successRate>=95?"#67C23A":e.successRate>=90?"#E6A23C":"#F56C6C"})},t(e.successRate)+"% ",5)]),_:1}),te(ql,{prop:"lastSwitch",label:"最后切换",width:"120"},{default:se(({row:e})=>[ie(t(e.lastSwitch||"无"),1)]),_:1})]),_:1},8,["data"])]),_:1}),te(fe,{class:"analytics-section"},{header:se(()=>W[75]||(W[75]=[ne("span",null,"🔒 防红保护效果分析",-1)])),default:se(()=>[te(he,{gutter:20},{default:se(()=>[te(de,{span:12},{default:se(()=>[ne("div",Ha,[W[80]||(W[80]=ne("h4",null,"保护状态统计",-1)),ne("div",Pa,[W[76]||(W[76]=ne("span",{class:"stat-label"},"已保护链接:",-1)),ne("span",Ga,t(_e.protected_links)+" 个",1)]),ne("div",Ja,[W[77]||(W[77]=ne("span",{class:"stat-label"},"未保护链接:",-1)),ne("span",Wa,t(_e.total_links-_e.protected_links)+" 个",1)]),ne("div",Za,[W[78]||(W[78]=ne("span",{class:"stat-label"},"保护覆盖率:",-1)),ne("span",Ka,t(Math.round(_e.protected_links/_e.total_links*100))+"%",1)]),ne("div",Xa,[W[79]||(W[79]=ne("span",{class:"stat-label"},"保护成功率:",-1)),ne("span",el,t(_e.protection_success_rate)+"%",1)])])]),_:1}),te(de,{span:12},{default:se(()=>W[81]||(W[81]=[ne("div",{class:"protection-comparison"},[ne("h4",null,"保护效果对比"),ne("div",{class:"comparison-item"},[ne("div",{class:"comparison-label"},"已保护链接平均存活率"),ne("div",{class:"comparison-bar"},[ne("div",{class:"bar-fill success",style:{width:"95%"}}),ne("span",{class:"bar-text"},"95%")])]),ne("div",{class:"comparison-item"},[ne("div",{class:"comparison-label"},"未保护链接平均存活率"),ne("div",{class:"comparison-bar"},[ne("div",{class:"bar-fill danger",style:{width:"60%"}}),ne("span",{class:"bar-text"},"60%")])]),ne("div",{class:"comparison-item"},[ne("div",{class:"comparison-label"},"已保护链接平均点击率"),ne("div",{class:"comparison-bar"},[ne("div",{class:"bar-fill success",style:{width:"88%"}}),ne("span",{class:"bar-text"},"88%")])]),ne("div",{class:"comparison-item"},[ne("div",{class:"comparison-label"},"未保护链接平均点击率"),ne("div",{class:"comparison-bar"},[ne("div",{class:"bar-fill warning",style:{width:"65%"}}),ne("span",{class:"bar-text"},"65%")])])],-1)])),_:1,__:[81]})]),_:1})]),_:1}),te(fe,{class:"analytics-section"},{header:se(()=>W[82]||(W[82]=[ne("span",null,"💡 推广建议",-1)])),default:se(()=>[ne("div",al,[ne("div",ll,[te(K,{color:"#67C23A"},{default:se(()=>[te(ce(D))]),_:1}),W[83]||(W[83]=ne("div",{class:"suggestion-content"},[ne("h4",null,"优化建议"),ne("p",null,"产品推广类链接转化率最高(8.5%)，建议增加此类链接的投放比例。")],-1))]),ne("div",tl,[te(K,{color:"#E6A23C"},{default:se(()=>[te(ce(E))]),_:1}),W[84]||(W[84]=ne("div",{class:"suggestion-content"},[ne("h4",null,"注意事项"),ne("p",null,"域名 d3.linkhub.pro 健康度较低(72%)，建议及时切换到更健康的域名。")],-1))]),ne("div",sl,[te(K,{color:"#409EFF"},{default:se(()=>[te(ce(F))]),_:1}),W[85]||(W[85]=ne("div",{class:"suggestion-content"},[ne("h4",null,"趋势分析"),ne("p",null,"整体点击量呈上升趋势，但转化率略有下降，建议优化目标页面内容。")],-1))]),ne("div",nl,[te(K,{color:"#F56C6C"},{default:se(()=>[te(ce(V))]),_:1}),ne("div",il,[W[86]||(W[86]=ne("h4",null,"安全建议",-1)),ne("p",null,t(_e.total_links-_e.protected_links)+" 个链接未开启防红保护，建议全部开启以提高存活率。",1)])])])]),_:1})]),_:1})])]),_:1},8,["modelValue"]),te(Pl,{modelValue:h.value,"onUpdate:modelValue":W[20]||(W[20]=e=>h.value=e),title:"推广链接反检测系统使用说明",width:"80%",top:"5vh"},{footer:se(()=>[ne("div",ul,[te(X,{onClick:xl},{default:se(()=>W[127]||(W[127]=[ie("查看完整文档",-1)])),_:1,__:[127]}),te(X,{type:"primary",onClick:W[19]||(W[19]=e=>h.value=!1)},{default:se(()=>W[128]||(W[128]=[ie("知道了",-1)])),_:1,__:[128]})])]),default:se(()=>[ne("div",cl,[te(Gl,{height:"70vh"},{default:se(()=>[ne("div",rl,[W[98]||(W[98]=ne("h2",null,"📖 系统概述",-1)),W[99]||(W[99]=ne("p",null,"推广链接反检测系统是一个智能化的链接管理和保护系统，通过域名轮换、健康监控、自动切换等技术手段，确保推广链接的高存活率和防封禁能力。",-1)),W[100]||(W[100]=ne("h2",null,"🚀 快速开始",-1)),W[101]||(W[101]=ne("h3",null,"1. 查看防红系统状态",-1)),W[102]||(W[102]=ne("p",null,"页面顶部显示防红系统状态概览：",-1)),W[103]||(W[103]=ne("ul",null,[ne("li",null,[ne("strong",null,"防红保护"),ie(": 显示已保护链接数量/总链接数量")]),ne("li",null,[ne("strong",null,"平均域名健康度"),ie(": 所有域名的平均健康分数")]),ne("li",null,[ne("strong",null,"今日域名切换"),ie(": 当天自动切换域名的次数")]),ne("li",null,[ne("strong",null,"保护成功率"),ie(": 防红保护的成功率百分比")])],-1)),W[104]||(W[104]=ne("h3",null,"2. 创建智能推广链接",-1)),W[105]||(W[105]=ne("ol",null,[ne("li",null,'点击"创建链接"按钮'),ne("li",null,"填写链接信息（名称、描述、目标URL等）"),ne("li",null,"系统会自动选择健康度最高的域名"),ne("li",null,'创建成功后显示"已自动选择最佳域名"提示')],-1)),W[106]||(W[106]=ne("h2",null,"🔧 核心功能详解",-1)),W[107]||(W[107]=ne("h3",null,"1. 智能域名选择",-1)),W[108]||(W[108]=ne("div",{class:"feature-box"},[ne("p",null,[ne("strong",null,"功能说明"),ie(": 创建推广链接时，系统会自动选择健康度最高的域名，确保链接的最佳可用性。")]),ne("p",null,[ne("strong",null,"选择规则"),ie(":")]),ne("ul",null,[ne("li",null,"优先选择健康度≥90%的域名"),ne("li",null,"考虑域名的访问成功率"),ne("li",null,"负载均衡，避免单一域名过载")])],-1)),W[109]||(W[109]=ne("h3",null,"2. 域名健康监控",-1)),ne("div",dl,[W[97]||(W[97]=ne("p",null,[ne("strong",null,"健康度指标"),ie(":")],-1)),ne("ul",null,[ne("li",null,[te(Tl,{type:"success",size:"small"},{default:se(()=>W[89]||(W[89]=[ie("90%以上",-1)])),_:1,__:[89]}),W[90]||(W[90]=ie(" 域名状态优秀，访问稳定",-1))]),ne("li",null,[te(Tl,{type:"primary",size:"small"},{default:se(()=>W[91]||(W[91]=[ie("80-89%",-1)])),_:1,__:[91]}),W[92]||(W[92]=ie(" 域名状态良好，可正常使用",-1))]),ne("li",null,[te(Tl,{type:"warning",size:"small"},{default:se(()=>W[93]||(W[93]=[ie("70-79%",-1)])),_:1,__:[93]}),W[94]||(W[94]=ie(" 域名状态一般，建议关注",-1))]),ne("li",null,[te(Tl,{type:"danger",size:"small"},{default:se(()=>W[95]||(W[95]=[ie("70%以下",-1)])),_:1,__:[95]}),W[96]||(W[96]=ie(" 域名状态较差，建议立即切换",-1))])])]),W[110]||(W[110]=ne("h3",null,"3. 智能域名切换",-1)),W[111]||(W[111]=ne("div",{class:"feature-box"},[ne("p",null,[ne("strong",null,"自动切换触发条件"),ie(":")]),ne("ul",null,[ne("li",null,"域名健康度低于70%"),ne("li",null,"访问成功率连续下降"),ne("li",null,"域名被检测到异常")]),ne("p",null,[ne("strong",null,"手动切换"),ie(': 在链接列表中点击"切换域名"按钮，系统会自动选择最佳域名。')])],-1)),W[112]||(W[112]=ne("h3",null,"4. 防红保护控制",-1)),W[113]||(W[113]=ne("div",{class:"feature-box"},[ne("p",null,[ne("strong",null,"功能说明"),ie(": 防红保护是一套综合性的反检测机制，包括域名轮换、参数混淆、流量分发等技术。")]),ne("p",null,[ne("strong",null,"操作方式"),ie(': 在链接操作菜单中选择"开启防红"或"关闭防红"。')])],-1)),W[114]||(W[114]=ne("h3",null,"5. 二维码生成",-1)),W[115]||(W[115]=ne("div",{class:"feature-box"},[ne("p",null,[ne("strong",null,"功能特性"),ie(":")]),ne("ul",null,[ne("li",null,"可调整二维码大小、颜色、容错级别"),ne("li",null,"支持下载、复制、打印功能"),ne("li",null,"包含完整的链接信息")])],-1)),W[116]||(W[116]=ne("h2",null,"🚨 注意事项与最佳实践",-1)),W[117]||(W[117]=ne("h3",null,"域名使用建议",-1)),W[118]||(W[118]=ne("div",{class:"tips-box"},[ne("ul",null,[ne("li",null,[ne("strong",null,"定期检查"),ie(": 每天查看域名健康状态")]),ne("li",null,[ne("strong",null,"及时切换"),ie(": 健康度低于80%时及时切换")]),ne("li",null,[ne("strong",null,"分散使用"),ie(": 避免所有链接使用同一域名")]),ne("li",null,[ne("strong",null,"备份准备"),ie(": 保持多个备用域名可用")])])],-1)),W[119]||(W[119]=ne("h3",null,"防红保护建议",-1)),W[120]||(W[120]=ne("div",{class:"tips-box"},[ne("ul",null,[ne("li",null,[ne("strong",null,"重要链接"),ie(": 对重要推广链接务必开启防红保护")]),ne("li",null,[ne("strong",null,"定期监控"),ie(": 关注防红保护的效果统计")]),ne("li",null,[ne("strong",null,"及时调整"),ie(": 根据效果调整保护策略")])])],-1)),W[121]||(W[121]=ne("h2",null,"🔍 常见问题",-1)),W[122]||(W[122]=ne("div",{class:"faq-item"},[ne("h4",null,'Q: 创建链接时提示"无可用域名"？'),ne("p",null,"A: 检查域名池状态，确保有健康的域名可用。联系管理员添加新域名。")],-1)),W[123]||(W[123]=ne("div",{class:"faq-item"},[ne("h4",null,"Q: 域名切换失败？"),ne("p",null,"A: 可能是网络问题或域名服务异常。稍后重试或联系技术支持。")],-1)),W[124]||(W[124]=ne("div",{class:"faq-item"},[ne("h4",null,"Q: 防红保护无效？"),ne("p",null,"A: 确认域名健康度，检查防红保护配置，必要时切换域名。")],-1)),W[125]||(W[125]=ne("h2",null,"📞 技术支持",-1)),W[126]||(W[126]=ne("div",{class:"contact-info"},[ne("p",null,[ne("strong",null,"技术支持邮箱"),ie(": <EMAIL>")]),ne("p",null,[ne("strong",null,"客服热线"),ie(": 400-xxx-xxxx")]),ne("p",null,[ne("strong",null,"在线客服"),ie(": 工作日 9:00-18:00")])],-1))])]),_:1})])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-418c2290"]]);export{pl as default};
