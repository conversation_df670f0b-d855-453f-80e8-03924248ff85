import{_ as e}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                *//* empty css                    *//* empty css                          *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css               */import{b4 as a,b2 as l,T as t,af as s,U as o,a$ as i,ao as d,b3 as u,b9 as n,b5 as r,bc as c,bd as _,be as p,bf as m,aH as f,bg as b,bh as v,aW as V,aV as y,as as g,bi as w,b6 as h,b7 as x,b8 as j,bj as U,bk as k,aM as z,a4 as C,Q as P}from"./element-plus-DcSKpKA8.js";import{P as S}from"./PageLayout-OFR6SHfu.js";import{r as q,L as D,e as I,k as L,l as T,E as A,z as E,t as M,u as Q,D as R,A as B,y as F}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const H={class:"security-management"},W={class:"overview-section"},X={class:"stat-card success"},$={class:"stat-icon"},G={class:"stat-content"},J={class:"stat-value"},K={class:"stat-card warning"},N={class:"stat-icon"},O={class:"stat-content"},Y={class:"stat-value"},Z={class:"stat-card primary"},ee={class:"stat-icon"},ae={class:"stat-content"},le={class:"stat-value"},te={class:"stat-card info"},se={class:"stat-icon"},oe={class:"stat-content"},ie={class:"stat-value"},de={class:"config-section"},ue={class:"card-header"},ne={class:"logs-section"},re={class:"card-header"},ce={class:"header-actions"},_e={class:"pagination-wrapper"},pe=e({__name:"SecurityManagement",setup(e){const pe=q("access"),me=q(!1),fe=q(!1),be=D({threat_blocked:1247,security_alerts:3,active_sessions:156,security_level:"高"}),ve=D({access:{max_login_attempts:5,lockout_duration:30,session_timeout:120,require_2fa:!0,ip_whitelist:""},password:{min_length:8,complexity:["uppercase","lowercase","numbers"],expiry_days:90,history_count:5},audit:{enabled:!0,log_level:"detailed",retention_days:90,events:["login","admin","security"]},protection:{firewall_enabled:!0,ddos_protection:!0,sql_injection_protection:!0,xss_protection:!0,csrf_protection:!0,rate_limit:100}}),Ve=q([]),ye=D({current:1,size:20,total:0}),ge=async()=>{me.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),P.success("安全配置保存成功")}catch(e){P.error("保存失败："+e.message)}finally{me.value=!1}},we=async()=>{fe.value=!0;try{await new Promise(e=>setTimeout(e,500)),Ve.value=[{id:1,timestamp:"2024-01-01 10:00:00",event_type:"login_failed",user:"admin",ip_address:"*************",description:"登录失败，密码错误",risk_level:"medium"},{id:2,timestamp:"2024-01-01 09:30:00",event_type:"suspicious_activity",user:"unknown",ip_address:"********",description:"检测到可疑活动",risk_level:"high"}],ye.total=2}catch(e){P.error("加载日志失败："+e.message)}finally{fe.value=!1}},he=async()=>{try{P.success("日志导出成功")}catch(e){P.error("导出失败："+e.message)}},xe=e=>{ye.size=e,we()},je=e=>{ye.current=e,we()},Ue=e=>({login_failed:"登录失败",suspicious_activity:"可疑活动",admin_action:"管理操作",security_alert:"安全警报"}[e]||"未知"),ke=e=>({low:"低",medium:"中",high:"高"}[e]||"未知");return I(()=>{we()}),(e,P)=>{const q=t,D=l,I=a,ze=g,Ce=p,Pe=_,Se=m,qe=f,De=c,Ie=r,Le=v,Te=b,Ae=y,Ee=V,Me=n,Qe=u,Re=x,Be=j,Fe=h,He=k,We=U;return T(),L("div",H,[A(S,{title:"安全管理",subtitle:"系统安全配置和监控"},{default:E(()=>[M("div",W,[A(I,{gutter:20},{default:E(()=>[A(D,{span:6},{default:E(()=>[M("div",X,[M("div",$,[A(q,null,{default:E(()=>[A(Q(s))]),_:1})]),M("div",G,[M("div",J,o(be.threat_blocked),1),P[22]||(P[22]=M("div",{class:"stat-label"},"威胁拦截",-1))])])]),_:1}),A(D,{span:6},{default:E(()=>[M("div",K,[M("div",N,[A(q,null,{default:E(()=>[A(Q(i))]),_:1})]),M("div",O,[M("div",Y,o(be.security_alerts),1),P[23]||(P[23]=M("div",{class:"stat-label"},"安全警报",-1))])])]),_:1}),A(D,{span:6},{default:E(()=>[M("div",Z,[M("div",ee,[A(q,null,{default:E(()=>[A(Q(d))]),_:1})]),M("div",ae,[M("div",le,o(be.active_sessions),1),P[24]||(P[24]=M("div",{class:"stat-label"},"活跃会话",-1))])])]),_:1}),A(D,{span:6},{default:E(()=>[M("div",te,[M("div",se,[A(q,null,{default:E(()=>[A(Q(s))]),_:1})]),M("div",oe,[M("div",ie,o(be.security_level),1),P[25]||(P[25]=M("div",{class:"stat-label"},"安全等级",-1))])])]),_:1})]),_:1})]),M("div",de,[A(Qe,{class:"config-card"},{header:E(()=>[M("div",ue,[P[27]||(P[27]=M("h3",null,"安全配置",-1)),A(ze,{type:"primary",onClick:ge,loading:me.value},{default:E(()=>[A(q,null,{default:E(()=>[A(Q(w))]),_:1}),P[26]||(P[26]=R(" 保存配置 ",-1))]),_:1,__:[26]},8,["loading"])])]),default:E(()=>[A(Me,{modelValue:pe.value,"onUpdate:modelValue":P[19]||(P[19]=e=>pe.value=e),class:"security-tabs"},{default:E(()=>[A(Ie,{label:"访问控制",name:"access"},{default:E(()=>[A(De,{model:ve.access,"label-width":"150px"},{default:E(()=>[A(Pe,{label:"登录失败限制"},{default:E(()=>[A(Ce,{modelValue:ve.access.max_login_attempts,"onUpdate:modelValue":P[0]||(P[0]=e=>ve.access.max_login_attempts=e),min:3,max:10},null,8,["modelValue"]),P[28]||(P[28]=M("span",{class:"form-tip"},"次失败后锁定账户",-1))]),_:1,__:[28]}),A(Pe,{label:"账户锁定时间"},{default:E(()=>[A(Ce,{modelValue:ve.access.lockout_duration,"onUpdate:modelValue":P[1]||(P[1]=e=>ve.access.lockout_duration=e),min:5,max:1440},null,8,["modelValue"]),P[29]||(P[29]=M("span",{class:"form-tip"},"分钟",-1))]),_:1,__:[29]}),A(Pe,{label:"会话超时"},{default:E(()=>[A(Ce,{modelValue:ve.access.session_timeout,"onUpdate:modelValue":P[2]||(P[2]=e=>ve.access.session_timeout=e),min:15,max:480},null,8,["modelValue"]),P[30]||(P[30]=M("span",{class:"form-tip"},"分钟",-1))]),_:1,__:[30]}),A(Pe,{label:"强制双因子认证"},{default:E(()=>[A(Se,{modelValue:ve.access.require_2fa,"onUpdate:modelValue":P[3]||(P[3]=e=>ve.access.require_2fa=e)},null,8,["modelValue"]),P[31]||(P[31]=M("span",{class:"form-tip"},"管理员账户强制启用",-1))]),_:1,__:[31]}),A(Pe,{label:"IP白名单"},{default:E(()=>[A(qe,{modelValue:ve.access.ip_whitelist,"onUpdate:modelValue":P[4]||(P[4]=e=>ve.access.ip_whitelist=e),type:"textarea",rows:4,placeholder:"每行一个IP地址或CIDR段"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),A(Ie,{label:"密码策略",name:"password"},{default:E(()=>[A(De,{model:ve.password,"label-width":"150px"},{default:E(()=>[A(Pe,{label:"最小长度"},{default:E(()=>[A(Ce,{modelValue:ve.password.min_length,"onUpdate:modelValue":P[5]||(P[5]=e=>ve.password.min_length=e),min:6,max:32},null,8,["modelValue"]),P[32]||(P[32]=M("span",{class:"form-tip"},"字符",-1))]),_:1,__:[32]}),A(Pe,{label:"复杂度要求"},{default:E(()=>[A(Te,{modelValue:ve.password.complexity,"onUpdate:modelValue":P[6]||(P[6]=e=>ve.password.complexity=e)},{default:E(()=>[A(Le,{label:"uppercase"},{default:E(()=>P[33]||(P[33]=[R("包含大写字母",-1)])),_:1,__:[33]}),A(Le,{label:"lowercase"},{default:E(()=>P[34]||(P[34]=[R("包含小写字母",-1)])),_:1,__:[34]}),A(Le,{label:"numbers"},{default:E(()=>P[35]||(P[35]=[R("包含数字",-1)])),_:1,__:[35]}),A(Le,{label:"symbols"},{default:E(()=>P[36]||(P[36]=[R("包含特殊字符",-1)])),_:1,__:[36]})]),_:1},8,["modelValue"])]),_:1}),A(Pe,{label:"密码有效期"},{default:E(()=>[A(Ce,{modelValue:ve.password.expiry_days,"onUpdate:modelValue":P[7]||(P[7]=e=>ve.password.expiry_days=e),min:0,max:365},null,8,["modelValue"]),P[37]||(P[37]=M("span",{class:"form-tip"},"天（0表示永不过期）",-1))]),_:1,__:[37]}),A(Pe,{label:"历史密码检查"},{default:E(()=>[A(Ce,{modelValue:ve.password.history_count,"onUpdate:modelValue":P[8]||(P[8]=e=>ve.password.history_count=e),min:0,max:10},null,8,["modelValue"]),P[38]||(P[38]=M("span",{class:"form-tip"},"不能重复最近几次密码",-1))]),_:1,__:[38]})]),_:1},8,["model"])]),_:1}),A(Ie,{label:"审计日志",name:"audit"},{default:E(()=>[A(De,{model:ve.audit,"label-width":"150px"},{default:E(()=>[A(Pe,{label:"启用审计日志"},{default:E(()=>[A(Se,{modelValue:ve.audit.enabled,"onUpdate:modelValue":P[9]||(P[9]=e=>ve.audit.enabled=e)},null,8,["modelValue"])]),_:1}),A(Pe,{label:"日志级别"},{default:E(()=>[A(Ee,{modelValue:ve.audit.log_level,"onUpdate:modelValue":P[10]||(P[10]=e=>ve.audit.log_level=e)},{default:E(()=>[A(Ae,{label:"基础",value:"basic"}),A(Ae,{label:"详细",value:"detailed"}),A(Ae,{label:"完整",value:"full"})]),_:1},8,["modelValue"])]),_:1}),A(Pe,{label:"日志保留期"},{default:E(()=>[A(Ce,{modelValue:ve.audit.retention_days,"onUpdate:modelValue":P[11]||(P[11]=e=>ve.audit.retention_days=e),min:7,max:365},null,8,["modelValue"]),P[39]||(P[39]=M("span",{class:"form-tip"},"天",-1))]),_:1,__:[39]}),A(Pe,{label:"记录事件"},{default:E(()=>[A(Te,{modelValue:ve.audit.events,"onUpdate:modelValue":P[12]||(P[12]=e=>ve.audit.events=e)},{default:E(()=>[A(Le,{label:"login"},{default:E(()=>P[40]||(P[40]=[R("登录/登出",-1)])),_:1,__:[40]}),A(Le,{label:"admin"},{default:E(()=>P[41]||(P[41]=[R("管理操作",-1)])),_:1,__:[41]}),A(Le,{label:"data"},{default:E(()=>P[42]||(P[42]=[R("数据变更",-1)])),_:1,__:[42]}),A(Le,{label:"security"},{default:E(()=>P[43]||(P[43]=[R("安全事件",-1)])),_:1,__:[43]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),A(Ie,{label:"防护设置",name:"protection"},{default:E(()=>[A(De,{model:ve.protection,"label-width":"150px"},{default:E(()=>[A(Pe,{label:"启用防火墙"},{default:E(()=>[A(Se,{modelValue:ve.protection.firewall_enabled,"onUpdate:modelValue":P[13]||(P[13]=e=>ve.protection.firewall_enabled=e)},null,8,["modelValue"])]),_:1}),A(Pe,{label:"DDoS防护"},{default:E(()=>[A(Se,{modelValue:ve.protection.ddos_protection,"onUpdate:modelValue":P[14]||(P[14]=e=>ve.protection.ddos_protection=e)},null,8,["modelValue"])]),_:1}),A(Pe,{label:"SQL注入防护"},{default:E(()=>[A(Se,{modelValue:ve.protection.sql_injection_protection,"onUpdate:modelValue":P[15]||(P[15]=e=>ve.protection.sql_injection_protection=e)},null,8,["modelValue"])]),_:1}),A(Pe,{label:"XSS防护"},{default:E(()=>[A(Se,{modelValue:ve.protection.xss_protection,"onUpdate:modelValue":P[16]||(P[16]=e=>ve.protection.xss_protection=e)},null,8,["modelValue"])]),_:1}),A(Pe,{label:"CSRF防护"},{default:E(()=>[A(Se,{modelValue:ve.protection.csrf_protection,"onUpdate:modelValue":P[17]||(P[17]=e=>ve.protection.csrf_protection=e)},null,8,["modelValue"])]),_:1}),A(Pe,{label:"请求频率限制"},{default:E(()=>[A(Ce,{modelValue:ve.protection.rate_limit,"onUpdate:modelValue":P[18]||(P[18]=e=>ve.protection.rate_limit=e),min:10,max:1e3},null,8,["modelValue"]),P[44]||(P[44]=M("span",{class:"form-tip"},"次/分钟",-1))]),_:1,__:[44]})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),M("div",ne,[A(Qe,{class:"logs-card"},{header:E(()=>[M("div",re,[P[47]||(P[47]=M("h3",null,"安全日志",-1)),M("div",ce,[A(ze,{onClick:we,loading:fe.value},{default:E(()=>[A(q,null,{default:E(()=>[A(Q(z))]),_:1}),P[45]||(P[45]=R(" 刷新 ",-1))]),_:1,__:[45]},8,["loading"]),A(ze,{onClick:he},{default:E(()=>[A(q,null,{default:E(()=>[A(Q(C))]),_:1}),P[46]||(P[46]=R(" 导出 ",-1))]),_:1,__:[46]})])])]),default:E(()=>[B((T(),F(Fe,{data:Ve.value,stripe:""},{default:E(()=>[A(Re,{prop:"timestamp",label:"时间",width:"160"}),A(Re,{prop:"event_type",label:"事件类型",width:"120"},{default:E(({row:e})=>{return[A(Be,{type:(a=e.event_type,{login_failed:"warning",suspicious_activity:"danger",admin_action:"primary",security_alert:"danger"}[a]||"info")},{default:E(()=>[R(o(Ue(e.event_type)),1)]),_:2},1032,["type"])];var a}),_:1}),A(Re,{prop:"user",label:"用户",width:"120"}),A(Re,{prop:"ip_address",label:"IP地址",width:"140"}),A(Re,{prop:"description",label:"描述","show-overflow-tooltip":""}),A(Re,{prop:"risk_level",label:"风险等级",width:"100"},{default:E(({row:e})=>{return[A(Be,{type:(a=e.risk_level,{low:"success",medium:"warning",high:"danger"}[a]||"info"),size:"small"},{default:E(()=>[R(o(ke(e.risk_level)),1)]),_:2},1032,["type"])];var a}),_:1})]),_:1},8,["data"])),[[We,fe.value]]),M("div",_e,[A(He,{"current-page":ye.current,"onUpdate:currentPage":P[20]||(P[20]=e=>ye.current=e),"page-size":ye.size,"onUpdate:pageSize":P[21]||(P[21]=e=>ye.size=e),total:ye.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:je},null,8,["current-page","page-size","total"])])]),_:1})])]),_:1})])}}},[["__scopeId","data-v-5b6240db"]]);export{pe as default};
