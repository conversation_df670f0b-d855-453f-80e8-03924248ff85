import{_ as e,c as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                     *//* empty css                       *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                    *//* empty css               */import{r as l,L as t,e as i,k as s,l as o,t as u,A as n,E as d,a2 as r,z as m,D as p,y as c,B as v,G as _}from"./vue-vendor-DGsK9sC4.js";import{P as f}from"./index-Do9uvhBr.js";import{aH as g,as as b,bj as h,b7 as y,U as w,b8 as V,bc as j,bd as x,be as k,bo as U,bp as C,au as z,R as B,bQ as I,b6 as q}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";/* empty css                      *//* empty css                  */const D={class:"app-container"},L={class:"filter-container"},R={slot:"footer",class:"dialog-footer"},T=e({__name:"GroupList",setup(e){const T=l(null),$=l(0),G=l(!0),H=t({page:1,limit:15,name:void 0}),O=l({id:void 0,name:"",commission_rate:90,description:"",status:1}),P=l(!1),A=l(""),E={update:"编辑分销组",create:"新增分销组"},F=l(null),K={name:[{required:!0,message:"分组名称不能为空",trigger:"blur"}],commission_rate:[{required:!0,message:"佣金比例不能为空",trigger:"blur"}]};function Q(){G.value=!0,a({url:"/distribution-groups",method:"get",params:H}).then(e=>{T.value=e.data.data,$.value=e.data.total,G.value=!1})}function J(){H.page=1,Q()}function M(){O.value={id:void 0,name:"",commission_rate:90,description:"",status:1},A.value="create",P.value=!0,F.value?.clearValidate()}return i(()=>{Q()}),(e,l)=>{const t=g,i=b,N=y,S=V,W=q,X=x,Y=k,Z=C,ee=U,ae=j,le=z,te=h;return o(),s("div",D,[u("div",L,[d(t,{modelValue:H.name,"onUpdate:modelValue":l[0]||(l[0]=e=>H.name=e),placeholder:"分组名称",style:{width:"200px"},class:"filter-item",onKeyup:r(J,["enter","native"])},null,8,["modelValue"]),d(i,{class:"filter-item",type:"primary",icon:"el-icon-search",onClick:J},{default:m(()=>l[10]||(l[10]=[p(" 搜索 ",-1)])),_:1,__:[10]}),d(i,{class:"filter-item",style:{"margin-left":"10px"},type:"primary",icon:"el-icon-edit",onClick:M},{default:m(()=>l[11]||(l[11]=[p(" 新增 ",-1)])),_:1,__:[11]})]),n((o(),c(W,{data:T.value,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""},{default:m(()=>[d(N,{align:"center",label:"ID",width:"95"},{default:m(e=>[p(w(e.row.id),1)]),_:1}),d(N,{label:"分组名称"},{default:m(e=>[p(w(e.row.name),1)]),_:1}),d(N,{label:"佣金比例 (%)",width:"150",align:"center"},{default:m(e=>[u("span",null,w(e.row.commission_rate),1)]),_:1}),d(N,{label:"描述"},{default:m(e=>[p(w(e.row.description),1)]),_:1}),d(N,{"class-name":"status-col",label:"状态",width:"110",align:"center"},{default:m(e=>[d(S,{type:1===e.row.status?"success":"info"},{default:m(()=>[p(w(1===e.row.status?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),d(N,{align:"center",prop:"created_at",label:"创建时间",width:"200"},{default:m(e=>[l[12]||(l[12]=u("i",{class:"el-icon-time"},null,-1)),u("span",null,w(e.row.created_at),1)]),_:1}),d(N,{label:"操作",align:"center",width:"230","class-name":"small-padding fixed-width"},{default:m(({row:e,$index:t})=>[d(i,{type:"primary",size:"mini",onClick:a=>function(e){O.value=Object.assign({},e),A.value="update",P.value=!0,F.value?.clearValidate()}(e)},{default:m(()=>l[13]||(l[13]=[p(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick"]),2!==e.status?(o(),c(i,{key:0,size:"mini",type:"danger",onClick:l=>function(e,l){B.confirm("此操作将永久删除该分销组, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{a({url:`/distribution-groups/${e.id}`,method:"delete"}).then(()=>{I({title:"成功",message:"删除成功",type:"success",duration:2e3}),T.value.splice(l,1)})})}(e,t)},{default:m(()=>l[14]||(l[14]=[p(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"])),[[te,G.value]]),n(d(f,{total:$.value,page:H.page,"onUpdate:page":l[1]||(l[1]=e=>H.page=e),limit:H.limit,"onUpdate:limit":l[2]||(l[2]=e=>H.limit=e),onPagination:Q},null,8,["total","page","limit"]),[[_,$.value>0]]),d(le,{title:E[A.value],modelValue:P.value,"onUpdate:modelValue":l[9]||(l[9]=e=>P.value=e)},{default:m(()=>[d(ae,{ref_key:"dataForm",ref:F,rules:K,model:O.value,"label-position":"left","label-width":"100px",style:{width:"400px","margin-left":"50px"}},{default:m(()=>[d(X,{label:"分组名称",prop:"name"},{default:m(()=>[d(t,{modelValue:O.value.name,"onUpdate:modelValue":l[3]||(l[3]=e=>O.value.name=e)},null,8,["modelValue"])]),_:1}),d(X,{label:"佣金比例",prop:"commission_rate"},{default:m(()=>[d(Y,{modelValue:O.value.commission_rate,"onUpdate:modelValue":l[4]||(l[4]=e=>O.value.commission_rate=e),precision:2,step:1,min:0,max:100},null,8,["modelValue"]),l[15]||(l[15]=p(" % ",-1))]),_:1,__:[15]}),d(X,{label:"状态"},{default:m(()=>[d(ee,{modelValue:O.value.status,"onUpdate:modelValue":l[5]||(l[5]=e=>O.value.status=e)},{default:m(()=>[d(Z,{label:1},{default:m(()=>l[16]||(l[16]=[p("正常",-1)])),_:1,__:[16]}),d(Z,{label:2},{default:m(()=>l[17]||(l[17]=[p("禁用",-1)])),_:1,__:[17]})]),_:1},8,["modelValue"])]),_:1}),d(X,{label:"描述"},{default:m(()=>[d(t,{modelValue:O.value.description,"onUpdate:modelValue":l[6]||(l[6]=e=>O.value.description=e),autosize:{minRows:2,maxRows:4},type:"textarea",placeholder:"请输入描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),u("div",R,[d(i,{onClick:l[7]||(l[7]=e=>P.value=!1)},{default:m(()=>l[18]||(l[18]=[p(" 取消 ",-1)])),_:1,__:[18]}),d(i,{type:"primary",onClick:l[8]||(l[8]=e=>"create"===A.value?void F.value.validate(e=>{e&&a({url:"/distribution-groups",method:"post",data:O.value}).then(()=>{T.value.unshift(O.value),P.value=!1,I({title:"成功",message:"创建成功",type:"success",duration:2e3}),Q()})}):void F.value.validate(e=>{if(e){const e=Object.assign({},O.value);a({url:`/distribution-groups/${e.id}`,method:"put",data:e}).then(()=>{const e=T.value.findIndex(e=>e.id===O.value.id);T.value.splice(e,1,O.value),P.value=!1,I({title:"成功",message:"更新成功",type:"success",duration:2e3})})}}))},{default:m(()=>l[19]||(l[19]=[p(" 确认 ",-1)])),_:1,__:[19]})])]),_:1},8,["title","modelValue"])])}}},[["__scopeId","data-v-3681ad87"]]);export{T as default};
