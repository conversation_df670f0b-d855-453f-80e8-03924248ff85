#!/bin/bash
# 自动修复PHP扩展脚本

echo "🔧 开始修复PHP扩展..."

# 检测系统类型
if [ -f /etc/redhat-release ]; then
    OS="centos"
elif [ -f /etc/lsb-release ]; then
    OS="ubuntu"
else
    echo "❌ 无法识别系统类型"
    exit 1
fi

# 获取PHP版本
PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)

echo "系统类型: $OS"
echo "PHP版本: $PHP_VERSION"

# 安装扩展
if [ "$OS" = "ubuntu" ]; then
    sudo apt-get update
    sudo apt-get install -y php${PHP_VERSION}-sodium php${PHP_VERSION}-redis php${PHP_VERSION}-gd php${PHP_VERSION}-zip php${PHP_VERSION}-curl php${PHP_VERSION}-mbstring php${PHP_VERSION}-xml php${PHP_VERSION}-mysql php${PHP_VERSION}-bcmath
elif [ "$OS" = "centos" ]; then
    sudo yum install -y php${PHP_VERSION}-sodium php${PHP_VERSION}-redis php${PHP_VERSION}-gd php${PHP_VERSION}-zip php${PHP_VERSION}-curl php${PHP_VERSION}-mbstring php${PHP_VERSION}-xml php${PHP_VERSION}-mysqlnd php${PHP_VERSION}-bcmath
fi

# 重启服务
if command -v systemctl &> /dev/null; then
    sudo systemctl restart php-fpm
    sudo systemctl restart nginx
fi

echo "✅ PHP扩展修复完成"