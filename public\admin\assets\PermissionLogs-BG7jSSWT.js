import{_ as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import{P as e}from"./PageLayout-OFR6SHfu.js";import{b3 as t,b6 as s,b7 as l,b8 as r,U as i,as as n,bj as o,bk as c,Q as d,R as u}from"./element-plus-DcSKpKA8.js";import{r as p,L as m,e as _,k as f,l as g,E as v,z as y,t as b,A as h,y as w,D as z}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const j={class:"page-container"},k={class:"content-wrapper"},C={class:"card-body"},x={class:"pagination-wrapper"},P=a({__name:"PermissionLogs",setup(a){const P=p(!1),L=p([]),T=m({current:1,size:20,total:0}),U=()=>{I()},B=()=>{d.info("新增功能待实现")},D=a=>{T.size=a,I()},E=a=>{T.current=a,I()},I=async()=>{P.value=!0;try{await new Promise(a=>setTimeout(a,1e3)),L.value=[{id:1,name:"示例数据1",status:"active",created_at:"2024-01-01 12:00:00"},{id:2,name:"示例数据2",status:"inactive",created_at:"2024-01-02 12:00:00"}],T.total=2}catch(a){console.error("加载数据失败:",a),d.error("加载数据失败")}finally{P.value=!1}};return _(()=>{I()}),(a,p)=>{const m=n,_=l,S=r,$=s,q=c,A=t,Q=o;return g(),f("div",j,[v(e,{title:"权限日志",subtitle:"查看权限变更和操作日志",loading:P.value},{actions:y(()=>[v(m,{class:"modern-btn secondary",onClick:U},{default:y(()=>p[2]||(p[2]=[b("i",{class:"el-icon-refresh"},null,-1),z(" 刷新数据 ",-1)])),_:1,__:[2]}),v(m,{class:"modern-btn primary",onClick:B},{default:y(()=>p[3]||(p[3]=[b("i",{class:"el-icon-plus"},null,-1),z(" 新增 ",-1)])),_:1,__:[3]})]),default:y(()=>[b("div",k,[v(A,{class:"modern-card"},{default:y(()=>[p[6]||(p[6]=b("div",{class:"card-header"},[b("h3",null,"权限日志"),b("p",{class:"text-muted"},"查看权限变更和操作日志")],-1)),b("div",C,[h((g(),w($,{data:L.value,style:{width:"100%"},class:"modern-table"},{default:y(()=>[v(_,{prop:"id",label:"ID",width:"80"}),v(_,{prop:"name",label:"名称","min-width":"150"}),v(_,{prop:"status",label:"状态",width:"100"},{default:y(({row:a})=>[v(S,{type:"active"===a.status?"success":"info",size:"small"},{default:y(()=>[z(i("active"===a.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),v(_,{prop:"created_at",label:"创建时间",width:"180"}),v(_,{label:"操作",width:"200",fixed:"right"},{default:y(({row:a})=>[v(m,{type:"primary",size:"small",onClick:e=>(a=>{d.info(`编辑功能待实现: ${a.name}`)})(a)},{default:y(()=>p[4]||(p[4]=[z(" 编辑 ",-1)])),_:2,__:[4]},1032,["onClick"]),v(m,{type:"danger",size:"small",onClick:e=>(async a=>{try{await u.confirm(`确定要删除 "${a.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),d.success("删除成功"),I()}catch{d.info("已取消删除")}})(a)},{default:y(()=>p[5]||(p[5]=[z(" 删除 ",-1)])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Q,P.value]]),b("div",x,[v(q,{"current-page":T.current,"onUpdate:currentPage":p[0]||(p[0]=a=>T.current=a),"page-size":T.size,"onUpdate:pageSize":p[1]||(p[1]=a=>T.size=a),total:T.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:D,onCurrentChange:E},null,8,["current-page","page-size","total"])])])]),_:1,__:[6]})])]),_:1},8,["loading"])])}}},[["__scopeId","data-v-d1e8e385"]]);export{P as default};
