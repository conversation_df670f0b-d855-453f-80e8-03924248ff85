<template>
  <div class="modern-dashboard">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            欢迎回来，{{ userStore.nickname || '管理员' }}！
          </h1>
          <p class="welcome-subtitle">
            今天是 {{ currentDate }}，{{ getGreeting() }}
          </p>
        </div>
        <div class="banner-actions">
          <el-button type="primary" class="modern-btn primary" @click="showQuickActions = true">
            <el-icon><Plus /></el-icon>
            快速操作
          </el-button>
          <el-button class="modern-btn secondary" @click="refreshDashboard">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
      <div class="banner-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card" v-for="metric in coreMetrics" :key="metric.key">
        <div class="metric-icon" :class="metric.iconClass">
          <el-icon :size="24">
            <component :is="metric.icon" />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatNumber(metric.value) }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-change" :class="metric.changeType">
            <el-icon :size="12">
              <ArrowUp v-if="metric.changeType === 'positive'" />
              <ArrowDown v-else />
            </el-icon>
            {{ metric.change }}
          </div>
        </div>
        <div class="metric-chart">
          <div class="mini-chart" :style="{ background: metric.chartGradient }"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧内容 -->
      <div class="content-left">
        <!-- 实时数据图表 -->
        <div class="chart-card">
          <div class="card-header">
            <h3 class="card-title">实时数据概览</h3>
            <div class="card-actions">
              <el-select v-model="chartTimeRange" size="small" class="time-selector">
                <el-option label="今日" value="today" />
                <el-option label="本周" value="week" />
                <el-option label="本月" value="month" />
                <el-option label="本年" value="year" />
              </el-select>
            </div>
          </div>
          <div class="chart-container">
            <div ref="mainChart" class="main-chart"></div>
          </div>
        </div>

        <!-- 用户活动热力图 -->
        <div class="activity-card">
          <div class="card-header">
            <h3 class="card-title">用户活动热力图</h3>
            <div class="activity-legend">
              <span class="legend-item">
                <div class="legend-color low"></div>
                低活跃
              </span>
              <span class="legend-item">
                <div class="legend-color medium"></div>
                中活跃
              </span>
              <span class="legend-item">
                <div class="legend-color high"></div>
                高活跃
              </span>
            </div>
          </div>
          <div class="activity-heatmap">
            <div class="heatmap-grid">
              <div 
                v-for="(day, index) in heatmapData" 
                :key="index"
                class="heatmap-cell"
                :class="day.level"
                :title="`${day.date}: ${day.count} 活跃用户`"
              ></div>
            </div>
          </div>
        </div>

        <!-- 最新订单 -->
        <div class="orders-card">
          <div class="card-header">
            <h3 class="card-title">最新订单</h3>
            <el-button text @click="$router.push('/orders/list')">
              查看全部
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="orders-list">
            <div 
              v-for="order in recentOrders" 
              :key="order.id"
              class="order-item"
              @click="viewOrderDetail(order.id)"
            >
              <div class="order-avatar">
                <el-avatar 
                  :size="40" 
                  :src="order.userAvatar"
                  :style="{ 
                    background: generateAvatarColor(order.userName),
                    color: 'white',
                    fontWeight: '600'
                  }"
                >
                  {{ getAvatarText(order.userName) }}
                </el-avatar>
              </div>
              <div class="order-info">
                <div class="order-title">{{ order.groupName }}</div>
                <div class="order-meta">
                  {{ order.userName }} · {{ formatTime(order.createdAt) }}
                </div>
              </div>
              <div class="order-amount">
                ¥{{ order.amount }}
              </div>
              <div class="order-status" :class="order.status">
                {{ getOrderStatusText(order.status) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 热门群组排行 -->
        <div class="popular-groups-card">
          <div class="card-header">
            <h3 class="card-title">热门群组排行</h3>
            <el-button text @click="$router.push('/community/groups')">
              查看全部
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="groups-ranking">
            <div 
              v-for="(group, index) in popularGroups" 
              :key="group.id"
              class="ranking-item"
              @click="viewGroupDetail(group.id)"
            >
              <div class="ranking-number" :class="{ 'top-three': index < 3 }">
                {{ index + 1 }}
              </div>
              <div class="group-info">
                <div class="group-name">{{ group.name }}</div>
                <div class="group-stats">
                  <span class="member-count">{{ group.memberCount }}人</span>
                  <span class="separator">·</span>
                  <span class="revenue">¥{{ group.revenue }}</span>
                </div>
              </div>
              <div class="group-trend">
                <div class="trend-icon" :class="group.trendType">
                  <el-icon>
                    <ArrowUp v-if="group.trendType === 'up'" />
                    <ArrowDown v-else-if="group.trendType === 'down'" />
                    <Minus v-else />
                  </el-icon>
                </div>
                <span class="trend-value">{{ group.trendValue }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最新动态 -->
        <div class="recent-activities-card">
          <div class="card-header">
            <h3 class="card-title">最新动态</h3>
            <div class="activity-filters">
              <el-button-group size="small">
                <el-button 
                  v-for="filter in activityFilters" 
                  :key="filter.key"
                  :type="activeActivityFilter === filter.key ? 'primary' : ''"
                  @click="activeActivityFilter = filter.key"
                >
                  {{ filter.label }}
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div class="activities-timeline">
            <div 
              v-for="activity in filteredActivities" 
              :key="activity.id"
              class="timeline-item"
            >
              <div class="timeline-dot" :class="activity.type"></div>
              <div class="timeline-content">
                <div class="activity-header">
                  <span class="activity-user">{{ activity.userName }}</span>
                  <span class="activity-action">{{ activity.action }}</span>
                  <span class="activity-target">{{ activity.target }}</span>
                </div>
                <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="content-right">
        <!-- 快速统计 -->
        <div class="quick-stats-card">
          <div class="card-header">
            <h3 class="card-title">今日统计</h3>
          </div>
          <div class="stats-grid">
            <div class="stat-item" v-for="stat in quickStats" :key="stat.key">
              <div class="stat-icon" :style="{ background: stat.color }">
                <el-icon>
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知中心 -->
        <div class="notifications-card">
          <div class="card-header">
            <h3 class="card-title">通知中心</h3>
            <el-badge :value="notifications.length" :hidden="notifications.length === 0">
              <el-button text @click="showAllNotifications">
                查看全部
              </el-button>
            </el-badge>
          </div>
          <div class="notifications-list">
            <div
              v-for="notification in notifications.slice(0, 5)"
              :key="notification.id"
              class="notification-item"
              :class="{ unread: !notification.read }"
            >
              <div class="notification-icon" :class="notification.type">
                <el-icon>
                  <component :is="getNotificationIcon(notification.type)" />
                </el-icon>
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
              </div>
              <div class="notification-actions">
                <el-button text size="small" @click="markAsRead(notification.id)">
                  标记已读
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="system-status-card">
          <div class="card-header">
            <h3 class="card-title">系统状态</h3>
            <div class="status-indicator online">
              <div class="status-dot"></div>
              运行正常
            </div>
          </div>
          <div class="status-metrics">
            <div class="status-item">
              <div class="status-label">CPU 使用率</div>
              <div class="status-progress">
                <el-progress :percentage="systemStatus.cpu" :show-text="false" />
                <span class="status-value">{{ systemStatus.cpu }}%</span>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">内存使用率</div>
              <div class="status-progress">
                <el-progress :percentage="systemStatus.memory" :show-text="false" color="#10b981" />
                <span class="status-value">{{ systemStatus.memory }}%</span>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">磁盘使用率</div>
              <div class="status-progress">
                <el-progress :percentage="systemStatus.disk" :show-text="false" color="#f59e0b" />
                <span class="status-value">{{ systemStatus.disk }}%</span>
              </div>
            </div>
            <div class="status-item">
              <div class="status-label">网络延迟</div>
              <div class="status-progress">
                <span class="status-value">{{ systemStatus.latency }}ms</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions-card">
          <div class="card-header">
            <h3 class="card-title">快捷操作</h3>
          </div>
          <div class="actions-grid">
            <div 
              v-for="action in quickActions" 
              :key="action.key"
              class="action-item"
              @click="handleQuickAction(action)"
            >
              <div class="action-icon" :style="{ background: action.color }">
                <el-icon>
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-label">{{ action.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作弹窗 -->
    <el-dialog v-model="showQuickActions" title="快速操作" width="600px" class="quick-actions-dialog">
      <div class="quick-actions-content">
        <div class="action-category" v-for="category in actionCategories" :key="category.name">
          <h4 class="category-title">{{ category.name }}</h4>
          <div class="category-actions">
            <div 
              v-for="action in category.actions" 
              :key="action.key"
              class="category-action-item"
              @click="handleQuickAction(action)"
            >
              <div class="action-icon" :style="{ background: action.color }">
                <el-icon>
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-info">
                <div class="action-name">{{ action.label }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Plus, Refresh, ArrowUp, ArrowDown, ArrowRight,
  User, UserFilled, Tickets, Money, TrendCharts,
  Bell, Warning, InfoFilled, SuccessFilled,
  Setting, Document, DataAnalysis, Connection, Grid
} from '@element-plus/icons-vue'
// import * as echarts from 'echarts' // 暂时注释掉，避免加载问题

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showQuickActions = ref(false)
const chartTimeRange = ref('today')
const mainChart = ref(null)

// 当前日期和问候语
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了，注意休息'
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

// 核心指标数据
const coreMetrics = reactive([
  {
    key: 'users',
    label: '总用户数',
    value: 12580,
    change: '+12.5%',
    changeType: 'positive',
    icon: UserFilled,
    iconClass: 'user-icon',
    chartGradient: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
  },
  {
    key: 'orders',
    label: '总订单数',
    value: 8964,
    change: '+8.2%',
    changeType: 'positive',
    icon: Tickets,
    iconClass: 'order-icon',
    chartGradient: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
  },
  {
    key: 'revenue',
    label: '总收入',
    value: 256780,
    change: '+15.3%',
    changeType: 'positive',
    icon: Money,
    iconClass: 'revenue-icon',
    chartGradient: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
  },
  {
    key: 'conversion',
    label: '转化率',
    value: 68.5,
    change: '-2.1%',
    changeType: 'negative',
    icon: TrendCharts,
    iconClass: 'conversion-icon',
    chartGradient: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
  }
])

// 系统状态
const systemStatus = reactive({
  cpu: 45,
  memory: 62,
  disk: 78,
  latency: 23
})

// 今日统计
const quickStats = reactive([
  {
    key: 'todayUsers',
    label: '今日新增用户',
    value: 156,
    icon: User,
    color: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
  },
  {
    key: 'todayOrders',
    label: '今日订单',
    value: 89,
    icon: Tickets,
    color: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
  },
  {
    key: 'todayRevenue',
    label: '今日收入',
    value: '¥12,580',
    icon: Money,
    color: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
  },
  {
    key: 'activeUsers',
    label: '在线用户',
    value: 1,
    icon: UserFilled,
    color: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
  }
])

// 最新订单
const recentOrders = reactive([
  {
    id: 1,
    groupName: '高端投资理财群',
    userName: '张三',
    userAvatar: '/avatars/user1.jpg',
    amount: 299,
    status: 'paid',
    createdAt: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: 2,
    groupName: '股票交流群',
    userName: '李四',
    userAvatar: '/avatars/user2.jpg',
    amount: 199,
    status: 'pending',
    createdAt: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: 3,
    groupName: '创业交流群',
    userName: '王五',
    userAvatar: '/avatars/user3.jpg',
    amount: 99,
    status: 'paid',
    createdAt: new Date(Date.now() - 30 * 60 * 1000)
  }
])

// 通知数据
const notifications = reactive([
  {
    id: 1,
    title: '系统维护通知',
    type: 'warning',
    read: false,
    createdAt: new Date(Date.now() - 10 * 60 * 1000)
  },
  {
    id: 2,
    title: '新用户注册',
    type: 'info',
    read: false,
    createdAt: new Date(Date.now() - 20 * 60 * 1000)
  },
  {
    id: 3,
    title: '订单支付成功',
    type: 'success',
    read: true,
    createdAt: new Date(Date.now() - 35 * 60 * 1000)
  }
])

// 快捷操作
const quickActions = reactive([
  {
    key: 'addUser',
    label: '添加用户',
    icon: User,
    color: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
    action: () => router.push('/user/add')
  },
  {
    key: 'createGroup',
    label: '创建群组',
    icon: Connection,
    color: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
    action: () => router.push('/community/groups/add')
  },
  {
    key: 'viewReports',
    label: '查看报表',
        icon: TrendCharts,
    color: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
    action: () => router.push('/dashboard/reports')
  },
  {
    key: 'systemSettings',
    label: '系统设置',
    icon: Setting,
    color: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
    action: () => router.push('/system/settings')
  }
])

// 操作分类
const actionCategories = reactive([
  {
    name: '用户管理',
    actions: [
      {
        key: 'addUser',
        label: '添加用户',
        description: '快速添加新用户到系统',
        icon: User,
        color: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
        action: () => router.push('/user/add')
      },
      {
        key: 'userAnalytics',
        label: '用户分析',
        description: '查看用户行为和统计数据',
        icon: DataAnalysis,
        color: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        action: () => router.push('/user/analytics')
      }
    ]
  },
  {
    name: '内容管理',
    actions: [
      {
        key: 'createGroup',
        label: '创建群组',
        description: '创建新的微信群组',
        icon: Connection,
        color: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        action: () => router.push('/community/groups/add')
      },
      {
        key: 'contentManage',
        label: '内容管理',
        description: '管理群组内容和模板',
        icon: Document,
        color: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        action: () => router.push('/content/management')
      }
    ]
  }
])

// 热力图数据
const heatmapData = reactive([])

// 热门群组数据
const popularGroups = reactive([
  {
    id: 1,
    name: '高端投资理财群',
    memberCount: 2580,
    revenue: 125600,
    trendType: 'up',
    trendValue: '+15.2%'
  },
  {
    id: 2,
    name: '股票交流群',
    memberCount: 1890,
    revenue: 89400,
    trendType: 'up',
    trendValue: '+8.7%'
  },
  {
    id: 3,
    name: '创业交流群',
    memberCount: 1456,
    revenue: 67200,
    trendType: 'down',
    trendValue: '-2.3%'
  },
  {
    id: 4,
    name: '技术分享群',
    memberCount: 1234,
    revenue: 45600,
    trendType: 'up',
    trendValue: '+5.1%'
  },
  {
    id: 5,
    name: '职场发展群',
    memberCount: 987,
    revenue: 32100,
    trendType: 'flat',
    trendValue: '0%'
  }
])

// 活动筛选器
const activeActivityFilter = ref('all')
const activityFilters = reactive([
  { key: 'all', label: '全部' },
  { key: 'user', label: '用户' },
  { key: 'order', label: '订单' },
  { key: 'system', label: '系统' }
])

// 最新动态数据
const recentActivities = reactive([
  {
    id: 1,
    userName: '张三',
    action: '加入了',
    target: '高端投资理财群',
    type: 'user',
    createdAt: new Date(Date.now() - 2 * 60 * 1000)
  },
  {
    id: 2,
    userName: '李四',
    action: '完成了订单支付',
    target: '¥299',
    type: 'order',
    createdAt: new Date(Date.now() - 8 * 60 * 1000)
  },
  {
    id: 3,
    userName: '系统',
    action: '自动备份了',
    target: '数据库',
    type: 'system',
    createdAt: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: 4,
    userName: '王五',
    action: '创建了',
    target: '新的群组',
    type: 'user',
    createdAt: new Date(Date.now() - 25 * 60 * 1000)
  },
  {
    id: 5,
    userName: '赵六',
    action: '申请了',
    target: '提现 ¥1200',
    type: 'order',
    createdAt: new Date(Date.now() - 35 * 60 * 1000)
  }
])

// 筛选后的活动数据
const filteredActivities = computed(() => {
  if (activeActivityFilter.value === 'all') {
    return recentActivities
  }
  return recentActivities.filter(activity => activity.type === activeActivityFilter.value)
})

// 生成热力图数据
const generateHeatmapData = () => {
  const data = []
  const today = new Date()
  for (let i = 364; i >= 0; i--) {
    const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000)
    const count = Math.floor(Math.random() * 20)
    let level = 'none'
    if (count > 15) level = 'high'
    else if (count > 8) level = 'medium'
    else if (count > 3) level = 'low'
    
    data.push({
      date: date.toLocaleDateString(),
      count,
      level
    })
  }
  heatmapData.splice(0, heatmapData.length, ...data)
}

// 工具函数
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}

const formatTime = (date) => {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

const getOrderStatusText = (status) => {
  const statusMap = {
    paid: '已支付',
    pending: '待支付',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

const getNotificationIcon = (type) => {
  const iconMap = {
    warning: Warning,
    info: InfoFilled,
    success: SuccessFilled,
    error: Warning
  }
  return iconMap[type] || InfoFilled
}

// 事件处理
const refreshDashboard = () => {
  ElMessage.success('数据已刷新')
  // 这里可以添加实际的数据刷新逻辑
}

const viewOrderDetail = (orderId) => {
  router.push(`/orders/detail/${orderId}`)
}

const markAsRead = (notificationId) => {
  const notification = notifications.find(n => n.id === notificationId)
  if (notification) {
    notification.read = true
    ElMessage.success('已标记为已读')
  }
}

const showAllNotifications = () => {
  router.push('/system/notifications')
}

const handleQuickAction = (action) => {
  if (action.action) {
    action.action()
    showQuickActions.value = false
  }
}

const viewGroupDetail = (groupId) => {
  router.push(`/community/groups/detail/${groupId}`)
}

// 头像相关函数
const generateAvatarColor = (name) => {
  const colors = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #ff8a80 0%, #ea6100 100%)',
    'linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)'
  ]
  
  // 根据用户名生成一个稳定的颜色索引
  let hash = 0
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash)
  }
  const index = Math.abs(hash) % colors.length
  return colors[index]
}

const getAvatarText = (name) => {
  if (!name) return '?'
  // 如果是中文名，取最后一个字符
  if (/[\u4e00-\u9fa5]/.test(name)) {
    return name.slice(-1)
  }
  // 如果是英文名，取首字母
  return name.charAt(0).toUpperCase()
}

// 初始化图表 - 暂时禁用
const initChart = () => {
  if (!mainChart.value) return

  // const chart = echarts.init(mainChart.value) // 暂时注释掉
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['用户数', '订单数', '收入']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用户数',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          color: '#3b82f6'
        },
        areaStyle: {
          // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //   { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
          //   { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
          // ])
          color: 'rgba(59, 130, 246, 0.2)'
        },
        data: [120, 132, 101, 134, 90, 230, 210]
      },
      {
        name: '订单数',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          color: '#10b981'
        },
        areaStyle: {
          // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //   { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
          //   { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
          // ])
          color: 'rgba(16, 185, 129, 0.2)'
        },
        data: [220, 182, 191, 234, 290, 330, 310]
      },
      {
        name: '收入',
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          color: '#f59e0b'
        },
        areaStyle: {
          // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //   { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },
          //   { offset: 1, color: 'rgba(245, 158, 11, 0.1)' }
          // ])
          color: 'rgba(245, 158, 11, 0.2)'
        },
        data: [150, 232, 201, 154, 190, 330, 410]
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(() => {
  generateHeatmapData()
  nextTick(() => {
    // initChart() // 暂时注释掉，避免echarts加载问题
  })
})
</script>

<style lang="scss" scoped>
.modern-dashboard {
  padding: 24px;
  background: transparent;
  min-height: 100vh;
}

// 欢迎横幅
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  color: white;

  .banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
  }

  .welcome-text {
    .welcome-title {
      font-size: 32px;
      font-weight: 700;
      margin: 0 0 8px 0;
      background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .welcome-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .banner-actions {
    display: flex;
    gap: 16px;
  }

  .banner-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.circle-1 {
        width: 200px;
        height: 200px;
        top: -100px;
        right: -100px;
      }

      &.circle-2 {
        width: 120px;
        height: 120px;
        top: 50px;
        right: 200px;
        background: rgba(255, 255, 255, 0.05);
      }

      &.circle-3 {
        width: 80px;
        height: 80px;
        bottom: -40px;
        right: 100px;
        background: rgba(255, 255, 255, 0.08);
      }
    }
  }
}

// 指标网格
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
  }

  .metric-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;

    &.user-icon {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    &.order-icon {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    &.revenue-icon {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    &.conversion-icon {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
  }

  .metric-content {
    flex: 1;

    .metric-value {
      font-size: 28px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 4px;
    }

    .metric-label {
      font-size: 14px;
      color: #64748b;
      margin-bottom: 8px;
    }

    .metric-change {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;

      &.positive {
        color: #10b981;
      }

      &.negative {
        color: #ef4444;
      }
    }
  }

  .metric-chart {
    width: 60px;
    height: 40px;

    .mini-chart {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      opacity: 0.2;
    }
  }
}

// 主要内容区域
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
  align-items: start;
  
  .content-left {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  .content-right {
    display: flex;
    flex-direction: column;
    gap: 24px;
    position: sticky;
    top: 24px;
  }
}

// 卡片通用样式
.chart-card,
.activity-card,
.orders-card,
.system-status-card,
.quick-stats-card,
.notifications-card,
.quick-actions-card,
.popular-groups-card,
.recent-activities-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    padding: 24px 24px 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }

    .card-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

// 图表卡片
.chart-card {
  .chart-container {
    padding: 0 24px 24px 24px;

    .main-chart {
      width: 100%;
      height: 300px;
    }
  }
}

// 活动热力图
.activity-card {
  .activity-legend {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #64748b;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;

      &.low {
        background: #e2e8f0;
      }

      &.medium {
        background: #94a3b8;
      }

      &.high {
        background: #3b82f6;
      }
    }
  }

  .activity-heatmap {
    padding: 0 24px 24px 24px;

    .heatmap-grid {
      display: grid;
      grid-template-columns: repeat(53, 1fr);
      gap: 2px;

      .heatmap-cell {
        width: 10px;
        height: 10px;
        border-radius: 2px;
        background: #f1f5f9;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.2);
        }

        &.low {
          background: #e2e8f0;
        }

        &.medium {
          background: #94a3b8;
        }

        &.high {
          background: #3b82f6;
        }
      }
    }
  }
}

// 订单列表
.orders-card {
  .orders-list {
    padding: 0 24px 24px 24px;

    .order-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f8fafc;
        transform: translateX(4px);
      }

      .order-avatar {
        img {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          object-fit: cover;
        }
      }

      .order-info {
        flex: 1;

        .order-title {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .order-meta {
          font-size: 12px;
          color: #64748b;
        }
      }

      .order-amount {
        font-size: 16px;
        font-weight: 700;
        color: #10b981;
      }

      .order-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;

        &.paid {
          background: #dcfce7;
          color: #166534;
        }

        &.pending {
          background: #fef3c7;
          color: #92400e;
        }

        &.cancelled {
          background: #fee2e2;
          color: #991b1b;
        }
      }
    }
  }
}

// 系统状态卡片
.system-status-card {
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;

    &.online {
      color: #10b981;

      .status-dot {
        width: 8px;
        height: 8px;
        background: #10b981;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
    }
  }

  .status-metrics {
    padding: 0 24px 24px 24px;

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .status-label {
        font-size: 14px;
        color: #64748b;
      }

      .status-progress {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        margin-left: 16px;

        .status-value {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          min-width: 40px;
          text-align: right;
        }
      }
    }
  }
}

// 快速统计
.quick-stats-card {
  .stats-grid {
    padding: 0 24px 24px 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    align-items: stretch;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      border-radius: 12px;
      background: #f8fafc;
      transition: all 0.3s ease;
      min-height: 80px;

      &:hover {
        background: #f1f5f9;
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .stat-content {
        flex: 1;
        min-width: 0;

        .stat-value {
          font-size: 18px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 2px;
          line-height: 1.2;
        }

        .stat-label {
          font-size: 12px;
          color: #64748b;
          line-height: 1.3;
          word-break: break-all;
        }
      }
    }
  }
}

// 通知列表
.notifications-card {
  .notifications-list {
    padding: 0 24px 24px 24px;

    .notification-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f8fafc;
      }

      &.unread {
        background: #eff6ff;
        border-left: 3px solid #3b82f6;
      }

      .notification-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;

        &.warning {
          background: #f59e0b;
        }

        &.info {
          background: #3b82f6;
        }

        &.success {
          background: #10b981;
        }

        &.error {
          background: #ef4444;
        }
      }

      .notification-content {
        flex: 1;

        .notification-title {
          font-size: 14px;
          font-weight: 500;
          color: #1e293b;
          margin-bottom: 2px;
        }

        .notification-time {
          font-size: 12px;
          color: #64748b;
        }
      }
    }
  }
}

// 快捷操作
.quick-actions-card {
  .actions-grid {
    padding: 0 24px 24px 24px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;

    .action-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      border-radius: 12px;
      background: #f8fafc;
      cursor: pointer;
      transition: all 0.3s ease;
      min-height: 64px;
      position: relative;
      overflow: hidden;

      &:hover {
        background: #f1f5f9;
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      .action-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        z-index: 2;
      }

      .action-label {
        font-size: 14px;
        font-weight: 500;
        color: #1e293b;
        line-height: 1.3;
        flex: 1;
        z-index: 2;
      }

      &:hover .action-icon {
        transform: scale(1.1);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      }

      // 添加微妙的背景装饰
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1;
      }

      &:hover::before {
        opacity: 1;
      }

      // 添加右侧箭头指示器
      &::after {
        content: '';
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        border-right: 2px solid #94a3b8;
        border-top: 2px solid #94a3b8;
        transform: translateY(-50%) rotate(45deg);
        opacity: 0.6;
        transition: all 0.3s ease;
        z-index: 2;
      }

      &:hover::after {
        opacity: 1;
        transform: translateY(-50%) rotate(45deg) translateX(2px);
        border-color: #3b82f6;
      }
    }
  }
}

// 热门群组排行
.popular-groups-card {
  .groups-ranking {
    padding: 0 24px 24px 24px;

    .ranking-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 8px;

      &:hover {
        background: #f8fafc;
        transform: translateX(4px);
      }

      .ranking-number {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 14px;
        background: #e2e8f0;
        color: #64748b;
        flex-shrink: 0;

        &.top-three {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }
      }

      .group-info {
        flex: 1;
        min-width: 0;

        .group-name {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .group-stats {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #64748b;

          .separator {
            color: #cbd5e1;
          }

          .revenue {
            color: #10b981;
            font-weight: 600;
          }
        }
      }

      .group-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        font-weight: 600;

        .trend-icon {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;

          &.up {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
          }

          &.down {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
          }

          &.flat {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
          }
        }

        .trend-value {
          &.up {
            color: #10b981;
          }

          &.down {
            color: #ef4444;
          }

          &.flat {
            color: #6b7280;
          }
        }
      }
    }
  }
}

// 最新动态
.recent-activities-card {
  .activity-filters {
    :deep(.el-button-group) {
      .el-button {
        border-radius: 6px;
        font-size: 12px;
        padding: 6px 12px;
        border: 1px solid #e2e8f0;
        
        &:not(.is-active) {
          background: white;
          color: #64748b;
          
          &:hover {
            background: #f8fafc;
            border-color: #3b82f6;
            color: #3b82f6;
          }
        }
      }
    }
  }

  .activities-timeline {
    padding: 0 24px 24px 24px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 40px;
      top: 0;
      bottom: 24px;
      width: 2px;
      background: linear-gradient(to bottom, #e2e8f0 0%, transparent 100%);
    }

    .timeline-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 12px 0;
      position: relative;

      .timeline-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
        margin-top: 4px;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #e2e8f0;
        z-index: 2;

        &.user {
          background: #3b82f6;
          box-shadow: 0 0 0 2px #dbeafe;
        }

        &.order {
          background: #10b981;
          box-shadow: 0 0 0 2px #dcfce7;
        }

        &.system {
          background: #f59e0b;
          box-shadow: 0 0 0 2px #fef3c7;
        }
      }

      .timeline-content {
        flex: 1;
        min-width: 0;

        .activity-header {
          font-size: 14px;
          line-height: 1.4;
          margin-bottom: 4px;

          .activity-user {
            font-weight: 600;
            color: #1e293b;
          }

          .activity-action {
            color: #64748b;
            margin: 0 4px;
          }

          .activity-target {
            font-weight: 500;
            color: #3b82f6;
          }
        }

        .activity-time {
          font-size: 12px;
          color: #94a3b8;
        }
      }
    }
  }
}

// 快捷操作弹窗
:deep(.quick-actions-dialog) {
  .el-dialog__body {
    padding: 0;
  }

  .quick-actions-content {
    .action-category {
      margin-bottom: 24px;

      .category-title {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 16px 0;
        padding: 0 24px;
      }

      .category-actions {
        .category-action-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px 24px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: #f8fafc;
          }

          .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
          }

          .action-info {
            .action-name {
              font-size: 14px;
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 4px;
            }

            .action-desc {
              font-size: 12px;
              color: #64748b;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .modern-dashboard {
    padding: 16px;
  }

  .welcome-banner {
    padding: 24px;

    .banner-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .welcome-text .welcome-title {
      font-size: 24px;
    }
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .quick-stats-card .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-card .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
    
    .action-item {
      padding: 16px 8px;
      min-height: 100px;
      
      .action-icon {
        width: 40px;
        height: 40px;
      }
      
      .action-label {
        font-size: 11px;
      }
    }
  }
}
</style>