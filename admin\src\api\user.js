import request from '@/utils/request'
import { mockUserAPI } from './mock/user'

const useMock = import.meta.env.VITE_ENABLE_MOCK === 'true' || 
                (import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL)

// --- 个人中心相关 ---
export function getProfile() {
  if (useMock) return mockUserAPI.getProfile()
  return request({ url: '/admin/user/profile', method: 'get' })
}

export function getStats() {
  if (useMock) return mockUserAPI.getStats()
  return request({ url: '/admin/user/stats', method: 'get' })
}

export function getRecentOrders(params) {
  if (useMock) return mockUserAPI.getRecentOrders(params)
  return request({ url: '/admin/user/recent-orders', method: 'get', params })
}

export function getPointsHistory(params) {
  if (useMock) return mockUserAPI.getPointsHistory(params)
  return request({ url: '/admin/user/points-history', method: 'get', params })
}

export function getConsumptionAnalysis(params) {
  if (useMock) return mockUserAPI.getConsumptionAnalysis(params)
  return request({ url: '/admin/user/consumption-analysis', method: 'get', params })
}

export function updateProfile(data) {
  if (useMock) return mockUserAPI.updateProfile(data)
  return request({ url: '/admin/user/profile', method: 'put', data })
}


// --- 用户管理相关 ---
export function getUserList(params) {
  if (useMock) return mockUserAPI.getUserList(params)
  return request({ url: '/admin/users', method: 'get', params })
}

export function getUserStats() {
  if (useMock) return mockUserAPI.getUserStats()
  return request({ url: '/admin/users/stats', method: 'get' })
}

export function getUserDetail(id) {
  if (useMock) return mockUserAPI.getUserDetail(id)
  return request({ url: `/admin/users/${id}`, method: 'get' })
}

export function createUser(data) {
  if (useMock) return mockUserAPI.createUser(data)
  return request({ url: '/admin/users', method: 'post', data })
}

export function updateUser(id, data) {
  if (useMock) return mockUserAPI.updateUser(id, data)
  return request({ url: `/admin/users/${id}`, method: 'put', data })
}

export function deleteUser(id) {
  if (useMock) return mockUserAPI.deleteUser(id)
  return request({ url: `/admin/users/${id}`, method: 'delete' })
}

export function updateUserStatus(id, status) {
  if (useMock) return mockUserAPI.updateUserStatus(id, status)
  return request({ url: `/admin/users/${id}/status`, method: 'put', data: { status } })
}

export function adjustUserBalance(id, data) {
  if (useMock) return mockUserAPI.adjustUserBalance(id, data)
  return request({ url: `/admin/users/${id}/balance`, method: 'put', data })
}

export function resetUserPassword(id) {
  if (useMock) return mockUserAPI.resetUserPassword(id)
  return request({ url: `/admin/users/${id}/reset-password`, method: 'post' })
}

export function exportUsers(params) {
  if (useMock) return mockUserAPI.exportUsers(params)
  return request({ url: '/admin/users/export', method: 'get', params, responseType: 'blob' })
}

export function batchUpdateUserStatus(userIds, status) {
  if (useMock) return mockUserAPI.batchUpdateUserStatus(userIds, status)
  return request({ url: '/admin/users/batch-status', method: 'put', data: { user_ids: userIds, status } })
}

export function getUserBalanceLogs(id, params) {
  if (useMock) return mockUserAPI.getUserBalanceLogs(id, params)
  return request({ url: `/admin/users/${id}/balance-logs`, method: 'get', params })
}

export function getUserOrders(id, params) {
  if (useMock) return mockUserAPI.getUserOrders(id, params)
  return request({ url: `/admin/users/${id}/orders`, method: 'get', params })
}