import{_ as e,u as a}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                 *//* empty css                       *//* empty css                        *//* empty css                *//* empty css               *//* empty css               */import{r as t,e as l,H as o,k as s,B as i,l as r,t as n,E as c,z as u,u as d,ag as m,L as p,c as v,n as f,A as _,D as g,F as y,Y as w,y as b,C as h}from"./vue-vendor-DGsK9sC4.js";import{T as C,bS as k,as as x,o as D,U as F,bQ as A,Q as M,bj as j,aN as E,b8 as z,ao as I,ap as P,b2 as B,p as T,a_ as S,aO as U,b4 as q,an as V,a8 as $,ai as L,a7 as H,b3 as R,bo as W,bv as N,bm as O,aM as Q,aI as J,aH as X,ab as Y,a0 as Z}from"./element-plus-DcSKpKA8.js";/* empty css                                                                 */import{L as G}from"./LineChart-Ba008-uu.js";import{D as K}from"./DoughnutChart-JEDVUFw0.js";import"./utils-4VKArNEK.js";import"./chart-Bup65vvO.js";const ee={key:0,class:"performance-monitor"},ae={class:"monitor-header"},te={class:"monitor-content"},le={class:"metric-item"},oe={class:"metric-item"},se={class:"metric-value"},ie={class:"metric-item"},re={class:"metric-item"},ne=e({__name:"PerformanceMonitor",setup(e,{expose:a}){const m=t(!1),p=t(0),v=t(0),f=t(60),_=t(0);let g=null;const y=()=>{m.value=!m.value},w=e=>e<500?"good":e<1500?"warning":"poor",b=()=>{performance.memory&&(v.value=Math.round(performance.memory.usedJSHeapSize/1024/1024))},h=()=>{const e=performance.now();setTimeout(()=>{_.value=Math.round(performance.now()-e)},500*Math.random()+200)};return l(()=>{(()=>{if(performance.timing){const e=performance.timing.navigationStart,a=performance.timing.loadEventEnd;p.value=a-e}})(),b(),(()=>{let e=0,a=performance.now();const t=l=>{e++,l>=a+1e3&&(f.value=Math.round(1e3*e/(l-a)),e=0,a=l),g=requestAnimationFrame(t)};g=requestAnimationFrame(t)})(),h();const e=setInterval(()=>{b(),h()},2e3);o(()=>{clearInterval(e),g&&cancelAnimationFrame(g)})}),a({toggleMonitor:y,showMonitor:m}),(e,a)=>{const t=C,l=x;return m.value?(r(),s("div",ee,[n("div",ae,[a[0]||(a[0]=n("span",null,"性能监控",-1)),c(l,{size:"small",onClick:y},{default:u(()=>[c(t,null,{default:u(()=>[c(d(k))]),_:1})]),_:1})]),n("div",te,[n("div",le,[a[1]||(a[1]=n("span",{class:"metric-label"},"页面加载时间:",-1)),n("span",{class:D(["metric-value",(g=p.value,g<1e3?"good":g<3e3?"warning":"poor")])},F(p.value)+"ms",3)]),n("div",oe,[a[2]||(a[2]=n("span",{class:"metric-label"},"内存使用:",-1)),n("span",se,F(v.value)+"MB",1)]),n("div",ie,[a[3]||(a[3]=n("span",{class:"metric-label"},"FPS:",-1)),n("span",{class:D(["metric-value",(o=f.value,o>=55?"good":o>=30?"warning":"poor")])},F(f.value),3)]),n("div",re,[a[4]||(a[4]=n("span",{class:"metric-label"},"API响应:",-1)),n("span",{class:D(["metric-value",w(_.value)])},F(_.value)+"ms",3)])])])):i("",!0);var o,g}}},[["__scopeId","data-v-de8bb903"]]),ce={class:"distributor-dashboard","element-loading-text":"加载中..."},ue={class:"page-header"},de={class:"distributor-info"},me={class:"info-content"},pe={class:"status-tags"},ve={class:"header-actions"},fe=["onClick"],_e={class:"stat-content"},ge={class:"stat-value"},ye={class:"stat-title"},we={class:"card-header"},be={class:"card-header"},he={class:"customer-activities"},Ce={class:"activity-avatar"},ke={class:"activity-content"},xe={class:"activity-title"},De={class:"activity-desc"},Fe={class:"activity-time"},Ae={key:0,class:"activity-value"},Me={class:"value-amount"},je={key:0,class:"empty-state"},Ee={class:"card-header"},ze={class:"follow-up-list"},Ie={class:"customer-info"},Pe={class:"customer-name"},Be={class:"customer-level"},Te={class:"follow-up-time"},Se={class:"follow-up-actions"},Ue={key:0,class:"empty-state"},qe="/avatars/default.jpg",Ve=e({__name:"DistributorDashboard",setup(e){const o=m(),k=a(),ee=t(!1),ae=t("30d"),te=p({customer:!1,group:!1,copy:!1,qrcode:!1}),le=p({revenue:!1,customer:!1}),oe=p({activities:!1,followUps:!1}),se=t([]),ie=t([]),re=t(""),Ve=p({todayClicks:0,totalClicks:0}),$e=t([{key:"customers",title:"客户总数",value:0,icon:"User",color:"#409EFF",trend:0,prefix:"",suffix:""},{key:"groups",title:"活跃群组",value:0,icon:"Comment",color:"#67C23A",trend:0,prefix:"",suffix:""},{key:"commission",title:"本月佣金",value:0,icon:"Money",color:"#E6A23C",trend:0,prefix:"¥",suffix:""},{key:"conversion",title:"转化率",value:0,icon:"TrendCharts",color:"#F56C6C",trend:0,prefix:"",suffix:"%"}]),Le=t([{key:"customer-management",title:"客户管理",icon:"User",color:"#409EFF",disabled:!1,badge:0},{key:"add-customer",title:"新增客户",icon:"UserFilled",color:"#67C23A",disabled:!1},{key:"group-management",title:"群组管理",icon:"Comment",color:"#E6A23C",disabled:!1,badge:0},{key:"promotion-links",title:"推广链接",icon:"Link",color:"#409EFF",disabled:!1},{key:"commission-logs",title:"佣金查看",icon:"Money",color:"#F56C6C",disabled:!1},{key:"order-list",title:"订单查看",icon:"Tickets",color:"#909399",disabled:!1}]),He=t({labels:[],datasets:[{label:"佣金收入",data:[],borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0}]}),Re=t({labels:["A级客户","B级客户","C级客户","D级客户"],datasets:[{data:[25,45,60,26],backgroundColor:["#F56C6C","#E6A23C","#409EFF","#67C23A"],borderWidth:0,hoverOffset:4}]}),We={responsive:!0,maintainAspectRatio:!1,interaction:{intersect:!1,mode:"index"},plugins:{legend:{display:!0,position:"top"},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"#409EFF",borderWidth:1}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"}},x:{grid:{display:!1}}}},Ne={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff"}}},Oe=v(()=>k.userInfo?.distributor_code||"D"+(k.userInfo?.id||"001")),Qe=v(()=>({A:"A级分销员",B:"B级分销员",C:"C级分销员",D:"D级分销员"}[k.userInfo?.distributor_level||"C"]||"C级分销员")),Je=async()=>{try{await new Promise(e=>setTimeout(e,300)),$e.value[0].value=156,$e.value[0].trend=8.2,$e.value[1].value=23,$e.value[1].trend=15.6,$e.value[2].value=8650,$e.value[2].trend=23.4,$e.value[3].value=12.5,$e.value[3].trend=-2.1,Le.value[0].badge=5,Le.value[2].badge=2,console.log("统计数据加载完成")}catch(e){console.error("加载统计数据失败:",e),M.error("加载统计数据失败")}},Xe=async()=>{le.revenue=!0;try{await new Promise(e=>setTimeout(e,500));const e={"7d":{labels:["周一","周二","周三","周四","周五","周六","周日"],data:[120,190,300,500,200,300,450]},"30d":{labels:Array.from({length:30},(e,a)=>`${a+1}日`),data:Array.from({length:30},()=>Math.floor(1e3*Math.random())+200)},"90d":{labels:["第1月","第2月","第3月"],data:[8e3,12e3,15e3]}}[ae.value];He.value={labels:e.labels,datasets:[{label:"佣金收入",data:e.data,borderColor:"#409EFF",backgroundColor:"rgba(64, 158, 255, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"#409EFF",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]},console.log("收入图表数据加载完成")}catch(e){console.error("加载收入数据失败:",e),M.error("加载收入数据失败")}finally{le.revenue=!1}},Ye=async()=>{oe.activities=!0;try{await new Promise(e=>setTimeout(e,400)),se.value=[{id:1,title:"新客户注册",description:'客户"张三"通过推广链接注册',created_at:new Date,customer:{avatar:qe}},{id:2,title:"客户下单",description:'客户"李四"购买了VIP群组',created_at:new Date(Date.now()-36e5),value:299,customer:{avatar:qe}},{id:3,title:"客户升级",description:'客户"王五"升级为A级客户',created_at:new Date(Date.now()-72e5),customer:{avatar:qe}},{id:4,title:"佣金到账",description:"获得推广佣金奖励",created_at:new Date(Date.now()-108e5),value:150,customer:{avatar:qe}}],console.log("客户动态数据加载完成")}catch(e){console.error("加载客户动态失败:",e),M.error("加载客户动态失败")}finally{oe.activities=!1}},Ze=async()=>{oe.followUps=!0;try{await new Promise(e=>setTimeout(e,350)),ie.value=[{id:1,name:"王五",level:"A",level_text:"A级客户",next_follow_up:new Date(Date.now()+864e5),following:!1},{id:2,name:"赵六",level:"B",level_text:"B级客户",next_follow_up:new Date(Date.now()-36e5),following:!1},{id:3,name:"孙七",level:"C",level_text:"C级客户",next_follow_up:new Date(Date.now()+1728e5),following:!1}],console.log("待跟进客户数据加载完成")}catch(e){console.error("加载待跟进客户失败:",e),M.error("加载待跟进客户失败")}finally{oe.followUps=!1}},Ge=async()=>{te.customer=!0;try{await o.push("/distribution/customers"),A.success({title:"跳转成功",message:"已跳转到客户管理页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}finally{te.customer=!1}},Ke=async()=>{try{await o.push("/distribution/customers?action=create")}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},ea=async()=>{te.group=!0;try{await o.push("/distributor/group-management"),A.success({title:"跳转成功",message:"已跳转到群组管理页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}finally{te.group=!1}},aa=async()=>{try{await o.push("/distributor/promotion-links"),A.success({title:"跳转成功",message:"已跳转到推广链接管理页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},ta=async()=>{try{await o.push("/distributor/commission-logs"),A.success({title:"跳转成功",message:"已跳转到佣金查看页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},la=async()=>{try{await o.push("/distributor/order-list"),A.success({title:"跳转成功",message:"已跳转到订单查看页面"})}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},oa=async()=>{try{await o.push("/distribution/customers?tab=follow-up")}catch(e){console.error("路由跳转失败:",e),M.warning("页面跳转失败，请稍后重试")}},sa=async()=>{te.copy=!0;try{if(navigator.clipboard)await navigator.clipboard.writeText(re.value),A.success({title:"复制成功",message:"推广链接已复制到剪贴板"});else{const e=document.createElement("textarea");e.value=re.value,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),A.success({title:"复制成功",message:"推广链接已复制到剪贴板"})}}catch(e){console.error("复制失败:",e),M.error("复制失败，请手动复制")}finally{te.copy=!1}},ia=async()=>{te.qrcode=!0;try{await new Promise(e=>setTimeout(e,1e3)),M.info("二维码生成功能开发中，敬请期待！")}finally{te.qrcode=!1}},ra=e=>{const a=new Date,t=new Date(e);return t<a?"overdue":t-a<864e5?"urgent":"normal"},na=e=>{const a=new Date,t=new Date(e)-a;if(t<0){return`逾期${Math.floor(Math.abs(t)/36e5)}小时`}if(t<864e5){return`${Math.floor(t/36e5)}小时后`}return`${Math.floor(t/864e5)}天后`};return l(async()=>{console.log("优化版分销员工作台开始加载..."),ee.value=!0;try{await Promise.all([Je(),Xe(),Ye(),Ze()]),(()=>{const e=k.userInfo?.id||"001";re.value=`${window.location.origin}/register?distributor=${e}`,Ve.todayClicks=Math.floor(50*Math.random())+10,Ve.totalClicks=Math.floor(1e3*Math.random())+500})(),await f(),console.log("优化版分销员工作台加载完成"),A.success({title:"加载完成",message:"工作台数据已全部加载完成",duration:2e3})}catch(e){console.error("页面数据加载失败:",e),M.error("页面数据加载失败，请刷新重试")}finally{ee.value=!1}}),(e,a)=>{const t=E,l=z,o=C,m=x,p=B,v=q,f=R,le=N,oe=W,Ve=O,Le=J,Je=X,Ze=j;return _((r(),s("div",ce,[c(ne,{ref:"performanceMonitor"},null,512),n("div",ue,[n("div",de,[c(t,{size:60,src:d(k).avatar||qe},null,8,["src"]),n("div",me,[n("h2",null,F(d(k).nickname||"分销员"),1),n("p",null,"分销员ID: "+F(Oe.value),1),n("div",pe,[c(l,{type:"success",effect:"light"},{default:u(()=>a[2]||(a[2]=[g("活跃分销员",-1)])),_:1,__:[2]}),c(l,{type:"primary",effect:"light"},{default:u(()=>[g(F(Qe.value),1)]),_:1})])])]),n("div",ve,[c(m,{type:"primary",onClick:Ge,loading:te.customer},{default:u(()=>[c(o,null,{default:u(()=>[c(d(I))]),_:1}),a[3]||(a[3]=g(" 客户管理 ",-1))]),_:1,__:[3]},8,["loading"]),c(m,{onClick:ea,loading:te.group},{default:u(()=>[c(o,null,{default:u(()=>[c(d(P))]),_:1}),a[4]||(a[4]=g(" 群组管理 ",-1))]),_:1,__:[4]},8,["loading"])])]),c(v,{gutter:20,class:"stats-row"},{default:u(()=>[(r(!0),s(y,null,w($e.value,(e,a)=>(r(),b(p,{span:6,key:a},{default:u(()=>{return[n("div",{class:"stat-card",onClick:a=>(e=>{switch(console.log("点击统计卡片:",e),e){case"customers":Ge();break;case"groups":ea();break;case"commission":ta();break;case"conversion":M.info("转化率详情功能开发中...")}})(e.key)},[n("div",{class:"stat-icon",style:T({backgroundColor:e.color+"20",color:e.color})},[c(o,{size:24},{default:u(()=>[(r(),b(h(e.icon)))]),_:2},1024)],4),n("div",_e,[n("div",ge,F(e.prefix)+F((t=e.value,t>=1e4?(t/1e4).toFixed(1)+"w":t>=1e3?(t/1e3).toFixed(1)+"k":t.toString()))+F(e.suffix),1),n("div",ye,F(e.title),1),e.trend?(r(),s("div",{key:0,class:D(["stat-trend",(a=e.trend,a>0?"trend-up":"trend-down")])},[c(o,null,{default:u(()=>[e.trend>0?(r(),b(d(S),{key:0})):(r(),b(d(U),{key:1}))]),_:2},1024),g(" "+F(Math.abs(e.trend))+"% ",1)],2)):i("",!0)])],8,fe)];var a,t}),_:2},1024))),128))]),_:1}),c(f,{class:"quick-actions-card"},{header:u(()=>a[5]||(a[5]=[n("span",null,"快捷操作",-1)])),default:u(()=>[c(v,{gutter:15},{default:u(()=>[c(p,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:Ge},[c(o,{class:"action-icon"},{default:u(()=>[c(d(I))]),_:1}),a[6]||(a[6]=n("span",null,"客户管理",-1))])]),_:1}),c(p,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:Ke},[c(o,{class:"action-icon"},{default:u(()=>[c(d(V))]),_:1}),a[7]||(a[7]=n("span",null,"新增客户",-1))])]),_:1}),c(p,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:ea},[c(o,{class:"action-icon"},{default:u(()=>[c(d(P))]),_:1}),a[8]||(a[8]=n("span",null,"群组管理",-1))])]),_:1}),c(p,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:aa},[c(o,{class:"action-icon"},{default:u(()=>[c(d($))]),_:1}),a[9]||(a[9]=n("span",null,"推广链接",-1))])]),_:1}),c(p,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:ta},[c(o,{class:"action-icon"},{default:u(()=>[c(d(L))]),_:1}),a[10]||(a[10]=n("span",null,"佣金查看",-1))])]),_:1}),c(p,{span:4},{default:u(()=>[n("div",{class:"action-item",onClick:la},[c(o,{class:"action-icon"},{default:u(()=>[c(d(H))]),_:1}),a[11]||(a[11]=n("span",null,"订单查看",-1))])]),_:1})]),_:1})]),_:1}),c(v,{gutter:20,class:"charts-row"},{default:u(()=>[c(p,{span:16},{default:u(()=>[c(f,null,{header:u(()=>[n("div",we,[a[15]||(a[15]=n("span",null,"收入趋势",-1)),c(oe,{modelValue:ae.value,"onUpdate:modelValue":a[0]||(a[0]=e=>ae.value=e),size:"small",onChange:Xe},{default:u(()=>[c(le,{value:"7d"},{default:u(()=>a[12]||(a[12]=[g("近7天",-1)])),_:1,__:[12]}),c(le,{value:"30d"},{default:u(()=>a[13]||(a[13]=[g("近30天",-1)])),_:1,__:[13]}),c(le,{value:"90d"},{default:u(()=>a[14]||(a[14]=[g("近3个月",-1)])),_:1,__:[14]})]),_:1},8,["modelValue"])])]),default:u(()=>[c(G,{data:He.value,options:We,height:"300px"},null,8,["data"])]),_:1})]),_:1}),c(p,{span:8},{default:u(()=>[c(f,null,{header:u(()=>a[16]||(a[16]=[n("span",null,"客户分布",-1)])),default:u(()=>[c(K,{data:Re.value,options:Ne,height:"300px"},null,8,["data"])]),_:1})]),_:1})]),_:1}),c(v,{gutter:20,class:"info-row"},{default:u(()=>[c(p,{span:12},{default:u(()=>[c(f,null,{header:u(()=>[n("div",be,[a[18]||(a[18]=n("span",null,"客户动态",-1)),c(m,{size:"small",onClick:Ye},{default:u(()=>[c(o,null,{default:u(()=>[c(d(Q))]),_:1}),a[17]||(a[17]=g(" 刷新 ",-1))]),_:1,__:[17]})])]),default:u(()=>[n("div",he,[(r(!0),s(y,null,w(se.value,e=>{return r(),s("div",{key:e.id,class:"activity-item"},[n("div",Ce,[c(t,{size:32,src:e.customer?.avatar},null,8,["src"])]),n("div",ke,[n("div",xe,F(e.title),1),n("div",De,F(e.description),1),n("div",Fe,F((a=e.created_at,new Date(a).toLocaleString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))),1)]),e.value?(r(),s("div",Ae,[n("span",Me,"¥"+F(e.value),1)])):i("",!0)]);var a}),128)),0===se.value.length?(r(),s("div",je,[c(Ve,{description:"暂无客户动态"})])):i("",!0)])]),_:1})]),_:1}),c(p,{span:12},{default:u(()=>[c(f,null,{header:u(()=>[n("div",Ee,[a[20]||(a[20]=n("span",null,"待跟进客户",-1)),c(Le,{value:ie.value.length,class:"follow-up-badge"},{default:u(()=>[c(m,{size:"small",onClick:oa},{default:u(()=>a[19]||(a[19]=[g("查看全部",-1)])),_:1,__:[19]})]),_:1},8,["value"])])]),default:u(()=>[n("div",ze,[(r(!0),s(y,null,w(ie.value,e=>{return r(),s("div",{key:e.id,class:"follow-up-item"},[n("div",Ie,[n("div",Pe,F(e.name),1),n("div",Be,[c(l,{type:(t=e.level,{A:"danger",B:"warning",C:"primary",D:"info"}[t]||"info"),size:"small"},{default:u(()=>[g(F(e.level_text),1)]),_:2},1032,["type"])])]),n("div",Te,[a[21]||(a[21]=n("span",{class:"time-label"},"下次跟进:",-1)),n("span",{class:D(["time-value",ra(e.next_follow_up)])},F(na(e.next_follow_up)),3)]),n("div",Se,[c(m,{size:"small",type:"primary",onClick:a=>(async e=>{e.following=!0;try{await new Promise(e=>setTimeout(e,800)),A.success({title:"跟进成功",message:`已为客户 ${e.name} 创建跟进任务`}),e.next_follow_up=new Date(Date.now()+6048e5)}catch(a){M.error("创建跟进任务失败")}finally{e.following=!1}})(e)},{default:u(()=>a[22]||(a[22]=[g(" 立即跟进 ",-1)])),_:2,__:[22]},1032,["onClick"])])]);var t}),128)),0===ie.value.length?(r(),s("div",Ue,[c(Ve,{description:"暂无待跟进客户"})])):i("",!0)])]),_:1})]),_:1})]),_:1}),c(f,{class:"promotion-card"},{header:u(()=>a[23]||(a[23]=[n("span",null,"我的推广链接",-1)])),default:u(()=>[c(v,{gutter:20},{default:u(()=>[c(p,{span:16},{default:u(()=>[c(Je,{modelValue:re.value,"onUpdate:modelValue":a[1]||(a[1]=e=>re.value=e),placeholder:"您的专属推广链接",readonly:""},{prepend:u(()=>a[24]||(a[24]=[g("推广链接",-1)])),append:u(()=>[c(m,{onClick:sa},{default:u(()=>[c(o,null,{default:u(()=>[c(d(Y))]),_:1}),a[25]||(a[25]=g(" 复制 ",-1))]),_:1,__:[25]})]),_:1},8,["modelValue"])]),_:1}),c(p,{span:8},{default:u(()=>[c(m,{type:"primary",onClick:ia},{default:u(()=>[c(o,null,{default:u(()=>[c(d(Z))]),_:1}),a[26]||(a[26]=g(" 生成二维码 ",-1))]),_:1,__:[26]}),c(m,{onClick:aa},{default:u(()=>[c(o,null,{default:u(()=>[c(d(Z))]),_:1}),a[27]||(a[27]=g(" 更多工具 ",-1))]),_:1,__:[27]})]),_:1})]),_:1})]),_:1})])),[[Ze,ee.value]])}}},[["__scopeId","data-v-c3206462"]]);export{Ve as default};
