import{_ as s}from"./index-D2bI4m-v.js";import{ag as a,k as r,l as e,t,E as o,z as l,u as i,D as n}from"./vue-vendor-DGsK9sC4.js";import{af as c,T as d,aS as u,as as p,aF as _}from"./element-plus-DcSKpKA8.js";import"./utils-4VKArNEK.js";const f={class:"error-page"},m={class:"error-container"},v={class:"error-content"},b={class:"error-icon"},h={class:"error-actions"},j=s({__name:"403",setup(s){const j=a(),g=()=>{j.go(-1)},k=()=>{j.push("/dashboard")};return(s,a)=>{const j=d,x=p;return e(),r("div",f,[t("div",m,[t("div",v,[t("div",b,[o(j,{size:"120"},{default:l(()=>[o(i(c))]),_:1})]),a[2]||(a[2]=t("h1",{class:"error-title"},"403",-1)),a[3]||(a[3]=t("h2",{class:"error-subtitle"},"访问被拒绝",-1)),a[4]||(a[4]=t("p",{class:"error-description"}," 抱歉，您没有权限访问此页面。请联系管理员获取相应权限。 ",-1)),t("div",h,[o(x,{type:"primary",onClick:g},{default:l(()=>[o(j,null,{default:l(()=>[o(i(u))]),_:1}),a[0]||(a[0]=n(" 返回上页 ",-1))]),_:1,__:[0]}),o(x,{onClick:k},{default:l(()=>[o(j,null,{default:l(()=>[o(i(_))]),_:1}),a[1]||(a[1]=n(" 回到首页 ",-1))]),_:1,__:[1]})])])])])}}},[["__scopeId","data-v-e0b01e6b"]]);export{j as default};
