<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

/**
 * 微信群模型
 * 支持付费进群、城市定位、智能推广等功能
 */
class WechatGroup extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 微信群状态常量
     */
    const STATUS_ACTIVE = 1;    // 正常
    const STATUS_DISABLED = 2;  // 禁用
    const STATUS_FULL = 3;      // 已满

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'substation_id',
        'distributor_id',
        'title',
        'description',
        'qr_code',
        'cover_image',
        'price',
        'max_members',
        'current_members',
        'joined_count',
        'payment_methods',
        'city_location',
        'owner_display',
        'hot_display',
        'template_id',
        'custom_fields',
        'status',
        'sort_order',
        // 营销展示字段
        'subtitle',
        'read_count_display',
        'read_count',
        'like_count',
        'want_see_count',
        'button_title',
        'avatar_library',
        'member_image',
        'wx_accessible',
        'show_customer_service',
        'customer_service_avatar',
        'customer_service_title',
        'customer_service_desc',
        'remark',
        // ThinkPHP源码包营销功能字段
        'group_intro_title',
        'group_intro_content',
        'faq_title',
        'faq_content',
        'member_reviews',
        'customer_service_qr',
        'ad_qr_code',
        'red_packet_count',
        'message_count',
        'quick_content',
        'member_content',
        'ad_content',
        'display_type',
        'view_count',
        'avatar_type',
        'specific_payments',
        'payment_status',
        'marketing_tags',
        // 虚拟数据字段
        'virtual_members',
        'virtual_orders',
        'virtual_income',
        'today_views',
        'total_joins',
        // 高级营销字段
        'show_virtual_activity',
        'auto_city_replace',
        'city_insert_strategy',
        'show_member_avatars',
        'show_member_reviews',
        'show_real_time_stats',
        // 扩展内容字段
        'extra_title1',
        'extra_content1',
        'extra_title2',
        'extra_content2',
        'extra_title3',
        'extra_content3',
        // 统计字段
        'last_activity_at',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'price' => 'decimal:2',
        'payment_methods' => 'array',
        'custom_fields' => 'array',
        'city_location' => 'integer',
        'owner_display' => 'integer',
        'hot_display' => 'integer',
        'wx_accessible' => 'integer',
        'show_customer_service' => 'integer',
        'display_type' => 'integer',
        'view_count' => 'integer',
        'red_packet_count' => 'integer',
        'message_count' => 'integer',
        'virtual_members' => 'integer',
        'virtual_orders' => 'integer',
        'virtual_income' => 'decimal:2',
        'today_views' => 'integer',
        'total_joins' => 'integer',
        'show_virtual_activity' => 'integer',
        'auto_city_replace' => 'integer',
        'show_member_avatars' => 'integer',
        'show_member_reviews' => 'integer',
        'show_real_time_stats' => 'integer',
        'like_count' => 'integer',
        'want_see_count' => 'integer',
        'payment_status' => 'integer',
        'marketing_tags' => 'array',
        'status' => 'integer',
        'sort_order' => 'integer',
        'max_members' => 'integer',
        'current_members' => 'integer',
        'joined_count' => 'integer',
        'last_activity_at' => 'datetime',
    ];

    /**
     * 追加的访问器
     */
    protected $appends = [
        'status_name',
        'qr_code_url',
        'cover_image_url',
        'display_type_name',
        'formatted_faq_content',
        'formatted_member_reviews',
        'virtual_stats',
    ];

    /**
     * 关联群主
     */
    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联分销员
     */
    public function distributor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'distributor_id');
    }

    /**
     * 关联分站
     */
    public function substation(): BelongsTo
    {
        return $this->belongsTo(Substation::class);
    }

    /**
     * 关联订单
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * 获取微信群状态中文名
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            self::STATUS_ACTIVE => '正常',
            self::STATUS_DISABLED => '禁用',
            self::STATUS_FULL => '已满',
        ];

        return $statuses[$this->status] ?? '未知状态';
    }

    /**
     * 获取二维码完整URL
     */
    public function getQrCodeUrlAttribute(): ?string
    {
        if ($this->qr_code) {
            return Storage::disk('public')->url($this->qr_code);
        }
        return null;
    }

    /**
     * 获取封面图完整URL
     */
    public function getCoverImageUrlAttribute(): ?string
    {
        if ($this->cover_image) {
            return Storage::disk('public')->url($this->cover_image);
        }
        return null;
    }

    /**
     * 检查微信群是否可加入
     */
    public function isJoinable(): bool
    {
        return $this->status === self::STATUS_ACTIVE && 
               $this->current_members < $this->max_members;
    }

    /**
     * 增加当前成员数
     */
    public function addMember(int $count = 1): bool
    {
        $this->current_members += $count;
        
        if ($this->current_members >= $this->max_members) {
            $this->status = self::STATUS_FULL;
        }

        return $this->save();
    }

    /**
     * 减少当前成员数
     */
    public function removeMember(int $count = 1): bool
    {
        $this->current_members = max(0, $this->current_members - $count);

        if ($this->current_members < $this->max_members && $this->status === self::STATUS_FULL) {
            $this->status = self::STATUS_ACTIVE;
        }
        
        return $this->save();
    }

    /**
     * 获取群组统计数据（优化版）
     */
    public function getStats(): array
    {
        // 使用单次查询获取所有统计数据
        $stats = $this->orders()
            ->selectRaw('
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = ? THEN 1 END) as paid_orders,
                SUM(CASE WHEN status = ? THEN amount ELSE 0 END) as total_income,
                COUNT(CASE WHEN status = ? AND DATE(created_at) = CURDATE() THEN 1 END) as today_orders,
                SUM(CASE WHEN status = ? AND DATE(created_at) = CURDATE() THEN amount ELSE 0 END) as today_income,
                COUNT(CASE WHEN status = ? AND DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) THEN 1 END) as yesterday_orders,
                SUM(CASE WHEN status = ? AND DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) THEN amount ELSE 0 END) as yesterday_income
            ', [
                Order::STATUS_PAID_INT, Order::STATUS_PAID_INT, Order::STATUS_PAID_INT,
                Order::STATUS_PAID_INT, Order::STATUS_PAID_INT, Order::STATUS_PAID_INT
            ])
            ->first();

        return [
            'total_orders' => (int) $stats->total_orders,
            'paid_orders' => (int) $stats->paid_orders,
            'total_income' => (float) $stats->total_income,
            'today_orders' => (int) $stats->today_orders,
            'today_income' => (float) $stats->today_income,
            'yesterday_orders' => (int) $stats->yesterday_orders,
            'yesterday_income' => (float) $stats->yesterday_income,
        ];
    }

    /**
     * 获取缓存的统计数据
     */
    public function getCachedStats(): array
    {
        $cacheKey = "group_stats:{$this->id}";
        
        return Cache::remember($cacheKey, 1800, function () {
            return $this->getStats();
        });
    }

    /**
     * 获取自定义字段值
     */
    public function getCustomField(string $key, $default = null)
    {
        return data_get($this->custom_fields, $key, $default);
    }

    /**
     * 设置自定义字段值
     */
    public function setCustomField(string $key, $value): bool
    {
        $custom_fields = $this->custom_fields ?? [];
        data_set($custom_fields, $key, $value);
        $this->custom_fields = $custom_fields;
        return $this->save();
    }

    /**
     * 检查是否需要支付
     */
    public function isFree(): bool
    {
        return $this->price <= 0;
    }

    /**
     * 获取可用的支付方式
     */
    public function getAvailablePaymentMethods(): array
    {
        // 使用PaymentService动态获取支付方式
        $paymentService = new \App\Services\PaymentService();
        $availablePayments = $paymentService->getGroupAvailablePayments($this);
        
        // 如果没有配置的支付方式，返回群组设置的默认支付方式
        if (empty($availablePayments)) {
            return $this->payment_methods ?? ['wechat', 'alipay'];
        }
        
        // 提取支付方式代码
        return array_column($availablePayments, 'channel_code');
    }

    /**
     * 获取详细的支付方式信息
     */
    public function getDetailedPaymentMethods(): array
    {
        $paymentService = new \App\Services\PaymentService();
        return $paymentService->getGroupAvailablePayments($this);
    }

    /**
     * 激活群组
     */
    public function activate(): bool
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 禁用群组
     */
    public function disable(): bool
    {
        $this->status = self::STATUS_DISABLED;
        return $this->save();
    }

    /**
     * 获取群组的高级统计数据
     */
    public function getAdvancedStats(): array
    {
        $basicStats = $this->getStats();
        
        return array_merge($basicStats, [
            'conversion_rate' => $this->getConversionRate(),
            'avg_order_value' => $this->getAvgOrderValue(),
            'peak_hours' => $this->getPeakHours(),
            'weekly_trend' => $this->getWeeklyTrend(),
            'member_growth' => $this->getMemberGrowth(),
        ]);
    }

    /**
     * 获取转化率
     */
    public function getConversionRate(): float
    {
        $pageViews = $this->getCustomField('page_views', 0);
        if ($pageViews === 0) return 0;
        
        $orders = $this->orders()->count();
        return round(($orders / $pageViews) * 100, 2);
    }

    /**
     * 获取平均订单价值
     */
    public function getAvgOrderValue(): float
    {
        $paidOrders = $this->orders()->where('status', 'paid');
        $totalIncome = $paidOrders->sum('amount');
        $orderCount = $paidOrders->count();
        
        return $orderCount > 0 ? round($totalIncome / $orderCount, 2) : 0;
    }

    /**
     * 获取高峰时段
     */
    public function getPeakHours(): array
    {
        $hourlyStats = $this->orders()
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get()
            ->toArray();

        return $hourlyStats;
    }

    /**
     * 获取周趋势
     */
    public function getWeeklyTrend(): array
    {
        $weeklyStats = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $weeklyStats[] = [
                'date' => $date->format('Y-m-d'),
                'day_name' => $date->format('l'),
                'orders' => $this->orders()->whereDate('created_at', $date)->count(),
                'income' => $this->orders()->whereDate('created_at', $date)->where('status', 'paid')->sum('amount'),
            ];
        }
        
        return $weeklyStats;
    }

    /**
     * 获取成员增长趋势
     */
    public function getMemberGrowth(): array
    {
        // 这里可以根据实际需求来实现成员增长统计
        // 暂时返回模拟数据
        return [
            'current_members' => $this->current_members,
            'max_members' => $this->max_members,
            'growth_rate' => $this->calculateGrowthRate(),
            'projected_full_date' => $this->getProjectedFullDate(),
        ];
    }

    /**
     * 计算增长率
     */
    private function calculateGrowthRate(): float
    {
        // 基于最近7天的订单增长来估算成员增长率
        $recentOrders = $this->orders()->where('created_at', '>=', now()->subDays(7))->count();
        return $recentOrders > 0 ? round(($recentOrders / 7), 2) : 0;
    }

    /**
     * 预测满员日期
     */
    private function getProjectedFullDate(): ?string
    {
        $growthRate = $this->calculateGrowthRate();
        if ($growthRate <= 0) return null;
        
        $remainingSpots = $this->max_members - $this->current_members;
        $daysToFull = ceil($remainingSpots / $growthRate);
        
        return now()->addDays($daysToFull)->format('Y-m-d');
    }

    /**
     * 批量设置自定义字段
     */
    public function setCustomFields(array $fields): bool
    {
        $this->custom_fields = array_merge($this->custom_fields ?? [], $fields);
        return $this->save();
    }

    /**
     * 增加页面访问量
     */
    public function incrementPageViews(int $count = 1): bool
    {
        $currentViews = $this->getCustomField('page_views', 0);
        return $this->setCustomField('page_views', $currentViews + $count);
    }

    /**
     * 获取群组链接
     */
    public function getGroupLink(): string
    {
        return route('group.show', ['id' => $this->id]);
    }

    /**
     * 获取群组二维码链接
     */
    public function getQrCodeLink(): string
    {
        return route('group.qrcode', ['id' => $this->id]);
    }

    /**
     * 检查是否为热门群组
     */
    public function isHot(): bool
    {
        $recentOrders = $this->orders()->where('created_at', '>=', now()->subDays(7))->count();
        return $recentOrders >= 10; // 7天内超过10个订单算热门
    }

    /**
     * 获取群组标签
     */
    public function getTags(): array
    {
        $tags = [];
        
        if ($this->isFree()) {
            $tags[] = '免费';
        }
        
        if ($this->isHot()) {
            $tags[] = '热门';
        }
        
        if ($this->current_members >= $this->max_members * 0.8) {
            $tags[] = '即将满员';
        }
        
        if ($this->price > 100) {
            $tags[] = '高端';
        }
        
        return $tags;
    }

    /**
     * 获取展示类型名称
     */
    public function getDisplayTypeNameAttribute(): string
    {
        return $this->display_type == 1 ? '文字+图片' : '仅图片';
    }

    /**
     * 获取格式化的FAQ内容
     */
    public function getFormattedFaqContentAttribute(): array
    {
        if (!$this->faq_content) {
            return [];
        }

        $faqs = [];
        $lines = explode("\n", $this->faq_content);
        
        foreach ($lines as $line) {
            if (strpos($line, '----') !== false) {
                $parts = explode('----', $line, 2);
                if (count($parts) == 2) {
                    $faqs[] = [
                        'question' => trim($parts[0]),
                        'answer' => trim($parts[1])
                    ];
                }
            }
        }

        return $faqs;
    }

    /**
     * 获取格式化的群友评价内容
     */
    public function getFormattedMemberReviewsAttribute(): array
    {
        if (!$this->member_reviews) {
            return [];
        }

        $reviews = [];
        $lines = explode("\n", $this->member_reviews);
        
        foreach ($lines as $line) {
            if (strpos($line, '----') !== false) {
                $parts = explode('----', $line, 2);
                if (count($parts) == 2) {
                    $reviews[] = [
                        'content' => trim($parts[0]),
                        'rating' => trim($parts[1])
                    ];
                }
            }
        }

        return $reviews;
    }

    /**
     * 获取虚拟统计数据
     */
    public function getVirtualStatsAttribute(): array
    {
        return [
            'members' => $this->virtual_members + $this->current_members,
            'orders' => $this->virtual_orders + $this->orders()->count(),
            'income' => $this->virtual_income + $this->total_earnings,
            'today_views' => $this->today_views + rand(10, 50),
            'total_joins' => $this->total_joins + rand(100, 500),
        ];
    }

    /**
     * 生成虚拟成员数据
     */
    public function generateVirtualMembers(int $count = 13): array
    {
        $nicknames = [
            '最美的太阳花', '孤海的浪漫', '薰衣草', '木槿，花', '森林小巷少女与狐@',
            '冬日暖阳', '午後の夏天', '嘴角的美人痣。', '朽梦挽歌', '心淡然',
            '歇斯底里的狂笑', '夏，好温暖', '彼岸花开', '岸与海的距离', '猫味萝莉',
            '软甜阮甜', '枯酒无味', '寄个拥抱', '少女病', '江南酒馆',
            '淡尘轻烟', '过气软妹', '檬℃柠叶', '仙九', '且听',
            '风铃', '野性_萌', '樱桃小丸子', '少女の烦躁期', '无名小姐',
            '香味少女', '清澈的眼眸', '海草舞蹈', '淡淡de茶香味', '雨后彩虹',
            '安全等待你来', '薄荷蓝', '指尖上的星空', '雲朵兒', '准风月谈', '柠檬'
        ];

        $members = [];
        for ($i = 0; $i < $count; $i++) {
            $avatar_num = rand(1, 41);
            $avatar_ext = $this->avatar_type == 'qq' ? 'jpg' : 'jpeg';
            
            $members[] = [
                'nickname' => $nicknames[array_rand($nicknames)],
                'avatar' => "/face/{$this->avatar_type}/{$avatar_num}.{$avatar_ext}",
                'join_time' => now()->subDays(rand(1, 30))->format('Y-m-d H:i')
            ];
        }

        return $members;
    }

    /**
     * 生成虚拟群友评价数据
     */
    public function generateVirtualReviews(): array
    {
        $nicknames = [
            '最美的太阳花', '孤海的浪漫', '薰衣草', '木槿，花', '森林小巷少女与狐@',
            '冬日暖阳', '午後の夏天', '嘴角的美人痣。', '朽梦挽歌', '心淡然'
        ];

        $reviews = [];
        foreach ($this->formatted_member_reviews as $review) {
            $avatar_num = rand(1, 41);
            $avatar_ext = $this->avatar_type == 'qq' ? 'jpg' : 'jpeg';
            
            $reviews[] = [
                'nickname' => $nicknames[array_rand($nicknames)],
                'avatar' => "/face/{$this->avatar_type}/{$avatar_num}.{$avatar_ext}",
                'content' => $review['content'],
                'rating' => $review['rating'],
                'time' => now()->subDays(rand(1, 30))->format('Y-m-d H:i')
            ];
        }

        return $reviews;
    }

    /**
     * 获取自动城市替换的标题（智能版本）
     */
    public function getCityReplacedTitle(?string $userCity = null): string
    {
        if (!$this->auto_city_replace) {
            return $this->title;
        }

        $city = $userCity ?? $this->detectUserCity();
        if (empty($city) || $city === '本地') {
            return $this->title;
        }

        // 使用智能城市替换策略
        return $this->applySmartCityReplacement($this->title, $city);
    }

    /**
     * 智能城市替换策略
     */
    private function applySmartCityReplacement(string $title, string $city): string
    {
        // 策略1：如果标题中包含"xxx"占位符，直接替换（兼容原有方式）
        if (stripos($title, 'xxx') !== false) {
            return str_ireplace('xxx', $city, $title);
        }

        // 策略2：如果标题已经包含城市名称，不重复添加
        if (stripos($title, $city) !== false) {
            return $title;
        }

        // 策略3：检测标题中是否已经包含其他城市名称
        $existingCity = $this->detectExistingCityInTitle($title);
        if ($existingCity) {
            // 替换现有城市为当前用户城市
            return str_ireplace($existingCity, $city, $title);
        }

        // 策略4：智能位置插入
        return $this->insertCityIntelligently($title, $city);
    }

    /**
     * 检测标题中是否已包含城市名称
     */
    private function detectExistingCityInTitle(string $title): ?string
    {
        // 常见城市列表（可以扩展）
        $cities = [
            '北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '武汉',
            '成都', '重庆', '西安', '青岛', '大连', '宁波', '厦门', '福州',
            '郑州', '石家庄', '太原', '沈阳', '长春', '哈尔滨', '长沙',
            '南昌', '合肥', '济南', '昆明', '贵阳', '兰州', '银川',
            '西宁', '乌鲁木齐', '拉萨', '海口', '三亚', '珠海', '中山',
            '佛山', '东莞', '惠州', '江门', '湛江', '茂名', '肇庆'
        ];

        foreach ($cities as $cityName) {
            if (stripos($title, $cityName) !== false) {
                return $cityName;
            }
        }

        return null;
    }

    /**
     * 智能位置插入城市名称
     */
    private function insertCityIntelligently(string $title, string $city): string
    {
        // 获取城市插入策略配置
        $strategy = $this->getCustomField('city_insert_strategy', 'auto');

        switch ($strategy) {
            case 'prefix':
                // 前缀模式：城市 + 标题
                return $city . $title;
                
            case 'suffix':
                // 后缀模式：标题 + 城市版
                return $title . '(' . $city . '版)';
                
            case 'natural':
                // 自然插入模式
                return $this->insertCityNaturally($title, $city);
                
            case 'none':
                // 不插入，保持原标题
                return $title;
                
            default:
            case 'auto':
                // 自动判断最佳插入方式
                return $this->autoInsertCity($title, $city);
        }
    }

    /**
     * 自然语言插入城市名称
     */
    private function insertCityNaturally(string $title, string $city): string
    {
        // 检测标题模式并智能插入
        $patterns = [
            // 模式1: "XXX交流群" -> "北京XXX交流群"
            '/^(.+)(交流群|讨论群|学习群|分享群)$/' => $city . '$1$2',
            
            // 模式2: "XXX爱好者" -> "北京XXX爱好者"
            '/^(.+)(爱好者|发烧友|玩家|达人)$/' => $city . '$1$2',
            
            // 模式3: "学XXX的群" -> "北京学XXX的群"
            '/^(学.+的群|玩.+的群)$/' => $city . '$1',
            
            // 模式4: "XXX群" -> "北京XXX群"
            '/^(.+群)$/' => $city . '$1',
            
            // 模式5: 包含"本地"关键词
            '/本地/' => str_replace('本地', $city, $title),
        ];

        foreach ($patterns as $pattern => $replacement) {
            if (preg_match($pattern, $title)) {
                return preg_replace($pattern, $replacement, $title);
            }
        }

        // 如果没有匹配的模式，使用前缀模式
        return $city . $title;
    }

    /**
     * 自动判断最佳插入方式
     */
    private function autoInsertCity(string $title, string $city): string
    {
        $titleLength = mb_strlen($title);
        
        // 短标题（≤4字）：使用前缀
        if ($titleLength <= 4) {
            return $city . $title;
        }
        
        // 中等长度标题：使用自然插入
        if ($titleLength <= 10) {
            return $this->insertCityNaturally($title, $city);
        }
        
        // 长标题：使用后缀，避免标题过长
        return $title . '(' . $city . ')';
    }

    /**
     * 获取城市插入策略的显示名称
     */
    public function getCityInsertStrategyName(): string
    {
        $strategy = $this->getCustomField('city_insert_strategy', 'auto');
        
        $names = [
            'auto' => '智能判断',
            'prefix' => '前缀模式',
            'suffix' => '后缀模式', 
            'natural' => '自然插入',
            'none' => '不插入',
        ];
        
        return $names[$strategy] ?? '智能判断';
    }

    /**
     * 设置城市插入策略
     */
    public function setCityInsertStrategy(string $strategy): bool
    {
        $validStrategies = ['auto', 'prefix', 'suffix', 'natural', 'none'];
        
        if (!in_array($strategy, $validStrategies)) {
            return false;
        }
        
        return $this->setCustomField('city_insert_strategy', $strategy);
    }

    /**
     * 检测用户城市（增强版实现）
     */
    private function detectUserCity(): string
    {
        $ipLocationService = new \App\Services\IPLocationService();
        $clientIP = $ipLocationService->getClientIP();
        return $ipLocationService->getCity($clientIP);
    }

    /**
     * 检查是否为微信浏览器
     */
    public function isWechatBrowser(): bool
    {
        if (!$this->wechat_browser_only) {
            return true;
        }

        $userAgent = request()->header('User-Agent');
        return strpos($userAgent, 'MicroMessenger') !== false;
    }

    /**
     * 增加浏览次数
     */
    public function incrementViewCount(): bool
    {
        $this->increment('view_count');
        
        // 如果是今天的第一次访问，也更新今日浏览量
        if ($this->wasRecentlyCreated || $this->updated_at->isToday()) {
            $this->increment('today_views');
        }
        
        $this->update(['last_activity_at' => now()]);
        
        return true;
    }

    /**
     * 验证群组链接的有效性
     */
    public function validateGroupAccess(): array
    {
        // 检查群组状态
        if ($this->status != self::STATUS_ACTIVE) {
            return [
                'valid' => false,
                'message' => '群组已被禁用或已满',
                'redirect' => 'https://www.baidu.com/s?ie=UTF-8&wd=群组不可用'
            ];
        }

        // 检查分销商状态
        if ($this->user && $this->user->status != 'active') {
            return [
                'valid' => false,
                'message' => '分销商账户异常',
                'redirect' => 'https://www.baidu.com/s?ie=UTF-8&wd=分销商不可用'
            ];
        }

        // 检查分站状态
        if ($this->substation && $this->substation->status != 'active') {
            return [
                'valid' => false,
                'message' => '分站账户异常',
                'redirect' => 'https://www.baidu.com/s?ie=UTF-8&wd=分站不可用'
            ];
        }

        return ['valid' => true];
    }

    /**
     * 获取支付配置
     */
    public function getPaymentConfig(): array
    {
        if ($this->specific_payments) {
            $paymentIds = explode(',', $this->specific_payments);
            // 这里可以根据支付ID获取具体的支付配置
            return ['payment_ids' => $paymentIds];
        }

        return ['payment_methods' => $this->payment_methods];
    }
} 