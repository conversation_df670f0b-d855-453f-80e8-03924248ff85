import{d as a}from"./index-D2bI4m-v.js";const s=()=>a.get("/admin/anti-block/stats"),t=s=>a.get("/admin/anti-block/domains",{params:s}),i=s=>a.get("/admin/anti-block/domains",{params:s}),n=s=>a.post("/admin/anti-block/domains",s),o=(s,t)=>a.put(`/admin/anti-block/domains/${s}`,t),m=s=>a.post("/admin/anti-block/domains/batch-check",s),c=s=>a.post("/admin/anti-block/domains/batch-delete",s),d=s=>a.get("/admin/anti-block/short-links",{params:s}),l=s=>a.post("/admin/anti-block/short-links",s),k=s=>a.delete(`/admin/anti-block/short-links/${s}`),e=s=>a.get("/admin/anti-block/short-links/export",{params:s}),b=s=>a.get("/admin/anti-block/access-stats",{params:s}),r=s=>a.get("/admin/anti-block/access-logs",{params:s}),p=s=>a.get("/admin/anti-block/click-trends",{params:s}),h=s=>a.post("/admin/anti-block/qrcode",s),g=(s,t)=>a.put(`/admin/anti-block/short-links/${s}`,t),$=(s,t)=>a.post(`/admin/anti-block/short-links/${s}/switch-domain`,t);export{n as a,t as b,m as c,c as d,d as e,i as f,s as g,h,k as i,g as j,l as k,r as l,e as m,p as n,b as o,$ as s,o as u};
