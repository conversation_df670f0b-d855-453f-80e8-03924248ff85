<template>
  <div class="data-screen">
    <!-- 大屏顶部 -->
    <div class="screen-header">
      <div class="header-left">
        <div class="logo">
          <div class="logo-icon">📊</div>
          <span class="logo-text">晨鑫流量变现系统 数据中心</span>
        </div>
      </div>
      <div class="header-center">
        <h1 class="screen-title">实时运营数据大屏</h1>
      </div>
      <div class="header-right">
        <div class="current-time">{{ currentTime }}</div>
        <div class="refresh-info">
          <span class="refresh-dot"></span>
          实时更新
        </div>
      </div>
    </div>

    <!-- 核心指标区域 -->
    <div class="metrics-section">
      <div class="metric-card" v-for="metric in coreMetrics" :key="metric.key">
        <div class="metric-icon">
          <i :class="metric.icon"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">
            <CountTo :end-val="metric.value" :duration="2000" />
            <span class="metric-unit">{{ metric.unit }}</span>
          </div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-trend" :class="metric.trend.type">
            <i :class="metric.trend.icon"></i>
            <span>{{ metric.trend.value }}%</span>
          </div>
        </div>
        <div class="metric-chart">
          <MiniLineChart :data="metric.chartData" :color="metric.color" />
        </div>
      </div>
    </div>

    <!-- 主要图表区域 -->
    <div class="charts-section">
      <!-- 收入趋势 -->
      <div class="chart-container large">
        <div class="chart-header">
          <h3>收入趋势分析</h3>
          <div class="chart-controls">
            <button 
              v-for="period in timePeriods" 
              :key="period.value"
              :class="['period-btn', { active: selectedPeriod === period.value }]"
              @click="selectedPeriod = period.value"
            >
              {{ period.label }}
            </button>
          </div>
        </div>
        <div class="chart-content">
          <LineChart 
            :data="revenueChartData" 
            :options="revenueChartOptions"
            height="300"
          />
        </div>
      </div>

      <!-- 用户活跃度 -->
      <div class="chart-container medium">
        <div class="chart-header">
          <h3>用户活跃度</h3>
        </div>
        <div class="chart-content">
          <DoughnutChart 
            :data="userActivityData" 
            :options="doughnutOptions"
            height="250"
          />
        </div>
      </div>

      <!-- 订单状态分布 -->
      <div class="chart-container medium">
        <div class="chart-header">
          <h3>订单状态分布</h3>
        </div>
        <div class="chart-content">
          <BarChart 
            :data="orderStatusData" 
            :options="barChartOptions"
            height="250"
          />
        </div>
      </div>
    </div>

    <!-- 详细数据区域 -->
    <div class="details-section">
      <!-- 地域分布 -->
      <div class="detail-card">
        <div class="card-header">
          <h3>地域分布TOP10</h3>
          <div class="header-action">
            <button class="refresh-btn" @click="refreshRegionData">
              <i class="icon-refresh"></i>
            </button>
          </div>
        </div>
        <div class="card-content">
          <div class="region-list">
            <div 
              class="region-item" 
              v-for="(region, index) in regionData" 
              :key="region.name"
            >
              <div class="region-rank">{{ index + 1 }}</div>
              <div class="region-name">{{ region.name }}</div>
              <div class="region-bar">
                <div 
                  class="region-progress" 
                  :style="{ width: region.percentage + '%' }"
                ></div>
              </div>
              <div class="region-value">{{ region.count }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时动态 -->
      <div class="detail-card">
        <div class="card-header">
          <h3>实时动态</h3>
        </div>
        <div class="card-content">
          <div class="activity-list">
            <div 
              class="activity-item" 
              v-for="activity in realtimeActivities" 
              :key="activity.id"
            >
              <div class="activity-icon">
                <i :class="activity.icon"></i>
              </div>
              <div class="activity-content">
                <div class="activity-text">{{ activity.text }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
              <div class="activity-value" :class="activity.type">
                {{ activity.value }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统监控 -->
      <div class="detail-card">
        <div class="card-header">
          <h3>系统监控</h3>
        </div>
        <div class="card-content">
          <div class="monitor-grid">
            <div class="monitor-item" v-for="monitor in systemMonitors" :key="monitor.name">
              <div class="monitor-name">{{ monitor.name }}</div>
              <div class="monitor-gauge">
                <CircularProgress 
                  :percentage="monitor.value" 
                  :color="monitor.color"
                  :size="80"
                />
              </div>
              <div class="monitor-value">{{ monitor.value }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部指标滚动 -->
    <div class="bottom-metrics">
      <div class="metrics-scroll">
        <div class="scroll-item" v-for="item in scrollMetrics" :key="item.key">
          <span class="scroll-label">{{ item.label }}：</span>
          <span class="scroll-value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import BarChart from '@/components/Charts/BarChart.vue'
import CountTo from '@/components/CountTo.vue'
import MiniLineChart from '@/components/Charts/MiniLineChart.vue'
import CircularProgress from '@/components/UI/CircularProgress.vue'

// 响应式数据
const currentTime = ref('')
const selectedPeriod = ref('7d')
const isLoading = ref(false)

// 时间周期选项
const timePeriods = [
  { label: '今日', value: '1d' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' }
]

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'totalRevenue',
    label: '总收入',
    value: 1254890,
    unit: '元',
    icon: 'icon-money',
    color: '#1890ff',
    trend: { type: 'up', icon: 'icon-arrow-up', value: 12.5 },
    chartData: [20, 35, 28, 45, 38, 52, 48, 60, 55, 70]
  },
  {
    key: 'activeUsers',
    label: '活跃用户',
    value: 8652,
    unit: '人',
    icon: 'icon-user',
    color: '#52c41a',
    trend: { type: 'up', icon: 'icon-arrow-up', value: 8.3 },
    chartData: [30, 25, 40, 35, 50, 45, 60, 55, 65, 58]
  },
  {
    key: 'totalOrders',
    label: '订单总数',
    value: 15428,
    unit: '单',
    icon: 'icon-order',
    color: '#722ed1',
    trend: { type: 'up', icon: 'icon-arrow-up', value: 15.2 },
    chartData: [25, 30, 35, 28, 42, 38, 55, 50, 58, 62]
  },
  {
    key: 'conversionRate',
    label: '转化率',
    value: 23.8,
    unit: '%',
    icon: 'icon-chart',
    color: '#fa8c16',
    trend: { type: 'down', icon: 'icon-arrow-down', value: 2.1 },
    chartData: [45, 42, 48, 40, 38, 35, 40, 38, 42, 45]
  }
])

// 收入趋势图表数据
const revenueChartData = ref({
  labels: ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06', '01/07'],
  datasets: [
    {
      label: '收入',
      data: [12000, 15000, 13500, 18000, 16500, 20000, 22000],
      borderColor: '#1890ff',
      backgroundColor: 'rgba(24, 144, 255, 0.1)',
      fill: true,
      tension: 0.4
    },
    {
      label: '目标',
      data: [15000, 15000, 15000, 15000, 15000, 15000, 15000],
      borderColor: '#ff4d4f',
      borderDash: [5, 5],
      fill: false
    }
  ]
})

// 图表配置
const revenueChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        usePointStyle: true,
        color: '#ffffff'
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        color: '#ffffff',
        callback: function(value) {
          return value / 1000 + 'k'
        }
      },
      grid: {
        color: 'rgba(255, 255, 255, 0.1)'
      }
    },
    x: {
      ticks: {
        color: '#ffffff'
      },
      grid: {
        color: 'rgba(255, 255, 255, 0.1)'
      }
    }
  }
}

// 用户活跃度数据
const userActivityData = ref({
  labels: ['活跃用户', '新用户', '流失用户'],
  datasets: [{
    data: [65, 25, 10],
    backgroundColor: ['#52c41a', '#1890ff', '#ff4d4f'],
    borderWidth: 0
  }]
})

// 圆环图配置
const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom',
      labels: {
        color: '#ffffff',
        usePointStyle: true,
        padding: 20
      }
    }
  }
}

// 订单状态数据
const orderStatusData = ref({
  labels: ['待支付', '已支付', '处理中', '已完成', '已取消'],
  datasets: [{
    label: '订单数量',
    data: [120, 890, 450, 1200, 80],
    backgroundColor: ['#faad14', '#52c41a', '#1890ff', '#722ed1', '#ff4d4f']
  }]
})

// 柱状图配置
const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        color: '#ffffff'
      },
      grid: {
        color: 'rgba(255, 255, 255, 0.1)'
      }
    },
    x: {
      ticks: {
        color: '#ffffff'
      },
      grid: {
        display: false
      }
    }
  }
}

// 地域分布数据
const regionData = ref([
  { name: '广东省', count: 2156, percentage: 100 },
  { name: '江苏省', count: 1847, percentage: 85.7 },
  { name: '浙江省', count: 1632, percentage: 75.7 },
  { name: '北京市', count: 1425, percentage: 66.1 },
  { name: '上海市', count: 1298, percentage: 60.2 },
  { name: '山东省', count: 1134, percentage: 52.6 },
  { name: '河南省', count: 976, percentage: 45.3 },
  { name: '四川省', count: 845, percentage: 39.2 },
  { name: '湖北省', count: 723, percentage: 33.5 },
  { name: '福建省', count: 658, percentage: 30.5 }
])

// 实时动态数据
const realtimeActivities = ref([
  {
    id: 1,
    icon: 'icon-user-add',
    text: '新用户注册',
    time: '2分钟前',
    value: '+1',
    type: 'success'
  },
  {
    id: 2,
    icon: 'icon-order',
    text: '订单创建',
    time: '3分钟前',
    value: '+5',
    type: 'info'
  },
  {
    id: 3,
    icon: 'icon-money',
    text: '支付完成',
    time: '5分钟前',
    value: '+¥1,200',
    type: 'success'
  },
  {
    id: 4,
    icon: 'icon-warning',
    text: '系统告警',
    time: '8分钟前',
    value: '处理中',
    type: 'warning'
  }
])

// 系统监控数据
const systemMonitors = ref([
  { name: 'CPU', value: 45, color: '#1890ff' },
  { name: '内存', value: 68, color: '#52c41a' },
  { name: '磁盘', value: 32, color: '#faad14' },
  { name: '网络', value: 78, color: '#722ed1' }
])

// 底部滚动指标
const scrollMetrics = ref([
  { key: 'pv', label: '今日PV', value: '125,432' },
  { key: 'uv', label: '今日UV', value: '23,156' },
  { key: 'bounce', label: '跳出率', value: '32.5%' },
  { key: 'duration', label: '平均时长', value: '4m 23s' },
  { key: 'conversion', label: '转化率', value: '12.8%' },
  { key: 'satisfaction', label: '满意度', value: '94.2%' }
])

// 更新当前时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 刷新地域数据
const refreshRegionData = () => {
  // 模拟数据刷新
  console.log('刷新地域数据')
}

// 生命周期
let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  
  // 模拟实时数据更新
  setInterval(() => {
    // 更新核心指标
    coreMetrics.value.forEach(metric => {
      metric.value += Math.floor(Math.random() * 10)
    })
  }, 5000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.data-screen {
  min-height: 100vh; /* 修复高度计算，确保完整显示 */
  height: auto; /* 允许内容自适应高度 */
  background: linear-gradient(135deg, #0c1838 0%, #1a2456 50%, #0f1b3c 100%);
  color: #ffffff;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  overflow-x: hidden;
  overflow-y: auto; /* 允许垂直滚动 */
  margin: -24px; /* 保持全屏效果 */
  border-radius: 0;
  padding-bottom: 40px; /* 添加底部间距，防止内容被遮挡 */
}

/* 头部样式 */
.screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left .logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 40px;
  line-height: 1;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(45deg, #1890ff, #52c41a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.screen-title {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(45deg, #ffffff, #a8d5ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.header-right {
  text-align: right;
}

.current-time {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #52c41a;
}

.refresh-dot {
  width: 8px;
  height: 8px;
  background: #52c41a;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 核心指标区域 - 优化间距和布局 */
.metrics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  padding: 24px 32px;
}

.metric-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 25px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1890ff, #52c41a, #722ed1, #fa8c16);
}

.metric-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 15px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.metric-unit {
  font-size: 16px;
  color: #a8d5ff;
}

.metric-label {
  font-size: 14px;
  color: #a8d5ff;
  margin-bottom: 12px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  font-weight: 600;
}

.metric-trend.up {
  color: #52c41a;
}

.metric-trend.down {
  color: #ff4d4f;
}

.metric-chart {
  position: absolute;
  right: 15px;
  top: 15px;
  width: 80px;
  height: 40px;
  opacity: 0.6;
}

/* 图表区域 - 优化间距和布局 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 24px;
  padding: 0 32px 24px;
}

.chart-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.period-btn {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: #ffffff;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-btn:hover,
.period-btn.active {
  background: #1890ff;
  border-color: #1890ff;
}

.chart-content {
  padding: 20px 25px;
}

/* 详细数据区域 - 优化间距和布局 */
.details-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  padding: 0 32px 24px;
}

.detail-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.refresh-btn {
  background: transparent;
  border: none;
  color: #1890ff;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  color: #40a9ff;
  transform: rotate(180deg);
}

.card-content {
  padding: 20px 25px;
}

/* 地域分布 */
.region-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.region-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.region-rank {
  width: 24px;
  height: 24px;
  background: linear-gradient(45deg, #1890ff, #40a9ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.region-name {
  width: 80px;
  font-size: 14px;
}

.region-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.region-progress {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.region-value {
  width: 60px;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
}

/* 实时动态 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.activity-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(45deg, #1890ff, #40a9ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #a8d5ff;
}

.activity-value {
  font-size: 14px;
  font-weight: 600;
}

.activity-value.success {
  color: #52c41a;
}

.activity-value.info {
  color: #1890ff;
}

.activity-value.warning {
  color: #faad14;
}

/* 系统监控 */
.monitor-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.monitor-item {
  text-align: center;
}

.monitor-name {
  font-size: 14px;
  margin-bottom: 10px;
  color: #a8d5ff;
}

.monitor-gauge {
  margin-bottom: 10px;
}

.monitor-value {
  font-size: 16px;
  font-weight: 600;
}

/* 底部滚动指标 - 确保完全可见 */
.bottom-metrics {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 15px 0;
  overflow: hidden;
  margin-top: 24px; /* 添加顶部间距 */
  position: relative; /* 确保正确定位 */
  z-index: 1; /* 确保在最上层 */
}

.metrics-scroll {
  display: flex;
  gap: 60px;
  animation: scroll 30s linear infinite;
  white-space: nowrap;
}

.scroll-item {
  font-size: 14px;
  display: flex;
  gap: 5px;
}

.scroll-label {
  color: #a8d5ff;
}

.scroll-value {
  color: #ffffff;
  font-weight: 600;
}

@keyframes scroll {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/* 响应式设计 - 全面优化 */

/* 大屏幕优化 (>1920px) */
@media (min-width: 1921px) {
  .data-screen {
    max-width: 1920px;
    margin: -24px auto;
  }

  .metrics-section,
  .charts-section,
  .details-section {
    padding-left: 60px;
    padding-right: 60px;
  }
}

/* 中大屏幕 (1400px-1600px) */
@media (max-width: 1600px) and (min-width: 1401px) {
  .metrics-section {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px 32px;
  }

  .charts-section {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 20px;
  }

  .details-section {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

/* 中等屏幕 (1200px-1400px) */
@media (max-width: 1400px) and (min-width: 1201px) {
  .metrics-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 20px 28px;
  }

  .charts-section {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 0 28px 20px;
  }

  .details-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 0 28px 20px;
  }
}

/* 小屏幕 (768px-1200px) */
@media (max-width: 1200px) and (min-width: 769px) {
  .data-screen {
    padding-bottom: 32px;
  }

  .screen-header {
    padding: 16px 24px;
  }

  .metrics-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px 24px;
  }

  .charts-section {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 24px 16px;
  }

  .details-section {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 24px 16px;
  }
}

/* 移动端 (<768px) */
@media (max-width: 768px) {
  .data-screen {
    padding-bottom: 24px;
  }

  .screen-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    padding: 12px 16px;
  }

  .screen-title {
    font-size: 20px;
  }

  .logo-text {
    font-size: 16px;
  }

  .metrics-section {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
  }

  .charts-section,
  .details-section {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 16px 16px;
  }

  .metric-card {
    padding: 16px;
  }

  .chart-header,
  .card-header {
    padding: 16px 20px;
  }

  .chart-content {
    padding: 16px 20px;
  }
}
</style> 