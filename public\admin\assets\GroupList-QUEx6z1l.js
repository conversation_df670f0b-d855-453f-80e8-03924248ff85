import{_ as e,u as a,g as l}from"./index-D2bI4m-v.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css                *//* empty css               */import{r as t,c as s,d as n,e as o,k as u,l as r,t as i,E as d,z as c,D as m,L as p,y as _,u as v,F as f,Y as g,a2 as b,n as h,B as y,A as w,a4 as V,G as k}from"./vue-vendor-DGsK9sC4.js";import{as as x,o as C,aY as q,p as U,U as D,Q as z,R as j,bc as T,b4 as M,b2 as $,bd as I,aH as B,aW as L,aV as A,be as S,bn as O,T as F,aU as H,bo as P,bp as G,bf as Q,b8 as R,au as N,ao as Y,an as E,bq as K,aO as Z,ax as W,ay as J,br as X,bs as ee,av as ae,a4 as le,bj as te,b7 as se,aN as ne,b6 as oe,bk as ue,b3 as re,a6 as ie,at as de,bm as ce,a5 as me,bt as pe,b1 as _e,bu as ve,bv as fe,bw as ge,aP as be,bx as he,bb as ye,ba as we,aM as Ve,ap as ke,by as xe,bz as Ce,bA as qe,bB as Ue,am as De,bi as ze,Y as je,bC as Te,bD as Me,a0 as $e,$ as Ie,aQ as Be,bl as Le,bE as Ae,ak as Se,bF as Oe,bG as Fe,ai as He,aZ as Pe,aw as Ge}from"./element-plus-DcSKpKA8.js";/* empty css                     *//* empty css                  *//* empty css                       *//* empty css                 *//* empty css                  *//* empty css                        */import{u as Qe,c as Re,g as Ne,a as Ye,b as Ee,d as Ke,e as Ze,f as We,h as Je}from"./community-Cx7BK33_.js";import Xe from"./UserProfile-nYpRIurq.js";/* empty css                 *//* empty css                       *//* empty css                         *//* empty css                        */import{I as ea}from"./ImageUpload-DAXj9MB_.js";/* empty css                         */import{f as aa}from"./format-3eU4VJ9V.js";import"./utils-4VKArNEK.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const la={class:"rich-text-editor"},ta={class:"editor-toolbar"},sa=["innerHTML"],na={class:"editor-footer"},oa={class:"word-count"},ua={__name:"RichTextEditor",props:{modelValue:{type:String,default:""},height:{type:Number,default:200},placeholder:{type:String,default:"请输入内容..."},maxLength:{type:Number,default:1e4}},emits:["update:modelValue","focus","blur"],setup(e,{expose:a,emit:l}){const p=e,_=l,v=t(null),f=t(""),g=t(!1),b=s(()=>(v.value?.innerText||"").length);n(()=>p.modelValue,e=>{e!==f.value&&(f.value=e,v.value&&(v.value.innerHTML=e))},{immediate:!0});const h=(e,a=null)=>{document.execCommand(e,!1,a),v.value?.focus(),A()},y=e=>document.queryCommandState(e),w=async()=>{try{const{value:e}=await j.prompt("请输入链接地址","插入链接",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+/,inputErrorMessage:"请输入有效的链接地址"});e&&h("createLink",e)}catch(e){}},V=async()=>{try{const{value:e}=await j.prompt("请输入图片地址","插入图片",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i,inputErrorMessage:"请输入有效的图片地址"});e&&h("insertImage",e)}catch(e){}},k=async()=>{try{const{value:e}=await j.prompt("请输入视频地址","插入视频",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:\/\/.+/,inputErrorMessage:"请输入有效的视频地址",inputPlaceholder:"支持 MP4、优酷、腾讯视频、B站等链接"});e&&T(e)}catch(e){}},T=e=>{let a="";if(M(e))a=`<video controls style="max-width: 100%; height: auto; margin: 8px 0;">\n      <source src="${e}" type="video/mp4">\n      您的浏览器不支持视频播放。\n    </video>`;else if($(e)){const l=I(e);l&&(a=`<iframe src="//player.bilibili.com/player.html?bvid=${l}" \n        scrolling="no" border="0" frameborder="no" framespacing="0" \n        allowfullscreen="true" style="width: 100%; height: 400px; margin: 8px 0;">\n      </iframe>`)}else if(B(e)){const l=L(e);l&&(a=`<iframe src="https://www.youtube.com/embed/${l}" \n        frameborder="0" allowfullscreen style="width: 100%; height: 400px; margin: 8px 0;">\n      </iframe>`)}else a=`<div class="video-container" style="margin: 8px 0;">\n      <p>视频链接：<a href="${e}" target="_blank">${e}</a></p>\n      <p style="color: #909399; font-size: 12px;">点击链接观看视频</p>\n    </div>`;a&&(document.execCommand("insertHTML",!1,a),A())},M=e=>/\.(mp4|webm|ogg|avi|mov|wmv|flv)$/i.test(e),$=e=>/bilibili\.com\/video\/(BV\w+|av\d+)/i.test(e),I=e=>{const a=e.match(/bilibili\.com\/video\/(BV\w+)/i);return a?a[1]:null},B=e=>/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/i.test(e),L=e=>{const a=e.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/i);return a?a[1]:null},A=()=>{const e=v.value?.innerHTML||"";f.value=e,_("update:modelValue",e),b.value>p.maxLength&&z.warning(`内容长度不能超过 ${p.maxLength} 字`)},S=()=>{g.value=!0,_("focus")},O=()=>{g.value=!1,_("blur")},F=e=>{e.preventDefault();const a=e.clipboardData.getData("text/plain").replace(/<[^>]*>/g,"");document.execCommand("insertText",!1,a),A()},H=()=>{v.value&&(f.value&&""!==f.value.trim()||(v.value.innerHTML=`<p style="color: #c0c4cc; pointer-events: none;">${p.placeholder}</p>`))},P=()=>{v.value&&v.value.innerHTML.includes(p.placeholder)&&(v.value.innerHTML="")};return o(()=>{v.value&&(v.value.innerHTML=f.value||"",f.value||H(),v.value.addEventListener("focus",P),v.value.addEventListener("blur",()=>{v.value.innerText.trim()||H()}))}),a({focus:()=>v.value?.focus(),blur:()=>v.value?.blur(),clear:()=>{v.value&&(v.value.innerHTML="",A())},insertText:e=>{h("insertText",e)},getContent:()=>f.value,getTextContent:()=>v.value?.innerText||""}),(a,l)=>{const t=x,s=q;return r(),u("div",la,[i("div",ta,[d(s,null,{default:c(()=>[d(t,{size:"small",onClick:l[0]||(l[0]=e=>h("bold")),class:C({active:y("bold")})},{default:c(()=>l[9]||(l[9]=[m(" 粗体 ",-1)])),_:1,__:[9]},8,["class"]),d(t,{size:"small",onClick:l[1]||(l[1]=e=>h("italic")),class:C({active:y("italic")})},{default:c(()=>l[10]||(l[10]=[m(" 斜体 ",-1)])),_:1,__:[10]},8,["class"]),d(t,{size:"small",onClick:l[2]||(l[2]=e=>h("underline")),class:C({active:y("underline")})},{default:c(()=>l[11]||(l[11]=[m(" 下划线 ",-1)])),_:1,__:[11]},8,["class"])]),_:1}),d(s,null,{default:c(()=>[d(t,{size:"small",onClick:l[3]||(l[3]=e=>h("justifyLeft")),class:C({active:y("justifyLeft")})},{default:c(()=>l[12]||(l[12]=[m(" 左对齐 ",-1)])),_:1,__:[12]},8,["class"]),d(t,{size:"small",onClick:l[4]||(l[4]=e=>h("justifyCenter")),class:C({active:y("justifyCenter")})},{default:c(()=>l[13]||(l[13]=[m(" 居中 ",-1)])),_:1,__:[13]},8,["class"]),d(t,{size:"small",onClick:l[5]||(l[5]=e=>h("justifyRight")),class:C({active:y("justifyRight")})},{default:c(()=>l[14]||(l[14]=[m(" 右对齐 ",-1)])),_:1,__:[14]},8,["class"])]),_:1}),d(s,null,{default:c(()=>[d(t,{size:"small",onClick:l[6]||(l[6]=e=>h("insertUnorderedList"))},{default:c(()=>l[15]||(l[15]=[m(" 列表 ",-1)])),_:1,__:[15]}),d(t,{size:"small",onClick:l[7]||(l[7]=e=>h("insertOrderedList"))},{default:c(()=>l[16]||(l[16]=[m(" 编号 ",-1)])),_:1,__:[16]})]),_:1}),d(s,null,{default:c(()=>[d(t,{size:"small",onClick:w},{default:c(()=>l[17]||(l[17]=[m(" 链接 ",-1)])),_:1,__:[17]}),d(t,{size:"small",onClick:V},{default:c(()=>l[18]||(l[18]=[m(" 图片 ",-1)])),_:1,__:[18]}),d(t,{size:"small",onClick:k},{default:c(()=>l[19]||(l[19]=[m(" 视频 ",-1)])),_:1,__:[19]})]),_:1}),d(s,null,{default:c(()=>[d(t,{size:"small",onClick:l[8]||(l[8]=e=>h("removeFormat"))},{default:c(()=>l[20]||(l[20]=[m(" 清除格式 ",-1)])),_:1,__:[20]})]),_:1})]),i("div",{ref_key:"editorRef",ref:v,class:"editor-content",style:U({height:e.height+"px"}),contenteditable:"true",onInput:A,onFocus:S,onBlur:O,onPaste:F,innerHTML:f.value},null,44,sa),i("div",na,[i("span",oa,D(b.value)+" 字",1)])])}}},ra=e(ua,[["__scopeId","data-v-49096e31"]]),ia={class:"avatar-upload"},da=["src"],ca={class:"qrcode-upload"},ma=["src"],pa={class:"dialog-footer"},_a={__name:"GroupDialog",props:{modelValue:{type:Boolean,default:!1},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{emit:l}){const o=e,y=l,w=a(),V=t(null),k=t(null),C=t(!1),q=t(!1),U=t(""),j=s({get:()=>o.modelValue,set:e=>y("update:modelValue",e)}),Y=s(()=>o.groupData&&o.groupData.id),E=s(()=>"/api/v1/upload/image"),K=s(()=>({Authorization:`Bearer ${w.token}`})),Z=p({name:"",category:"",description:"",price:0,max_members:500,avatar:"",qr_code:"",status:1,is_recommended:!1,tags:[],announcement:""}),W={name:[{required:!0,message:"请输入群组名称",trigger:"blur"},{min:2,max:50,message:"群组名称长度在 2 到 50 个字符",trigger:"blur"}],category:[{required:!0,message:"请选择群组分类",trigger:"change"}],price:[{required:!0,message:"请输入入群价格",trigger:"blur"},{type:"number",min:0,message:"价格不能小于0",trigger:"blur"}],max_members:[{required:!0,message:"请输入最大成员数",trigger:"blur"},{type:"number",min:1,max:500,message:"成员数在 1 到 500 之间",trigger:"blur"}],description:[{max:200,message:"描述长度不能超过 200 个字符",trigger:"blur"}]};n(()=>o.groupData,e=>{e&&Object.keys(e).length>0?Object.assign(Z,{name:e.name||"",category:e.category||"",description:e.description||"",price:e.price||0,max_members:e.max_members||500,avatar:e.avatar||"",qr_code:e.qr_code||"",status:void 0!==e.status?e.status:1,is_recommended:e.is_recommended||!1,tags:e.tags||[],announcement:e.announcement||""}):Object.assign(Z,{name:"",category:"",description:"",price:0,max_members:500,avatar:"",qr_code:"",status:1,is_recommended:!1,tags:[],announcement:""})},{immediate:!0,deep:!0});const J=e=>{e.success?(Z.avatar=e.data.url,z.success("头像上传成功")):z.error("头像上传失败")},X=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<2;return a?!!l||(z.error("图片大小不能超过 2MB!"),!1):(z.error("只能上传图片文件!"),!1)},ee=e=>{e.success?(Z.qr_code=e.data.url,z.success("二维码上传成功")):z.error("二维码上传失败")},ae=e=>X(e),le=()=>{q.value=!0,h(()=>{k.value?.focus()})},te=()=>{U.value&&!Z.tags.includes(U.value)&&Z.tags.push(U.value),q.value=!1,U.value=""},se=async()=>{if(V.value)try{await V.value.validate(),C.value=!0;const e={...Z};Y.value?(await Qe(o.groupData.id,e),z.success("群组更新成功")):(await Re(e),z.success("群组创建成功")),y("success"),ne()}catch(e){console.error("提交失败:",e),e.response?.data?.message?z.error(e.response.data.message):z.error(Y.value?"更新失败":"创建失败")}finally{C.value=!1}},ne=()=>{V.value?.resetFields(),j.value=!1};return(e,a)=>{const l=B,t=I,s=$,n=A,o=L,p=M,h=S,y=F,w=O,z=G,oe=P,ue=Q,re=R,ie=x,de=T,ce=N;return r(),_(ce,{modelValue:j.value,"onUpdate:modelValue":a[9]||(a[9]=e=>j.value=e),title:Y.value?"编辑群组":"创建群组",width:"800px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:ne},{footer:c(()=>[i("div",pa,[d(ie,{onClick:ne},{default:c(()=>a[15]||(a[15]=[m("取消",-1)])),_:1,__:[15]}),d(ie,{type:"primary",loading:C.value,onClick:se},{default:c(()=>[m(D(Y.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:c(()=>[d(de,{ref_key:"formRef",ref:V,model:Z,rules:W,"label-width":"100px",class:"group-form"},{default:c(()=>[d(p,{gutter:20},{default:c(()=>[d(s,{span:12},{default:c(()=>[d(t,{label:"群组名称",prop:"name"},{default:c(()=>[d(l,{modelValue:Z.name,"onUpdate:modelValue":a[0]||(a[0]=e=>Z.name=e),placeholder:"请输入群组名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),d(s,{span:12},{default:c(()=>[d(t,{label:"群组分类",prop:"category"},{default:c(()=>[d(o,{modelValue:Z.category,"onUpdate:modelValue":a[1]||(a[1]=e=>Z.category=e),placeholder:"请选择分类",style:{width:"100%"}},{default:c(()=>[d(n,{label:"创业交流",value:"startup"}),d(n,{label:"投资理财",value:"finance"}),d(n,{label:"科技互联网",value:"tech"}),d(n,{label:"教育培训",value:"education"}),d(n,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(p,{gutter:20},{default:c(()=>[d(s,{span:12},{default:c(()=>[d(t,{label:"入群价格",prop:"price"},{default:c(()=>[d(h,{modelValue:Z.price,"onUpdate:modelValue":a[2]||(a[2]=e=>Z.price=e),min:0,max:9999,precision:2,style:{width:"100%"},placeholder:"0.00"},null,8,["modelValue"])]),_:1})]),_:1}),d(s,{span:12},{default:c(()=>[d(t,{label:"最大成员数",prop:"max_members"},{default:c(()=>[d(h,{modelValue:Z.max_members,"onUpdate:modelValue":a[3]||(a[3]=e=>Z.max_members=e),min:1,max:500,style:{width:"100%"},placeholder:"500"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(t,{label:"群组描述",prop:"description"},{default:c(()=>[d(l,{modelValue:Z.description,"onUpdate:modelValue":a[4]||(a[4]=e=>Z.description=e),type:"textarea",rows:3,placeholder:"请输入群组描述",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),d(p,{gutter:20},{default:c(()=>[d(s,{span:12},{default:c(()=>[d(t,{label:"群组头像"},{default:c(()=>[i("div",ia,[d(w,{class:"avatar-uploader",action:E.value,headers:K.value,"show-file-list":!1,"on-success":J,"before-upload":X,accept:"image/*"},{default:c(()=>[Z.avatar?(r(),u("img",{key:0,src:Z.avatar,class:"avatar",alt:"群组头像"},null,8,da)):(r(),_(y,{key:1,class:"avatar-uploader-icon"},{default:c(()=>[d(v(H))]),_:1}))]),_:1},8,["action","headers"]),a[10]||(a[10]=i("div",{class:"upload-tip"},"建议尺寸：200x200px",-1))])]),_:1})]),_:1}),d(s,{span:12},{default:c(()=>[d(t,{label:"群二维码"},{default:c(()=>[i("div",ca,[d(w,{class:"qrcode-uploader",action:E.value,headers:K.value,"show-file-list":!1,"on-success":ee,"before-upload":ae,accept:"image/*"},{default:c(()=>[Z.qr_code?(r(),u("img",{key:0,src:Z.qr_code,class:"qrcode",alt:"群二维码"},null,8,ma)):(r(),_(y,{key:1,class:"qrcode-uploader-icon"},{default:c(()=>[d(v(H))]),_:1}))]),_:1},8,["action","headers"]),a[11]||(a[11]=i("div",{class:"upload-tip"},"建议尺寸：400x400px",-1))])]),_:1})]),_:1})]),_:1}),d(p,{gutter:20},{default:c(()=>[d(s,{span:12},{default:c(()=>[d(t,{label:"群组状态",prop:"status"},{default:c(()=>[d(oe,{modelValue:Z.status,"onUpdate:modelValue":a[5]||(a[5]=e=>Z.status=e)},{default:c(()=>[d(z,{label:1},{default:c(()=>a[12]||(a[12]=[m("活跃",-1)])),_:1,__:[12]}),d(z,{label:0},{default:c(()=>a[13]||(a[13]=[m("暂停",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(s,{span:12},{default:c(()=>[d(t,{label:"是否推荐",prop:"is_recommended"},{default:c(()=>[d(ue,{modelValue:Z.is_recommended,"onUpdate:modelValue":a[6]||(a[6]=e=>Z.is_recommended=e),"active-text":"推荐","inactive-text":"不推荐"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(t,{label:"群组标签"},{default:c(()=>[(r(!0),u(f,null,g(Z.tags,e=>(r(),_(re,{key:e,closable:"",onClose:a=>(e=>{const a=Z.tags.indexOf(e);a>-1&&Z.tags.splice(a,1)})(e),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:c(()=>[m(D(e),1)]),_:2},1032,["onClose"]))),128)),q.value?(r(),_(l,{key:0,ref_key:"inputRef",ref:k,modelValue:U.value,"onUpdate:modelValue":a[7]||(a[7]=e=>U.value=e),size:"small",style:{width:"100px"},onKeyup:b(te,["enter"]),onBlur:te},null,8,["modelValue"])):(r(),_(ie,{key:1,size:"small",onClick:le},{default:c(()=>a[14]||(a[14]=[m("+ 添加标签",-1)])),_:1,__:[14]}))]),_:1}),d(t,{label:"群组公告"},{default:c(()=>[d(ra,{modelValue:Z.announcement,"onUpdate:modelValue":a[8]||(a[8]=e=>Z.announcement=e),height:200,placeholder:"请输入群组公告"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}},va=e(_a,[["__scopeId","data-v-9e0a6e12"]]),fa={class:"group-member-manager"},ga={class:"member-stats"},ba={class:"stat-card"},ha={class:"stat-icon"},ya={class:"stat-content"},wa={class:"stat-value"},Va={class:"stat-card"},ka={class:"stat-icon"},xa={class:"stat-content"},Ca={class:"stat-value"},qa={class:"stat-card"},Ua={class:"stat-icon"},Da={class:"stat-content"},za={class:"stat-value"},ja={class:"stat-card"},Ta={class:"stat-icon"},Ma={class:"stat-content"},$a={class:"stat-value"},Ia={class:"member-toolbar"},Ba={class:"toolbar-right"},La={class:"member-filters"},Aa={class:"member-list"},Sa={class:"member-info"},Oa={class:"member-details"},Fa={class:"member-name"},Ha={class:"member-phone"},Pa={class:"pagination-container"},Ga={__name:"GroupMemberManager",props:{groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},setup(e){const a=e,l=t(!1),s=t([]),n=t(0),h=t([]),V=t(!1),k=t(null),C=t({total_members:0,active_members:0,new_members_today:0,left_members_today:0}),q=p({page:1,limit:20,keyword:"",status:""}),U=async()=>{l.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),s.value=[{id:1,username:"user001",nickname:"张三",phone:"138****1234",avatar:"",joined_at:"2024-01-15 10:30:00",status:"active",tags:["核心用户","活跃"],inputVisible:!1,inputValue:""},{id:2,username:"user002",nickname:"李四",phone:"139****5678",avatar:"",joined_at:"2024-01-16 14:20:00",status:"active",tags:["潜在客户"],inputVisible:!1,inputValue:""},{id:3,username:"user003",nickname:"王五",phone:"137****4321",avatar:"",joined_at:"2024-02-10 09:00:00",status:"left",tags:[],inputVisible:!1,inputValue:""}],n.value=2,C.value={total_members:a.groupData.member_count||0,active_members:Math.floor(.8*(a.groupData.member_count||0)),new_members_today:5,left_members_today:1}}catch(e){z.error("获取成员列表失败")}finally{l.value=!1}},S=()=>{z.info("添加成员功能开发中")},O=()=>{z.info("导出成员功能开发中")},P=e=>{h.value=e},G=()=>{Object.assign(q,{page:1,limit:20,keyword:"",status:""}),U()},Q=e=>{const a=h.value.map(e=>e.id);"tag"===e?j.prompt("请输入要批量添加的标签","批量打标签",{confirmButtonText:"确定",cancelButtonText:"取消"}).then(({value:e})=>{z.success(`已为 ${a.length} 位成员添加标签: "${e}"`),U()}).catch(()=>{}):"mute"===e?j.confirm(`确定要批量禁言这 ${a.length} 位成员吗？`,"确认禁言").then(()=>{z.success(`已成功禁言 ${a.length} 位成员`)}).catch(()=>{}):"kick"===e&&j.confirm(`确定要批量移除这 ${a.length} 位成员吗？`,"确认移除",{type:"warning"}).then(()=>{z.success(`已成功移除 ${a.length} 位成员`),U()}).catch(()=>{})},N=e=>{e.inputValue&&e.tags.push(e.inputValue),e.inputVisible=!1,e.inputValue=""},re=e=>({active:"正常",left:"已退出",kicked:"被踢出"}[e]||"未知");return o(()=>{U()}),(e,a)=>{const t=F,o=$,p=M,ie=x,de=J,ce=W,me=ae,pe=B,_e=I,ve=A,fe=L,ge=T,be=se,he=ne,ye=R,we=oe,Ve=ue,ke=te;return r(),u("div",fa,[i("div",ga,[d(p,{gutter:20},{default:c(()=>[d(o,{span:6},{default:c(()=>[i("div",ba,[i("div",ha,[d(t,null,{default:c(()=>[d(v(Y))]),_:1})]),i("div",ya,[i("div",wa,D(C.value.total_members),1),a[5]||(a[5]=i("div",{class:"stat-label"},"总成员数",-1))])])]),_:1}),d(o,{span:6},{default:c(()=>[i("div",Va,[i("div",ka,[d(t,null,{default:c(()=>[d(v(E))]),_:1})]),i("div",xa,[i("div",Ca,D(C.value.active_members),1),a[6]||(a[6]=i("div",{class:"stat-label"},"活跃成员",-1))])])]),_:1}),d(o,{span:6},{default:c(()=>[i("div",qa,[i("div",Ua,[d(t,null,{default:c(()=>[d(v(H))]),_:1})]),i("div",Da,[i("div",za,D(C.value.new_members_today),1),a[7]||(a[7]=i("div",{class:"stat-label"},"今日新增",-1))])])]),_:1}),d(o,{span:6},{default:c(()=>[i("div",ja,[i("div",Ta,[d(t,null,{default:c(()=>[d(v(K))]),_:1})]),i("div",Ma,[i("div",$a,D(C.value.left_members_today),1),a[8]||(a[8]=i("div",{class:"stat-label"},"今日退出",-1))])])]),_:1})]),_:1})]),i("div",Ia,[a[14]||(a[14]=i("div",{class:"toolbar-left"},[i("h4",null,"成员列表")],-1)),i("div",Ba,[h.value.length>0?(r(),_(me,{key:0,onCommand:Q},{dropdown:c(()=>[d(ce,null,{default:c(()=>[d(de,{command:"tag"},{default:c(()=>[d(t,null,{default:c(()=>[d(v(X))]),_:1}),a[9]||(a[9]=m("批量打标签 ",-1))]),_:1,__:[9]}),d(de,{command:"mute"},{default:c(()=>[d(t,null,{default:c(()=>[d(v(ee))]),_:1}),a[10]||(a[10]=m("批量禁言 ",-1))]),_:1,__:[10]}),d(de,{command:"kick",divided:""},{default:c(()=>[d(t,null,{default:c(()=>[d(v(K))]),_:1}),a[11]||(a[11]=m("批量移除 ",-1))]),_:1,__:[11]})]),_:1})]),default:c(()=>[d(ie,{type:"primary"},{default:c(()=>[m(" 批量操作 ("+D(h.value.length)+")",1),d(t,{class:"el-icon--right"},{default:c(()=>[d(v(Z))]),_:1})]),_:1})]),_:1})):y("",!0),d(ie,{type:"primary",onClick:S},{default:c(()=>[d(t,null,{default:c(()=>[d(v(H))]),_:1}),a[12]||(a[12]=m(" 添加成员 ",-1))]),_:1,__:[12]}),d(ie,{onClick:O},{default:c(()=>[d(t,null,{default:c(()=>[d(v(le))]),_:1}),a[13]||(a[13]=m(" 导出成员 ",-1))]),_:1,__:[13]})])]),i("div",La,[d(ge,{inline:!0,model:q},{default:c(()=>[d(_e,{label:"搜索"},{default:c(()=>[d(pe,{modelValue:q.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>q.keyword=e),placeholder:"用户名/手机号",clearable:"",onKeyup:b(U,["enter"])},null,8,["modelValue"])]),_:1}),d(_e,{label:"状态"},{default:c(()=>[d(fe,{modelValue:q.status,"onUpdate:modelValue":a[1]||(a[1]=e=>q.status=e),placeholder:"全部状态",clearable:""},{default:c(()=>[d(ve,{label:"正常",value:"active"}),d(ve,{label:"已退出",value:"left"}),d(ve,{label:"被踢出",value:"kicked"})]),_:1},8,["modelValue"])]),_:1}),d(_e,null,{default:c(()=>[d(ie,{type:"primary",onClick:U},{default:c(()=>a[15]||(a[15]=[m("搜索",-1)])),_:1,__:[15]}),d(ie,{onClick:G},{default:c(()=>a[16]||(a[16]=[m("重置",-1)])),_:1,__:[16]})]),_:1})]),_:1},8,["model"])]),i("div",Aa,[w((r(),_(we,{data:s.value,style:{width:"100%"},onSelectionChange:P},{default:c(()=>[d(be,{type:"selection",width:"55"}),d(be,{label:"成员信息","min-width":"200"},{default:c(({row:e})=>[i("div",Sa,[d(he,{src:e.avatar,size:40},{default:c(()=>[d(t,null,{default:c(()=>[d(v(Y))]),_:1})]),_:2},1032,["src"]),i("div",Oa,[i("div",Fa,D(e.nickname||e.username),1),i("div",Ha,D(e.phone||"未绑定"),1)])])]),_:1}),d(be,{label:"加入时间",width:"160"},{default:c(({row:e})=>{return[m(D((a=e.joined_at,a?new Date(a).toLocaleString("zh-CN"):"未知")),1)];var a}),_:1}),d(be,{label:"成员标签","min-width":"150"},{default:c(({row:e})=>[(r(!0),u(f,null,g(e.tags,a=>(r(),_(ye,{key:a,class:"member-tag",size:"small",closable:"",onClose:l=>((e,a)=>{e.tags.splice(e.tags.indexOf(a),1)})(e,a)},{default:c(()=>[m(D(a),1)]),_:2},1032,["onClose"]))),128)),e.inputVisible?(r(),_(pe,{key:0,modelValue:e.inputValue,"onUpdate:modelValue":a=>e.inputValue=a,class:"tag-input",size:"small",onKeyup:b(a=>N(e),["enter"]),onBlur:a=>N(e)},null,8,["modelValue","onUpdate:modelValue","onKeyup","onBlur"])):(r(),_(ie,{key:1,class:"button-new-tag",size:"small",onClick:a=>(e=>{e.inputVisible=!0})(e)},{default:c(()=>a[17]||(a[17]=[m(" + 新标签 ",-1)])),_:2,__:[17]},1032,["onClick"]))]),_:1}),d(be,{label:"状态",width:"100"},{default:c(({row:e})=>{return[d(ye,{type:(a=e.status,{active:"success",left:"info",kicked:"danger"}[a]||"info")},{default:c(()=>[m(D(re(e.status)),1)]),_:2},1032,["type"])];var a}),_:1}),d(be,{label:"操作",width:"200",fixed:"right"},{default:c(({row:e})=>[d(ie,{link:"",type:"primary",size:"small",onClick:a=>{return l=e,k.value=l.id,void(V.value=!0);var l}},{default:c(()=>a[18]||(a[18]=[m(" 用户画像 ",-1)])),_:2,__:[18]},1032,["onClick"]),d(ie,{link:"",type:"primary",size:"small",onClick:a=>{return l=e,void z.info(`查看成员: ${l.nickname}`);var l}},{default:c(()=>a[19]||(a[19]=[m(" 查看 ",-1)])),_:2,__:[19]},1032,["onClick"]),d(ie,{link:"",type:"danger",size:"small",onClick:a=>{return l=e,void j.confirm(`确定要移除成员 "${l.nickname}" 吗？`,"确认移除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{z.success("成员移除成功"),U()});var l}},{default:c(()=>a[20]||(a[20]=[m(" 移除 ",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ke,l.value]]),i("div",Pa,[d(Ve,{"current-page":q.page,"onUpdate:currentPage":a[2]||(a[2]=e=>q.page=e),"page-size":q.limit,"onUpdate:pageSize":a[3]||(a[3]=e=>q.limit=e),total:n.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:U,onCurrentChange:U},null,8,["current-page","page-size","total"])])]),d(Xe,{visible:V.value,"user-id":k.value,"onUpdate:visible":a[4]||(a[4]=e=>V.value=e)},null,8,["visible","user-id"])])}}},Qa=e(Ga,[["__scopeId","data-v-4e29aac0"]]),Ra={class:"analytics-content"},Na={class:"stat-item"},Ya={class:"stat-value"},Ea={class:"stat-item"},Ka={class:"stat-value"},Za={class:"stat-item"},Wa={class:"stat-value"},Ja={class:"stat-item"},Xa={class:"stat-value"},el={class:"chart-placeholder"},al={class:"placeholder-content"},ll={class:"chart-placeholder"},tl={class:"placeholder-content"},sl={__name:"GroupAnalyticsSimple",props:{visible:{type:Boolean,default:!1},groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},emits:["update:visible"],setup(e,{emit:a}){const l=e,s=a,o=t([{date:"2024-01-01",newMembers:12,activeMembers:156,messages:234,engagement:78.5},{date:"2024-01-02",newMembers:8,activeMembers:162,messages:198,engagement:82.1},{date:"2024-01-03",newMembers:15,activeMembers:171,messages:267,engagement:85.3},{date:"2024-01-04",newMembers:6,activeMembers:168,messages:189,engagement:79.8},{date:"2024-01-05",newMembers:11,activeMembers:175,messages:245,engagement:88.2}]),u=()=>{s("update:visible",!1)};return n(()=>l.visible,e=>{e&&console.log("群组分析打开，群组ID:",l.groupId)}),(a,l)=>{const t=re,s=$,n=M,p=F,f=se,g=oe,b=de;return r(),_(b,{"model-value":e.visible,title:"群组分析",size:"60%","onUpdate:modelValue":l[0]||(l[0]=e=>a.$emit("update:visible",e)),onClose:u},{default:c(()=>[i("div",Ra,[d(n,{gutter:20,class:"stats-row"},{default:c(()=>[d(s,{span:6},{default:c(()=>[d(t,{class:"stat-card"},{default:c(()=>[i("div",Na,[i("div",Ya,D(e.groupData.memberCount||0),1),l[1]||(l[1]=i("div",{class:"stat-label"},"总成员数",-1))])]),_:1})]),_:1}),d(s,{span:6},{default:c(()=>[d(t,{class:"stat-card"},{default:c(()=>[i("div",Ea,[i("div",Ka,D(e.groupData.monthlyActiveUsers||0),1),l[2]||(l[2]=i("div",{class:"stat-label"},"月活跃用户",-1))])]),_:1})]),_:1}),d(s,{span:6},{default:c(()=>[d(t,{class:"stat-card"},{default:c(()=>[i("div",Za,[i("div",Wa,D(e.groupData.dailyMessages||0),1),l[3]||(l[3]=i("div",{class:"stat-label"},"日均消息数",-1))])]),_:1})]),_:1}),d(s,{span:6},{default:c(()=>[d(t,{class:"stat-card"},{default:c(()=>[i("div",Ja,[i("div",Xa,D(((e.groupData.memberCount||0)/(e.groupData.maxMembers||500)*100).toFixed(1))+"%",1),l[4]||(l[4]=i("div",{class:"stat-label"},"群组满员率",-1))])]),_:1})]),_:1})]),_:1}),d(n,{gutter:20,style:{"margin-top":"20px"}},{default:c(()=>[d(s,{span:12},{default:c(()=>[d(t,{class:"chart-card",shadow:"never"},{header:c(()=>l[5]||(l[5]=[i("span",{class:"chart-title"},"成员增长趋势",-1)])),default:c(()=>[i("div",el,[i("div",al,[d(p,{size:"48"},{default:c(()=>[d(v(ie))]),_:1}),l[6]||(l[6]=i("p",null,"成员增长趋势图",-1)),l[7]||(l[7]=i("p",{class:"placeholder-note"},"图表组件加载中...",-1))])])]),_:1})]),_:1}),d(s,{span:12},{default:c(()=>[d(t,{class:"chart-card",shadow:"never"},{header:c(()=>l[8]||(l[8]=[i("span",{class:"chart-title"},"活跃度分析",-1)])),default:c(()=>[i("div",ll,[i("div",tl,[d(p,{size:"48"},{default:c(()=>[d(v(ie))]),_:1}),l[9]||(l[9]=i("p",null,"活跃度分析图",-1)),l[10]||(l[10]=i("p",{class:"placeholder-note"},"图表组件加载中...",-1))])])]),_:1})]),_:1})]),_:1}),d(t,{style:{"margin-top":"20px"},shadow:"never"},{header:c(()=>l[11]||(l[11]=[i("span",null,"详细数据",-1)])),default:c(()=>[d(g,{data:o.value,style:{width:"100%"}},{default:c(()=>[d(f,{prop:"date",label:"日期",width:"120"}),d(f,{prop:"newMembers",label:"新增成员",width:"100"}),d(f,{prop:"activeMembers",label:"活跃成员",width:"100"}),d(f,{prop:"messages",label:"消息数",width:"100"}),d(f,{prop:"engagement",label:"参与度",width:"100"},{default:c(({row:e})=>[m(D(e.engagement)+"% ",1)]),_:1})]),_:1},8,["data"])]),_:1})])]),_:1},8,["model-value"])}}},nl=e(sl,[["__scopeId","data-v-f8722904"]]),ol={class:"qrcode-container"},ul={class:"qrcode-display"},rl={key:0,class:"qrcode-image"},il=["src"],dl={class:"qrcode-info"},cl={key:1,class:"no-qrcode"},ml={class:"qrcode-actions"},pl={class:"dialog-footer"},_l={__name:"QRCodeDialog",props:{modelValue:{type:Boolean,default:!1},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","qrcode-updated"],setup(e,{emit:a}){const t=e,n=a,o=s({get:()=>t.modelValue,set:e=>n("update:modelValue",e)}),p=s(()=>`/api/v1/wechat-groups/${t.groupData.id}/qr-code`),f=s(()=>({Authorization:`Bearer ${l()}`})),g=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<5;return a?!!l||(z.error("图片大小不能超过 5MB!"),!1):(z.error("只能上传图片文件!"),!1)},b=e=>{e.success||0===e.code?(z.success("二维码上传成功"),n("qrcode-updated")):z.error("二维码上传失败")},h=()=>{if(t.groupData.qr_code){const e=document.createElement("a");e.href=t.groupData.qr_code,e.download=`${t.groupData.name}-二维码.png`,document.body.appendChild(e),e.click(),document.body.removeChild(e)}},w=()=>{j.confirm("确定要删除群组二维码吗？删除后用户将无法通过二维码加入群组。","确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then(()=>{z.success("二维码删除成功"),n("qrcode-updated")})};return(a,l)=>{const t=ce,s=F,n=x,V=O,k=N;return r(),_(k,{modelValue:o.value,"onUpdate:modelValue":l[1]||(l[1]=e=>o.value=e),title:"群组二维码",width:"500px","destroy-on-close":!0},{footer:c(()=>[i("div",pl,[d(n,{onClick:l[0]||(l[0]=e=>o.value=!1)},{default:c(()=>l[5]||(l[5]=[m("关闭",-1)])),_:1,__:[5]})])]),default:c(()=>[i("div",ol,[i("div",ul,[e.groupData.qr_code?(r(),u("div",rl,[i("img",{src:e.groupData.qr_code,alt:"群组二维码"},null,8,il),i("div",dl,[i("h4",null,D(e.groupData.name),1),l[2]||(l[2]=i("p",null,"扫码加入群组",-1))])])):(r(),u("div",cl,[d(t,{description:"暂未上传二维码","image-size":100})]))]),i("div",ml,[d(V,{action:p.value,headers:f.value,"on-success":b,"before-upload":g,"show-file-list":!1,accept:"image/*"},{default:c(()=>[d(n,{type:"primary"},{default:c(()=>[d(s,null,{default:c(()=>[d(v(me))]),_:1}),m(" "+D(e.groupData.qr_code?"更换二维码":"上传二维码"),1)]),_:1})]),_:1},8,["action","headers"]),e.groupData.qr_code?(r(),_(n,{key:0,onClick:h},{default:c(()=>[d(s,null,{default:c(()=>[d(v(le))]),_:1}),l[3]||(l[3]=m(" 下载二维码 ",-1))]),_:1,__:[3]})):y("",!0),e.groupData.qr_code?(r(),_(n,{key:1,type:"danger",onClick:w},{default:c(()=>[d(s,null,{default:c(()=>[d(v(pe))]),_:1}),l[4]||(l[4]=m(" 删除二维码 ",-1))]),_:1,__:[4]})):y("",!0)])])]),_:1},8,["modelValue"])}}},vl=e(_l,[["__scopeId","data-v-287bc7ce"]]),fl={class:"content-template-library"},gl={class:"card-header"},bl={class:"card-title"},hl={class:"header-actions"},yl={class:"template-filters"},wl={class:"template-list"},Vl=["onClick"],kl={class:"template-header"},xl={class:"template-content"},Cl={class:"template-title"},ql={class:"template-preview"},Ul={class:"template-footer"},Dl={class:"usage-count"},zl={__name:"ContentTemplateLibrary",emits:["template-selected"],setup(e,{emit:a}){const l=a,n=t(""),o=t(""),p=t(""),b=t([{id:1,title:"互联网技术交流群标题模板",category:"title",industry:"tech",content:"{city}程序员技术交流群 - 汇聚IT精英，分享前沿技术",usageCount:156}]),h=s(()=>b.value.filter(e=>{const a=!n.value||e.category===n.value,l=!o.value||e.industry===o.value,t=!p.value||e.title.includes(p.value)||e.content.includes(p.value);return a&&l&&t})),y=()=>{z.info("创建模板功能开发中")},w=e=>({title:"标题",description:"描述",faq:"FAQ",reviews:"评论"}[e]||e);return(e,a)=>{const t=F,s=x,b=A,V=L,k=$,C=B,q=M,U=R,j=re;return r(),u("div",fl,[d(j,{class:"library-card",shadow:"never"},{header:c(()=>[i("div",gl,[i("span",bl,[d(t,null,{default:c(()=>[d(v(ve))]),_:1}),a[3]||(a[3]=m(" 内容模板库 ",-1))]),i("div",hl,[d(s,{type:"primary",size:"small",onClick:y},{default:c(()=>[d(t,null,{default:c(()=>[d(v(H))]),_:1}),a[4]||(a[4]=m(" 新建模板 ",-1))]),_:1,__:[4]})])])]),default:c(()=>[i("div",yl,[d(q,{gutter:20},{default:c(()=>[d(k,{span:8},{default:c(()=>[d(V,{modelValue:n.value,"onUpdate:modelValue":a[0]||(a[0]=e=>n.value=e),placeholder:"选择分类",clearable:""},{default:c(()=>[d(b,{label:"全部分类",value:""}),d(b,{label:"群组标题",value:"title"}),d(b,{label:"群组描述",value:"description"}),d(b,{label:"FAQ问答",value:"faq"}),d(b,{label:"用户评论",value:"reviews"})]),_:1},8,["modelValue"])]),_:1}),d(k,{span:8},{default:c(()=>[d(V,{modelValue:o.value,"onUpdate:modelValue":a[1]||(a[1]=e=>o.value=e),placeholder:"选择行业",clearable:""},{default:c(()=>[d(b,{label:"全部行业",value:""}),d(b,{label:"互联网/科技",value:"tech"}),d(b,{label:"金融/投资",value:"finance"}),d(b,{label:"教育/培训",value:"education"})]),_:1},8,["modelValue"])]),_:1}),d(k,{span:8},{default:c(()=>[d(C,{modelValue:p.value,"onUpdate:modelValue":a[2]||(a[2]=e=>p.value=e),placeholder:"搜索模板...",clearable:""},{prefix:c(()=>[d(t,null,{default:c(()=>[d(v(_e))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),i("div",wl,[d(q,{gutter:20},{default:c(()=>[(r(!0),u(f,null,g(h.value,e=>(r(),_(k,{span:8,key:e.id},{default:c(()=>{return[i("div",{class:"template-card",onClick:a=>(e=>{l("template-selected",e),z.success(`已选择模板：${e.title}`)})(e)},[i("div",kl,[d(U,{type:(a=e.category,{title:"primary",description:"success",faq:"warning",reviews:"info"}[a]||""),size:"small"},{default:c(()=>[m(D(w(e.category)),1)]),_:2},1032,["type"])]),i("div",xl,[i("h4",Cl,D(e.title),1),i("p",ql,D(e.content.substring(0,100))+"...",1)]),i("div",Ul,[i("span",Dl,"使用 "+D(e.usageCount)+" 次",1)])],8,Vl)];var a}),_:2},1024))),128))]),_:1})])]),_:1})])}}},jl=e(zl,[["__scopeId","data-v-e02fe381"]]),Tl={class:"ai-content-generator"},Ml={class:"card-header"},$l={class:"card-title"},Il={class:"content-type-selector"},Bl={class:"generation-config"},Ll={class:"generation-actions"},Al={key:0,class:"generation-results"},Sl={class:"results-list"},Ol={class:"result-header"},Fl={class:"result-index"},Hl={class:"result-actions"},Pl={class:"result-content"},Gl={class:"batch-actions"},Ql={key:1,class:"generation-history"},Rl={class:"history-item"},Nl={class:"history-type"},Yl={class:"history-content"},El=e({__name:"AIContentGenerator",emits:["content-generated"],setup(e,{emit:a}){const l=a,s=t("title"),n=t(!1),o=t([]),b=t([]),h=p({industry:"",audience:"",style:"professional",count:3,keywords:"",customPrompt:""}),w={title:{tech:["{city}程序员技术交流群","{city}互联网从业者聚集地","{city}IT精英学习成长群","{city}技术大牛经验分享群"],finance:["{city}投资理财交流群","{city}财富增值学习群","{city}金融精英圈","{city}理财规划师群"],education:["{city}学习成长交流群","{city}知识分享互助群","{city}教育资源共享群","{city}终身学习者联盟"]},description:{professional:"汇聚行业精英，分享前沿资讯，共同成长进步。严格筛选成员，确保高质量交流环境。",casual:"轻松愉快的交流氛围，大家一起聊天学习，分享生活和工作中的点点滴滴。",warm:"温馨的大家庭，互帮互助，共同进步。每个人都能在这里找到归属感。"},faq:[{question:"这个群主要讨论什么内容？",answer:"主要围绕{industry}相关话题，包括行业动态、经验分享、资源交换等。"},{question:"群里有什么规则吗？",answer:"禁止发广告、恶意刷屏，鼓励有价值的分享和讨论，营造良好的交流环境。"},{question:"如何更好地融入群聊？",answer:"主动参与讨论，分享有价值的内容，尊重其他成员，积极互动交流。"}],reviews:[{username:"行业专家",content:"群里的分享质量很高，学到了很多实用的知识和经验。",rating:5},{username:"资深从业者",content:"氛围很好，大家都很乐于分享，是个不错的学习平台。",rating:5},{username:"新手小白",content:"刚入行的时候加入的，得到了很多前辈的指导和帮助。",rating:4}]},V=e=>{console.log("切换内容类型:",e)},k=async()=>{if(h.industry&&h.audience){n.value=!0;try{await new Promise(e=>setTimeout(e,2e3));const e=[];for(let a=0;a<h.count;a++){const l=await C();e.push({id:Date.now()+a,content:l,rating:0,type:s.value})}o.value=e,b.value.unshift({timestamp:new Date,type:s.value,content:e[0].content,config:{...h}}),z.success(`成功生成 ${e.length} 个内容方案`)}catch(e){z.error("生成失败，请重试")}finally{n.value=!1}}else z.warning("请先选择行业类型和目标人群")},C=async()=>{const{industry:e,audience:a,style:l,keywords:t,customPrompt:n}=h;switch(s.value){case"title":return q(e,a,t);case"description":return U(l,e,n);case"faq":return j(e);case"reviews":return T();case"intro":return O(e,a);default:return"生成的内容"}},q=(e,a,l)=>{const t=w.title[e]||w.title.tech;let s=t[Math.floor(Math.random()*t.length)].replace("{city}","xxx");if(l){const e=l.split(",").map(e=>e.trim()),a=e[Math.floor(Math.random()*e.length)];s=s.replace("程序员",a).replace("投资理财",a)}return s},U=(e,a,l)=>{let t=w.description[e]||w.description.professional;return l&&(t+="\n\n"+l),t.replace("{industry}",N(a))},j=(e,a)=>w.faq.map(a=>({...a,answer:a.answer.replace("{industry}",N(e))})).map(e=>`${e.question}----${e.answer}`).join("\n"),T=(e,a)=>w.reviews.map(e=>`${e.username}----${e.content}----${e.rating}`).join("\n"),O=(e,a,l)=>`欢迎加入我们的${N(e)}交流群！这里汇聚了众多${Y(a)}，大家可以在这里分享经验、交流心得、互相学习。我们致力于打造一个高质量的交流平台，让每个成员都能在这里收获成长。`,H=()=>{o.value=[]},G=()=>{k()},Q=()=>{const e=o.value.map((e,a)=>`方案${a+1}:\n${e.content}\n\n`).join(""),a=new Blob([e],{type:"text/plain"}),l=URL.createObjectURL(a),t=document.createElement("a");t.href=l,t.download=`AI生成内容_${(new Date).toISOString().slice(0,10)}.txt`,t.click(),URL.revokeObjectURL(l)},N=e=>({tech:"互联网科技",finance:"金融投资",education:"教育培训",ecommerce:"电商零售",health:"健康医疗",entertainment:"娱乐游戏",realestate:"房产装修",food:"美食餐饮",travel:"旅游出行",other:"其他行业"}[e]||e),Y=e=>({students:"学生朋友",newcomers:"职场新人",experts:"资深专家",entrepreneurs:"创业者",mothers:"宝妈群体",seniors:"中老年朋友",youngpro:"年轻白领",freelancers:"自由职业者"}[e]||e);return(e,a)=>{const t=F,p=R,w=fe,C=P,q=A,U=L,j=I,T=$,O=M,N=S,Y=B,E=x,K=be,Z=he,W=we,J=ye,X=re;return r(),u("div",Tl,[d(X,{class:"generator-card",shadow:"never"},{header:c(()=>[i("div",Ml,[i("span",$l,[d(t,null,{default:c(()=>[d(v(ge))]),_:1}),a[7]||(a[7]=m(" AI智能内容生成 ",-1))]),d(p,{type:"success",size:"small"},{default:c(()=>a[8]||(a[8]=[m("Beta",-1)])),_:1,__:[8]})])]),default:c(()=>[i("div",Il,[d(C,{modelValue:s.value,"onUpdate:modelValue":a[0]||(a[0]=e=>s.value=e),onChange:V},{default:c(()=>[d(w,{label:"title"},{default:c(()=>a[9]||(a[9]=[m("群组标题",-1)])),_:1,__:[9]}),d(w,{label:"description"},{default:c(()=>a[10]||(a[10]=[m("群组描述",-1)])),_:1,__:[10]}),d(w,{label:"faq"},{default:c(()=>a[11]||(a[11]=[m("FAQ问答",-1)])),_:1,__:[11]}),d(w,{label:"reviews"},{default:c(()=>a[12]||(a[12]=[m("用户评论",-1)])),_:1,__:[12]}),d(w,{label:"intro"},{default:c(()=>a[13]||(a[13]=[m("群组介绍",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"])]),i("div",Bl,[d(O,{gutter:20},{default:c(()=>[d(T,{span:12},{default:c(()=>[d(j,{label:"行业类型"},{default:c(()=>[d(U,{modelValue:h.industry,"onUpdate:modelValue":a[1]||(a[1]=e=>h.industry=e),placeholder:"选择行业"},{default:c(()=>[d(q,{label:"互联网/科技",value:"tech"}),d(q,{label:"金融/投资",value:"finance"}),d(q,{label:"教育/培训",value:"education"}),d(q,{label:"电商/零售",value:"ecommerce"}),d(q,{label:"健康/医疗",value:"health"}),d(q,{label:"娱乐/游戏",value:"entertainment"}),d(q,{label:"房产/装修",value:"realestate"}),d(q,{label:"美食/餐饮",value:"food"}),d(q,{label:"旅游/出行",value:"travel"}),d(q,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(T,{span:12},{default:c(()=>[d(j,{label:"目标人群"},{default:c(()=>[d(U,{modelValue:h.audience,"onUpdate:modelValue":a[2]||(a[2]=e=>h.audience=e),placeholder:"选择目标人群"},{default:c(()=>[d(q,{label:"学生群体",value:"students"}),d(q,{label:"职场新人",value:"newcomers"}),d(q,{label:"资深专家",value:"experts"}),d(q,{label:"创业者",value:"entrepreneurs"}),d(q,{label:"宝妈群体",value:"mothers"}),d(q,{label:"中老年人",value:"seniors"}),d(q,{label:"年轻白领",value:"youngpro"}),d(q,{label:"自由职业者",value:"freelancers"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(O,{gutter:20},{default:c(()=>[d(T,{span:12},{default:c(()=>[d(j,{label:"内容风格"},{default:c(()=>[d(U,{modelValue:h.style,"onUpdate:modelValue":a[3]||(a[3]=e=>h.style=e),placeholder:"选择风格"},{default:c(()=>[d(q,{label:"专业严谨",value:"professional"}),d(q,{label:"轻松幽默",value:"casual"}),d(q,{label:"温馨亲切",value:"warm"}),d(q,{label:"激励鼓舞",value:"motivational"}),d(q,{label:"简洁明了",value:"concise"}),d(q,{label:"详细全面",value:"detailed"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(T,{span:12},{default:c(()=>[d(j,{label:"生成数量"},{default:c(()=>[d(N,{modelValue:h.count,"onUpdate:modelValue":a[4]||(a[4]=e=>h.count=e),min:1,max:10,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(j,{label:"关键词"},{default:c(()=>[d(Y,{modelValue:h.keywords,"onUpdate:modelValue":a[5]||(a[5]=e=>h.keywords=e),placeholder:"输入相关关键词，用逗号分隔",type:"textarea",rows:2},null,8,["modelValue"])]),_:1}),d(j,{label:"自定义要求"},{default:c(()=>[d(Y,{modelValue:h.customPrompt,"onUpdate:modelValue":a[6]||(a[6]=e=>h.customPrompt=e),placeholder:"描述您的具体要求，如：需要包含价格信息、突出优势等",type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),i("div",Ll,[d(E,{type:"primary",onClick:k,loading:n.value,size:"large"},{default:c(()=>[d(t,null,{default:c(()=>[d(v(ge))]),_:1}),m(" "+D(n.value?"生成中...":"智能生成"),1)]),_:1},8,["loading"]),d(E,{onClick:H},{default:c(()=>a[14]||(a[14]=[m("清空结果",-1)])),_:1,__:[14]})]),o.value.length>0?(r(),u("div",Al,[d(K,{"content-position":"left"},{default:c(()=>a[15]||(a[15]=[m("生成结果",-1)])),_:1,__:[15]}),i("div",Sl,[(r(!0),u(f,null,g(o.value,(e,t)=>(r(),u("div",{key:t,class:"result-item"},[i("div",Ol,[i("span",Fl,"方案 "+D(t+1),1),i("div",Hl,[d(E,{type:"primary",size:"small",onClick:a=>(e=>{l("content-generated",{type:s.value,content:e.content}),z.success("内容已应用")})(e)},{default:c(()=>a[16]||(a[16]=[m(" 使用此内容 ",-1)])),_:2,__:[16]},1032,["onClick"]),d(E,{size:"small",onClick:a=>(async e=>{if("undefined"!=typeof navigator&&navigator.clipboard)try{await navigator.clipboard.writeText(e.content),z.success("内容已复制到剪贴板")}catch(a){z.error("复制失败")}else z.warning("剪贴板功能在当前环境中不可用")})(e)},{default:c(()=>a[17]||(a[17]=[m(" 复制 ",-1)])),_:2,__:[17]},1032,["onClick"]),d(Z,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,size:"small",onChange:a=>((e,a)=>{e.rating=a})(e,a)},null,8,["modelValue","onUpdate:modelValue","onChange"])])]),i("div",Pl,[i("pre",null,D(e.content),1)])]))),128))]),i("div",Gl,[d(E,{onClick:G},{default:c(()=>a[18]||(a[18]=[m("重新生成全部",-1)])),_:1,__:[18]}),d(E,{onClick:Q},{default:c(()=>a[19]||(a[19]=[m("导出结果",-1)])),_:1,__:[19]})])])):y("",!0),b.value.length>0?(r(),u("div",Ql,[d(K,{"content-position":"left"},{default:c(()=>a[20]||(a[20]=[m("历史记录",-1)])),_:1,__:[20]}),d(J,null,{default:c(()=>[(r(!0),u(f,null,g(b.value.slice(0,5),(e,l)=>{return r(),_(W,{key:l,timestamp:(t=e.timestamp,new Date(t).toLocaleString("zh-CN"))},{default:c(()=>{return[i("div",Rl,[i("div",Nl,D((l=e.type,{title:"群组标题",description:"群组描述",faq:"FAQ问答",reviews:"用户评论",intro:"群组介绍"}[l]||l)),1),i("div",Yl,D(e.content.substring(0,50))+"...",1),d(E,{type:"text",size:"small",onClick:a=>(e=>{Object.assign(h,e.config),s.value=e.type})(e)},{default:c(()=>a[21]||(a[21]=[m(" 重新使用 ",-1)])),_:2,__:[21]},1032,["onClick"])])];var l}),_:2},1032,["timestamp"]);var t}),128))]),_:1})])):y("",!0)]),_:1})])}}},[["__scopeId","data-v-18d311bd"]]),Kl={class:"preview-container"},Zl={class:"preview-header"},Wl={class:"preview-actions"},Jl={class:"group-preview"},Xl={class:"group-header"},et={class:"group-avatar"},at={class:"group-info"},lt={class:"group-name"},tt={class:"group-meta"},st={class:"group-price"},nt={class:"group-members"},ot={key:0,class:"content-section"},ut={class:"section-title"},rt=["innerHTML"],it={key:1,class:"content-section"},dt={class:"section-title"},ct={class:"faq-content"},mt={class:"faq-answer"},pt={key:1,class:"faq-list"},_t={class:"faq-question"},vt={class:"faq-index"},ft={class:"faq-answer"},gt={key:2,class:"faq-cards"},bt={class:"faq-question"},ht={class:"faq-answer"},yt={key:2,class:"content-section"},wt={class:"section-title"},Vt={class:"reviews-content"},kt={key:0,class:"reviews-cards"},xt={class:"review-header"},Ct={class:"review-user"},qt={class:"username"},Ut={class:"review-time"},Dt={class:"review-content"},zt={key:1,class:"reviews-list"},jt={class:"review-left"},Tt={class:"review-right"},Mt={class:"review-meta"},$t={class:"username"},It={class:"review-time"},Bt={class:"review-content"},Lt={key:2,class:"reviews-carousel"},At={class:"carousel-review"},St={class:"review-content"},Ot={class:"review-author"},Ft={class:"username"},Ht={key:3,class:"content-section"},Pt={key:0,class:"extra-block"},Gt={class:"section-title"},Qt=["innerHTML"],Rt={key:1,class:"extra-block"},Nt={class:"section-title"},Yt=["innerHTML"],Et={key:4,class:"content-section"},Kt={class:"materials-content"},Zt={key:0,class:"material-item"},Wt=["src"],Jt={key:1,class:"material-item"},Xt={class:"ad-images"},es=["src"],as={class:"join-section"},ls={class:"dialog-footer"},ts=e({__name:"ContentPreviewDialog",props:{modelValue:{type:Boolean,default:!1},contentData:{type:Object,default:()=>({})},groupData:{type:Object,default:()=>({})}},emits:["update:modelValue","apply-content"],setup(e,{emit:a}){const l=e,n=a,o=s({get:()=>l.modelValue,set:e=>n("update:modelValue",e)}),p=t("desktop"),b=s(()=>{if(!l.contentData.faq_content)return[];return l.contentData.faq_content.split("\n").filter(e=>e.includes("----")).map(e=>{const[a,l]=e.split("----");return{question:a?.trim()||"",answer:l?.trim()||""}})}),h=s(()=>{if(!l.contentData.user_reviews)return[];return l.contentData.user_reviews.split("\n").filter(e=>e.includes("----")).map(e=>{const a=e.split("----");return{username:a[0]?.trim()||"",content:a[1]?.trim()||"",rating:parseInt(a[2])||5,avatar:a[3]?.trim()||"",created_at:a[4]?.trim()||(new Date).toISOString().slice(0,19).replace("T"," ")}})}),w=s(()=>{const e=l.contentData.reviews_count||6;return h.value.slice(0,e)}),V=()=>{z.success("预览已刷新")},k=()=>{z.info("导出功能开发中")},q=()=>{n("apply-content",l.contentData),o.value=!1},U=e=>{if(!e)return"";const a=new Date(e),l=new Date-a,t=Math.floor(l/864e5);return 0===t?"今天":1===t?"昨天":t<7?`${t}天前`:a.toLocaleDateString("zh-CN")};return(a,l)=>{const t=fe,s=P,n=F,z=x,j=ne,T=Ce,M=xe,$=re,I=he,B=Ue,L=qe,A=N;return r(),_(A,{modelValue:o.value,"onUpdate:modelValue":l[2]||(l[2]=e=>o.value=e),title:"内容预览",width:"800px","destroy-on-close":!0,class:"content-preview-dialog"},{footer:c(()=>[i("div",ls,[d(z,{onClick:l[1]||(l[1]=e=>o.value=!1)},{default:c(()=>l[12]||(l[12]=[m("关闭",-1)])),_:1,__:[12]}),d(z,{type:"primary",onClick:q},{default:c(()=>l[13]||(l[13]=[m("应用内容",-1)])),_:1,__:[13]})])]),default:c(()=>[i("div",Kl,[i("div",Zl,[d(s,{modelValue:p.value,"onUpdate:modelValue":l[0]||(l[0]=e=>p.value=e),size:"small"},{default:c(()=>[d(t,{label:"desktop"},{default:c(()=>l[3]||(l[3]=[m("桌面端",-1)])),_:1,__:[3]}),d(t,{label:"mobile"},{default:c(()=>l[4]||(l[4]=[m("移动端",-1)])),_:1,__:[4]})]),_:1},8,["modelValue"]),i("div",Wl,[d(z,{size:"small",onClick:V},{default:c(()=>[d(n,null,{default:c(()=>[d(v(Ve))]),_:1}),l[5]||(l[5]=m(" 刷新 ",-1))]),_:1,__:[5]}),d(z,{size:"small",onClick:k},{default:c(()=>[d(n,null,{default:c(()=>[d(v(le))]),_:1}),l[6]||(l[6]=m(" 导出 ",-1))]),_:1,__:[6]})])]),i("div",{class:C(["preview-content",{"mobile-view":"mobile"===p.value}])},[i("div",Jl,[i("div",Xl,[i("div",et,[d(j,{src:e.groupData.avatar,size:60},{default:c(()=>[d(n,null,{default:c(()=>[d(v(ke))]),_:1})]),_:1},8,["src"])]),i("div",at,[i("h3",lt,D(e.groupData.name||"群组名称"),1),i("div",tt,[i("span",st,D(e.groupData.price>0?`¥${e.groupData.price}`:"免费"),1),i("span",nt,D(e.groupData.member_count||0)+"/"+D(e.groupData.max_members||500)+"人 ",1)])])]),e.contentData.show_intro&&e.contentData.group_intro_content?(r(),u("div",ot,[i("h4",ut,D(e.contentData.group_intro_title||"群简介"),1),i("div",{class:"section-content",innerHTML:e.contentData.group_intro_content},null,8,rt)])):y("",!0),e.contentData.show_faq&&b.value.length>0?(r(),u("div",it,[i("h4",dt,D(e.contentData.faq_title||"常见问题"),1),i("div",ct,["collapse"===e.contentData.faq_style?(r(),_(M,{key:0,accordion:""},{default:c(()=>[(r(!0),u(f,null,g(b.value,(e,a)=>(r(),_(T,{key:a,title:e.question,name:a},{default:c(()=>[i("div",mt,D(e.answer),1)]),_:2},1032,["title","name"]))),128))]),_:1})):"list"===e.contentData.faq_style?(r(),u("div",pt,[(r(!0),u(f,null,g(b.value,(e,a)=>(r(),u("div",{key:a,class:"faq-item"},[i("div",_t,[i("span",vt,"Q"+D(a+1),1),m(" "+D(e.question),1)]),i("div",ft,[l[7]||(l[7]=i("span",{class:"answer-label"},"A:",-1)),m(" "+D(e.answer),1)])]))),128))])):(r(),u("div",gt,[(r(!0),u(f,null,g(b.value,(e,a)=>(r(),_($,{key:a,class:"faq-card",shadow:"hover"},{header:c(()=>[i("div",bt,D(e.question),1)]),default:c(()=>[i("div",ht,D(e.answer),1)]),_:2},1024))),128))]))])])):y("",!0),e.contentData.show_reviews&&h.value.length>0?(r(),u("div",yt,[i("h4",wt,D(e.contentData.reviews_title||"用户评价"),1),i("div",Vt,["card"===e.contentData.reviews_style?(r(),u("div",kt,[(r(!0),u(f,null,g(w.value,(e,a)=>(r(),u("div",{key:a,class:"review-card"},[i("div",xt,[d(j,{src:e.avatar,size:32},{default:c(()=>[d(n,null,{default:c(()=>[d(v(Y))]),_:1})]),_:2},1032,["src"]),i("div",Ct,[i("div",qt,D(e.username),1),d(I,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),i("div",Ut,D(U(e.created_at)),1)]),i("div",Dt,D(e.content),1)]))),128))])):"list"===e.contentData.reviews_style?(r(),u("div",zt,[(r(!0),u(f,null,g(w.value,(e,a)=>(r(),u("div",{key:a,class:"review-item"},[i("div",jt,[d(j,{src:e.avatar,size:40},{default:c(()=>[d(n,null,{default:c(()=>[d(v(Y))]),_:1})]),_:2},1032,["src"])]),i("div",Tt,[i("div",Mt,[i("span",$t,D(e.username),1),d(I,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),i("span",It,D(U(e.created_at)),1)]),i("div",Bt,D(e.content),1)])]))),128))])):(r(),u("div",Lt,[d(L,{height:"120px",autoplay:!0,interval:3e3},{default:c(()=>[(r(!0),u(f,null,g(w.value,(e,a)=>(r(),_(B,{key:a},{default:c(()=>[i("div",At,[i("div",St,'"'+D(e.content)+'"',1),i("div",Ot,[i("span",Ft,"—— "+D(e.username),1),d(I,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,disabled:"",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])])]),_:2},1024))),128))]),_:1})]))])])):y("",!0),e.contentData.show_extra?(r(),u("div",Ht,[e.contentData.extra_title1&&e.contentData.extra_content1?(r(),u("div",Pt,[i("h4",Gt,D(e.contentData.extra_title1),1),i("div",{class:"section-content",innerHTML:e.contentData.extra_content1},null,8,Qt)])):y("",!0),e.contentData.extra_title2&&e.contentData.extra_content2?(r(),u("div",Rt,[i("h4",Nt,D(e.contentData.extra_title2),1),i("div",{class:"section-content",innerHTML:e.contentData.extra_content2},null,8,Yt)])):y("",!0)])):y("",!0),e.contentData.customer_service_qr||e.contentData.ad_image?.length>0?(r(),u("div",Et,[l[10]||(l[10]=i("h4",{class:"section-title"},"相关素材",-1)),i("div",Kt,[e.contentData.customer_service_qr?(r(),u("div",Zt,[l[8]||(l[8]=i("div",{class:"material-label"},"客服二维码",-1)),i("img",{src:e.contentData.customer_service_qr,alt:"客服二维码",class:"qr-code"},null,8,Wt)])):y("",!0),e.contentData.ad_image?.length>0?(r(),u("div",Jt,[l[9]||(l[9]=i("div",{class:"material-label"},"广告图片",-1)),i("div",Xt,[(r(!0),u(f,null,g(e.contentData.ad_image,(e,a)=>(r(),u("img",{key:a,src:e,alt:"广告图片",class:"ad-image"},null,8,es))),128))])])):y("",!0)])])):y("",!0),i("div",as,[d(z,{type:"primary",size:"large",class:"join-button"},{default:c(()=>[d(n,null,{default:c(()=>[d(v(H))]),_:1}),l[11]||(l[11]=m(" 立即加入群组 ",-1))]),_:1,__:[11]})])])],2)])]),_:1},8,["modelValue"])}}},[["__scopeId","data-v-4cce9b66"]]),ss={class:"group-content-manager"},ns={class:"content-toolbar"},os={class:"toolbar-left"},us={class:"content-title"},rs={class:"toolbar-right"},is={class:"smart-tools-bar"},ds={class:"ai-generator-panel"},cs={class:"template-library-panel"},ms={class:"content-editor"},ps={class:"section-header"},_s={class:"section-title"},vs={class:"section-header"},fs={class:"section-title"},gs={class:"section-header"},bs={class:"section-title"},hs={class:"faq-manager"},ys={class:"faq-header"},ws={class:"faq-list"},Vs={class:"faq-item-header"},ks={class:"faq-index"},xs={class:"section-header"},Cs={class:"section-title"},qs={class:"reviews-manager"},Us={class:"reviews-header"},Ds={class:"reviews-list"},zs={class:"review-header"},js={class:"review-user"},Ts={class:"review-meta"},Ms={class:"section-header"},$s={class:"section-title"},Is={class:"section-header"},Bs={class:"section-title"},Ls={key:0},As=e({__name:"GroupContentManager",props:{groupId:{type:[Number,String],required:!0},groupData:{type:Object,default:()=>({})}},emits:["content-updated"],setup(e,{emit:a}){const l=e,s=a,b=t(!1),h=t(!1),C=t(!1),U=t(null),O=p({cover_image:"",member_image:"",price:0,title:"",subtitle:"",read_count:"10万+",like_count:3659,want_see_count:665,button_title:"加入群，学习更多副业知识",avatar_library:"default",wx_accessible:1,remark:"",group_intro_title:"群简介",group_intro_content:"",faq_title:"常见问题",faq_content:"",user_reviews:"",customer_service_qr:"",ad_qr_code:"",show_customer_service:1,customer_service_avatar:"",customer_service_title:"VIP专属客服",customer_service_desc:"出现不能付款，不能入群等问题，请联系我！看到信息发回",extra_title3:"",extra_content3:"",payment_methods:[],status:1,show_faq:!0,faq_style:"collapse",show_reviews:!0,reviews_title:"用户评价",reviews_count:6,reviews_style:"card",show_extra:!0,extra_title1:"",extra_content1:"",extra_title2:"",extra_content2:""}),R=t([]),N=t([]),E={group_intro_title:[{required:!0,message:"请输入群组介绍标题",trigger:"blur"},{max:60,message:"标题长度不能超过60个字符",trigger:"blur"}],faq_title:[{required:!0,message:"请输入FAQ标题",trigger:"blur"},{max:60,message:"标题长度不能超过60个字符",trigger:"blur"}]};n(()=>l.groupId,e=>{e&&K()},{immediate:!0});const K=async()=>{if(l.groupId){b.value=!0;try{const{data:e}=await Ne(l.groupId);Object.assign(O,e.content||{}),e.faq_content&&(R.value=ae(e.faq_content)),e.user_reviews&&(N.value=se(e.user_reviews))}catch(e){console.error("获取群组内容失败:",e),z.error("获取群组内容失败")}finally{b.value=!1}}},Z=async()=>{U.value&&await U.value.validate(async e=>{if(e){h.value=!0;try{const e={...O,faq_content:le(R.value),user_reviews:oe(N.value)};await Ye(l.groupId,e),z.success("内容保存成功"),s("content-updated")}catch(a){console.error("保存内容失败:",a),z.error("保存内容失败")}finally{h.value=!1}}})},W=()=>{C.value=!0},J=()=>{j.confirm("确定要重置所有内容吗？此操作不可恢复。","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{K(),z.success("内容已重置")})},X=()=>{R.value.push({question:"",answer:""})},ee=()=>{N.value.push({username:"",content:"",rating:5,avatar:"",created_at:(new Date).toISOString().slice(0,19).replace("T"," ")})},ae=e=>{if(!e)return[];return e.split("\n").filter(e=>e.includes("----")).map(e=>{const[a,l]=e.split("----");return{question:a?.trim()||"",answer:l?.trim()||""}})},le=e=>e.filter(e=>e.question&&e.answer).map(e=>`${e.question}----${e.answer}`).join("\n"),se=e=>{if(!e)return[];return e.split("\n").filter(e=>e.includes("----")).map(e=>{const a=e.split("----");return{username:a[0]?.trim()||"",content:a[1]?.trim()||"",rating:parseInt(a[2])||5,avatar:a[3]?.trim()||"",created_at:a[4]?.trim()||(new Date).toISOString().slice(0,19).replace("T"," ")}})},oe=e=>e.filter(e=>e.username&&e.content).map(e=>`${e.username}----${e.content}----${e.rating}----${e.avatar}----${e.created_at}`).join("\n");return o(()=>{l.groupId&&K()}),(a,l)=>{const t=F,s=x,n=V("MagicStick"),o=V("Collection"),p=V("TrendCharts"),z=q,j=El,K=Me,ae=jl,le=I,se=$,oe=M,ue=S,ie=B,de=A,me=L,_e=G,ve=P,fe=re,ge=Q,ye=ce,we=ne,Ve=he,ke=Le,xe=be,Ce=T,qe=te;return r(),u("div",ss,[i("div",ns,[i("div",os,[i("h4",us,[d(t,null,{default:c(()=>[d(v(De))]),_:1}),l[37]||(l[37]=m(" 群组内容管理 ",-1))]),l[38]||(l[38]=i("p",{class:"content-desc"},"管理群组的展示内容、FAQ、用户评论等信息",-1))]),i("div",rs,[d(s,{type:"primary",onClick:Z,loading:h.value},{default:c(()=>[d(t,null,{default:c(()=>[d(v(ze))]),_:1}),l[39]||(l[39]=m(" 保存内容 ",-1))]),_:1,__:[39]},8,["loading"]),d(s,{onClick:W},{default:c(()=>[d(t,null,{default:c(()=>[d(v(je))]),_:1}),l[40]||(l[40]=m(" 预览效果 ",-1))]),_:1,__:[40]}),d(s,{onClick:J},{default:c(()=>[d(t,null,{default:c(()=>[d(v(Te))]),_:1}),l[41]||(l[41]=m(" 重置 ",-1))]),_:1,__:[41]})])]),i("div",is,[d(z,null,{default:c(()=>[d(s,{onClick:l[0]||(l[0]=e=>a.showAIGenerator=!a.showAIGenerator)},{default:c(()=>[d(t,null,{default:c(()=>[d(n)]),_:1}),l[42]||(l[42]=m(" AI生成助手 ",-1))]),_:1,__:[42]}),d(s,{onClick:l[1]||(l[1]=e=>a.showTemplateLibrary=!a.showTemplateLibrary)},{default:c(()=>[d(t,null,{default:c(()=>[d(o)]),_:1}),l[43]||(l[43]=m(" 模板库 ",-1))]),_:1,__:[43]}),d(s,{onClick:a.analyzeContent},{default:c(()=>[d(t,null,{default:c(()=>[d(p)]),_:1}),l[44]||(l[44]=m(" 内容分析 ",-1))]),_:1,__:[44]},8,["onClick"]),d(s,{onClick:a.optimizeContent},{default:c(()=>[d(t,null,{default:c(()=>[d(p)]),_:1}),l[45]||(l[45]=m(" 智能优化 ",-1))]),_:1,__:[45]},8,["onClick"])]),_:1})]),d(K,null,{default:c(()=>[w(i("div",ds,[d(j,{onContentGenerated:a.handleAIGenerated},null,8,["onContentGenerated"])],512),[[k,a.showAIGenerator]])]),_:1}),d(K,null,{default:c(()=>[w(i("div",cs,[d(ae,{onTemplateSelected:a.handleTemplateSelected},null,8,["onTemplateSelected"])],512),[[k,a.showTemplateLibrary]])]),_:1}),w((r(),u("div",ms,[d(Ce,{model:O,rules:E,ref_key:"contentFormRef",ref:U,"label-width":"120px"},{default:c(()=>[d(fe,{class:"content-section",shadow:"never"},{header:c(()=>[i("div",ps,[i("span",_s,[d(t,null,{default:c(()=>[d(v($e))]),_:1}),l[46]||(l[46]=m(" 基础设置 ",-1))])])]),default:c(()=>[d(oe,{gutter:20},{default:c(()=>[d(se,{span:12},{default:c(()=>[d(le,{label:"群组头像"},{default:c(()=>[d(ea,{modelValue:O.cover_image,"onUpdate:modelValue":l[2]||(l[2]=e=>O.cover_image=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:12},{default:c(()=>[d(le,{label:"成员图"},{default:c(()=>[d(ea,{modelValue:O.member_image,"onUpdate:modelValue":l[3]||(l[3]=e=>O.member_image=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(oe,{gutter:20},{default:c(()=>[d(se,{span:8},{default:c(()=>[d(le,{label:"入群费用",prop:"price"},{default:c(()=>[d(ue,{modelValue:O.price,"onUpdate:modelValue":l[4]||(l[4]=e=>O.price=e),min:0,precision:2,style:{width:"100%"},placeholder:"0表示免费"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:8},{default:c(()=>[d(le,{label:"群名称",prop:"title"},{default:c(()=>[d(ie,{modelValue:O.title,"onUpdate:modelValue":l[5]||(l[5]=e=>O.title=e),placeholder:"请输入群名称"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:8},{default:c(()=>[d(le,{label:"副标题"},{default:c(()=>[d(ie,{modelValue:O.subtitle,"onUpdate:modelValue":l[6]||(l[6]=e=>O.subtitle=e),placeholder:"请输入副标题"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(oe,{gutter:20},{default:c(()=>[d(se,{span:6},{default:c(()=>[d(le,{label:"阅读数"},{default:c(()=>[d(ie,{modelValue:O.read_count,"onUpdate:modelValue":l[7]||(l[7]=e=>O.read_count=e),placeholder:"如：10万+"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:6},{default:c(()=>[d(le,{label:"点赞数"},{default:c(()=>[d(ue,{modelValue:O.like_count,"onUpdate:modelValue":l[8]||(l[8]=e=>O.like_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:6},{default:c(()=>[d(le,{label:"想看数"},{default:c(()=>[d(ue,{modelValue:O.want_see_count,"onUpdate:modelValue":l[9]||(l[9]=e=>O.want_see_count=e),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:6},{default:c(()=>[d(le,{label:"头像库选择"},{default:c(()=>[d(me,{modelValue:O.avatar_library,"onUpdate:modelValue":l[10]||(l[10]=e=>O.avatar_library=e),placeholder:"选择头像库"},{default:c(()=>[d(de,{label:"默认头像",value:"default"}),d(de,{label:"商务头像",value:"business"}),d(de,{label:"交友头像",value:"dating"}),d(de,{label:"征婚头像",value:"marriage"}),d(de,{label:"健身头像",value:"fitness"}),d(de,{label:"家庭头像",value:"family"}),d(de,{label:"扩列头像",value:"qq"}),d(de,{label:"综合头像",value:"za"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(le,{label:"按键名称"},{default:c(()=>[d(ie,{modelValue:O.button_title,"onUpdate:modelValue":l[11]||(l[11]=e=>O.button_title=e),placeholder:"如：加入群，学习更多副业知识"},null,8,["modelValue"])]),_:1}),d(oe,{gutter:20},{default:c(()=>[d(se,{span:12},{default:c(()=>[d(le,{label:"引导浏览器打开"},{default:c(()=>[d(ve,{modelValue:O.wx_accessible,"onUpdate:modelValue":l[12]||(l[12]=e=>O.wx_accessible=e)},{default:c(()=>[d(_e,{label:1},{default:c(()=>l[47]||(l[47]=[m("微信能打开",-1)])),_:1,__:[47]}),d(_e,{label:2},{default:c(()=>l[48]||(l[48]=[m("微信内不能打开",-1)])),_:1,__:[48]})]),_:1},8,["modelValue"])]),_:1})]),_:1}),d(se,{span:12},{default:c(()=>[d(le,{label:"备注信息"},{default:c(()=>[d(ie,{modelValue:O.remark,"onUpdate:modelValue":l[13]||(l[13]=e=>O.remark=e),placeholder:"备注信息"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),d(fe,{class:"content-section",shadow:"never"},{header:c(()=>[i("div",vs,[i("span",fs,[d(t,null,{default:c(()=>[d(v(Ie))]),_:1}),l[49]||(l[49]=m(" 内容设置 ",-1))])])]),default:c(()=>[d(le,{label:"区块一标题"},{default:c(()=>[d(ie,{modelValue:O.group_intro_title,"onUpdate:modelValue":l[14]||(l[14]=e=>O.group_intro_title=e),placeholder:"如：群简介"},null,8,["modelValue"])]),_:1}),d(le,{label:"区块一内容"},{default:c(()=>[d(ie,{type:"textarea",modelValue:O.group_intro_content,"onUpdate:modelValue":l[15]||(l[15]=e=>O.group_intro_content=e),rows:4,placeholder:"输入群组介绍内容"},null,8,["modelValue"])]),_:1})]),_:1}),d(fe,{class:"content-section",shadow:"never"},{header:c(()=>[i("div",gs,[i("span",bs,[d(t,null,{default:c(()=>[d(v(Be))]),_:1}),l[50]||(l[50]=m(" 常见问题 (FAQ) ",-1))]),d(ge,{modelValue:O.show_faq,"onUpdate:modelValue":l[16]||(l[16]=e=>O.show_faq=e),"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])])]),default:c(()=>[d(oe,{gutter:20},{default:c(()=>[d(se,{span:12},{default:c(()=>[d(le,{label:"FAQ标题",prop:"faq_title"},{default:c(()=>[d(ie,{modelValue:O.faq_title,"onUpdate:modelValue":l[17]||(l[17]=e=>O.faq_title=e),placeholder:"如：常见问题、FAQ",maxlength:"60","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:12},{default:c(()=>[d(le,{label:"展示样式"},{default:c(()=>[d(me,{modelValue:O.faq_style,"onUpdate:modelValue":l[18]||(l[18]=e=>O.faq_style=e),placeholder:"选择展示样式"},{default:c(()=>[d(de,{label:"折叠面板",value:"collapse"}),d(de,{label:"列表展示",value:"list"}),d(de,{label:"卡片展示",value:"card"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(le,{label:"FAQ内容"},{default:c(()=>[i("div",hs,[i("div",ys,[l[52]||(l[52]=i("span",null,"问答列表",-1)),d(s,{type:"primary",size:"small",onClick:X},{default:c(()=>[d(t,null,{default:c(()=>[d(v(H))]),_:1}),l[51]||(l[51]=m(" 添加问答 ",-1))]),_:1,__:[51]})]),i("div",ws,[(r(!0),u(f,null,g(R.value,(e,a)=>(r(),u("div",{key:a,class:"faq-item"},[i("div",Vs,[i("span",ks,D(a+1),1),d(ie,{modelValue:e.question,"onUpdate:modelValue":a=>e.question=a,placeholder:"输入问题",class:"faq-question"},null,8,["modelValue","onUpdate:modelValue"]),d(s,{type:"danger",size:"small",circle:"",onClick:e=>(e=>{R.value.splice(e,1)})(a)},{default:c(()=>[d(t,null,{default:c(()=>[d(v(pe))]),_:1})]),_:2},1032,["onClick"])]),d(ie,{type:"textarea",modelValue:e.answer,"onUpdate:modelValue":a=>e.answer=a,placeholder:"输入答案",rows:3,class:"faq-answer"},null,8,["modelValue","onUpdate:modelValue"])]))),128)),0===R.value.length?(r(),_(ye,{key:0,description:"暂无FAQ内容","image-size":80})):y("",!0)])])]),_:1})]),_:1}),d(fe,{class:"content-section",shadow:"never"},{header:c(()=>[i("div",xs,[i("span",Cs,[d(t,null,{default:c(()=>[d(v(Ae))]),_:1}),l[53]||(l[53]=m(" 用户评论 ",-1))]),d(ge,{modelValue:O.show_reviews,"onUpdate:modelValue":l[19]||(l[19]=e=>O.show_reviews=e),"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])])]),default:c(()=>[d(oe,{gutter:20},{default:c(()=>[d(se,{span:8},{default:c(()=>[d(le,{label:"评论标题"},{default:c(()=>[d(ie,{modelValue:O.reviews_title,"onUpdate:modelValue":l[20]||(l[20]=e=>O.reviews_title=e),placeholder:"如：用户评价、群友反馈"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:8},{default:c(()=>[d(le,{label:"显示数量"},{default:c(()=>[d(ue,{modelValue:O.reviews_count,"onUpdate:modelValue":l[21]||(l[21]=e=>O.reviews_count=e),min:1,max:20,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:8},{default:c(()=>[d(le,{label:"评论样式"},{default:c(()=>[d(me,{modelValue:O.reviews_style,"onUpdate:modelValue":l[22]||(l[22]=e=>O.reviews_style=e),placeholder:"选择样式"},{default:c(()=>[d(de,{label:"卡片样式",value:"card"}),d(de,{label:"列表样式",value:"list"}),d(de,{label:"轮播样式",value:"carousel"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(le,{label:"评论内容"},{default:c(()=>[i("div",qs,[i("div",Us,[l[55]||(l[55]=i("span",null,"评论列表",-1)),d(s,{type:"primary",size:"small",onClick:ee},{default:c(()=>[d(t,null,{default:c(()=>[d(v(H))]),_:1}),l[54]||(l[54]=m(" 添加评论 ",-1))]),_:1,__:[54]})]),i("div",Ds,[(r(!0),u(f,null,g(N.value,(e,a)=>(r(),u("div",{key:a,class:"review-item"},[i("div",zs,[d(we,{src:e.avatar,size:40},{default:c(()=>[d(t,null,{default:c(()=>[d(v(Y))]),_:1})]),_:2},1032,["src"]),i("div",js,[d(ie,{modelValue:e.username,"onUpdate:modelValue":a=>e.username=a,placeholder:"用户名",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),d(Ve,{modelValue:e.rating,"onUpdate:modelValue":a=>e.rating=a,size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),d(s,{type:"danger",size:"small",circle:"",onClick:e=>(e=>{N.value.splice(e,1)})(a)},{default:c(()=>[d(t,null,{default:c(()=>[d(v(pe))]),_:1})]),_:2},1032,["onClick"])]),d(ie,{type:"textarea",modelValue:e.content,"onUpdate:modelValue":a=>e.content=a,placeholder:"输入评论内容",rows:2},null,8,["modelValue","onUpdate:modelValue"]),i("div",Ts,[d(ie,{modelValue:e.avatar,"onUpdate:modelValue":a=>e.avatar=a,placeholder:"头像URL（可选）",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),d(ke,{modelValue:e.created_at,"onUpdate:modelValue":a=>e.created_at=a,type:"datetime",placeholder:"评论时间",size:"small",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","onUpdate:modelValue"])])]))),128)),0===N.value.length?(r(),_(ye,{key:0,description:"暂无用户评论","image-size":80})):y("",!0)])])]),_:1})]),_:1}),d(fe,{class:"content-section",shadow:"never"},{header:c(()=>[i("div",Ms,[i("span",$s,[d(t,null,{default:c(()=>[d(v(Se))]),_:1}),l[56]||(l[56]=m(" 扩展内容区块 ",-1))]),d(ge,{modelValue:O.show_extra,"onUpdate:modelValue":l[23]||(l[23]=e=>O.show_extra=e),"active-text":"显示","inactive-text":"隐藏"},null,8,["modelValue"])])]),default:c(()=>[d(oe,{gutter:20},{default:c(()=>[d(se,{span:12},{default:c(()=>[d(le,{label:"区块1标题"},{default:c(()=>[d(ie,{modelValue:O.extra_title1,"onUpdate:modelValue":l[24]||(l[24]=e=>O.extra_title1=e),placeholder:"如：服务优势、特色功能"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:12},{default:c(()=>[d(le,{label:"区块2标题"},{default:c(()=>[d(ie,{modelValue:O.extra_title2,"onUpdate:modelValue":l[25]||(l[25]=e=>O.extra_title2=e),placeholder:"如：联系方式、注意事项"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(oe,{gutter:20},{default:c(()=>[d(se,{span:12},{default:c(()=>[d(le,{label:"区块1内容"},{default:c(()=>[d(ra,{modelValue:O.extra_content1,"onUpdate:modelValue":l[26]||(l[26]=e=>O.extra_content1=e),height:150,placeholder:"输入扩展区块1的内容"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:12},{default:c(()=>[d(le,{label:"区块2内容"},{default:c(()=>[d(ra,{modelValue:O.extra_content2,"onUpdate:modelValue":l[27]||(l[27]=e=>O.extra_content2=e),height:150,placeholder:"输入扩展区块2的内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),d(fe,{class:"content-section",shadow:"never"},{header:c(()=>[i("div",Is,[i("span",Bs,[d(t,null,{default:c(()=>[d(v(Oe))]),_:1}),l[57]||(l[57]=m(" 展示广告 ",-1))])])]),default:c(()=>[d(oe,{gutter:20},{default:c(()=>[d(se,{span:12},{default:c(()=>[d(le,{label:"客服二维码"},{default:c(()=>[d(ea,{modelValue:O.customer_service_qr,"onUpdate:modelValue":l[28]||(l[28]=e=>O.customer_service_qr=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:12},{default:c(()=>[d(le,{label:"广告二维码"},{default:c(()=>[d(ea,{modelValue:O.ad_qr_code,"onUpdate:modelValue":l[29]||(l[29]=e=>O.ad_qr_code=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(xe,{"content-position":"left"},{default:c(()=>l[58]||(l[58]=[m("客服信息设置",-1)])),_:1,__:[58]}),d(le,{label:"客服信息显示"},{default:c(()=>[d(ve,{modelValue:O.show_customer_service,"onUpdate:modelValue":l[30]||(l[30]=e=>O.show_customer_service=e)},{default:c(()=>[d(_e,{label:1},{default:c(()=>l[59]||(l[59]=[m("不显示",-1)])),_:1,__:[59]}),d(_e,{label:2},{default:c(()=>l[60]||(l[60]=[m("显示",-1)])),_:1,__:[60]})]),_:1},8,["modelValue"])]),_:1}),2===O.show_customer_service?(r(),u("div",Ls,[d(oe,{gutter:20},{default:c(()=>[d(se,{span:12},{default:c(()=>[d(le,{label:"客服头像"},{default:c(()=>[d(ea,{modelValue:O.customer_service_avatar,"onUpdate:modelValue":l[31]||(l[31]=e=>O.customer_service_avatar=e),limit:1,accept:"image/*","list-type":"picture-card"},null,8,["modelValue"])]),_:1})]),_:1}),d(se,{span:12},{default:c(()=>[d(le,{label:"客服标题"},{default:c(()=>[d(ie,{modelValue:O.customer_service_title,"onUpdate:modelValue":l[32]||(l[32]=e=>O.customer_service_title=e),placeholder:"如：VIP专属客服"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),d(le,{label:"客服描述"},{default:c(()=>[d(ie,{modelValue:O.customer_service_desc,"onUpdate:modelValue":l[33]||(l[33]=e=>O.customer_service_desc=e),placeholder:"如：出现不能付款，不能入群等问题，请联系我！看到信息发回"},null,8,["modelValue"])]),_:1})])):y("",!0),d(xe,{"content-position":"left"},{default:c(()=>l[61]||(l[61]=[m("区块三设置",-1)])),_:1,__:[61]}),d(le,{label:"区块三标题"},{default:c(()=>[d(ie,{modelValue:O.extra_title3,"onUpdate:modelValue":l[34]||(l[34]=e=>O.extra_title3=e),placeholder:"区块三标题"},null,8,["modelValue"])]),_:1}),d(le,{label:"区块三图片"},{default:c(()=>[d(ra,{modelValue:O.extra_content3,"onUpdate:modelValue":l[35]||(l[35]=e=>O.extra_content3=e),height:200,placeholder:"可以插入图片和富文本内容"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])])),[[qe,b.value]]),d(ts,{modelValue:C.value,"onUpdate:modelValue":l[36]||(l[36]=e=>C.value=e),"content-data":O,"group-data":e.groupData},null,8,["modelValue","content-data","group-data"])])}}},[["__scopeId","data-v-94a82f95"]]),Ss={class:"modern-group-list"},Os={class:"page-header"},Fs={class:"header-content"},Hs={class:"header-actions"},Ps={class:"stats-section"},Gs={class:"stats-card primary"},Qs={class:"stats-content"},Rs={class:"stats-icon"},Ns={class:"stats-info"},Ys={class:"stats-value"},Es={class:"stats-trend positive"},Ks={class:"stats-bg-icon"},Zs={class:"stats-card success"},Ws={class:"stats-content"},Js={class:"stats-icon"},Xs={class:"stats-info"},en={class:"stats-value"},an={class:"stats-trend positive"},ln={class:"stats-bg-icon"},tn={class:"stats-card warning"},sn={class:"stats-content"},nn={class:"stats-icon"},on={class:"stats-info"},un={class:"stats-value"},rn={class:"stats-trend positive"},dn={class:"stats-bg-icon"},cn={class:"stats-card danger"},mn={class:"stats-content"},pn={class:"stats-icon"},_n={class:"stats-info"},vn={class:"stats-value"},fn={class:"stats-trend positive"},gn={class:"stats-bg-icon"},bn={class:"filter-section"},hn={class:"filter-content"},yn={class:"filter-left"},wn={class:"search-group"},Vn={class:"filter-group"},kn={class:"filter-right"},xn={class:"table-section"},Cn={class:"table-header"},qn={class:"header-left"},Un={class:"total-count"},Dn={class:"header-right"},zn={key:0,class:"table-view"},jn={class:"group-info"},Tn={class:"group-avatar"},Mn={class:"group-details"},$n={class:"group-name"},In={class:"group-desc"},Bn={class:"group-meta"},Ln={class:"group-id"},An={class:"group-category"},Sn={class:"owner-info"},On={class:"owner-name"},Fn={class:"price-info"},Hn={class:"price-value"},Pn={class:"member-stats"},Gn={class:"member-count"},Qn={class:"current"},Rn={class:"max"},Nn={class:"health-score"},Yn={class:"score-label"},En={class:"time-info"},Kn={class:"date"},Zn={class:"action-buttons"},Wn={key:1,class:"card-view"},Jn=["onClick"],Xn={class:"card-header"},eo={class:"group-avatar"},ao={class:"card-actions"},lo={class:"card-content"},to={class:"group-title"},so={class:"group-description"},no={class:"group-stats"},oo={class:"stat-item"},uo={class:"value"},ro={class:"stat-item"},io={class:"value price"},co={class:"group-tags"},mo={class:"pagination-wrapper"},po=e({__name:"GroupList",setup(e){const a=t([]),l=t(0),s=t(!0),n=t(!1),h=t(!1),V=t(!1),k=t(!1),U=t(!1),T=t({}),I=t(null),S=t([]),O=t("table"),P=t({total_groups:0,active_groups:0,total_members:0,total_revenue:0}),G=p({page:1,limit:20,keyword:"",status:"",category:""}),Q=async()=>{s.value=!0;try{const e=await Ee(G),{data:t}=e;t&&t.list?(a.value=t.list,l.value=t.total||0):(a.value=[],l.value=0,z.warning("暂无数据"))}catch(e){console.error("获取群组列表失败:",e),z.error("获取群组列表失败"),a.value=[],l.value=0}finally{s.value=!1}},N=async()=>{try{const e=await Ke(),{data:a}=e;a&&(P.value=a)}catch(e){console.error("获取统计数据失败:",e)}},E=()=>{G.page=1,Q()},K=()=>{T.value={},n.value=!0},X=e=>{T.value={...e},n.value=!0},ee=e=>{T.value={...e},h.value=!0},ie=e=>{I.value=e.id,V.value=!0},de=()=>{z.info("正在跳转到自动化规则配置页面...")},ce=async(e,a)=>{try{const l="paused"===a?"暂停":"恢复";await j.confirm(`确定要${l}该群组吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Je(e,a),z.success(`${l}成功`),Q()}catch(l){"cancel"!==l&&z.error("操作失败")}},me=e=>{const[l,t]=e.split("-"),s=parseInt(t),n=a.value.find(e=>e.id===s);switch(l){case"edit":X(n);break;case"members":ee(n);break;case"analytics":ie(n);break;case"qrcode":T.value={...n},k.value=!0;break;case"content":T.value={...n},U.value=!0;break;case"clone":(async()=>{try{await j.confirm("确定要克隆该群组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),z.success("群组克隆成功"),Q()}catch(e){"cancel"!==e&&z.error("克隆失败")}})();break;case"pause":ce(s,"paused");break;case"resume":ce(s,"active");break;case"delete":(async e=>{try{await j.confirm("确定要删除该群组吗？此操作不可恢复！","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await We(e),z.success("删除成功"),Q()}catch(a){"cancel"!==a&&z.error("删除失败")}})(s)}},ve=async()=>{try{await Ze(G),z.success("导出成功")}catch(e){z.error("导出失败")}},fe=()=>{0!==S.value.length?j.confirm("请选择批量操作类型","批量操作",{distinguishCancelAndClose:!0,confirmButtonText:"暂停群组",cancelButtonText:"恢复群组"}).then(()=>{ge()}).catch(e=>{"cancel"===e&&ge()}):z.warning("请先选择要操作的群组")},ge=async e=>{try{S.value.map(e=>e.id);z.success("批量操作成功"),Q()}catch(a){z.error("批量操作失败")}},be=e=>{S.value=e},he=e=>{G.limit=e,Q()},ye=e=>{G.page=e,Q()},we=()=>{Q(),N()},Ve=()=>{Q()},xe=e=>({startup:"创业交流",finance:"投资理财",tech:"科技互联网",education:"教育培训",other:"其他"}[e]||"未知"),Ce=e=>({active:"success",paused:"warning",full:"info",pending:"danger"}[e]||""),qe=e=>({active:"活跃",paused:"暂停",full:"已满",pending:"待审核"}[e]||"未知"),Ue=e=>!e||e<40?"较差":e<60?"一般":e<80?"良好":"优秀";return o(()=>{Q(),N()}),(e,t)=>{const o=F,p=x,z=$,j=M,Q=B,N=A,ce=L,ge=re,ze=q,je=se,Te=ne,Me=R,Ie=Pe,Be=J,Le=W,Se=ae,Oe=oe,Qe=ue,Re=te;return r(),u("div",Ss,[i("div",Os,[i("div",Fs,[t[13]||(t[13]=i("div",{class:"header-left"},[i("h1",{class:"page-title"},"社群管理"),i("p",{class:"page-subtitle"},"管理和监控您的社群运营状况")],-1)),i("div",Hs,[d(p,{type:"primary",size:"large",onClick:K,class:"create-btn"},{default:c(()=>[d(o,null,{default:c(()=>[d(v(H))]),_:1}),t[12]||(t[12]=m(" 创建群组 ",-1))]),_:1,__:[12]})])])]),i("div",Ps,[d(j,{gutter:24},{default:c(()=>[d(z,{span:6},{default:c(()=>[i("div",Gs,[i("div",Qs,[i("div",Rs,[d(o,null,{default:c(()=>[d(v(ke))]),_:1})]),i("div",Ns,[i("div",Ys,D(P.value.total_groups),1),t[14]||(t[14]=i("div",{class:"stats-label"},"总群组数",-1))])]),i("div",Es,[d(o,null,{default:c(()=>[d(v(Fe))]),_:1}),t[15]||(t[15]=i("span",null,"+15.8%",-1))]),i("div",Ks,[d(o,null,{default:c(()=>[d(v(ke))]),_:1})])])]),_:1}),d(z,{span:6},{default:c(()=>[i("div",Zs,[i("div",Ws,[i("div",Js,[d(o,null,{default:c(()=>[d(v(Ae))]),_:1})]),i("div",Xs,[i("div",en,D(P.value.active_groups),1),t[16]||(t[16]=i("div",{class:"stats-label"},"活跃群组",-1))])]),i("div",an,[d(o,null,{default:c(()=>[d(v(Fe))]),_:1}),t[17]||(t[17]=i("span",null,"+8.2%",-1))]),i("div",ln,[d(o,null,{default:c(()=>[d(v(Ae))]),_:1})])])]),_:1}),d(z,{span:6},{default:c(()=>[i("div",tn,[i("div",sn,[i("div",nn,[d(o,null,{default:c(()=>[d(v(Y))]),_:1})]),i("div",on,[i("div",un,D(P.value.total_members),1),t[18]||(t[18]=i("div",{class:"stats-label"},"总成员数",-1))])]),i("div",rn,[d(o,null,{default:c(()=>[d(v(Fe))]),_:1}),t[19]||(t[19]=i("span",null,"+12.1%",-1))]),i("div",dn,[d(o,null,{default:c(()=>[d(v(Y))]),_:1})])])]),_:1}),d(z,{span:6},{default:c(()=>[i("div",cn,[i("div",mn,[i("div",pn,[d(o,null,{default:c(()=>[d(v(He))]),_:1})]),i("div",_n,[i("div",vn,"¥"+D((P.value.total_revenue||0).toFixed(0)),1),t[20]||(t[20]=i("div",{class:"stats-label"},"总收入",-1))])]),i("div",fn,[d(o,null,{default:c(()=>[d(v(Fe))]),_:1}),t[21]||(t[21]=i("span",null,"+18.5%",-1))]),i("div",gn,[d(o,null,{default:c(()=>[d(v(He))]),_:1})])])]),_:1})]),_:1})]),i("div",bn,[d(ge,{class:"filter-card",shadow:"never"},{default:c(()=>[i("div",hn,[i("div",yn,[i("div",wn,[d(Q,{modelValue:G.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>G.keyword=e),placeholder:"搜索群组名称、群主...",class:"search-input",onKeyup:b(E,["enter"]),clearable:""},{prefix:c(()=>[d(o,null,{default:c(()=>[d(v(_e))]),_:1})]),_:1},8,["modelValue"])]),i("div",Vn,[d(ce,{modelValue:G.status,"onUpdate:modelValue":t[1]||(t[1]=e=>G.status=e),placeholder:"群组状态",clearable:"",class:"filter-select"},{default:c(()=>[d(N,{label:"全部状态",value:""}),d(N,{label:"活跃",value:"active"}),d(N,{label:"暂停",value:"paused"}),d(N,{label:"已满",value:"full"}),d(N,{label:"待审核",value:"pending"})]),_:1},8,["modelValue"]),d(ce,{modelValue:G.category,"onUpdate:modelValue":t[2]||(t[2]=e=>G.category=e),placeholder:"群组分类",clearable:"",class:"filter-select"},{default:c(()=>[d(N,{label:"全部分类",value:""}),d(N,{label:"创业交流",value:"startup"}),d(N,{label:"投资理财",value:"finance"}),d(N,{label:"科技互联网",value:"tech"}),d(N,{label:"教育培训",value:"education"}),d(N,{label:"其他",value:"other"})]),_:1},8,["modelValue"])])]),i("div",kn,[d(p,{type:"primary",onClick:E,class:"action-btn"},{default:c(()=>[d(o,null,{default:c(()=>[d(v(_e))]),_:1}),t[22]||(t[22]=m(" 搜索 ",-1))]),_:1,__:[22]}),d(p,{type:"success",onClick:de,class:"action-btn"},{default:c(()=>[d(o,null,{default:c(()=>[d(v($e))]),_:1}),t[23]||(t[23]=m(" 自动化规则 ",-1))]),_:1,__:[23]}),d(p,{type:"warning",onClick:ve,class:"action-btn"},{default:c(()=>[d(o,null,{default:c(()=>[d(v(le))]),_:1}),t[24]||(t[24]=m(" 导出数据 ",-1))]),_:1,__:[24]}),S.value.length>0?(r(),_(p,{key:0,type:"info",onClick:fe,class:"action-btn"},{default:c(()=>[m(" 批量操作 ("+D(S.value.length)+") ",1)]),_:1})):y("",!0)])])]),_:1})]),i("div",xn,[d(ge,{class:"table-card",shadow:"never"},{header:c(()=>[i("div",Cn,[i("div",qn,[t[25]||(t[25]=i("h3",null,"群组列表",-1)),i("span",Un,"共 "+D(l.value)+" 个群组",1)]),i("div",Dn,[d(ze,null,{default:c(()=>[d(p,{type:"table"===O.value?"primary":"",onClick:t[3]||(t[3]=e=>O.value="table")},{default:c(()=>t[26]||(t[26]=[m(" 表格视图 ",-1)])),_:1,__:[26]},8,["type"]),d(p,{type:"card"===O.value?"primary":"",onClick:t[4]||(t[4]=e=>O.value="card")},{default:c(()=>t[27]||(t[27]=[m(" 卡片视图 ",-1)])),_:1,__:[27]},8,["type"])]),_:1})])])]),default:c(()=>["table"===O.value?(r(),u("div",zn,[w((r(),_(Oe,{data:a.value,"element-loading-text":"加载中...",class:"modern-table",onSelectionChange:be},{default:c(()=>[d(je,{type:"selection",width:"55"}),d(je,{label:"群组信息",width:"280",fixed:"left"},{default:c(({row:e})=>[i("div",jn,[i("div",Tn,[d(Te,{src:e.avatar,alt:e.name,size:"large"},{default:c(()=>[m(D(e.name.charAt(0)),1)]),_:2},1032,["src","alt"]),i("div",{class:C(["status-dot",e.status])},null,2)]),i("div",Mn,[i("div",$n,D(e.name),1),i("div",In,D(e.description),1),i("div",Bn,[i("span",Ln,"ID: "+D(e.id),1),i("span",An,D(xe(e.category)),1)])])])]),_:1}),d(je,{label:"群主信息",width:"150"},{default:c(({row:e})=>[i("div",Sn,[i("div",On,D(e.owner?.name||e.owner_name),1),d(Me,{size:"small",type:"info",class:"owner-role"},{default:c(()=>[m(D(e.owner?.role||e.owner_role||"群主"),1)]),_:2},1024)])]),_:1}),d(je,{label:"价格",width:"100"},{default:c(({row:e})=>[i("div",Fn,[i("span",Hn,"¥"+D((e.price||0).toFixed(2)),1)])]),_:1}),d(je,{label:"成员统计",width:"180"},{default:c(({row:e})=>{return[i("div",Pn,[i("div",Gn,[i("span",Qn,D(e.memberCount||e.current_members||0),1),t[28]||(t[28]=i("span",{class:"separator"},"/",-1)),i("span",Rn,D(e.maxMembers||e.max_members||500),1)]),d(Ie,{percentage:(e.memberCount||e.current_members||0)/(e.maxMembers||e.max_members||500)*100,"stroke-width":6,"show-text":!1,color:(a=(e.memberCount||e.current_members||0)/(e.maxMembers||e.max_members||500),a<.5?"#67c23a":a<.8?"#e6a23c":"#f56c6c"),class:"member-progress"},null,8,["percentage","color"])])];var a}),_:1}),d(je,{label:"健康度",width:"120"},{default:c(({row:e})=>{return[i("div",Nn,[i("div",{class:C(["score-circle",(a=e.health_score,!a||a<40?"poor":a<60?"fair":a<80?"good":"excellent")])},D(e.health_score||"N/A"),3),i("div",Yn,D(Ue(e.health_score)),1)])];var a}),_:1}),d(je,{label:"状态",width:"100"},{default:c(({row:e})=>[d(Me,{type:Ce(e.status),class:"status-tag"},{default:c(()=>[m(D(qe(e.status)),1)]),_:2},1032,["type"])]),_:1}),d(je,{label:"创建时间",width:"160"},{default:c(({row:e})=>[i("div",En,[i("div",Kn,D(v(aa)(e.created_at)),1)])]),_:1}),d(je,{label:"操作",width:"280",fixed:"right"},{default:c(({row:e})=>[i("div",Zn,[d(p,{type:"primary",size:"small",onClick:a=>X(e),class:"action-btn-small"},{default:c(()=>t[29]||(t[29]=[m(" 编辑 ",-1)])),_:2,__:[29]},1032,["onClick"]),d(p,{type:"success",size:"small",onClick:a=>ee(e),class:"action-btn-small"},{default:c(()=>t[30]||(t[30]=[m(" 成员 ",-1)])),_:2,__:[30]},1032,["onClick"]),d(p,{type:"info",size:"small",onClick:a=>ie(e),class:"action-btn-small"},{default:c(()=>t[31]||(t[31]=[m(" 分析 ",-1)])),_:2,__:[31]},1032,["onClick"]),d(Se,{onCommand:me,class:"action-dropdown"},{dropdown:c(()=>[d(Le,null,{default:c(()=>[d(Be,{command:`qrcode-${e.id}`},{default:c(()=>t[33]||(t[33]=[m(" 二维码 ",-1)])),_:2,__:[33]},1032,["command"]),d(Be,{command:`content-${e.id}`},{default:c(()=>[d(o,null,{default:c(()=>[d(v(De))]),_:1}),t[34]||(t[34]=m(" 内容管理 ",-1))]),_:2,__:[34]},1032,["command"]),d(Be,{command:`clone-${e.id}`},{default:c(()=>t[35]||(t[35]=[m(" 克隆群组 ",-1)])),_:2,__:[35]},1032,["command"]),"active"===e.status?(r(),_(Be,{key:0,command:`pause-${e.id}`},{default:c(()=>t[36]||(t[36]=[m(" 暂停群组 ",-1)])),_:2,__:[36]},1032,["command"])):y("",!0),"paused"===e.status?(r(),_(Be,{key:1,command:`resume-${e.id}`},{default:c(()=>t[37]||(t[37]=[m(" 恢复群组 ",-1)])),_:2,__:[37]},1032,["command"])):y("",!0),d(Be,{command:`delete-${e.id}`,divided:""},{default:c(()=>[d(o,null,{default:c(()=>[d(v(pe))]),_:1}),t[38]||(t[38]=m(" 删除群组 ",-1))]),_:2,__:[38]},1032,["command"])]),_:2},1024)]),default:c(()=>[d(p,{type:"warning",size:"small",class:"action-btn-small"},{default:c(()=>[t[32]||(t[32]=m(" 更多",-1)),d(o,{class:"el-icon--right"},{default:c(()=>[d(v(Z))]),_:1})]),_:1,__:[32]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Re,s.value]])])):(r(),u("div",Wn,[d(j,{gutter:24},{default:c(()=>[(r(!0),u(f,null,g(a.value,e=>(r(),_(z,{span:8,key:e.id},{default:c(()=>{return[i("div",{class:"group-card",onClick:a=>X(e)},[i("div",Xn,[i("div",eo,[d(Te,{src:e.avatar,alt:e.name,size:"large"},{default:c(()=>[m(D(e.name.charAt(0)),1)]),_:2},1032,["src","alt"]),i("div",{class:C(["status-indicator",e.status])},null,2)]),i("div",ao,[d(Se,{onCommand:me,trigger:"click"},{dropdown:c(()=>[d(Le,null,{default:c(()=>[d(Be,{command:`edit-${e.id}`},{default:c(()=>t[39]||(t[39]=[m("编辑",-1)])),_:2,__:[39]},1032,["command"]),d(Be,{command:`members-${e.id}`},{default:c(()=>t[40]||(t[40]=[m("成员管理",-1)])),_:2,__:[40]},1032,["command"]),d(Be,{command:`analytics-${e.id}`},{default:c(()=>t[41]||(t[41]=[m("数据分析",-1)])),_:2,__:[41]},1032,["command"]),d(Be,{command:`delete-${e.id}`,divided:""},{default:c(()=>t[42]||(t[42]=[m("删除",-1)])),_:2,__:[42]},1032,["command"])]),_:2},1024)]),default:c(()=>[d(p,{text:"",class:"more-btn"},{default:c(()=>[d(o,null,{default:c(()=>[d(v(Ge))]),_:1})]),_:1})]),_:2},1024)])]),i("div",lo,[i("h4",to,D(e.name),1),i("p",so,D(e.description),1),i("div",no,[i("div",oo,[t[43]||(t[43]=i("span",{class:"label"},"成员",-1)),i("span",uo,D(e.memberCount||e.current_members||0)+"/"+D(e.maxMembers||e.max_members||500),1)]),i("div",ro,[t[44]||(t[44]=i("span",{class:"label"},"价格",-1)),i("span",io,"¥"+D((e.price||0).toFixed(2)),1)])]),i("div",co,[d(Me,{type:(a=e.category,{startup:"success",finance:"warning",tech:"primary",education:"info",other:""}[a]||""),size:"small"},{default:c(()=>[m(D(xe(e.category)),1)]),_:2},1032,["type"]),d(Me,{type:Ce(e.status),size:"small"},{default:c(()=>[m(D(qe(e.status)),1)]),_:2},1032,["type"])])])],8,Jn)];var a}),_:2},1024))),128))]),_:1})])),i("div",mo,[d(Qe,{"current-page":G.page,"onUpdate:currentPage":t[5]||(t[5]=e=>G.page=e),"page-size":G.limit,"onUpdate:pageSize":t[6]||(t[6]=e=>G.limit=e),"page-sizes":[12,24,36,48],total:l.value,background:"",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:he,onCurrentChange:ye,class:"modern-pagination"},null,8,["current-page","page-size","total"])])]),_:1})]),d(va,{modelValue:n.value,"onUpdate:modelValue":t[7]||(t[7]=e=>n.value=e),"group-data":T.value,onSuccess:we},null,8,["modelValue","group-data"]),d(Qa,{modelValue:h.value,"onUpdate:modelValue":t[8]||(t[8]=e=>h.value=e),"group-data":T.value,onSuccess:Ve},null,8,["modelValue","group-data"]),d(nl,{visible:V.value,"onUpdate:visible":t[9]||(t[9]=e=>V.value=e),"group-id":I.value,"group-data":T.value},null,8,["visible","group-id","group-data"]),d(vl,{modelValue:k.value,"onUpdate:modelValue":t[10]||(t[10]=e=>k.value=e),"group-data":T.value},null,8,["modelValue","group-data"]),d(As,{modelValue:U.value,"onUpdate:modelValue":t[11]||(t[11]=e=>U.value=e),"group-data":T.value},null,8,["modelValue","group-data"])])}}},[["__scopeId","data-v-771304e2"]]);export{po as default};
