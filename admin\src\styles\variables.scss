// SCSS 变量定义

// 颜色系统
$primary-color: #3b82f6;
$primary-hover-color: #2563eb;
$primary-active-color: #1d4ed8;
$primary-light-color: #dbeafe;

$secondary-color: #8b5cf6;
$secondary-hover-color: #7c3aed;
$secondary-active-color: #6d28d9;
$secondary-light-color: #ede9fe;

$success-color: #10b981;
$success-hover-color: #059669;
$success-active-color: #047857;
$success-light-color: #d1fae5;

$warning-color: #f59e0b;
$warning-hover-color: #d97706;
$warning-active-color: #b45309;
$warning-light-color: #fef3c7;

$danger-color: #ef4444;
$danger-hover-color: #dc2626;
$danger-active-color: #b91c1c;
$danger-light-color: #fee2e2;

$info-color: #06b6d4;
$info-hover-color: #0891b2;
$info-active-color: #0e7490;
$info-light-color: #cffafe;

// 中性色
$white: #ffffff;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;
$black: #000000;

// 文本颜色
$text-color: #1e293b;
$text-secondary-color: #64748b;
$text-muted-color: #94a3b8;
$text-light-color: #cbd5e1;
$text-white-color: #ffffff;

// 背景颜色
$bg-color: #f8fafc;
$bg-secondary-color: #f1f5f9;
$bg-dark-color: #0f172a;
$bg-card-color: #ffffff;

// 边框颜色
$border-color: #e2e8f0;
$border-light-color: #f1f5f9;
$border-dark-color: #cbd5e1;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// 圆角
$border-radius-sm: 4px;
$border-radius: 8px;
$border-radius-md: 12px;
$border-radius-lg: 16px;
$border-radius-xl: 20px;
$border-radius-2xl: 24px;
$border-radius-full: 9999px;

// 字体
$font-family-sans: 'Inter', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
$font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;
$font-size-5xl: 48px;
$font-size-6xl: 60px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;

// 行高
$line-height-none: 1;
$line-height-tight: 1.25;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

// 间距
$spacing-0: 0;
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-5: 20px;
$spacing-6: 24px;
$spacing-8: 32px;
$spacing-10: 40px;
$spacing-12: 48px;
$spacing-16: 64px;
$spacing-20: 80px;
$spacing-24: 96px;
$spacing-32: 128px;

// 断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 过渡动画
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.5s ease;

// 缓动函数
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in: cubic-bezier(0.4, 0, 1, 1);

// 渐变
$gradient-primary: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
$gradient-success: linear-gradient(135deg, $success-color 0%, #059669 100%);
$gradient-warning: linear-gradient(135deg, $warning-color 0%, #d97706 100%);
$gradient-danger: linear-gradient(135deg, $danger-color 0%, #dc2626 100%);
$gradient-info: linear-gradient(135deg, $info-color 0%, #0891b2 100%);

// 背景渐变
$bg-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$bg-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$bg-gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$bg-gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
$bg-gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

// 组件特定变量
$header-height: 64px;
$sidebar-width: 280px;
$sidebar-collapsed-width: 64px;
$footer-height: 60px;

// 表格
$table-border-color: $border-color;
$table-header-bg: $gray-50;
$table-row-hover-bg: rgba($primary-color, 0.04);

// 表单
$input-border-color: $border-color;
$input-focus-border-color: $primary-color;
$input-bg-color: $white;
$input-placeholder-color: $text-muted-color;

// 按钮
$button-border-radius: $border-radius;
$button-font-weight: $font-weight-medium;
$button-transition: $transition-base;

// 卡片
$card-bg: $white;
$card-border-color: $border-color;
$card-border-radius: $border-radius-lg;
$card-shadow: $shadow;

// 模态框
$modal-backdrop-bg: rgba($black, 0.5);
$modal-bg: $white;
$modal-border-radius: $border-radius-lg;

// 提示框
$tooltip-bg: rgba($gray-900, 0.9);
$tooltip-color: $white;
$tooltip-border-radius: $border-radius;

// 进度条
$progress-bg: $gray-200;
$progress-bar-bg: $primary-color;
$progress-height: 8px;
$progress-border-radius: $border-radius-full;

// 标签
$tag-border-radius: $border-radius-sm;
$tag-font-size: $font-size-xs;
$tag-font-weight: $font-weight-medium;

// 徽章
$badge-border-radius: $border-radius-full;
$badge-font-size: $font-size-xs;
$badge-font-weight: $font-weight-bold;

// 分页
$pagination-color: $text-color;
$pagination-bg: $white;
$pagination-border-color: $border-color;
$pagination-hover-color: $primary-color;
$pagination-hover-bg: $primary-light-color;
$pagination-active-color: $white;
$pagination-active-bg: $primary-color;

// 面包屑
$breadcrumb-divider-color: $text-muted-color;
$breadcrumb-item-color: $text-secondary-color;
$breadcrumb-active-color: $text-color;

// 警告框
$alert-border-radius: $border-radius;
$alert-padding: $spacing-4;

// 加载器
$loader-color: $primary-color;
$loader-size: 20px;

// 开关
$switch-width: 44px;
$switch-height: 24px;
$switch-border-radius: $border-radius-full;

// 滑块
$slider-track-height: 6px;
$slider-track-bg: $gray-200;
$slider-thumb-size: 20px;
$slider-thumb-bg: $white;

// 评分
$rate-star-color: $warning-color;
$rate-star-size: 18px;

// 时间轴
$timeline-color: $border-color;
$timeline-dot-size: 12px;

// 树形控件
$tree-indent: 24px;
$tree-node-height: 32px;

// 穿梭框
$transfer-width: 200px;
$transfer-height: 300px;

// 颜色选择器
$color-picker-size: 32px;

// 日期选择器
$date-picker-cell-size: 32px;

// 时间选择器
$time-picker-item-height: 32px;

// 级联选择器
$cascader-menu-width: 180px;

// 自动完成
$autocomplete-item-height: 34px;

// 提及
$mention-item-height: 32px;

// 锚点
$anchor-link-padding: 8px 16px;

// 回到顶部
$backtop-size: 48px;
$backtop-bg: rgba($gray-900, 0.7);

// 图片
$image-placeholder-bg: $gray-100;
$image-placeholder-color: $text-muted-color;

// 骨架屏
$skeleton-color: $gray-200;
$skeleton-active-color: $gray-300;

// 空状态
$empty-color: $text-muted-color;
$empty-img-size: 64px;

// 结果页
$result-icon-size: 72px;
$result-title-color: $text-color;
$result-subtitle-color: $text-secondary-color;