<template>
  <PageLayout
    title="用户管理"
    subtitle="管理平台所有用户信息"
    icon="el-icon-user"
    :stats="userStats"
    :loading="loading"
  >
    <template #actions>
      <el-button class="modern-btn success" @click="handleCreate">
        <i class="el-icon-plus"></i>
        添加用户
      </el-button>
      <el-button class="modern-btn secondary" @click="handleExport">
        <i class="el-icon-download"></i>
        导出数据
      </el-button>
    </template>

    <!-- 数据表格 -->
    <DataTable
      :data="list"
      :columns="tableColumns"
      :loading="listLoading"
      :pagination="pagination"
      :search-config="searchConfig"
      :batch-actions="batchActions"
      @search="handleSearch"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
      @batch-action="handleBatchAction"
      @row-action="handleRowAction"
      class="user-table"
    />



    <!-- 用户编辑对话框 -->
    <UserDialog
      v-model="dialogVisible"
      :user-data="currentUser"
      @success="handleDialogSuccess"
    />

    <!-- 余额调整对话框 -->
    <BalanceDialog
      v-model="balanceDialogVisible"
      :user-data="currentUser"
      @success="handleBalanceSuccess"
    />

    <!-- 用户详情抽屉 -->
    <UserDetailDrawer
      v-model="detailDrawerVisible"
      :user-id="currentUserId"
    />
  </PageLayout>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PageLayout from '@/components/layout/PageLayout.vue'
import DataTable from '@/components/common/DataTable.vue'
import UserDialog from './components/UserDialog.vue'
import BalanceDialog from './components/BalanceDialog.vue'
import UserDetailDrawer from './components/UserDetailDrawer.vue'
import { getUserList, deleteUser, updateUserStatus, exportUsers, getUserStats } from '@/api/user'
import { formatDate } from '@/utils/format'

// 响应式数据
const loading = ref(false)
const list = ref([])
const listLoading = ref(false)
const dialogVisible = ref(false)
const balanceDialogVisible = ref(false)
const detailDrawerVisible = ref(false)
const currentUser = ref({})
const currentUserId = ref(null)

// 统计数据
const stats = ref({
  total_users: 1234,
  active_users: 1156,
  distributors: 89,
  agents: 23
})

// 分页数据
const pagination = ref({
  current: 1,
  size: 20,
  total: 0
})

// 计算属性
const userStats = computed(() => [
  {
    icon: 'el-icon-user',
    label: '总用户数',
    value: stats.value.total_users,
    color: 'primary',
    change: '+12.5%',
    changeType: 'increase'
  },
  {
    icon: 'el-icon-user-solid',
    label: '活跃用户',
    value: stats.value.active_users,
    color: 'success',
    change: '+8.3%',
    changeType: 'increase'
  },
  {
    icon: 'el-icon-s-custom',
    label: '分销员',
    value: stats.value.distributors,
    color: 'warning',
    change: '+15.2%',
    changeType: 'increase'
  },
  {
    icon: 'el-icon-medal',
    label: '代理商',
    value: stats.value.agents,
    color: 'info',
    change: '****%',
    changeType: 'increase'
  }
])

// 表格列配置
const tableColumns = ref([
  {
    type: 'selection',
    width: 55
  },
  {
    prop: 'id',
    label: '用户ID',
    width: 80
  },
  {
    prop: 'avatar',
    label: '头像',
    width: 80,
    type: 'image',
    fallback: (row) => row.username?.charAt(0)?.toUpperCase() || 'U'
  },
  {
    prop: 'username',
    label: '用户名',
    width: 120
  },
  {
    prop: 'email',
    label: '邮箱',
    width: 180
  },
  {
    prop: 'phone',
    label: '手机号',
    width: 120
  },
  {
    prop: 'role',
    label: '角色',
    width: 100,
    type: 'tag',
    tagMap: {
      user: { text: '普通用户', type: 'info' },
      distributor: { text: '分销员', type: 'success' },
      agent: { text: '代理商', type: 'warning' },
      admin: { text: '管理员', type: 'danger' }
    }
  },
  {
    prop: 'balance',
    label: '余额',
    width: 100,
    type: 'currency'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    type: 'tag',
    tagMap: {
      active: { text: '正常', type: 'success' },
      disabled: { text: '禁用', type: 'danger' },
      pending: { text: '待审核', type: 'warning' }
    }
  },
  {
    prop: 'created_at',
    label: '注册时间',
    width: 160,
    type: 'date'
  },
  {
    prop: 'last_login',
    label: '最后登录',
    width: 160,
    type: 'date'
  },
  {
    type: 'actions',
    label: '操作',
    width: 200,
    fixed: 'right',
    actions: [
      {
        key: 'edit',
        label: '编辑',
        type: 'primary',
        size: 'small'
      },
      {
        key: 'balance',
        label: '调整余额',
        type: 'warning',
        size: 'small'
      },
      {
        key: 'more',
        label: '更多',
        type: 'info',
        size: 'small',
        dropdown: true,
        dropdownItems: [
          { key: 'view', label: '查看详情' },
          { key: 'reset', label: '重置密码' },
          { key: 'toggle', label: '切换状态' },
          { key: 'delete', label: '删除用户', divided: true, danger: true }
        ]
      }
    ]
  }
])

// 搜索配置
const searchConfig = ref({
  fields: [
    {
      key: 'keyword',
      type: 'input',
      placeholder: '搜索用户名、邮箱、手机号',
      width: 200
    },
    {
      key: 'role',
      type: 'select',
      placeholder: '用户角色',
      width: 120,
      options: [
        { label: '全部', value: '' },
        { label: '普通用户', value: 'user' },
        { label: '分销员', value: 'distributor' },
        { label: '代理商', value: 'agent' },
        { label: '管理员', value: 'admin' }
      ]
    },
    {
      key: 'status',
      type: 'select',
      placeholder: '用户状态',
      width: 120,
      options: [
        { label: '全部', value: '' },
        { label: '正常', value: 'active' },
        { label: '禁用', value: 'disabled' },
        { label: '待审核', value: 'pending' }
      ]
    }
  ]
})

// 批量操作配置
const batchActions = ref([
  {
    key: 'enable',
    label: '批量启用',
    type: 'success',
    icon: 'el-icon-check'
  },
  {
    key: 'disable',
    label: '批量禁用',
    type: 'warning',
    icon: 'el-icon-close'
  },
  {
    key: 'delete',
    label: '批量删除',
    type: 'danger',
    icon: 'el-icon-delete'
  }
])

// 获取用户列表
const getList = async (params = {}) => {
  listLoading.value = true
  try {
    const queryParams = {
      page: pagination.value.current,
      limit: pagination.value.size,
      ...params
    }
    const { data } = await getUserList(queryParams)
    list.value = data.list || []
    pagination.value.total = data.total || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    listLoading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const { data } = await getUserStats()
    stats.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 搜索处理
const handleSearch = (searchParams) => {
  pagination.value.current = 1
  getList(searchParams)
}

// 分页处理
const handlePageChange = (page) => {
  pagination.value.current = page
  getList()
}

const handleSizeChange = (size) => {
  pagination.value.size = size
  pagination.value.current = 1
  getList()
}

// 行操作处理
const handleRowAction = (action, row) => {
  switch (action.key) {
    case 'edit':
      handleEdit(row)
      break
    case 'balance':
      handleBalance(row)
      break
    case 'view':
      handleViewDetail(row.id)
      break
    case 'reset':
      handleResetPassword(row.id)
      break
    case 'toggle':
      handleToggleStatus(row)
      break
    case 'delete':
      handleDelete(row.id)
      break
  }
}

// 批量操作处理
const handleBatchAction = (action, selectedRows) => {
  switch (action.key) {
    case 'enable':
      handleBatchStatus(selectedRows, 'active')
      break
    case 'disable':
      handleBatchStatus(selectedRows, 'disabled')
      break
    case 'delete':
      handleBatchDelete(selectedRows)
      break
  }
}

// 创建用户
const handleCreate = () => {
  currentUser.value = {}
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  currentUser.value = { ...row }
  dialogVisible.value = true
}

// 调整余额
const handleBalance = (row) => {
  currentUser.value = { ...row }
  balanceDialogVisible.value = true
}

// 查看详情
const handleViewDetail = (userId) => {
  currentUserId.value = userId
  detailDrawerVisible.value = true
}

// 重置密码
const handleResetPassword = async (userId) => {
  try {
    await ElMessageBox.confirm('确定要重置该用户的密码吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 调用重置密码API
    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置密码失败')
    }
  }
}

// 切换用户状态
const handleToggleStatus = async (row) => {
  const newStatus = row.status === 'active' ? 'disabled' : 'active'
  const action = newStatus === 'disabled' ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(`确定要${action}该用户吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateUserStatus(row.id, newStatus)
    ElMessage.success(`${action}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 删除用户
const handleDelete = async (userId) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })

    await deleteUser(userId)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量状态更新
const handleBatchStatus = async (selectedRows, status) => {
  const action = status === 'disabled' ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(`确定要批量${action}选中的用户吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const userIds = selectedRows.map(row => row.id)
    // 这里应该调用批量更新API
    ElMessage.success(`批量${action}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async (selectedRows) => {
  try {
    await ElMessageBox.confirm('确定要批量删除选中的用户吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })

    const userIds = selectedRows.map(row => row.id)
    // 这里应该调用批量删除API
    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 导出数据
const handleExport = async () => {
  try {
    // 这里应该调用导出API
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  getList()
  getStats()
}

const handleBalanceSuccess = () => {
  getList()
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.user-table {
  margin-top: var(--spacing-xl);
}
</style>