import{_ as l,u as a}from"./index-D2bI4m-v.js";/* empty css                 *//* empty css                     *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    *//* empty css                    *//* empty css                *//* empty css               */import{g as e,a as t,c as s}from"./anti-block-BQ2PwvXK.js";import{Q as i,as as o,b2 as n,U as c,o as r,b4 as d,b3 as u,b6 as p,b7 as m,b8 as _,aZ as v,bc as h,bd as f,aH as y,aW as b,aV as g,bT as k,au as w,bR as x,c1 as j}from"./element-plus-DcSKpKA8.js";import{r as C,c as V,e as z,k as D,l as A,t as S,E as Q,y as I,B as P,z as Z,D as q,u as H}from"./vue-vendor-DGsK9sC4.js";import"./utils-4VKArNEK.js";const T={class:"anti-block-dashboard"},U={class:"page-header"},$={class:"quick-actions"},L={class:"stats-overview"},M={class:"stat-card"},B={class:"stat-content"},N={class:"stat-number"},E={class:"stat-detail"},F={class:"text-success"},R={class:"text-warning"},W={class:"text-danger"},G={class:"stat-card"},J={class:"stat-content"},K={class:"stat-number"},O={class:"stat-detail"},X={class:"text-success"},Y={class:"text-info"},ll={class:"stat-card"},al={class:"stat-content"},el={class:"stat-number"},tl={class:"stat-card"},sl={class:"stat-content"},il={class:"stat-number"},ol={class:"stat-detail"},nl={class:"help-section"},cl={slot:"header",class:"card-header"},rl={class:"help-item"},dl={class:"help-content"},ul={class:"help-item"},pl={class:"help-content"},ml={class:"help-item"},_l={class:"help-content"},vl={class:"domain-status"},hl={slot:"header",class:"card-header"},fl={"slot-scope":"scope"},yl={class:"domain-text"},bl={"slot-scope":"scope"},gl={"slot-scope":"scope"},kl={"slot-scope":"scope"},wl={"slot-scope":"scope"},xl={"slot-scope":"scope"},jl={slot:"footer"},Cl={class:"help-content-detail"},Vl={class:"monitor-content"},zl=l({__name:"Dashboard",setup(l){const zl=a(),Dl=C({domain_stats:{total:0,active:0,abnormal:0,blocked:0},link_stats:{total:0,active:0,today_created:0,today_clicks:0}}),Al=C([]),Sl=C(!1),Ql=C(!1),Il=C(!1),Pl=C(!1),Zl=C(!1),ql=C({domain:"",domain_type:"redirect",priority:80,remarks:""}),Hl={domain:[{required:!0,message:"请输入域名",trigger:"blur"},{pattern:/^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/,message:"请输入有效的域名格式",trigger:"blur"}],domain_type:[{required:!0,message:"请选择域名类型",trigger:"change"}]},Tl=[{type:"短链接域名",count:"3-5个",priority:"70-100",purpose:"生成防红短链接"},{type:"中转页域名",count:"2-3个",priority:"80-90",purpose:"微信/QQ跳转中转"},{type:"API服务域名",count:"1-2个",priority:"90-100",purpose:"API接口访问"}],Ul=[{condition:"HTTP访问失败",penalty:"-20分",description:"域名无法正常访问"},{condition:"DNS解析失败",penalty:"-20分",description:"域名解析异常"},{condition:"微信检测封禁",penalty:"-50分",description:"被微信平台封禁"},{condition:"响应时间>5秒",penalty:"-10分",description:"访问速度过慢"}],$l=V(()=>"admin"===zl.userInfo?.role),Ll=V(()=>0===Dl.value.domain_stats.total?0:Math.round(Dl.value.domain_stats.active/Dl.value.domain_stats.total*100)),Ml=V(()=>{const l=Ll.value;return l>=90?"优秀":l>=80?"良好":l>=60?"一般":"需要关注"}),Bl=V(()=>{const l=Ll.value;return l>=90?"text-success":l>=80?"text-primary":l>=60?"text-warning":"text-danger"});z(()=>{Nl(),El()});const Nl=async()=>{try{const{data:l}=await e();Dl.value=l}catch(l){i.error("加载统计数据失败")}},El=async()=>{Al.value=[{domain:"short1.linkhub.pro",domain_type:"redirect",health_score:95,status:1,use_count:1250,last_check_time:new Date},{domain:"short2.linkhub.pro",domain_type:"redirect",health_score:88,status:1,use_count:856,last_check_time:new Date},{domain:"landing.linkhub.pro",domain_type:"landing",health_score:100,status:1,use_count:432,last_check_time:new Date}]},Fl=async()=>{Sl.value=!0;try{await s({limit:20}),i.success("域名检测完成"),Nl(),El()}catch(l){i.error("域名检测失败")}finally{Sl.value=!1}},Rl=()=>{Ql.value=!0,ql.value={domain:"",domain_type:"redirect",priority:80,remarks:""}},Wl=()=>{El()},Gl=()=>{Il.value=!0},Jl=()=>{Pl.value=!0},Kl=l=>({redirect:"短链接",landing:"中转页",api:"API服务"}[l]||l),Ol=l=>({1:"正常",2:"异常",3:"封禁",4:"维护"}[l]||"未知");return(l,a)=>{const e=o,s=n,C=d,V=u,z=m,zl=_,Nl=v,El=p,Xl=y,Yl=f,la=g,aa=b,ea=k,ta=h,sa=w,ia=x;return A(),D("div",T,[S("div",U,[a[13]||(a[13]=S("div",{class:"page-title"},[S("h1",null,"🛡️ 防红系统管理"),S("p",{class:"page-desc"},"智能域名管理，自动检测切换，确保推广链接稳定可用")],-1)),S("div",$,[Q(e,{type:"primary",onClick:Fl,loading:Sl.value},{default:Z(()=>a[10]||(a[10]=[S("i",{class:"el-icon-refresh"},null,-1),q(" 立即检测 ",-1)])),_:1,__:[10]},8,["loading"]),$l.value?(A(),I(e,{key:0,type:"success",onClick:Rl},{default:Z(()=>a[11]||(a[11]=[S("i",{class:"el-icon-plus"},null,-1),q(" 添加域名 ",-1)])),_:1,__:[11]})):P("",!0),Q(e,{type:"info",onClick:Gl},{default:Z(()=>a[12]||(a[12]=[S("i",{class:"el-icon-question"},null,-1),q(" 使用说明 ",-1)])),_:1,__:[12]})])]),S("div",L,[Q(C,{gutter:20},{default:Z(()=>[Q(s,{span:6},{default:Z(()=>[S("div",M,[a[15]||(a[15]=S("div",{class:"stat-icon domain-icon"},[S("i",{class:"el-icon-connection"})],-1)),S("div",B,[S("div",N,c(Dl.value.domain_stats.total),1),a[14]||(a[14]=S("div",{class:"stat-label"},"总域名数",-1)),S("div",E,[S("span",F,c(Dl.value.domain_stats.active)+" 正常",1),S("span",R,c(Dl.value.domain_stats.abnormal)+" 异常",1),S("span",W,c(Dl.value.domain_stats.blocked)+" 封禁",1)])])])]),_:1}),Q(s,{span:6},{default:Z(()=>[S("div",G,[a[17]||(a[17]=S("div",{class:"stat-icon link-icon"},[S("i",{class:"el-icon-link"})],-1)),S("div",J,[S("div",K,c(Dl.value.link_stats.total),1),a[16]||(a[16]=S("div",{class:"stat-label"},"短链接数",-1)),S("div",O,[S("span",X,c(Dl.value.link_stats.active)+" 激活",1),S("span",Y,"今日新增 "+c(Dl.value.link_stats.today_created),1)])])])]),_:1}),Q(s,{span:6},{default:Z(()=>[S("div",ll,[a[20]||(a[20]=S("div",{class:"stat-icon click-icon"},[S("i",{class:"el-icon-view"})],-1)),S("div",al,[S("div",el,c(Dl.value.link_stats.today_clicks),1),a[18]||(a[18]=S("div",{class:"stat-label"},"今日访问",-1)),a[19]||(a[19]=S("div",{class:"stat-detail"},[S("span",{class:"text-primary"},"实时统计")],-1))])])]),_:1}),Q(s,{span:6},{default:Z(()=>[S("div",tl,[a[22]||(a[22]=S("div",{class:"stat-icon health-icon"},[S("i",{class:"el-icon-success"})],-1)),S("div",sl,[S("div",il,c(Ll.value)+"%",1),a[21]||(a[21]=S("div",{class:"stat-label"},"系统健康度",-1)),S("div",ol,[S("span",{class:r(Bl.value)},c(Ml.value),3)])])])]),_:1})]),_:1})]),S("div",nl,[Q(V,{class:"help-card"},{default:Z(()=>[S("div",cl,[a[24]||(a[24]=S("span",null,"📖 快速使用指南",-1)),Q(e,{type:"text",onClick:Gl},{default:Z(()=>a[23]||(a[23]=[q("查看详细说明",-1)])),_:1,__:[23]})]),Q(C,{gutter:20},{default:Z(()=>[Q(s,{span:8},{default:Z(()=>[S("div",rl,[a[28]||(a[28]=S("div",{class:"help-step"},"1",-1)),S("div",dl,[a[26]||(a[26]=S("h4",null,"配置域名池",-1)),a[27]||(a[27]=S("p",null,"添加多个备用域名，系统会自动选择最佳域名生成短链接",-1)),Q(e,{type:"text",onClick:a[0]||(a[0]=a=>l.$router.push("/anti-block/domains"))},{default:Z(()=>a[25]||(a[25]=[q(" 管理域名池 → ",-1)])),_:1,__:[25]})])])]),_:1}),Q(s,{span:8},{default:Z(()=>[S("div",ul,[a[32]||(a[32]=S("div",{class:"help-step"},"2",-1)),S("div",pl,[a[30]||(a[30]=S("h4",null,"自动生成短链接",-1)),a[31]||(a[31]=S("p",null,"分销员推广链接将自动使用防红短链接，无需手动操作",-1)),Q(e,{type:"text",onClick:a[1]||(a[1]=a=>l.$router.push("/anti-block/short-links"))},{default:Z(()=>a[29]||(a[29]=[q(" 查看短链接 → ",-1)])),_:1,__:[29]})])])]),_:1}),Q(s,{span:8},{default:Z(()=>[S("div",ml,[a[36]||(a[36]=S("div",{class:"help-step"},"3",-1)),S("div",_l,[a[34]||(a[34]=S("h4",null,"监控和维护",-1)),a[35]||(a[35]=S("p",null,"系统每5分钟自动检测，异常域名自动切换，无需人工干预",-1)),Q(e,{type:"text",onClick:Jl},{default:Z(()=>a[33]||(a[33]=[q(" 查看监控 → ",-1)])),_:1,__:[33]})])])]),_:1})]),_:1})]),_:1})]),S("div",vl,[Q(V,null,{default:Z(()=>[S("div",hl,[a[39]||(a[39]=S("span",null,"🌐 域名状态监控",-1)),S("div",null,[Q(e,{type:"text",onClick:Wl},{default:Z(()=>a[37]||(a[37]=[q("刷新",-1)])),_:1,__:[37]}),Q(e,{type:"text",onClick:a[2]||(a[2]=a=>l.$router.push("/anti-block/domains"))},{default:Z(()=>a[38]||(a[38]=[q(" 查看全部 → ",-1)])),_:1,__:[38]})])]),Q(El,{data:Al.value,style:{width:"100%"}},{default:Z(()=>[Q(z,{prop:"domain",label:"域名",width:"200"},{default:Z(()=>[S("template",fl,[S("span",yl,c(l.scope.row.domain),1)])]),_:1}),Q(z,{prop:"domain_type",label:"类型",width:"100"},{default:Z(()=>{return[S("template",bl,[Q(zl,{size:"small",type:(a=l.scope.row.domain_type,{redirect:"primary",landing:"success",api:"warning"}[a]||"")},{default:Z(()=>[q(c(Kl(l.scope.row.domain_type)),1)]),_:1},8,["type"])])];var a}),_:1}),Q(z,{prop:"health_score",label:"健康度",width:"120"},{default:Z(()=>{return[S("template",gl,[Q(Nl,{percentage:l.scope.row.health_score,color:(a=l.scope.row.health_score,a>=90?"#67c23a":a>=80?"#409eff":a>=60?"#e6a23c":"#f56c6c"),"stroke-width":8},null,8,["percentage","color"])])];var a}),_:1}),Q(z,{prop:"status",label:"状态",width:"100"},{default:Z(()=>{return[S("template",kl,[Q(zl,{type:(a=l.scope.row.status,{1:"success",2:"warning",3:"danger",4:"info"}[a]||""),size:"small"},{default:Z(()=>[q(c(Ol(l.scope.row.status)),1)]),_:1},8,["type"])])];var a}),_:1}),Q(z,{prop:"use_count",label:"使用次数",width:"100"}),Q(z,{prop:"last_check_time",label:"最后检测",width:"160"},{default:Z(()=>{return[S("template",wl,[S("span",null,c((a=l.scope.row.last_check_time,a?j(a).format("MM-DD HH:mm"):"-")),1)])];var a}),_:1}),$l.value?(A(),I(z,{key:0,label:"操作",width:"150"},{default:Z(()=>[S("template",xl,[Q(e,{type:"text",size:"small",onClick:a[3]||(a[3]=a=>(async l=>{l.checking=!0;try{i.success(`${l.domain} 检测完成`)}catch(a){i.error("检测失败")}finally{l.checking=!1}})(l.scope.row)),loading:l.scope.row.checking},{default:Z(()=>a[40]||(a[40]=[q(" 检测 ",-1)])),_:1,__:[40]},8,["loading"]),Q(e,{type:"text",size:"small",onClick:a[4]||(a[4]=a=>{l.scope.row})},{default:Z(()=>a[41]||(a[41]=[q(" 编辑 ",-1)])),_:1,__:[41]})])]),_:1})):P("",!0)]),_:1},8,["data"])]),_:1})]),Q(sa,{title:"添加域名",visible:Ql.value,width:"500px"},{default:Z(()=>[Q(ta,{model:ql.value,rules:Hl,ref_key:"domainForm",ref:ql,"label-width":"100px"},{default:Z(()=>[Q(Yl,{label:"域名",prop:"domain"},{default:Z(()=>[Q(Xl,{modelValue:ql.value.domain,"onUpdate:modelValue":a[5]||(a[5]=l=>ql.value.domain=l),placeholder:"例如: short.example.com"},null,8,["modelValue"]),a[42]||(a[42]=S("div",{class:"form-tip"}," ⚠️ 请确保域名已正确解析到服务器，且已配置SSL证书 ",-1))]),_:1,__:[42]}),Q(Yl,{label:"域名类型",prop:"domain_type"},{default:Z(()=>[Q(aa,{modelValue:ql.value.domain_type,"onUpdate:modelValue":a[6]||(a[6]=l=>ql.value.domain_type=l),placeholder:"选择域名类型"},{default:Z(()=>[Q(la,{label:"短链接域名",value:"redirect"}),Q(la,{label:"中转页域名",value:"landing"}),Q(la,{label:"API服务域名",value:"api"})]),_:1},8,["modelValue"]),a[43]||(a[43]=S("div",{class:"form-tip"}," 📝 建议：短链接域名用于生成短链接，中转页域名用于微信防红跳转 ",-1))]),_:1,__:[43]}),Q(Yl,{label:"优先级",prop:"priority"},{default:Z(()=>[Q(ea,{modelValue:ql.value.priority,"onUpdate:modelValue":a[7]||(a[7]=l=>ql.value.priority=l),min:0,max:100,"show-input":""},null,8,["modelValue"]),a[44]||(a[44]=S("div",{class:"form-tip"}," 💡 优先级越高，越优先使用。建议主域名设置90-100，备用域名设置60-80 ",-1))]),_:1,__:[44]}),Q(Yl,{label:"备注"},{default:Z(()=>[Q(Xl,{modelValue:ql.value.remarks,"onUpdate:modelValue":a[8]||(a[8]=l=>ql.value.remarks=l),type:"textarea",rows:"2",placeholder:"域名用途说明"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),S("div",jl,[Q(e,{onClick:a[9]||(a[9]=l=>Ql.value=!1)},{default:Z(()=>a[45]||(a[45]=[q("取消",-1)])),_:1,__:[45]}),Q(e,{type:"primary",onClick:H(t),loading:Zl.value},{default:Z(()=>a[46]||(a[46]=[q("添加域名",-1)])),_:1,__:[46]},8,["onClick","loading"])])]),_:1},8,["visible"]),Q(sa,{title:"防红系统使用说明",visible:Il.value,width:"800px"},{default:Z(()=>[S("div",Cl,[a[48]||(a[48]=S("h3",null,"🛡️ 什么是防红系统？",-1)),a[49]||(a[49]=S("p",null,"防红系统是一套智能域名管理和短链接生成系统，专门用于防止推广链接被微信、QQ等平台检测和封禁。",-1)),a[50]||(a[50]=S("h3",null,"🚀 主要功能特性",-1)),a[51]||(a[51]=S("ul",null,[S("li",null,[S("strong",null,"智能域名轮换"),q("：自动选择最佳域名生成短链接")]),S("li",null,[S("strong",null,"实时健康检测"),q("：每5分钟检测域名状态，发现异常立即处理")]),S("li",null,[S("strong",null,"自动切换机制"),q("：域名被封时自动切换到备用域名")]),S("li",null,[S("strong",null,"中转页面防护"),q("：微信/QQ访问自动跳转中转页面")]),S("li",null,[S("strong",null,"详细访问统计"),q("：完整的点击数据和来源分析")])],-1)),a[52]||(a[52]=S("h3",null,"⚙️ 配置步骤",-1)),a[53]||(a[53]=S("ol",null,[S("li",null,[S("strong",null,"准备域名"),q("：至少准备3-5个域名，确保已解析和配置SSL")]),S("li",null,[S("strong",null,"添加到域名池"),q("：在系统中添加域名，设置优先级")]),S("li",null,[S("strong",null,"设置定时任务"),q("：确保服务器crontab已配置域名检测任务")]),S("li",null,[S("strong",null,"测试功能"),q("：生成测试短链接，验证访问和跳转正常")])],-1)),a[54]||(a[54]=S("h3",null,"📋 域名配置建议",-1)),Q(El,{data:Tl,style:{margin:"10px 0"}},{default:Z(()=>[Q(z,{prop:"type",label:"域名类型",width:"120"}),Q(z,{prop:"count",label:"建议数量",width:"100"}),Q(z,{prop:"priority",label:"优先级范围",width:"120"}),Q(z,{prop:"purpose",label:"主要用途"})]),_:1}),a[55]||(a[55]=S("h3",null,"⚠️ 注意事项",-1)),Q(ia,{type:"warning",closable:!1,style:{margin:"10px 0"}},{default:Z(()=>a[47]||(a[47]=[S("ul",{style:{margin:"0","padding-left":"20px"}},[S("li",null,"域名必须已备案并正确解析到服务器"),S("li",null,"建议使用不同注册商、不同后缀的域名"),S("li",null,"避免使用包含敏感词的域名"),S("li",null,"定期检查域名到期时间，及时续费")],-1)])),_:1,__:[47]}),a[56]||(a[56]=S("h3",null,"🔧 定时任务配置",-1)),a[57]||(a[57]=S("div",{class:"code-block"},[S("p",null,"在服务器上添加以下crontab任务："),S("pre",null,"# 每5分钟检测域名状态\n*/5 * * * * cd /项目路径 && php artisan domains:check >/dev/null 2>&1\n\n# 每小时全面检测\n0 * * * * cd /项目路径 && php artisan domains:check --force >/dev/null 2>&1\n          ")],-1))])]),_:1},8,["visible"]),Q(sa,{title:"系统监控状态",visible:Pl.value,width:"600px"},{default:Z(()=>[S("div",Vl,[a[58]||(a[58]=S("h4",null,"🔍 检测机制",-1)),a[59]||(a[59]=S("ul",null,[S("li",null,[S("strong",null,"HTTP状态检测"),q("：检查域名是否能正常访问")]),S("li",null,[S("strong",null,"DNS解析检测"),q("：验证域名解析是否正常")]),S("li",null,[S("strong",null,"微信平台检测"),q("：模拟微信访问，检测是否被封")]),S("li",null,[S("strong",null,"SSL证书检测"),q("：检查证书有效性和到期时间")])],-1)),a[60]||(a[60]=S("h4",null,"⚡ 自动处理流程",-1)),a[61]||(a[61]=S("div",{class:"process-flow"},[S("div",{class:"flow-item"},"检测异常"),S("div",{class:"flow-arrow"},"→"),S("div",{class:"flow-item"},"标记状态"),S("div",{class:"flow-arrow"},"→"),S("div",{class:"flow-item"},"切换域名"),S("div",{class:"flow-arrow"},"→"),S("div",{class:"flow-item"},"发送告警")],-1)),a[62]||(a[62]=S("h4",null,"📊 健康评分规则",-1)),Q(El,{data:Ul,size:"small"},{default:Z(()=>[Q(z,{prop:"condition",label:"检测条件",width:"150"}),Q(z,{prop:"penalty",label:"扣分",width:"80"}),Q(z,{prop:"description",label:"说明"})]),_:1})])]),_:1},8,["visible"])])}}},[["__scopeId","data-v-0df3a171"]]);export{zl as default};
