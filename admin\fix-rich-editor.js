// 富文本编辑器修复脚本
// 在浏览器控制台中运行此脚本来检查和修复富文本编辑器显示问题

console.log('🔧 开始检查富文本编辑器状态...');

// 检查富文本编辑器组件是否存在
function checkRichTextEditor() {
  const editors = document.querySelectorAll('.rich-text-editor');
  console.log(`📝 找到 ${editors.length} 个富文本编辑器`);
  
  editors.forEach((editor, index) => {
    console.log(`📋 编辑器 ${index + 1}:`);
    
    // 检查工具栏
    const toolbar = editor.querySelector('.editor-toolbar');
    if (toolbar) {
      console.log('  ✅ 工具栏存在');
      console.log(`  📊 工具栏显示状态: ${getComputedStyle(toolbar).display}`);
      console.log(`  👁️ 工具栏可见性: ${getComputedStyle(toolbar).visibility}`);
    } else {
      console.log('  ❌ 工具栏不存在');
    }
    
    // 检查编辑区域
    const content = editor.querySelector('.editor-content');
    if (content) {
      console.log('  ✅ 编辑区域存在');
      console.log(`  📏 编辑区域高度: ${getComputedStyle(content).height}`);
      console.log(`  🎨 编辑区域背景: ${getComputedStyle(content).backgroundColor}`);
    } else {
      console.log('  ❌ 编辑区域不存在');
    }
    
    // 检查按钮
    const buttons = editor.querySelectorAll('.el-button');
    console.log(`  🔘 找到 ${buttons.length} 个按钮`);
  });
}

// 修复富文本编辑器显示问题
function fixRichTextEditor() {
  console.log('🔨 开始修复富文本编辑器...');
  
  const editors = document.querySelectorAll('.rich-text-editor');
  
  editors.forEach((editor, index) => {
    console.log(`🔧 修复编辑器 ${index + 1}...`);
    
    // 确保工具栏显示
    const toolbar = editor.querySelector('.editor-toolbar');
    if (toolbar) {
      toolbar.style.display = 'flex';
      toolbar.style.visibility = 'visible';
      toolbar.style.opacity = '1';
      console.log('  ✅ 工具栏显示已修复');
    }
    
    // 确保编辑区域正确显示
    const content = editor.querySelector('.editor-content');
    if (content) {
      content.style.minHeight = '200px';
      content.style.padding = '12px';
      content.style.border = 'none';
      content.style.outline = 'none';
      console.log('  ✅ 编辑区域样式已修复');
    }
    
    // 确保整个编辑器容器样式正确
    editor.style.border = '1px solid #dcdfe6';
    editor.style.borderRadius = '4px';
    editor.style.backgroundColor = '#fff';
    editor.style.width = '100%';
    
    console.log(`  ✅ 编辑器 ${index + 1} 修复完成`);
  });
  
  console.log('🎉 富文本编辑器修复完成！');
}

// 添加样式修复
function addFixStyles() {
  console.log('🎨 添加修复样式...');
  
  const style = document.createElement('style');
  style.textContent = `
    .rich-text-editor {
      border: 1px solid #dcdfe6 !important;
      border-radius: 4px !important;
      background: #fff !important;
      width: 100% !important;
    }
    
    .rich-text-editor .editor-toolbar {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      padding: 8px 12px !important;
      border-bottom: 1px solid #e4e7ed !important;
      background: #f5f7fa !important;
      gap: 8px !important;
      flex-wrap: wrap !important;
    }
    
    .rich-text-editor .editor-content {
      min-height: 200px !important;
      padding: 12px !important;
      outline: none !important;
      line-height: 1.6 !important;
      font-size: 14px !important;
      color: #606266 !important;
    }
    
    .rich-text-editor .editor-footer {
      padding: 8px 12px !important;
      border-top: 1px solid #e4e7ed !important;
      background: #f5f7fa !important;
      display: flex !important;
      justify-content: flex-end !important;
    }
    
    .el-tabs .rich-text-editor {
      margin-top: 8px !important;
    }
  `;
  
  document.head.appendChild(style);
  console.log('✅ 修复样式已添加');
}

// 主修复函数
function main() {
  console.log('🚀 富文本编辑器修复工具启动');
  console.log('=====================================');
  
  // 检查当前状态
  checkRichTextEditor();
  
  console.log('=====================================');
  
  // 添加修复样式
  addFixStyles();
  
  // 等待一下让样式生效
  setTimeout(() => {
    // 执行修复
    fixRichTextEditor();
    
    console.log('=====================================');
    console.log('🔍 修复后状态检查:');
    
    // 再次检查状态
    setTimeout(() => {
      checkRichTextEditor();
      console.log('=====================================');
      console.log('✨ 修复完成！请检查富文本编辑器是否正常显示');
      console.log('💡 如果问题仍然存在，请刷新页面后重新运行此脚本');
    }, 500);
  }, 100);
}

// 运行修复
main();

// 导出函数供手动调用
window.fixRichTextEditor = {
  check: checkRichTextEditor,
  fix: fixRichTextEditor,
  addStyles: addFixStyles,
  runAll: main
};

console.log('🔧 修复工具已加载，可以通过以下命令手动调用:');
console.log('  - window.fixRichTextEditor.check() // 检查状态');
console.log('  - window.fixRichTextEditor.fix() // 执行修复');
console.log('  - window.fixRichTextEditor.addStyles() // 添加样式');
console.log('  - window.fixRichTextEditor.runAll() // 运行完整修复');