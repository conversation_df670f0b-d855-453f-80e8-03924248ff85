<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserSegmentMember extends Model
{
    protected $fillable = [
        'segment_id',
        'user_id',
        'added_at',
        'score',
    ];

    protected $casts = [
        'added_at' => 'datetime',
        'score' => 'decimal:2',
    ];

    /**
     * 所属分群
     */
    public function segment()
    {
        return $this->belongsTo(UserSegment::class, 'segment_id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
} 