import{_ as l}from"./index-D2bI4m-v.js";/* empty css                         *//* empty css                 *//* empty css               *//* empty css                        *//* empty css                    *//* empty css                *//* empty css               */import{r as a,d as e,e as s,n as t,H as n,k as i,l as u,ag as c,t as d,E as o,z as r,D as p,u as v,F as f,Y as _}from"./vue-vendor-DGsK9sC4.js";import{p as m,T as h,aQ as g,as as b,aM as y,b2 as w,ai as x,U as k,bG as j,bW as C,_ as F,a6 as V,b4 as q,b3 as z,aY as P,a7 as O,a2 as A,a5 as E,a4 as L,o as R,b6 as U,b7 as Z,b8 as D,ap as K,al as N,bZ as Q,bw as S,by as W,bz as Y,bR as B,au as G,Q as H}from"./element-plus-DcSKpKA8.js";import{L as I}from"./LineChart-Ba008-uu.js";import{i as M}from"./echarts-DTArWCqr.js";import{g as T,e as X,a as J,b as $}from"./finance-Cskg9amr.js";import{f as ll}from"./format-3eU4VJ9V.js";import"./utils-4VKArNEK.js";import"./chart-Bup65vvO.js";import"./chunk-KZPPZA2C-BZQYgWVq.js";const al={__name:"PieChart",props:{data:{type:Object,required:!0},options:{type:Object,default:()=>({})},height:{type:Number,default:300}},setup(l){const c=l,d=a();let o=null;const r=()=>{if(!o||!c.data)return;const l=c.data.datasets?.[0];if(!l)return;const a=c.data.labels?.map((a,e)=>({name:a,value:l.data[e],itemStyle:{color:l.backgroundColor[e]}}))||[],e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:c.data.labels||[]},series:[{name:"数据",type:"pie",radius:"50%",data:a,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}],...c.options};o.setOption(e)},p=()=>{o&&o.resize()};return e(()=>c.data,()=>{t(()=>{r()})},{deep:!0}),e(()=>c.options,()=>{t(()=>{r()})},{deep:!0}),s(()=>{t(()=>{d.value&&(o=M(d.value),r()),window.addEventListener("resize",p)})}),n(()=>{o&&o.dispose(),window.removeEventListener("resize",p)}),(a,e)=>(u(),i("div",{ref_key:"chartRef",ref:d,style:m({height:l.height+"px"})},null,4))}},el={class:"app-container"},sl={class:"page-header"},tl={class:"header-actions"},nl={class:"stats-card"},il={class:"stats-icon primary"},ul={class:"stats-value"},cl={class:"stats-change positive"},dl={class:"stats-card"},ol={class:"stats-icon success"},rl={class:"stats-value"},pl={class:"stats-change positive"},vl={class:"stats-card"},fl={class:"stats-icon warning"},_l={class:"stats-value"},ml={class:"stats-change"},hl={class:"stats-card"},gl={class:"stats-icon danger"},bl={class:"stats-value"},yl={class:"stats-change positive"},wl={class:"card-header"},xl={class:"chart-controls"},kl={class:"chart-content"},jl={class:"chart-content"},Cl={class:"quick-actions"},Fl={class:"action-grid"},Vl={class:"card-header"},ql={class:"recent-transactions"},zl={class:"transaction-info"},Pl={class:"transaction-title"},Ol={class:"transaction-time"},Al={class:"card-header"},El={class:"money-text"},Ll={class:"money-text"},Rl={class:"money-text"},Ul={class:"money-text"},Zl={class:"money-text total"},Dl={class:"help-content"},Kl={class:"help-section"},Nl={class:"feature-item"},Ql={class:"feature-icon"},Sl={class:"feature-item"},Wl={class:"feature-icon"},Yl={class:"feature-item"},Bl={class:"feature-icon"},Gl={class:"feature-item"},Hl={class:"feature-icon"},Il={class:"feature-item"},Ml={class:"feature-icon"},Tl={class:"feature-item"},Xl={class:"feature-icon"},Jl={class:"help-section"},$l={class:"help-section"},la={class:"revenue-sources"},aa={class:"source-item"},ea={class:"source-icon",style:{background:"#1890ff"}},sa={class:"source-item"},ta={class:"source-icon",style:{background:"#52c41a"}},na={class:"source-item"},ia={class:"source-icon",style:{background:"#faad14"}},ua={class:"source-item"},ca={class:"source-icon",style:{background:"#f5222d"}},da={class:"help-section"},oa={class:"guide-content"},ra={class:"guide-content"},pa={class:"guide-content"},va={class:"guide-content"},fa={class:"help-section"},_a={class:"risk-alerts"},ma={class:"help-section"},ha=l({__name:"FinanceDashboard",setup(l){const e=c(),t=a({total_revenue:0,total_commission:0,pending_withdrawal:0,today_revenue:0,revenue_growth:0,commission_growth:0,today_growth:0,pending_count:0}),n=a("7d"),m=a([]),M=a([]),ha=a(!1),ga=a(["view-data"]),ba=a([]),ya=a([{metric:"总收入",color:"primary",description:"平台所有收入来源的总和",calculation:"群组收入 + 佣金收入 + 分站收入 + 其他收入",significance:"反映平台整体盈利能力和业务规模"},{metric:"总佣金",color:"success",description:"支付给分销商的佣金总额",calculation:"所有分销商佣金的累计金额",significance:"体现分销体系的活跃度和激励效果"},{metric:"待提现",color:"warning",description:"用户申请但尚未处理的提现金额",calculation:"所有待审核提现申请的金额总和",significance:"反映资金流动性需求和风险控制情况"},{metric:"今日收入",color:"info",description:"当日产生的收入总额",calculation:"当日所有收入来源的实时统计",significance:"监控日常运营状况和收入波动"}]),wa=[{label:"7天",value:"7d"},{label:"30天",value:"30d"},{label:"90天",value:"90d"},{label:"1年",value:"1y"}],xa=a({labels:[],datasets:[{label:"收入",data:[],borderColor:"#1890ff",backgroundColor:"rgba(24, 144, 255, 0.1)",fill:!0,tension:.4}]}),ka=a({labels:["群组收入","佣金收入","分站收入","其他收入"],datasets:[{data:[0,0,0,0],backgroundColor:["#1890ff","#52c41a","#faad14","#f5222d"]}]}),ja=a({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{callback:function(l){return"¥"+(l/1e3).toFixed(1)+"k"}}}}}),Ca=a({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}),Fa=async()=>{try{const{data:l}=await J();t.value=l.overview,M.value=l.details}catch(l){console.error("获取财务统计失败:",l),H.error("获取财务统计失败")}},Va=async()=>{try{const{data:l}=await T({period:n.value});xa.value=l.revenue,ka.value=l.source}catch(l){console.error("获取图表数据失败:",l)}},qa=async()=>{try{const{data:l}=await $({limit:10});m.value=l}catch(l){console.error("获取最新交易失败:",l)}},za=async()=>{await Promise.all([Fa(),Va(),qa()]),H.success("数据刷新成功")},Pa=()=>{e.push("/finance/transactions")},Oa=()=>{e.push("/finance/commission-logs")},Aa=()=>{e.push("/finance/withdraw")},Ea=async()=>{try{await X(),H.success("报表导出成功")}catch(l){H.error("导出失败")}},La=l=>l>=1e4?(l/1e4).toFixed(1)+"W":l>=1e3?(l/1e3).toFixed(1)+"K":l.toFixed(2);return s(()=>{za()}),(l,a)=>{const e=h,s=b,c=w,H=q,T=P,X=z,J=Z,$=U,Fa=D,qa=B,Ra=Y,Ua=W,Za=G;return u(),i("div",el,[d("div",sl,[a[6]||(a[6]=d("div",{class:"header-left"},[d("h2",null,"财务管理工作台"),d("p",{class:"page-description"},"全面掌握平台财务状况，实时监控收支情况，高效管理资金流转")],-1)),d("div",tl,[o(s,{type:"info",onClick:a[0]||(a[0]=l=>ha.value=!0)},{default:r(()=>[o(e,null,{default:r(()=>[o(v(g))]),_:1}),a[4]||(a[4]=p(" 功能说明 ",-1))]),_:1,__:[4]}),o(s,{type:"primary",onClick:za},{default:r(()=>[o(e,null,{default:r(()=>[o(v(y))]),_:1}),a[5]||(a[5]=p(" 刷新数据 ",-1))]),_:1,__:[5]})])]),o(H,{gutter:20,class:"stats-row"},{default:r(()=>[o(c,{span:6},{default:r(()=>[d("div",nl,[d("div",il,[o(e,null,{default:r(()=>[o(v(x))]),_:1})]),d("div",ul,"¥"+k(La(t.value.total_revenue)),1),a[7]||(a[7]=d("div",{class:"stats-label"},"总收入",-1)),d("div",cl,[o(e,null,{default:r(()=>[o(v(j))]),_:1}),p(" +"+k(t.value.revenue_growth)+"% ",1)])])]),_:1}),o(c,{span:6},{default:r(()=>[d("div",dl,[d("div",ol,[o(e,null,{default:r(()=>[o(v(C))]),_:1})]),d("div",rl,"¥"+k(La(t.value.total_commission)),1),a[8]||(a[8]=d("div",{class:"stats-label"},"总佣金",-1)),d("div",pl,[o(e,null,{default:r(()=>[o(v(j))]),_:1}),p(" +"+k(t.value.commission_growth)+"% ",1)])])]),_:1}),o(c,{span:6},{default:r(()=>[d("div",vl,[d("div",fl,[o(e,null,{default:r(()=>[o(v(F))]),_:1})]),d("div",_l,"¥"+k(La(t.value.pending_withdrawal)),1),a[9]||(a[9]=d("div",{class:"stats-label"},"待提现",-1)),d("div",ml,k(t.value.pending_count)+"笔申请 ",1)])]),_:1}),o(c,{span:6},{default:r(()=>[d("div",hl,[d("div",gl,[o(e,null,{default:r(()=>[o(v(V))]),_:1})]),d("div",bl,"¥"+k(La(t.value.today_revenue)),1),a[10]||(a[10]=d("div",{class:"stats-label"},"今日收入",-1)),d("div",yl,[o(e,null,{default:r(()=>[o(v(j))]),_:1}),p(" +"+k(t.value.today_growth)+"% ",1)])])]),_:1})]),_:1}),o(H,{gutter:20,class:"chart-section"},{default:r(()=>[o(c,{span:16},{default:r(()=>[o(X,null,{header:r(()=>[d("div",wl,[a[11]||(a[11]=d("h3",null,"收入趋势分析",-1)),d("div",xl,[o(T,null,{default:r(()=>[(u(),i(f,null,_(wa,l=>o(s,{key:l.value,type:n.value===l.value?"primary":"default",size:"small",onClick:a=>{n.value=l.value,Va()}},{default:r(()=>[p(k(l.label),1)]),_:2},1032,["type","onClick"])),64))]),_:1})])])]),default:r(()=>[d("div",kl,[o(I,{data:xa.value,options:ja.value,height:300},null,8,["data","options"])])]),_:1})]),_:1}),o(c,{span:8},{default:r(()=>[o(X,null,{header:r(()=>a[12]||(a[12]=[d("div",{class:"card-header"},[d("h3",null,"收入来源分布")],-1)])),default:r(()=>[d("div",jl,[o(al,{data:ka.value,options:Ca.value,height:300},null,8,["data","options"])])]),_:1})]),_:1})]),_:1}),o(H,{gutter:20,class:"action-section"},{default:r(()=>[o(c,{span:12},{default:r(()=>[o(X,null,{header:r(()=>a[13]||(a[13]=[d("div",{class:"card-header"},[d("h3",null,"快捷操作")],-1)])),default:r(()=>[d("div",Cl,[d("div",Fl,[d("div",{class:"action-item",onClick:Pa},[o(e,null,{default:r(()=>[o(v(O))]),_:1}),a[14]||(a[14]=d("span",null,"交易记录",-1))]),d("div",{class:"action-item",onClick:Oa},[o(e,null,{default:r(()=>[o(v(A))]),_:1}),a[15]||(a[15]=d("span",null,"佣金明细",-1))]),d("div",{class:"action-item",onClick:Aa},[o(e,null,{default:r(()=>[o(v(E))]),_:1}),a[16]||(a[16]=d("span",null,"提现管理",-1))]),d("div",{class:"action-item",onClick:Ea},[o(e,null,{default:r(()=>[o(v(L))]),_:1}),a[17]||(a[17]=d("span",null,"导出报表",-1))])])])]),_:1})]),_:1}),o(c,{span:12},{default:r(()=>[o(X,null,{header:r(()=>[d("div",Vl,[a[19]||(a[19]=d("h3",null,"最新交易",-1)),o(s,{type:"text",onClick:Pa},{default:r(()=>a[18]||(a[18]=[p("查看全部",-1)])),_:1,__:[18]})])]),default:r(()=>[d("div",ql,[(u(!0),i(f,null,_(m.value,l=>(u(),i("div",{key:l.id,class:"transaction-item"},[d("div",zl,[d("div",Pl,k(l.title),1),d("div",Ol,k(v(ll)(l.created_at)),1)]),d("div",{class:R(["transaction-amount","income"===l.type?"positive":"negative"])},k("income"===l.type?"+":"-")+"¥"+k(l.amount.toFixed(2)),3)]))),128))])]),_:1})]),_:1})]),_:1}),o(X,null,{header:r(()=>[d("div",Al,[a[21]||(a[21]=d("h3",null,"财务统计详情",-1)),d("div",null,[o(s,{type:"primary",size:"small",onClick:za},{default:r(()=>[o(e,null,{default:r(()=>[o(v(y))]),_:1}),a[20]||(a[20]=p(" 刷新数据 ",-1))]),_:1,__:[20]})])])]),default:r(()=>[o($,{data:M.value,border:""},{default:r(()=>[o(J,{label:"统计项目",prop:"name",width:"150"}),o(J,{label:"今日",width:"120"},{default:r(({row:l})=>[d("span",El,"¥"+k(l.today.toFixed(2)),1)]),_:1}),o(J,{label:"本周",width:"120"},{default:r(({row:l})=>[d("span",Ll,"¥"+k(l.week.toFixed(2)),1)]),_:1}),o(J,{label:"本月",width:"120"},{default:r(({row:l})=>[d("span",Rl,"¥"+k(l.month.toFixed(2)),1)]),_:1}),o(J,{label:"本年",width:"120"},{default:r(({row:l})=>[d("span",Ul,"¥"+k(l.year.toFixed(2)),1)]),_:1}),o(J,{label:"总计"},{default:r(({row:l})=>[d("span",Zl,"¥"+k(l.total.toFixed(2)),1)]),_:1}),o(J,{label:"增长率",width:"100"},{default:r(({row:l})=>[d("span",{class:R(l.growth>=0?"positive-growth":"negative-growth")},k(l.growth>=0?"+":"")+k(l.growth.toFixed(1))+"% ",3)]),_:1})]),_:1},8,["data"])]),_:1}),o(Za,{modelValue:ha.value,"onUpdate:modelValue":a[3]||(a[3]=l=>ha.value=l),title:"财务管理工作台功能说明",width:"1000px",class:"help-dialog"},{default:r(()=>[d("div",Dl,[a[56]||(a[56]=d("div",{class:"help-section"},[d("h3",null,"💰 功能概述"),d("p",null,"财务管理工作台是平台资金管理的核心系统，提供全面的财务数据统计、收支分析、提现管理等功能，帮助您实时掌握平台财务状况，做出明智的财务决策。")],-1)),d("div",Kl,[a[28]||(a[28]=d("h3",null,"🚀 核心功能模块",-1)),o(H,{gutter:20},{default:r(()=>[o(c,{span:8},{default:r(()=>[d("div",Nl,[d("div",Ql,[o(e,null,{default:r(()=>[o(v(V))]),_:1})]),a[22]||(a[22]=d("div",{class:"feature-content"},[d("h4",null,"财务概览"),d("p",null,"实时显示总收入、总佣金、待提现等关键财务指标")],-1))])]),_:1}),o(c,{span:8},{default:r(()=>[d("div",Sl,[d("div",Wl,[o(e,null,{default:r(()=>[o(v(V))]),_:1})]),a[23]||(a[23]=d("div",{class:"feature-content"},[d("h4",null,"数据分析"),d("p",null,"收入趋势图表、来源分布分析、增长率统计")],-1))])]),_:1}),o(c,{span:8},{default:r(()=>[d("div",Yl,[d("div",Bl,[o(e,null,{default:r(()=>[o(v(O))]),_:1})]),a[24]||(a[24]=d("div",{class:"feature-content"},[d("h4",null,"交易管理"),d("p",null,"查看所有交易记录，支持筛选和导出功能")],-1))])]),_:1}),o(c,{span:8},{default:r(()=>[d("div",Gl,[d("div",Hl,[o(e,null,{default:r(()=>[o(v(A))]),_:1})]),a[25]||(a[25]=d("div",{class:"feature-content"},[d("h4",null,"佣金管理"),d("p",null,"佣金明细查询、结算记录、分成比例管理")],-1))])]),_:1}),o(c,{span:8},{default:r(()=>[d("div",Il,[d("div",Ml,[o(e,null,{default:r(()=>[o(v(E))]),_:1})]),a[26]||(a[26]=d("div",{class:"feature-content"},[d("h4",null,"提现管理"),d("p",null,"处理用户提现申请、审核提现资格、资金划转")],-1))])]),_:1}),o(c,{span:8},{default:r(()=>[d("div",Tl,[d("div",Xl,[o(e,null,{default:r(()=>[o(v(L))]),_:1})]),a[27]||(a[27]=d("div",{class:"feature-content"},[d("h4",null,"报表导出"),d("p",null,"生成财务报表、数据导出、定期统计分析")],-1))])]),_:1})]),_:1})]),d("div",Jl,[a[29]||(a[29]=d("h3",null,"📊 财务指标说明",-1)),o($,{data:ya.value,style:{width:"100%"}},{default:r(()=>[o(J,{prop:"metric",label:"指标名称",width:"120"},{default:r(({row:l})=>[o(Fa,{type:l.color},{default:r(()=>[p(k(l.metric),1)]),_:2},1032,["type"])]),_:1}),o(J,{prop:"description",label:"指标说明"}),o(J,{prop:"calculation",label:"计算方式"}),o(J,{prop:"significance",label:"业务意义"})]),_:1},8,["data"])]),d("div",$l,[a[34]||(a[34]=d("h3",null,"💸 收入来源分析",-1)),d("div",la,[d("div",aa,[d("div",ea,[o(e,null,{default:r(()=>[o(v(K))]),_:1})]),a[30]||(a[30]=d("div",{class:"source-content"},[d("h4",null,"群组收入"),d("p",null,"用户加入付费群组产生的收入，是平台主要收入来源"),d("div",{class:"source-details"},[d("span",null,"占比：约60-70%"),d("span",null,"特点：稳定性高，增长潜力大")])],-1))]),d("div",sa,[d("div",ta,[o(e,null,{default:r(()=>[o(v(N))]),_:1})]),a[31]||(a[31]=d("div",{class:"source-content"},[d("h4",null,"佣金收入"),d("p",null,"分销商推广产生的佣金收入，激励推广积极性"),d("div",{class:"source-details"},[d("span",null,"占比：约20-25%"),d("span",null,"特点：增长快速，波动较大")])],-1))]),d("div",na,[d("div",ia,[o(e,null,{default:r(()=>[o(v(Q))]),_:1})]),a[32]||(a[32]=d("div",{class:"source-content"},[d("h4",null,"分站收入"),d("p",null,"分站管理费用和服务费收入"),d("div",{class:"source-details"},[d("span",null,"占比：约10-15%"),d("span",null,"特点：稳定增长，利润率高")])],-1))]),d("div",ua,[d("div",ca,[o(e,null,{default:r(()=>[o(v(S))]),_:1})]),a[33]||(a[33]=d("div",{class:"source-content"},[d("h4",null,"其他收入"),d("p",null,"广告收入、增值服务等其他收入来源"),d("div",{class:"source-details"},[d("span",null,"占比：约5-10%"),d("span",null,"特点：多样化，补充性收入")])],-1))])])]),a[57]||(a[57]=d("div",{class:"help-section"},[d("h3",null,"🏦 提现管理流程"),d("div",{class:"withdrawal-process"},[d("div",{class:"process-step"},[d("div",{class:"step-number"},"1"),d("div",{class:"step-content"},[d("h4",null,"用户申请"),d("p",null,"用户在前台提交提现申请，填写提现金额和收款信息")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"2"),d("div",{class:"step-content"},[d("h4",null,"系统审核"),d("p",null,"系统自动检查用户余额、提现限额、实名认证等条件")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"3"),d("div",{class:"step-content"},[d("h4",null,"人工审核"),d("p",null,"管理员审核提现申请，核实用户身份和提现合规性")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"4"),d("div",{class:"step-content"},[d("h4",null,"资金处理"),d("p",null,"审核通过后，系统自动或手动处理资金转账")])]),d("div",{class:"process-arrow"},"→"),d("div",{class:"process-step"},[d("div",{class:"step-number"},"5"),d("div",{class:"step-content"},[d("h4",null,"完成提现"),d("p",null,"资金到账，更新提现状态，发送通知给用户")])])])],-1)),d("div",da,[a[43]||(a[43]=d("h3",null,"📝 操作指南",-1)),o(Ua,{modelValue:ga.value,"onUpdate:modelValue":a[1]||(a[1]=l=>ga.value=l)},{default:r(()=>[o(Ra,{title:"如何查看财务数据？",name:"view-data"},{default:r(()=>[d("div",oa,[a[36]||(a[36]=d("ol",null,[d("li",null,"在工作台首页查看核心财务指标卡片"),d("li",null,'查看"收入趋势分析"图表了解收入变化'),d("li",null,'在"收入来源分布"图表中查看收入结构'),d("li",null,"在财务统计表格中查看详细的分时段数据"),d("li",null,"可以切换时间段查看不同期间的数据")],-1)),o(qa,{type:"info",closable:!1},{default:r(()=>a[35]||(a[35]=[p(" 💡 提示：数据每小时更新一次，增长率基于同期对比计算 ",-1)])),_:1,__:[35]})])]),_:1}),o(Ra,{title:"如何处理提现申请？",name:"withdrawal-process"},{default:r(()=>[d("div",ra,[a[38]||(a[38]=d("ol",null,[d("li",null,'点击"提现管理"进入提现管理页面'),d("li",null,"查看待审核的提现申请列表"),d("li",null,'点击"详情"查看申请人信息和提现详情'),d("li",null,"核实用户身份、余额、提现合规性"),d("li",null,'点击"通过"或"拒绝"处理申请'),d("li",null,"填写审核意见并确认操作")],-1)),o(qa,{type:"warning",closable:!1},{default:r(()=>a[37]||(a[37]=[p(" ⚠️ 注意：提现审核要严格核实用户身份，防范资金风险 ",-1)])),_:1,__:[37]})])]),_:1}),o(Ra,{title:"如何导出财务报表？",name:"export-report"},{default:r(()=>[d("div",pa,[a[40]||(a[40]=d("ol",null,[d("li",null,'点击"导出报表"按钮'),d("li",null,"选择报表类型（收入报表、佣金报表、提现报表等）"),d("li",null,"设置导出时间范围"),d("li",null,"选择导出格式（Excel、PDF等）"),d("li",null,'点击"确认导出"生成报表'),d("li",null,"下载生成的报表文件")],-1)),o(qa,{type:"success",closable:!1},{default:r(()=>a[39]||(a[39]=[p(" ✅ 说明：报表支持多种格式，可用于财务分析和对账 ",-1)])),_:1,__:[39]})])]),_:1}),o(Ra,{title:"如何分析收入趋势？",name:"revenue-analysis"},{default:r(()=>[d("div",va,[a[42]||(a[42]=d("ol",null,[d("li",null,"查看收入趋势图表，观察收入变化规律"),d("li",null,"切换不同时间段（7天、30天、90天、1年）"),d("li",null,"对比不同时期的收入水平和增长率"),d("li",null,"分析收入来源分布，识别主要收入驱动因素"),d("li",null,"结合业务活动分析收入波动原因")],-1)),o(qa,{type:"info",closable:!1},{default:r(()=>a[41]||(a[41]=[p(" 💡 建议：定期分析收入趋势，制定相应的运营策略 ",-1)])),_:1,__:[41]})])]),_:1})]),_:1},8,["modelValue"])]),d("div",fa,[a[50]||(a[50]=d("h3",null,"⚠️ 风险提示与注意事项",-1)),d("div",_a,[o(qa,{type:"error",closable:!1,style:{"margin-bottom":"15px"}},{title:r(()=>a[44]||(a[44]=[d("strong",null,"资金安全风险",-1)])),default:r(()=>[a[45]||(a[45]=d("ul",{style:{margin:"10px 0","padding-left":"20px"}},[d("li",null,"严格审核提现申请，防范虚假提现和洗钱风险"),d("li",null,"定期核对账务，确保资金流水准确无误"),d("li",null,"建立资金监控机制，及时发现异常交易")],-1))]),_:1,__:[45]}),o(qa,{type:"warning",closable:!1,style:{"margin-bottom":"15px"}},{title:r(()=>a[46]||(a[46]=[d("strong",null,"合规风险",-1)])),default:r(()=>[a[47]||(a[47]=d("ul",{style:{margin:"10px 0","padding-left":"20px"}},[d("li",null,"确保所有财务操作符合相关法律法规"),d("li",null,"完善财务记录，保留必要的凭证和文档"),d("li",null,"定期进行财务审计，确保合规经营")],-1))]),_:1,__:[47]}),o(qa,{type:"info",closable:!1},{title:r(()=>a[48]||(a[48]=[d("strong",null,"操作建议",-1)])),default:r(()=>[a[49]||(a[49]=d("ul",{style:{margin:"10px 0","padding-left":"20px"}},[d("li",null,"建议每日查看财务数据，及时发现异常情况"),d("li",null,"定期备份财务数据，防止数据丢失"),d("li",null,"建立多人审核机制，降低操作风险")],-1))]),_:1,__:[49]})])]),d("div",ma,[a[55]||(a[55]=d("h3",null,"❓ 常见问题",-1)),o(Ua,{modelValue:ba.value,"onUpdate:modelValue":a[2]||(a[2]=l=>ba.value=l)},{default:r(()=>[o(Ra,{title:"财务数据多久更新一次？",name:"faq1"},{default:r(()=>a[51]||(a[51]=[d("p",null,'财务数据每小时自动更新一次，重要指标如收入、佣金等实时更新。您也可以点击"刷新数据"按钮手动更新。',-1)])),_:1,__:[51]}),o(Ra,{title:"提现申请的处理时间是多久？",name:"faq2"},{default:r(()=>a[52]||(a[52]=[d("p",null,"一般情况下，提现申请在1-3个工作日内处理完成。具体时间取决于申请金额、用户等级和审核复杂度。",-1)])),_:1,__:[52]}),o(Ra,{title:"如何设置提现限额？",name:"faq3"},{default:r(()=>a[53]||(a[53]=[d("p",null,"可以在系统设置中配置不同用户等级的提现限额，包括单次提现限额、日提现限额和月提现限额。",-1)])),_:1,__:[53]}),o(Ra,{title:"财务报表可以定期自动生成吗？",name:"faq4"},{default:r(()=>a[54]||(a[54]=[d("p",null,"系统支持定期自动生成财务报表功能，可以设置每日、每周或每月自动生成并发送到指定邮箱。",-1)])),_:1,__:[54]})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-422fcd35"]]);export{ha as default};
