<template>
  <div class="distributor-dashboard" v-loading="isLoading" element-loading-text="加载中...">
    <!-- 性能监控组件 -->
    <PerformanceMonitor ref="performanceMonitor" />
    
    <!-- 页面头部 - 优化版 -->
    <div class="page-header">
      <div class="distributor-info">
        <el-avatar :size="60" :src="userStore.avatar || defaultAvatar" />
        <div class="info-content">
          <h2>{{ userStore.nickname || '分销员' }}</h2>
          <p>分销员ID: {{ distributorCode }}</p>
          <div class="status-tags">
            <el-tag type="success" effect="light">活跃分销员</el-tag>
            <el-tag type="primary" effect="light">{{ levelText }}</el-tag>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="goToCustomerManagement" :loading="buttonLoading.customer">
          <el-icon><User /></el-icon>
          客户管理
        </el-button>
        <el-button @click="goToGroupManagement" :loading="buttonLoading.group">
          <el-icon><Comment /></el-icon>
          群组管理
        </el-button>
      </div>
    </div>

    <!-- 核心数据统计 - 优化版 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6" v-for="(stat, index) in statsData" :key="index">
        <div class="stat-card" @click="handleStatClick(stat.key)">
          <div class="stat-icon" :style="{ backgroundColor: stat.color + '20', color: stat.color }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ stat.prefix }}{{ formatNumber(stat.value) }}{{ stat.suffix }}
            </div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-trend" v-if="stat.trend" :class="getTrendClass(stat.trend)">
              <el-icon><ArrowUp v-if="stat.trend > 0" /><ArrowDown v-else /></el-icon>
              {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 快捷操作面板 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快捷操作</span>
      </template>
      <el-row :gutter="15">
        <el-col :span="4">
          <div class="action-item" @click="goToCustomerManagement">
            <el-icon class="action-icon"><User /></el-icon>
            <span>客户管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="addCustomer">
            <el-icon class="action-icon"><UserFilled /></el-icon>
            <span>新增客户</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToGroupManagement">
            <el-icon class="action-icon"><Comment /></el-icon>
            <span>群组管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToPromotionLinks">
            <el-icon class="action-icon"><Link /></el-icon>
            <span>推广链接</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToCommissionLogs">
            <el-icon class="action-icon"><Money /></el-icon>
            <span>佣金查看</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToOrderList">
            <el-icon class="action-icon"><Tickets /></el-icon>
            <span>订单查看</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>收入趋势</span>
              <el-radio-group v-model="revenuePeriod" size="small" @change="loadRevenueData">
                <el-radio-button value="7d">近7天</el-radio-button>
                <el-radio-button value="30d">近30天</el-radio-button>
                <el-radio-button value="90d">近3个月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <LineChart
            :data="revenueChartData"
            :options="chartOptions"
            height="300px"
          />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>客户分布</span>
          </template>
          <DoughnutChart
            :data="customerDistributionData"
            :options="doughnutOptions"
            height="300px"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 客户动态和任务提醒 -->
    <el-row :gutter="20" class="info-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>客户动态</span>
              <el-button size="small" @click="loadCustomerActivities">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="customer-activities">
            <div v-for="activity in customerActivities" :key="activity.id" class="activity-item">
              <div class="activity-avatar">
                <el-avatar :size="32" :src="activity.customer?.avatar" />
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.created_at) }}</div>
              </div>
              <div class="activity-value" v-if="activity.value">
                <span class="value-amount">¥{{ activity.value }}</span>
              </div>
            </div>
            <div v-if="customerActivities.length === 0" class="empty-state">
              <el-empty description="暂无客户动态" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待跟进客户</span>
              <el-badge :value="pendingFollowUps.length" class="follow-up-badge">
                <el-button size="small" @click="goToFollowUpList">查看全部</el-button>
              </el-badge>
            </div>
          </template>
          <div class="follow-up-list">
            <div v-for="customer in pendingFollowUps" :key="customer.id" class="follow-up-item">
              <div class="customer-info">
                <div class="customer-name">{{ customer.name }}</div>
                <div class="customer-level">
                  <el-tag :type="getLevelColor(customer.level)" size="small">
                    {{ customer.level_text }}
                  </el-tag>
                </div>
              </div>
              <div class="follow-up-time">
                <span class="time-label">下次跟进:</span>
                <span class="time-value" :class="getFollowUpTimeClass(customer.next_follow_up)">
                  {{ formatFollowUpTime(customer.next_follow_up) }}
                </span>
              </div>
              <div class="follow-up-actions">
                <el-button size="small" type="primary" @click="quickFollowUp(customer)">
                  立即跟进
                </el-button>
              </div>
            </div>
            <div v-if="pendingFollowUps.length === 0" class="empty-state">
              <el-empty description="暂无待跟进客户" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 推广链接快速生成 -->
    <el-card class="promotion-card">
      <template #header>
        <span>我的推广链接</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="promotionLink"
            placeholder="您的专属推广链接"
            readonly
          >
            <template #prepend>推广链接</template>
            <template #append>
              <el-button @click="copyPromotionLink">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="generateQRCode">
            <el-icon><Setting /></el-icon>
            生成二维码
          </el-button>
          <el-button @click="goToPromotionLinks">
            <el-icon><Setting /></el-icon>
            更多工具
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 转化率详情对话框 -->
    <el-dialog v-model="showConversionDialog" title="转化率详情分析" width="800px" class="conversion-dialog">
      <div class="conversion-analysis">
        <div class="analysis-header">
          <h3>转化率趋势分析</h3>
          <el-date-picker
            v-model="conversionDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
          />
        </div>

        <div class="conversion-stats">
          <div class="stat-item">
            <div class="stat-value">{{ conversionStats.totalVisits }}</div>
            <div class="stat-label">总访问量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ conversionStats.totalConversions }}</div>
            <div class="stat-label">总转化数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ conversionStats.conversionRate }}%</div>
            <div class="stat-label">转化率</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">¥{{ conversionStats.avgOrderValue }}</div>
            <div class="stat-label">平均订单价值</div>
          </div>
        </div>

        <div class="conversion-chart">
          <h4>转化率趋势图</h4>
          <div class="chart-placeholder">
            <p>转化率趋势图表将在这里显示</p>
          </div>
        </div>

        <div class="conversion-sources">
          <h4>转化来源分析</h4>
          <el-table :data="conversionSources" size="small">
            <el-table-column prop="source" label="来源" />
            <el-table-column prop="visits" label="访问量" />
            <el-table-column prop="conversions" label="转化数" />
            <el-table-column prop="rate" label="转化率">
              <template #default="{ row }">
                <span>{{ row.rate }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 二维码显示对话框 -->
    <el-dialog v-model="showQRDialog" title="推广二维码" width="400px" class="qr-dialog">
      <div v-if="qrCodeData" class="qr-content">
        <div class="qr-info">
          <h4>{{ qrCodeData.name }} 的推广二维码</h4>
          <p>推广码: {{ qrCodeData.code }}</p>
        </div>

        <div class="qr-image-container">
          <div class="qr-placeholder">
            <el-icon size="80"><QrCode /></el-icon>
            <p>二维码图片</p>
          </div>
        </div>

        <div class="qr-actions">
          <el-button type="primary" @click="downloadQRCode">
            <el-icon><Download /></el-icon>
            下载二维码
          </el-button>
          <el-button @click="copyQRLink">
            <el-icon><DocumentCopy /></el-icon>
            复制链接
          </el-button>
        </div>

        <div class="qr-tips">
          <p><el-icon><InfoFilled /></el-icon> 扫描此二维码或分享链接给客户，客户通过此链接注册将自动绑定为您的下级</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElNotification } from 'element-plus'
import {
  User, UserFilled, Comment, Link, Money, Tickets, Refresh,
  DocumentCopy, Setting, TrendCharts, ArrowUp, ArrowDown,
  QrCode, Download, InfoFilled
} from '@element-plus/icons-vue'
import StatCard from '@/components/StatCard.vue'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import PerformanceMonitor from '@/components/PerformanceMonitor.vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isLoading = ref(false)
const revenuePeriod = ref('30d')
const defaultAvatar = '/avatars/default.jpg'

// 对话框状态
const showConversionDialog = ref(false)
const showQRDialog = ref(false)
const qrCodeData = ref(null)

// 转化率分析数据
const conversionDateRange = ref([])
const conversionStats = reactive({
  totalVisits: 1250,
  totalConversions: 89,
  conversionRate: 7.12,
  avgOrderValue: 156.8
})

const conversionSources = ref([
  { source: '微信群', visits: 450, conversions: 32, rate: 7.11 },
  { source: '朋友圈', visits: 380, conversions: 28, rate: 7.37 },
  { source: '直接分享', visits: 280, conversions: 19, rate: 6.79 },
  { source: '其他渠道', visits: 140, conversions: 10, rate: 7.14 }
])

// 加载状态管理
const buttonLoading = reactive({
  customer: false,
  group: false,
  copy: false,
  qrcode: false
})

const chartLoading = reactive({
  revenue: false,
  customer: false
})

const dataLoading = reactive({
  activities: false,
  followUps: false
})

// 数据状态
const customerActivities = ref([])
const pendingFollowUps = ref([])
const promotionLink = ref('')
const promotionStats = reactive({
  todayClicks: 0,
  totalClicks: 0
})

// 统计数据
const statsData = ref([
  {
    key: 'customers',
    title: '客户总数',
    value: 0,
    icon: 'User',
    color: '#409EFF',
    trend: 0,
    prefix: '',
    suffix: ''
  },
  {
    key: 'groups',
    title: '活跃群组',
    value: 0,
    icon: 'Comment',
    color: '#67C23A',
    trend: 0,
    prefix: '',
    suffix: ''
  },
  {
    key: 'commission',
    title: '本月佣金',
    value: 0,
    icon: 'Money',
    color: '#E6A23C',
    trend: 0,
    prefix: '¥',
    suffix: ''
  },
  {
    key: 'conversion',
    title: '转化率',
    value: 0,
    icon: 'TrendCharts',
    color: '#F56C6C',
    trend: 0,
    prefix: '',
    suffix: '%'
  }
])

// 快捷操作
const quickActions = ref([
  {
    key: 'customer-management',
    title: '客户管理',
    icon: 'User',
    color: '#409EFF',
    disabled: false,
    badge: 0
  },
  {
    key: 'add-customer',
    title: '新增客户',
    icon: 'UserFilled',
    color: '#67C23A',
    disabled: false
  },
  {
    key: 'group-management',
    title: '群组管理',
    icon: 'Comment',
    color: '#E6A23C',
    disabled: false,
    badge: 0
  },
  {
    key: 'promotion-links',
    title: '推广链接',
    icon: 'Link',
    color: '#409EFF',
    disabled: false
  },
  {
    key: 'commission-logs',
    title: '佣金查看',
    icon: 'Money',
    color: '#F56C6C',
    disabled: false
  },
  {
    key: 'order-list',
    title: '订单查看',
    icon: 'Tickets',
    color: '#909399',
    disabled: false
  }
])

// 图表数据
const revenueChartData = ref({
  labels: [],
  datasets: [{
    label: '佣金收入',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4,
    fill: true
  }]
})

const customerDistributionData = ref({
  labels: ['A级客户', 'B级客户', 'C级客户', 'D级客户'],
  datasets: [{
    data: [25, 45, 60, 26],
    backgroundColor: ['#F56C6C', '#E6A23C', '#409EFF', '#67C23A'],
    borderWidth: 0,
    hoverOffset: 4
  }]
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index'
  },
  plugins: {
    legend: {
      display: true,
      position: 'top'
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: '#409EFF',
      borderWidth: 1
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)'
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom',
      labels: {
        padding: 20,
        usePointStyle: true
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff'
    }
  }
}

// 计算属性
const distributorCode = computed(() => {
  return userStore.userInfo?.distributor_code || 'D' + (userStore.userInfo?.id || '001')
})

const levelText = computed(() => {
  const level = userStore.userInfo?.distributor_level || 'C'
  const levelMap = {
    'A': 'A级分销员',
    'B': 'B级分销员', 
    'C': 'C级分销员',
    'D': 'D级分销员'
  }
  return levelMap[level] || 'C级分销员'
})

// 方法
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const getTrendClass = (trend) => {
  return trend > 0 ? 'trend-up' : 'trend-down'
}

const loadStats = async () => {
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 更新统计数据
    statsData.value[0].value = 156
    statsData.value[0].trend = 8.2
    statsData.value[1].value = 23
    statsData.value[1].trend = 15.6
    statsData.value[2].value = 8650
    statsData.value[2].trend = 23.4
    statsData.value[3].value = 12.5
    statsData.value[3].trend = -2.1
    
    // 更新快捷操作徽章
    quickActions.value[0].badge = 5 // 待处理客户
    quickActions.value[2].badge = 2 // 待审核群组
    
    console.log('统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const loadRevenueData = async () => {
  chartLoading.revenue = true
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockData = {
      '7d': {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [120, 190, 300, 500, 200, 300, 450]
      },
      '30d': {
        labels: Array.from({length: 30}, (_, i) => `${i + 1}日`),
        data: Array.from({length: 30}, () => Math.floor(Math.random() * 1000) + 200)
      },
      '90d': {
        labels: ['第1月', '第2月', '第3月'],
        data: [8000, 12000, 15000]
      }
    }
    
    const data = mockData[revenuePeriod.value]
    revenueChartData.value = {
      labels: data.labels,
      datasets: [{
        label: '佣金收入',
        data: data.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4,
        fill: true,
        pointBackgroundColor: '#409EFF',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      }]
    }
    
    console.log('收入图表数据加载完成')
  } catch (error) {
    console.error('加载收入数据失败:', error)
    ElMessage.error('加载收入数据失败')
  } finally {
    chartLoading.revenue = false
  }
}

const refreshRevenueData = () => {
  loadRevenueData()
  ElMessage.success('收入数据已刷新')
}

const refreshCustomerData = async () => {
  chartLoading.customer = true
  try {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 随机更新客户分布数据
    const newData = [
      Math.floor(Math.random() * 50) + 20,
      Math.floor(Math.random() * 60) + 30,
      Math.floor(Math.random() * 80) + 40,
      Math.floor(Math.random() * 40) + 15
    ]
    
    customerDistributionData.value.datasets[0].data = newData
    ElMessage.success('客户分布数据已刷新')
  } catch (error) {
    ElMessage.error('刷新客户数据失败')
  } finally {
    chartLoading.customer = false
  }
}

const loadCustomerActivities = async () => {
  dataLoading.activities = true
  try {
    await new Promise(resolve => setTimeout(resolve, 400))
    
    customerActivities.value = [
      {
        id: 1,
        title: '新客户注册',
        description: '客户"张三"通过推广链接注册',
        created_at: new Date(),
        customer: { avatar: defaultAvatar }
      },
      {
        id: 2,
        title: '客户下单',
        description: '客户"李四"购买了VIP群组',
        created_at: new Date(Date.now() - 3600000),
        value: 299,
        customer: { avatar: defaultAvatar }
      },
      {
        id: 3,
        title: '客户升级',
        description: '客户"王五"升级为A级客户',
        created_at: new Date(Date.now() - 7200000),
        customer: { avatar: defaultAvatar }
      },
      {
        id: 4,
        title: '佣金到账',
        description: '获得推广佣金奖励',
        created_at: new Date(Date.now() - 10800000),
        value: 150,
        customer: { avatar: defaultAvatar }
      }
    ]
    
    console.log('客户动态数据加载完成')
  } catch (error) {
    console.error('加载客户动态失败:', error)
    ElMessage.error('加载客户动态失败')
  } finally {
    dataLoading.activities = false
  }
}

const loadPendingFollowUps = async () => {
  dataLoading.followUps = true
  try {
    await new Promise(resolve => setTimeout(resolve, 350))
    
    pendingFollowUps.value = [
      {
        id: 1,
        name: '王五',
        level: 'A',
        level_text: 'A级客户',
        next_follow_up: new Date(Date.now() + 86400000),
        following: false
      },
      {
        id: 2,
        name: '赵六',
        level: 'B', 
        level_text: 'B级客户',
        next_follow_up: new Date(Date.now() - 3600000),
        following: false
      },
      {
        id: 3,
        name: '孙七',
        level: 'C',
        level_text: 'C级客户',
        next_follow_up: new Date(Date.now() + 172800000),
        following: false
      }
    ]
    
    console.log('待跟进客户数据加载完成')
  } catch (error) {
    console.error('加载待跟进客户失败:', error)
    ElMessage.error('加载待跟进客户失败')
  } finally {
    dataLoading.followUps = false
  }
}

const generatePromotionLink = () => {
  const userId = userStore.userInfo?.id || '001'
  promotionLink.value = `${window.location.origin}/register?distributor=${userId}`
  
  // 模拟推广统计数据
  promotionStats.todayClicks = Math.floor(Math.random() * 50) + 10
  promotionStats.totalClicks = Math.floor(Math.random() * 1000) + 500
}

// 事件处理
const handleStatClick = (key) => {
  console.log('点击统计卡片:', key)
  switch (key) {
    case 'customers':
      goToCustomerManagement()
      break
    case 'groups':
      goToGroupManagement()
      break
    case 'commission':
      goToCommissionLogs()
      break
    case 'conversion':
      showConversionDetail()
      break
  }
}

const handleActionClick = (action) => {
  if (action.disabled) {
    ElMessage.warning(`${action.title}功能暂时不可用`)
    return
  }
  
  console.log('点击快捷操作:', action.key)
  switch (action.key) {
    case 'customer-management':
      goToCustomerManagement()
      break
    case 'add-customer':
      addCustomer()
      break
    case 'group-management':
      goToGroupManagement()
      break
    case 'promotion-links':
      goToPromotionLinks()
      break
    case 'commission-logs':
      goToCommissionLogs()
      break
    case 'order-list':
      goToOrderList()
      break
  }
}

// 导航方法
const goToCustomerManagement = async () => {
  buttonLoading.customer = true
  try {
    await router.push('/distribution/customers')
    ElNotification.success({
      title: '跳转成功',
      message: '已跳转到客户管理页面'
    })
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.warning('页面跳转失败，请稍后重试')
  } finally {
    buttonLoading.customer = false
  }
}

const addCustomer = async () => {
  try {
    await router.push('/distribution/customers?action=create')
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.warning('页面跳转失败，请稍后重试')
  }
}

const goToGroupManagement = async () => {
  buttonLoading.group = true
  try {
    await router.push('/distributor/group-management')
    ElNotification.success({
      title: '跳转成功',
      message: '已跳转到群组管理页面'
    })
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.warning('页面跳转失败，请稍后重试')
  } finally {
    buttonLoading.group = false
  }
}

const goToPromotionLinks = async () => {
  try {
    await router.push('/distributor/promotion-links')
    ElNotification.success({
      title: '跳转成功',
      message: '已跳转到推广链接管理页面'
    })
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.warning('页面跳转失败，请稍后重试')
  }
}

const goToCommissionLogs = async () => {
  try {
    await router.push('/distributor/commission-logs')
    ElNotification.success({
      title: '跳转成功',
      message: '已跳转到佣金查看页面'
    })
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.warning('页面跳转失败，请稍后重试')
  }
}

const goToOrderList = async () => {
  try {
    await router.push('/distributor/order-list')
    ElNotification.success({
      title: '跳转成功',
      message: '已跳转到订单查看页面'
    })
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.warning('页面跳转失败，请稍后重试')
  }
}

const goToFollowUpList = async () => {
  try {
    await router.push('/distribution/customers?tab=follow-up')
  } catch (error) {
    console.error('路由跳转失败:', error)
    ElMessage.warning('页面跳转失败，请稍后重试')
  }
}

const quickFollowUp = async (customer) => {
  customer.following = true
  try {
    await new Promise(resolve => setTimeout(resolve, 800))
    ElNotification.success({
      title: '跟进成功',
      message: `已为客户 ${customer.name} 创建跟进任务`
    })
    
    // 更新跟进时间
    customer.next_follow_up = new Date(Date.now() + 86400000 * 7) // 一周后
  } catch (error) {
    ElMessage.error('创建跟进任务失败')
  } finally {
    customer.following = false
  }
}

const copyPromotionLink = async () => {
  buttonLoading.copy = true
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(promotionLink.value)
      ElNotification.success({
        title: '复制成功',
        message: '推广链接已复制到剪贴板'
      })
    } else {
      // 降级处理
      const textArea = document.createElement('textarea')
      textArea.value = promotionLink.value
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElNotification.success({
        title: '复制成功',
        message: '推广链接已复制到剪贴板'
      })
    }
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  } finally {
    buttonLoading.copy = false
  }
}

const generateQRCode = async () => {
  buttonLoading.qrcode = true
  try {
    // 模拟生成二维码
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成推广二维码数据
    const qrData = {
      url: `https://app.example.com/invite?code=${distributorStore.distributorCode}`,
      code: distributorStore.distributorCode,
      name: distributorStore.distributorInfo.name
    }

    // 显示二维码对话框
    showQRCodeDialog(qrData)

    ElMessage.success('推广二维码生成成功！')
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败，请重试')
  } finally {
    buttonLoading.qrcode = false
  }
}

// 新增功能方法
const showConversionDetail = () => {
  // 显示转化率详情对话框
  showConversionDialog.value = true
}

const showQRCodeDialog = (qrData) => {
  // 显示二维码对话框
  qrCodeData.value = qrData
  showQRDialog.value = true
}

const downloadQRCode = () => {
  ElMessage.success('二维码下载功能开发中')
}

const copyQRLink = () => {
  if (qrCodeData.value?.url) {
    navigator.clipboard.writeText(qrCodeData.value.url).then(() => {
      ElMessage.success('推广链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
  }
}

// 工具方法
const getLevelColor = (level) => {
  const colors = {
    'A': 'danger',
    'B': 'warning',
    'C': 'primary',
    'D': 'info'
  }
  return colors[level] || 'info'
}

const getFollowUpTimeClass = (time) => {
  const now = new Date()
  const followUpTime = new Date(time)
  
  if (followUpTime < now) return 'overdue'
  if (followUpTime - now < 86400000) return 'urgent'
  return 'normal'
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFollowUpTime = (time) => {
  const now = new Date()
  const followUpTime = new Date(time)
  const diff = followUpTime - now
  
  if (diff < 0) {
    const hours = Math.floor(Math.abs(diff) / (1000 * 60 * 60))
    return `逾期${hours}小时`
  } else if (diff < 86400000) {
    const hours = Math.floor(diff / (1000 * 60 * 60))
    return `${hours}小时后`
  } else {
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    return `${days}天后`
  }
}

// 生命周期
onMounted(async () => {
  console.log('优化版分销员工作台开始加载...')
  isLoading.value = true
  
  try {
    // 并行加载所有数据
    await Promise.all([
      loadStats(),
      loadRevenueData(),
      loadCustomerActivities(),
      loadPendingFollowUps()
    ])
    
    generatePromotionLink()
    
    // 确保DOM更新完成
    await nextTick()
    
    console.log('优化版分销员工作台加载完成')
    ElNotification.success({
      title: '加载完成',
      message: '工作台数据已全部加载完成',
      duration: 2000
    })
  } catch (error) {
    console.error('页面数据加载失败:', error)
    ElMessage.error('页面数据加载失败，请刷新重试')
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
.distributor-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  color: white;
}

.distributor-info {
  display: flex;
  align-items: center;
}

.info-content {
  margin-left: 20px;
}

.info-content h2 {
  margin: 0 0 8px 0;
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.info-content p {
  margin: 0 0 12px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.status-tags {
  display: flex;
  gap: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: #409EFF;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}

.quick-actions-card {
  margin-bottom: 24px;
  border-radius: 16px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #f0f2f5;
  background: white;
  position: relative;
  overflow: hidden;
}

.action-item:hover:not(.action-disabled) {
  background: #f8f9ff;
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.action-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-icon {
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.action-item:hover:not(.action-disabled) .action-icon {
  transform: scale(1.1);
}

.action-text {
  font-size: 14px;
  color: #606266;
  text-align: center;
  font-weight: 500;
}

.action-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  min-height: 300px;
  position: relative;
}

.info-row {
  margin-bottom: 24px;
}

.customer-activities,
.follow-up-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.customer-activities::-webkit-scrollbar,
.follow-up-list::-webkit-scrollbar {
  width: 6px;
}

.customer-activities::-webkit-scrollbar-thumb,
.follow-up-list::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

.activity-item,
.follow-up-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: background-color 0.3s ease;
}

.activity-item:hover,
.follow-up-item:hover {
  background: #f8f9ff;
  border-radius: 8px;
  margin: 0 -8px;
  padding: 16px 8px;
}

.activity-item:last-child,
.follow-up-item:last-child {
  border-bottom: none;
}

.activity-avatar {
  margin-right: 16px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
  font-size: 14px;
}

.activity-desc {
  color: #606266;
  font-size: 13px;
  margin-bottom: 6px;
  line-height: 1.4;
}

.activity-time {
  color: #909399;
  font-size: 12px;
}

.activity-value {
  text-align: right;
  margin-left: 16px;
}

.value-amount {
  color: #67C23A;
  font-weight: 700;
  font-size: 16px;
}

.follow-up-item {
  align-items: flex-start;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.follow-up-time {
  margin: 0 20px;
  text-align: center;
  min-width: 100px;
}

.time-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.time-value {
  font-size: 13px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
}

.time-value.normal {
  color: #67C23A;
  background: rgba(103, 194, 58, 0.1);
}

.time-value.urgent {
  color: #E6A23C;
  background: rgba(230, 162, 60, 0.1);
}

.time-value.overdue {
  color: #F56C6C;
  background: rgba(245, 108, 108, 0.1);
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.promotion-card {
  border-radius: 16px;
  overflow: hidden;
}

.promotion-stats {
  display: flex;
  gap: 12px;
}

.promotion-input {
  border-radius: 8px;
}

.promotion-actions {
  display: flex;
  gap: 12px;
  width: 100%;
}

.promotion-actions .el-button {
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row .el-col {
    margin-bottom: 16px;
  }
  
  .charts-row .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .distributor-dashboard {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }
  
  .distributor-info {
    flex-direction: column;
    text-align: center;
  }
  
  .info-content {
    margin-left: 0;
    margin-top: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .action-item {
    padding: 20px 12px;
  }
  
  .follow-up-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .follow-up-time {
    margin: 0;
  }
  
  .promotion-actions {
    flex-direction: column;
  }
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 卡片阴影动画 */
.el-card {
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* 对话框样式 */
.conversion-dialog {
  .analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      color: #1f2937;
    }
  }

  .conversion-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;

    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f8fafc;
      border-radius: 8px;

      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #6b7280;
      }
    }
  }

  .conversion-chart {
    margin-bottom: 30px;

    h4 {
      margin: 0 0 16px 0;
      color: #1f2937;
    }

    .chart-placeholder {
      height: 200px;
      background: #f8fafc;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #6b7280;
    }
  }

  .conversion-sources {
    h4 {
      margin: 0 0 16px 0;
      color: #1f2937;
    }
  }
}

.qr-dialog {
  .qr-content {
    text-align: center;

    .qr-info {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 8px 0;
        color: #1f2937;
      }

      p {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
      }
    }

    .qr-image-container {
      margin: 20px 0;

      .qr-placeholder {
        width: 200px;
        height: 200px;
        margin: 0 auto;
        background: #f8fafc;
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #6b7280;

        p {
          margin: 8px 0 0 0;
          font-size: 14px;
        }
      }
    }

    .qr-actions {
      margin: 20px 0;
      display: flex;
      gap: 12px;
      justify-content: center;
    }

    .qr-tips {
      background: #eff6ff;
      border: 1px solid #bfdbfe;
      border-radius: 8px;
      padding: 12px;
      margin-top: 20px;

      p {
        margin: 0;
        font-size: 12px;
        color: #1e40af;
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }
}
</style>
